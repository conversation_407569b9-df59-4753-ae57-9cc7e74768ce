import { api } from '@/lib/api/pos/pos-api'
import { createItemsExcelBlob } from '../utils'

import type {
  ItemsInCityApiResponse,
  GetItemsInCityParams,
  DeleteItemInCityParams,
  DeleteMultipleItemsInCityParams,
  CreateItemInCityRequest,
  UpdateItemInCityRequest,
  GetItemByListIdParams,
  GetItemByIdParams,
  UpdateItemStatusRequest,
  DownloadTemplateParams,
  ImportItemsRequest,
  ApiError
} from './items-in-city-types'

interface CityData {
  id: string
  city_name: string
}

const getAllCityUids = (): string[] => {
  try {
    const citiesData = localStorage.getItem('pos_cities_data')
    if (citiesData) {
      const cities: CityData[] = JSON.parse(citiesData)
      return cities.map(city => city.id)
    }
  } catch (_e) {
    // ignore localStorage parse errors
  }
  return []
}

const getActualCityUid = (cityUidOrName: string): string | null => {
  if (!cityUidOrName) return null

  const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  if (guidRegex.test(cityUidOrName)) {
    return cityUidOrName
  }

  try {
    const citiesData = localStorage.getItem('pos_cities_data')
    if (citiesData) {
      const cities: CityData[] = JSON.parse(citiesData)
      const cityByName = cities.find(city => city.city_name === cityUidOrName)
      return cityByName?.id || null
    }
  } catch (_e) {
    return null
  }

  return null
}

const itemsInCityCache = new Map<string, { data: ItemsInCityApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<ItemsInCityApiResponse>>()
const CACHE_DURATION = 10 * 60 * 1000 // 10 minutes

function isApiError(error: unknown): error is ApiError {
  return typeof error === 'object' && error !== null && 'response' in error
}


export const itemsInCityApiService = {
  getItemsInCity: async (params: GetItemsInCityParams): Promise<ItemsInCityApiResponse> => {
    const actualCityUid =
      params.city_uid && params.city_uid !== 'all' ? getActualCityUid(params.city_uid) : params.city_uid

    const activeValue = params.active !== undefined ? params.active.toString() : 'undefined'
    const skipLimitValue = params.skip_limit ? 'true' : 'false'
    const requestKey = `${params.company_uid}-${params.brand_uid}-${params.page || 1}-${actualCityUid || 'all'}-${params.list_city_uid || 'all'}-${params.item_type_uid || 'all'}-${params.time_sale_date_week || ''}-${activeValue}-${params.reverse || 0}-${params.search || ''}-${params.limit || 50}-${skipLimitValue}`

    const cached = itemsInCityCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams()
        queryParams.append('company_uid', params.company_uid)
        queryParams.append('brand_uid', params.brand_uid)

        if (params.page) {
          queryParams.append('page', params.page.toString())
        }

        if (params.item_type_uid) {
          queryParams.append('item_type_uid', params.item_type_uid)
        }

        if (params.list_city_uid) {
          queryParams.append('list_city_uid', params.list_city_uid)
        } else if (params.city_uid && params.city_uid !== 'all') {
          const actualCityUid = getActualCityUid(params.city_uid)
          if (actualCityUid) {
            queryParams.append('city_uid', actualCityUid)
          }
        } else {
          const allCityUids = getAllCityUids()
          if (allCityUids.length > 0) {
            queryParams.append('list_city_uid', allCityUids.join(','))
          }
        }

        if (params.time_sale_date_week) {
          queryParams.append('time_sale_date_week', params.time_sale_date_week)
        }

        if (params.reverse !== undefined) {
          queryParams.append('reverse', params.reverse.toString())
        }

        if (params.search) {
          queryParams.append('search', params.search)
        }

        if (params.active !== undefined) {
          queryParams.append('active', params.active.toString())
        }

        if (params.limit) {
          queryParams.append('limit', params.limit.toString())
        }

        if (params.skip_limit) {
          queryParams.append('skip_limit', 'true')
        }

        const response = await api.get(`/mdata/v1/items?${queryParams.toString()}`)

        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from items in city API')
        }

        const result = response.data as ItemsInCityApiResponse

        itemsInCityCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)
    return requestPromise
  },

  deleteItemInCity: async (params: DeleteItemInCityParams): Promise<void> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('company_uid', params.company_uid)
      queryParams.append('brand_uid', params.brand_uid)
      queryParams.append('id', params.id)

      await api.delete(`/mdata/v1/item?${queryParams.toString()}`)

      itemsInCityCache.clear()
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Item not found.')
      }
      throw error
    }
  },

  deleteMultipleItemsInCity: async (params: DeleteMultipleItemsInCityParams): Promise<void> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('company_uid', params.company_uid)
      queryParams.append('brand_uid', params.brand_uid)
      queryParams.append('list_item_uid', params.list_item_uid.join(','))

      await api.delete(`/mdata/v1/items?${queryParams.toString()}`)

      itemsInCityCache.clear()
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Items not found.')
      }
      throw error
    }
  },

  downloadTemplate: async (params: DownloadTemplateParams): Promise<Blob> => {
    // Convert city name to GUID if needed
    const actualCityUid = params.city_uid && params.city_uid !== 'all' ? getActualCityUid(params.city_uid) : null

    const queryParams = new URLSearchParams({
      skip_limit: 'true',
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      ...(actualCityUid && { city_uid: actualCityUid }),
      ...(params.item_type_uid &&
        params.item_type_uid !== 'all' && {
          item_type_uid: params.item_type_uid
        }),
      ...(params.active && params.active !== 'all' && { active: params.active })
    })

    const response = await api.get(`/mdata/v1/items?${queryParams}`)
    const data = Array.isArray(response.data?.data) ? response.data.data : []
    return await createItemsExcelBlob(data, params.referenceData)
  },

  fetchItemsData: async (params: DownloadTemplateParams): Promise<any[]> => {
    // Convert city name to GUID if needed
    const actualCityUid = params.city_uid && params.city_uid !== 'all' ? getActualCityUid(params.city_uid) : null

    const queryParams = new URLSearchParams({
      skip_limit: 'true',
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      ...(actualCityUid && { city_uid: actualCityUid }),
      ...(params.item_type_uid &&
        params.item_type_uid !== 'all' && {
          item_type_uid: params.item_type_uid
        }),
      ...(params.active && params.active !== 'all' && { active: params.active })
    })

    const response = await api.get(`/mdata/v1/items?${queryParams}`)
    return Array.isArray(response.data?.data) ? response.data.data : []
  },

  createItemInCity: async (data: CreateItemInCityRequest): Promise<unknown> => {
    try {
      const response = await api.post('/mdata/v1/item', data)

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  updateItemInCity: async (data: UpdateItemInCityRequest): Promise<unknown> => {
    try {
      const response = await api.put('/mdata/v1/item', data, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  getItemByListId: async (params: GetItemByListIdParams): Promise<{ data: unknown }> => {
    try {
      const queryParams = new URLSearchParams({
        skip_limit: 'true',
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        is_all: 'true',
        list_item_id: params.list_item_id
      })

      const response = await api.get(`/mdata/v1/items?${queryParams}`, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      const items = Array.isArray(response.data?.data) ? response.data.data : []
      if (!items.length) {
        throw new Error('Item not found')
      }

      return { data: items[0] }
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Item not found.')
      }
      throw error
    }
  },

  getItemById: async (params: GetItemByIdParams): Promise<{ data: unknown }> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('id', params.id)

      if (params.company_uid) {
        queryParams.append('company_uid', params.company_uid)
      }

      if (params.brand_uid) {
        queryParams.append('brand_uid', params.brand_uid)
      }

      const response = await api.get(`/mdata/v1/item?${queryParams.toString()}`)

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from item detail API')
      }

      return response.data as { data: unknown }
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Item not found.')
      }
      throw error
    }
  },

  importItems: async (params: ImportItemsRequest) => {
    const response = await api.post('/mdata/v1/items/import', {
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      items: params.items
    })
    return response.data
  },

  updateItemStatus: async (data: UpdateItemStatusRequest): Promise<unknown> => {
    try {
      const currentItem = await itemsInCityApiService.getItemById({ id: data.id })

      const updateData = {
        ...(currentItem.data as Record<string, unknown>),
        active: data.active,
        company_uid: data.company_uid,
        brand_uid: data.brand_uid
      }

      const response = await api.put('/mdata/v1/item', updateData, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  clearCache: () => {
    itemsInCityCache.clear()
    pendingRequests.clear()
  },

  getCacheStats: () => ({ cacheSize: itemsInCityCache.size, pendingRequests: pendingRequests.size })
}
