import{h as C,r as y,j as e,B as j}from"./index-CVQ6JZo2.js";import"./date-range-picker-CXbMaowj.js";import{u as S,F as k,a as c,b as l,c as n,d,e as x}from"./form-CzmGigtT.js";import{I as p}from"./input-Al6WtUZF.js";import{C as w}from"./checkbox-BfLSzhzg.js";import{c as E,u as P}from"./use-account-management-BE8zW77I.js";import{s as V}from"./zod-ByV4TDQ9.js";import"./calendar-BszTCdZH.js";import"./createLucideIcon-DKVxsQv7.js";import"./index-CtK-wKtB.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-CxgpqvrH.js";import"./react-icons.esm-DMMA_g0o.js";import"./popover-DnoSPJNX.js";import"./select-BFhNE0YE.js";import"./index-LVHINuqD.js";import"./index-nc1u7392.js";import"./check-BE_j5GZD.js";function A(){const a=S({resolver:V(E),defaultValues:{username:"",email:"",password:"",permissions:[]}});return{form:a,resetForm:()=>{a.reset({username:"",email:"",password:"",permissions:[]})},isValid:a.formState.isValid,errors:a.formState.errors}}function M(){const a=C(),{form:t,resetForm:h}=A(),{createUser:f,permissionCategories:g,isLoading:u}=P(),[i,m]=y.useState([]),N=async s=>{try{await f({...s,permissions:i}),h(),m([]),a({to:"/general-setups/account"})}catch(r){console.error("Error creating account:",r)}},b=(s,r)=>{m(o=>r?[...o,s]:o.filter(F=>F!==s)),t.setValue("permissions",r?[...i,s]:i.filter(o=>o!==s))},v=()=>{h(),m([]),a({to:"/general-setups/account"})};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h1",{className:"text-2xl font-semibold",children:"Tạo tài khoản mới"})}),e.jsx("div",{className:"max-w-4xl",children:e.jsx(k,{...t,children:e.jsxs("form",{onSubmit:t.handleSubmit(N),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx(c,{control:t.control,name:"username",render:({field:s})=>e.jsxs(l,{children:[e.jsx(n,{children:"Tên người dùng *"}),e.jsx(d,{children:e.jsx(p,{placeholder:"Nhập tên người dùng",...s})}),e.jsx(x,{})]})}),e.jsx(c,{control:t.control,name:"email",render:({field:s})=>e.jsxs(l,{children:[e.jsx(n,{children:"Email"}),e.jsx(d,{children:e.jsx(p,{placeholder:"Nhập email",type:"email",...s})}),e.jsx(x,{})]})})]}),e.jsx(c,{control:t.control,name:"password",render:({field:s})=>e.jsxs(l,{children:[e.jsx(n,{children:"Mật khẩu *"}),e.jsx(d,{children:e.jsx(p,{placeholder:"Nhập mật khẩu",type:"password",...s})}),e.jsx(x,{})]})}),e.jsxs("div",{children:[e.jsx(n,{className:"text-base font-medium",children:"Cài đặt quyền cho tài khoản *"}),e.jsx("div",{className:"mt-4 space-y-6",children:g.map(s=>e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-sm font-medium",children:s.name}),e.jsx("div",{className:"grid grid-cols-1 gap-2 md:grid-cols-2",children:s.permissions.map(r=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(w,{id:r.id,checked:i.includes(r.id),onCheckedChange:o=>b(r.id,o)}),e.jsx("label",{htmlFor:r.id,className:"text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:r.name})]},r.id))})]},s.name))}),t.formState.errors.permissions&&e.jsx("p",{className:"text-destructive mt-2 text-sm",children:t.formState.errors.permissions.message})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx(j,{type:"button",variant:"outline",onClick:v,children:"Hủy"}),e.jsx(j,{type:"submit",disabled:u,children:u?"Đang tạo...":"Tạo tài khoản"})]})]})})})]})}const Z=M;export{Z as component};
