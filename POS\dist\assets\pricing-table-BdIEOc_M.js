import{r as a,j as e}from"./index-UcdZ5AHH.js";import{M as y}from"./main-C1Ukb9JX.js";import{u as S,d as C,e as R,f as b}from"./index-Bfo4u1qA.js";import{u as w}from"./useQuery-B4yhTgGk.js";import{c as T}from"./crm-api-APQEjHWd.js";import{C as M}from"./query-keys-DQo7uRnN.js";import"./pos-api-j20LMGrC.js";import"./user-9ajIul7r.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{T as V,a as H,b as u,c as F,d as L,e as v}from"./table-DHWQVnPn.js";import{T as P}from"./table-skeleton-Ul7YLj1J.js";import{D as x}from"./data-table-column-header-CZeJA5fJ.js";import"./utils-km2FGkQ4.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./select-DOexGcsG.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";import"./skeleton-B5wLl279.js";import"./dropdown-menu-D3XvynCv.js";import"./index-iiVug-md.js";const B=async(t={})=>{try{return(await T.get("billing/get-list-service",{params:{pos_parent:"BRAND-953H",limit:1e3,...t}})).data}catch{throw new Error("Failed to fetch billing services")}},E=(t={})=>{const[s,m]=a.useState({limit:1e3,...t}),{data:r,isLoading:d,error:l,refetch:h}=w({queryKey:[M.BILLING_SERVICES,s],queryFn:()=>B(s)});return{services:(r==null?void 0:r.services)||[],isLoading:d,error:(l==null?void 0:l.message)||null,refetch:h,params:s,setParams:m}};function K(){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"BẢNG GIÁ"})}),e.jsxs("div",{className:"space-y-2 py-4 text-center",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Chính sách giá dưới đây được áp dụng từ 04/2020"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Hệ thống thông báo chi phí theo lượng dữ liệu sử dụng và hiển thị hóa đơn vào ngày 1 hàng tháng."}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Quý doanh nghiệp vui lòng thanh toán trước ngày 15 để không bị gián đoạn dịch vụ."}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Lưu ý: Hoá đơn thanh toán cuối tháng dưới 5000đ sẽ được miễn phí đến hết 31/12/2024."})]})]})}const k=()=>[{accessorKey:"name",header:({column:t})=>e.jsx(x,{column:t,title:"Loại chi phí"}),cell:({row:t})=>e.jsxs("div",{className:"text-left break-words whitespace-normal",children:[e.jsx("div",{className:"font-medium",children:t.getValue("name")}),e.jsx("div",{className:"text-muted-foreground mt-1 text-sm leading-relaxed",children:t.original.desc})]}),enableSorting:!1,enableHiding:!1},{accessorKey:"unitName",header:({column:t})=>e.jsx(x,{column:t,title:"Đơn vị"}),cell:({row:t})=>e.jsx("div",{className:"text-center",children:t.getValue("unitName")}),enableSorting:!1,enableHiding:!1},{accessorKey:"unitPrice",header:({column:t})=>e.jsx(x,{column:t,title:"Giá (VNĐ)"}),cell:({row:t})=>{const s=t.getValue("unitPrice");return e.jsxs("div",{className:"text-center font-medium",children:[s.toLocaleString("vi-VN")," VNĐ"]})},enableSorting:!1,enableHiding:!1}];function D(){var f,j;const[t,s]=a.useState([]),[m,r]=a.useState({}),[d,l]=a.useState({}),{services:h,isLoading:o,error:g}=E(),N=h.filter(i=>i.sort!==1e3),p=a.useMemo(()=>k(),[]),c=S({data:N,columns:p,onColumnFiltersChange:s,getCoreRowModel:R(),getFilteredRowModel:C(),onColumnVisibilityChange:r,onRowSelectionChange:l,state:{columnFilters:t,columnVisibility:m,rowSelection:d}});return g?e.jsx("div",{className:"flex flex-col items-center justify-center space-y-4 py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-destructive text-lg font-semibold",children:"Lỗi khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:g})]})}):e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"rounded-md border",children:e.jsxs(V,{children:[e.jsx(H,{children:c.getHeaderGroups().map(i=>e.jsx(u,{children:i.headers.map(n=>e.jsx(F,{children:n.isPlaceholder?null:b(n.column.columnDef.header,n.getContext())},n.id))},i.id))}),e.jsxs(L,{children:[o&&e.jsx(P,{}),!o&&((f=c.getRowModel().rows)==null?void 0:f.length)&&c.getRowModel().rows.map(i=>e.jsx(u,{"data-state":i.getIsSelected()&&"selected",children:i.getVisibleCells().map(n=>e.jsx(v,{children:b(n.column.columnDef.cell,n.getContext())},n.id))},i.id)),!o&&!((j=c.getRowModel().rows)!=null&&j.length)&&e.jsx(u,{children:e.jsx(v,{colSpan:p.length,className:"h-24 text-center",children:"Không có dữ liệu"})})]})]})})})}function I(){return e.jsx(e.Fragment,{children:e.jsx(y,{children:e.jsxs("div",{className:"container mx-auto space-y-6 py-6",children:[e.jsx(K,{}),e.jsx(D,{})]})})})}const xe=I;export{xe as component};
