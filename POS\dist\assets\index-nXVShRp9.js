import{e as a,r as p,j as t}from"./index-UcdZ5AHH.js";import{I as e}from"./item-detail-form-CexFJE_y.js";import"./form-D_U5B5Go.js";import"./exceljs.min-DdGS1m1w.js";import"./pos-api-j20LMGrC.js";import{j as c}from"./customization-dialog-CBVIYqux.js";import"./user-9ajIul7r.js";import"./vietqr-api-9FERZtmQ.js";import"./crm-api-APQEjHWd.js";import"./header-CE1GZ327.js";import"./main-C1Ukb9JX.js";import"./search-context-DK2BgvuK.js";import"./date-range-picker-DxA68ufO.js";import"./multi-select-CznXmmsw.js";import"./zod-5jr7PwGQ.js";import"./use-upload-image-CQ4q2JJm.js";import"./images-api-BMYin8XI.js";import"./use-item-types-Ba660Fo2.js";import"./useQuery-B4yhTgGk.js";import"./utils-km2FGkQ4.js";import"./useMutation-q12VR5WX.js";import"./query-keys-3lmd-xp6.js";import"./use-item-classes-K5Si5Xyw.js";import"./use-units-DpGqz_uu.js";import"./use-items-D1TaTLXR.js";import"./item-api-BSj8C3Ww.js";import"./use-removed-items-B2DPNXQs.js";import"./use-customizations-DjbYm2qv.js";import"./use-customization-by-id-C_Tj_zpw.js";import"./use-sources-ZKvrcnmA.js";import"./sources-api-D61PDxyv.js";import"./sources-CfiQ7039.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-CDB9_T0n.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";import"./input-CBpgGfUv.js";import"./textarea-snABgVxD.js";import"./combobox-TkEauTt9.js";import"./command-DJT46NtT.js";import"./dialog-DmI079wB.js";import"./search-B6f_4BGP.js";import"./popover-BeZit_vZ.js";import"./chevrons-up-down-BkDfU9b3.js";import"./upload-BCz72beh.js";import"./collapsible-Dz-Iaa-P.js";import"./confirm-dialog-geNSWVL7.js";import"./alert-dialog-DeDOQ4ck.js";import"./date-picker-i2MEZYtF.js";import"./calendar-DZLqW2ag.js";import"./circle-help-Drh6YFda.js";import"./select-DOexGcsG.js";import"./index-MuNXZ_zP.js";import"./chevron-right-Dup7TmpK.js";import"./use-dialog-state-D3rbBEhX.js";import"./modal-DNIlBRJT.js";import"./separator-C5UQ7YqK.js";import"./createReactComponent-C1S2Ujit.js";import"./scroll-area-DQUG4R9C.js";import"./IconChevronRight-CnyriCST.js";import"./react-icons.esm-DpPH1mSm.js";import"./badge-BlAal7b-.js";import"./circle-x-DouBnnMt.js";const vt=function(){const i=a({from:"/_authenticated/menu/items/items-in-city/detail"}),[r,s]=p.useState(null),o=i==null?void 0:i.id,{data:m,isLoading:n}=c(o,!!o);return o?(p.useEffect(()=>{m&&s(m.data)},[m]),n||!r?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):t.jsx(e,{currentRow:{...r,item_id:"",item_id_barcode:""},isCopyMode:!!r})):t.jsx(e,{})};export{vt as component};
