import{r as S,j as e,c as d}from"./index-CfbMU4Ye.js";import{C as m}from"./checkbox-CSFn543p.js";import{C as y,a as I,b as P,c as _,d as z,e as h}from"./command-Jt-qPT7s.js";import{P as A,a as E,b as D}from"./popover-C4SSkcaE.js";import{C as M}from"./chevrons-up-down-BYVIbgdq.js";import{C as O}from"./check-C1W3FWto.js";function q({options:a,value:s,onValueChange:i,placeholder:c="Select options...",searchPlaceholder:x="Search...",emptyText:p="No results found.",className:g,disabled:u=!1,selectAllLabel:f="Select all",allSelectedText:b="All",selectedCountText:j=n=>`${n} selected`}){const[n,w]=S.useState(!1),l=a.length>0&&s.length===a.length,k=s.length>0&&s.length<a.length,v=()=>{i(l?[]:a.map(t=>t.value))},C=t=>{const r=s.includes(t)?s.filter(N=>N!==t):[...s,t];i(r)},o=()=>{if(s.length===0)return c;if(s.length===1){const t=a.find(r=>r.value===s[0]);return(t==null?void 0:t.label)||c}return s.length===a.length?b:j(s.length)};return e.jsxs(A,{open:n,onOpenChange:w,children:[e.jsx(E,{asChild:!0,children:e.jsxs("button",{type:"button",role:"combobox","aria-expanded":n,"aria-label":o(),className:d("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex h-9 items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",g||"w-fit"),disabled:u,children:[e.jsx("span",{className:d(s.length===0&&"text-muted-foreground","truncate"),children:o()}),e.jsx(M,{className:"size-4 opacity-50"})]})}),e.jsx(D,{className:"p-0",sideOffset:4,align:"start",style:{width:"var(--radix-popover-trigger-width)"},children:e.jsxs(y,{children:[e.jsx(I,{placeholder:x}),e.jsxs(P,{children:[e.jsx(_,{children:p}),e.jsxs(z,{children:[e.jsxs(h,{onSelect:v,className:"flex cursor-pointer items-center gap-2",children:[e.jsx(m,{checked:l?!0:k?"indeterminate":!1,className:"pointer-events-none data-[state=checked]:text-white data-[state=indeterminate]:text-white data-[state=unchecked]:text-black",classNameIcon:"!stroke-white"}),e.jsx("span",{className:"font-medium",children:f})]}),a.map(t=>{const r=s.includes(t.value);return e.jsxs(h,{onSelect:()=>C(t.value),className:"flex cursor-pointer items-center gap-2",children:[e.jsx(m,{checked:r,className:"pointer-events-none data-[state=checked]:text-white data-[state=indeterminate]:text-white data-[state=unchecked]:text-black",classNameIcon:"!stroke-white"}),e.jsx("span",{children:t.label}),r&&e.jsx(O,{className:"text-primary ml-auto h-4 w-4"})]},t.value)})]})]})]})})]})}export{q as M};
