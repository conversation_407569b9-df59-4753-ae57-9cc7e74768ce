import{aZ as r,j as t}from"./index-UcdZ5AHH.js";import{S as i}from"./index-BsoEVFd3.js";import"./use-service-charge-form-BujYVBx_.js";import"./date-utils-DBbLjCz0.js";import"./useQuery-B4yhTgGk.js";import"./utils-km2FGkQ4.js";import"./useMutation-q12VR5WX.js";import"./pos-api-j20LMGrC.js";import"./query-keys-3lmd-xp6.js";import"./discount-toggle-button-DBc5xtbm.js";import"./date-range-picker-DxA68ufO.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./select-DOexGcsG.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";import"./form-D_U5B5Go.js";import"./input-CBpgGfUv.js";import"./tabs-0XXLAiGI.js";import"./index-iiVug-md.js";import"./textarea-snABgVxD.js";import"./checkbox-CDB9_T0n.js";import"./modal-DNIlBRJT.js";import"./dialog-DmI079wB.js";import"./collapsible-Dz-Iaa-P.js";import"./calendar-DZLqW2ag.js";import"./circle-help-Drh6YFda.js";import"./switch-DcX02SvY.js";const G=function(){const{id:o}=r.useParams();return console.log("🔥 Service Charge Detail Page - URL Params:"),console.log("🔥 serviceChargeId:",o),t.jsx(i,{serviceChargeId:o})};export{G as component};
