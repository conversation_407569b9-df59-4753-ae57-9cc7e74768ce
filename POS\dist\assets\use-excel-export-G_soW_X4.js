import{r as m}from"./index-CfbMU4Ye.js";import{a as p}from"./excel-export-e2hVU3Iz.js";function y(i){const[t,r]=m.useState(!1);return{exportData:async()=>{const{data:s,filename:e,sheetName:c,columnMapping:a,onExportStart:n,onExportComplete:l,onExportError:f}=i;if(s.length===0){alert("Không có dữ liệu để xuất");return}try{r(!0),n==null||n(),await new Promise(u=>setTimeout(u,100)),p(s,{filename:e,sheetName:c,columnMapping:a}),l==null||l()}catch(u){f==null||f(u),alert("Có lỗi xảy ra khi xuất file Excel")}finally{r(!1)}},isExporting:t}}function D(i,t,r="xlsx"){const o=new Date().toISOString().split("T")[0];if(t!=null&&t.from&&(t!=null&&t.to)){const s=t.from.toISOString().split("T")[0],e=t.to.toISOString().split("T")[0];return`${i}-${s}-${e}.${r}`}return`${i}-${o}.${r}`}function $(i,t={}){return i.map(r=>{const o={...r};return Object.entries(t).forEach(([s,e])=>{const c=s;o[c]!==void 0&&e&&(o[c]=e(o[c]))}),o})}export{$ as f,D as g,y as u};
