import{j as t,B as c,L as m}from"./index-UcdZ5AHH.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{D as d}from"./data-table-C_fR-seh.js";import{B as l}from"./badge-BlAal7b-.js";import{S as p}from"./settings-DBM6wTh3.js";import{u as h}from"./use-account-management-g4K9-VD1.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./select-DOexGcsG.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";import"./table-pagination-CokcdUZG.js";import"./pagination-CTPzQV-r.js";import"./table-DHWQVnPn.js";function x({users:a,isLoading:s,onEditUser:n,onToggleStatus:r}){const o=[{key:"username",header:"Tên người dùng",width:"200px"},{key:"email",header:"Email",width:"250px"},{key:"status",header:"Trạng thái",width:"120px",render:i=>t.jsx(l,{variant:i==="active"?"default":"secondary",children:i==="active"?"Hoạt động":"Không hoạt động"})},{key:"actions",header:"",width:"100px",render:(i,e)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(c,{variant:"ghost",size:"icon",onClick:()=>n(e),className:"h-8 w-8",children:t.jsx(p,{className:"h-4 w-4"})}),t.jsx(c,{variant:"ghost",size:"icon",onClick:()=>r(e.id),className:"h-8 w-8",children:e.status==="active"?"Hủy":"Kích hoạt"})]})}];return s?t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{children:"Đang tải dữ liệu..."})}):t.jsx(d,{data:a,columns:o,isLoading:s,pageSize:20,emptyMessage:"Không có tài khoản nào",loadingMessage:"Đang tải..."})}function g(){const{users:a,isLoading:s,error:n,toggleUserStatus:r}=h(),o=e=>{console.log("Edit user:",e)},i=async e=>{await r(e)};return n?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-red-600",children:n})})}):t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx("div",{className:"mb-6",children:t.jsx(m,{to:"/general-setups/create-user",children:t.jsx(c,{children:"Tạo tài khoản"})})}),t.jsx(x,{users:a,isLoading:s,onEditUser:o,onToggleStatus:i})]})}const L=g;export{L as component};
