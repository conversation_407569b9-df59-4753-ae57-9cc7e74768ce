import{aV as r,j as i}from"./index-CfbMU4Ye.js";import"./pos-api-BBB_ZiZD.js";import"./vietqr-api-BHQxfNzq.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import"./header-CiiJInbE.js";import"./main-B69tr6A0.js";import"./search-context-DXPkaUlN.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{C as m}from"./create-table-form-rmnVmXMF.js";import"./separator-DVvwOaSX.js";import"./command-Jt-qPT7s.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-FztlF_ds.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./scroll-area-Bx6sgJqp.js";import"./index-D41EikqA.js";import"./select-_nXsh5SU.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./IconChevronRight-1SGwHwL2.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./use-areas-CyxUzBoL.js";import"./useQuery-BvDWg4vp.js";import"./utils-km2FGkQ4.js";import"./useMutation-C9PewMvL.js";import"./images-api-RS3EYfrE.js";import"./query-keys-3lmd-xp6.js";import"./use-sales-channels-xiPEaovV.js";import"./use-tables-Dso0mLE3.js";import"./input-D8TU6hMD.js";import"./checkbox-CSFn543p.js";import"./collapsible-BVDeq-Zm.js";import"./use-items-in-store-data-BeUnNL6h.js";import"./use-item-types-mN8TSC7t.js";import"./use-item-classes-DJrzbexi.js";import"./use-units-Cyx-GSz4.js";import"./use-removed-items-DWRDzX0n.js";import"./items-in-store-api-5JRL88nZ.js";import"./xlsx-DkH2s96g.js";import"./copy-C6hItMHG.js";import"./plus-CerxpTbe.js";import"./minus-D5pkcsCa.js";const ro=function(){const{areaId:o}=r.useParams(),{store_uid:t}=r.useSearch();return console.log("🔍 TableDetailPage rendered"),console.log("📍 Route params:",{areaId:o}),console.log("🔍 Route search:",{store_uid:t}),console.log("📊 Full URL:",window.location.href),i.jsx(m,{areaId:o,storeUid:t})};export{ro as component};
