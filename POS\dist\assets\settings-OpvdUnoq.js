import{r as f,l as U,a4 as v,j as e,c as W,B as y,z as o}from"./index-CfbMU4Ye.js";import{H as Z}from"./header-CiiJInbE.js";import{M as ee}from"./main-B69tr6A0.js";import{P as ne}from"./profile-dropdown-HjZ6UGjk.js";import{S as se,T as ae}from"./search-Bbt2JnTN.js";import"./date-range-picker-FRR8J6T3.js";import{a as x,b as u,c as b,d as p,e as g,u as le,F as re}from"./form-DPp_Bp7A.js";import{C as S,a as I,b as k,d as A}from"./card-Dq-aHO9v.js";import{I as E}from"./input-D8TU6hMD.js";import{c as T}from"./settings-api-Drf7NDC1.js";import{u as te}from"./use-pos-company-data-DHh5_mMz.js";import"./user-3BSjwAvJ.js";import{X as ie}from"./calendar-DmzcYdpW.js";import{c as ce}from"./createLucideIcon-BH-J_-vM.js";import{I as V}from"./IconUpload-WlthH4Pc.js";import{I as oe}from"./IconX-D1iK2feM.js";import{S as R,a as G,b as K,c as L,d as O}from"./select-_nXsh5SU.js";import{R as _,a as C}from"./radio-group-KASmsiJo.js";import{s as me}from"./zod-BFJv4_uG.js";import"./separator-DVvwOaSX.js";import"./avatar-CE3yFgmj.js";import"./dropdown-menu-8bnotEGr.js";import"./index-D41EikqA.js";import"./index-CBP3KeI0.js";import"./index-4DjKSQeL.js";import"./check-C1W3FWto.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./dialog-FztlF_ds.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./pos-api-BBB_ZiZD.js";import"./scroll-area-Bx6sgJqp.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./isSameMonth-C8JQo-AN.js";import"./crm-api-8UaIokQG.js";import"./index-Cqf6DKEV.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const he=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],de=ce("circle-alert",he);function F(){const[n,a]=f.useState(!1),{selectedBrand:s}=U();return{isLoading:n,saveBrandConfig:async c=>{var i;a(!0);try{const h={id:(s==null?void 0:s.brandId)||"",Hotline:c.hotline,image:c.bannerImage,Manager_Email_List:((i=c.email)==null?void 0:i.join(", "))||""};await T.updateBrandConfig((s==null?void 0:s.brandId)||"",h),v.success("Cấu hình thương hiệu đã được lưu thành công")}catch(h){console.error("Error saving brand config:",h),v.error("Có lỗi xảy ra khi lưu cấu hình thương hiệu")}finally{a(!1)}},saveDeliveryConfig:async c=>{a(!0);const{selectedBrand:i}=U();try{const h={id:"6886d8581ed12b0001d6672e",pos_parent:(i==null?void 0:i.brandId)||"",vat:c.vat,service_charge:c.serviceCharge,warning_cheat_mail_config:{mailType:"Brand"},alert_ws_mail_config:{mailType:"Brand"}};await T.updateSettingConfig((i==null?void 0:i.brandId)||"",h),v.success("Cấu hình đặt giao hàng đã được lưu thành công")}catch(h){console.error("Error saving delivery config:",h),v.error("Có lỗi xảy ra khi lưu cấu hình đặt giao hàng")}finally{a(!1)}},saveMembershipConfig:async c=>{var h;a(!0);const i=te();try{const w={company_id:(i==null?void 0:i.id)||"",is_number_rounding:c.enablePointAccumulation?1:0,reset_point_cycle:c.resetPointsDays,membership_downgrade_cycle:c.autoUpgradeDays,none_point_sources:((h=c.excludeInvoiceOrigins)==null?void 0:h.join(","))||"",id:"6886d8505ae6c800017f6fb2",created_at:new Date().toISOString().slice(0,19).replace("T"," ")};await T.updateLoyaltySettings((s==null?void 0:s.brandId)||"",w),v.success("Chương trình thành viên đã được lưu thành công")}catch(w){console.error("Error saving membership config:",w),v.error("Có lỗi xảy ra khi lưu chương trình thành viên")}finally{a(!1)}},saveTransactionAlert:async c=>{a(!0);try{const i={limit_eat_count_per_day:c.balanceThreshold,limit_pay_amount_per_day:c.balanceThresholdVND};await T.updateCheatConfig((s==null?void 0:s.brandId)||"",i),v.success("Cảnh báo giao dịch bất thường đã được lưu thành công")}catch(i){console.error("Error saving transaction alert:",i),v.error("Có lỗi xảy ra khi lưu cảnh báo giao dịch bất thường")}finally{a(!1)}},saveOnlineOrderAlert:async c=>{a(!0);try{const i={alert_ws_mail_config:{mailType:c.onlineOrderEmailType==="other"?"Custom":"Brand",mailList:c.onlineOrderEmailType==="other"?c.onlineOrderCustomEmails:void 0},id:"6886d8581ed12b0001d6672e"};await T.updateSettingConfig((s==null?void 0:s.brandId)||"",i),v.success("Cảnh báo đơn hàng online đã được lưu thành công")}catch(i){console.error("Error saving online order alert:",i),v.error("Có lỗi xảy ra khi lưu cảnh báo đơn hàng online")}finally{a(!1)}}}}function H({value:n=[],onChange:a,placeholder:s="Nhập email và ấn Enter hoặc dấu phẩy",disabled:t=!1,className:d,error:m,maxEmails:r=10}){const[l,c]=f.useState(""),[i,h]=f.useState(""),[w,B]=f.useState(!1),D=f.useRef(null),z=j=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(j.trim()),P=j=>{const N=j.trim();if(!N){h("Email không được để trống");return}if(!z(N)){h("Email không hợp lệ");return}if(n.includes(N)){h("Email đã tồn tại");return}if(n.length>=r){h(`Chỉ được phép tối đa ${r} email`);return}a([...n,N]),c(""),h("")},X=j=>{j.key==="Enter"||j.key===","?(j.preventDefault(),P(l)):j.key==="Backspace"&&!l&&n.length>0&&a(n.slice(0,-1))},$=()=>{B(!1),l.trim()&&P(l)},q=j=>{c(j.target.value),i&&h("")},Y=()=>{!t&&D.current&&D.current.focus()};f.useEffect(()=>{m&&h(m)},[m]);const J=j=>{a(n.filter(N=>N!==j))};return e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{onClick:Y,className:W("border-input flex min-h-9 w-full flex-wrap items-center gap-1 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",w&&"border-ring ring-ring/50 ring-[3px]",(i||m)&&"border-red-500",t&&"cursor-not-allowed opacity-50",d),children:[n.map((j,N)=>e.jsxs("div",{className:"inline-flex items-center gap-1 rounded-md bg-blue-100 px-2 py-1 text-sm text-blue-800 transition-colors hover:bg-blue-200",children:[e.jsx("span",{children:j}),!t&&e.jsx("button",{type:"button",onClick:Q=>{Q.stopPropagation(),J(j)},className:"flex h-4 w-4 items-center justify-center rounded-full transition-colors hover:bg-blue-300","aria-label":`Xóa email ${j}`,children:e.jsx(ie,{className:"h-3 w-3"})})]},N)),e.jsx(E,{ref:D,value:l,onChange:q,onKeyDown:X,onFocus:()=>B(!0),onBlur:$,placeholder:n.length===0?s:"",disabled:t,className:"min-w-[120px] flex-1 border-0 bg-transparent p-0 shadow-none focus-visible:ring-0"})]}),(i||m)&&e.jsxs("div",{className:"mt-1 flex items-center gap-1 text-sm text-red-600",children:[e.jsx(de,{className:"h-4 w-4"}),e.jsx("span",{children:i||m})]})]})}const xe={IMAGES:"image/*"},ue=(n={})=>{const{accept:a=xe.IMAGES,multiple:s=!1,onSuccess:t,onError:d}=n;return()=>new Promise(m=>{const r=document.createElement("input");r.type="file",r.accept=a,r.multiple=s,r.onchange=l=>{var i;const c=(i=l.target.files)==null?void 0:i[0];if(!c){m(null);return}try{const h=URL.createObjectURL(c),w={file:c,url:h};t==null||t(c,h),m(w)}catch{d==null||d("Failed to process file"),m(null)}},r.click()})},ge=({label:n,onUpload:a,disabled:s})=>e.jsxs("div",{className:"flex flex-col items-center justify-center space-y-2",children:[e.jsx(V,{className:"h-8 w-8 text-gray-400"}),e.jsx("p",{className:"text-sm text-gray-500",children:n}),e.jsxs(y,{type:"button",variant:"outline",size:"sm",disabled:s,onClick:a,children:[e.jsx(V,{className:"mr-2 h-4 w-4"}),"Tải lên"]})]}),pe=({src:n,alt:a,onUpload:s,onRemove:t,disabled:d,className:m="mx-auto h-32 w-full rounded-lg object-cover"})=>e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:n,alt:a,className:m}),e.jsxs("div",{className:"mt-4 flex justify-center space-x-2",children:[e.jsxs(y,{type:"button",variant:"outline",size:"sm",disabled:d,onClick:s,children:[e.jsx(V,{className:"mr-2 h-4 w-4"}),"Thay đổi"]}),e.jsxs(y,{type:"button",variant:"outline",size:"sm",disabled:d,onClick:t,children:[e.jsx(oe,{className:"mr-2 h-4 w-4"}),"Xóa"]})]})]}),M=({label:n,value:a,onChange:s,disabled:t=!1,className:d})=>{const m=f.useCallback(async()=>{await ue({onSuccess:(c,i)=>s(i),onError:c=>console.error("Upload error:",c)})()},[s]),r=f.useCallback(()=>{s("")},[s]);return e.jsx("div",{className:`rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8 text-center ${d}`,children:a?e.jsx(pe,{src:a,alt:n,onUpload:m,onRemove:r,disabled:t}):e.jsx(ge,{label:n,onUpload:m,disabled:t})})},je=({onSave:n,isLoading:a,isSaving:s})=>e.jsx("div",{className:"flex justify-start",children:e.jsx(y,{type:"button",disabled:a||s,onClick:n,children:s?"Đang lưu...":"Lưu"})});function be({form:n,isLoading:a=!1}){const{saveBrandConfig:s,isLoading:t}=F(),d=f.useCallback(async()=>{const l=n.getValues();await s({brandName:l.brandName,bannerImage:l.bannerImage,logo:l.logo,hotline:l.hotline,email:l.email})},[n,s]),m=f.useCallback(l=>{n.setValue("bannerImage",l)},[n]),r=f.useCallback(l=>{n.setValue("logo",l)},[n]);return e.jsxs(S,{className:"shadow-sm",children:[e.jsx(I,{children:e.jsx(k,{className:"flex items-center gap-2 text-lg font-semibold text-gray-900",children:"CẤU HÌNH THƯƠNG HIỆU"})}),e.jsxs(A,{className:"space-y-6 p-6",children:[e.jsx(x,{control:n.control,name:"brandName",render:({field:l})=>e.jsxs(u,{children:[e.jsx(b,{className:"text-sm font-medium",children:"Tên thương hiệu"}),e.jsx(p,{children:e.jsx(E,{placeholder:"Nhập tên thương hiệu",value:l.value||"",onChange:l.onChange,onBlur:l.onBlur,name:l.name,ref:l.ref,disabled:a})}),e.jsx(g,{})]})}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsx("div",{children:e.jsx(x,{control:n.control,name:"bannerImage",render:({field:l})=>e.jsxs(u,{children:[e.jsx(b,{className:"text-sm font-medium",children:"Banner thương hiệu"}),e.jsx(M,{label:"Tải ảnh banner lên",value:l.value,onChange:m,disabled:a}),e.jsx(g,{})]})})}),e.jsx("div",{children:e.jsx(x,{control:n.control,name:"logo",render:({field:l})=>e.jsxs(u,{children:[e.jsx(b,{className:"text-sm font-medium",children:"Logo"}),e.jsx(M,{label:"Tải ảnh logo lên",value:l.value,onChange:r,disabled:a,className:"rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8 text-center"}),e.jsx(g,{})]})})})]}),e.jsx(x,{control:n.control,name:"hotline",render:({field:l})=>e.jsxs(u,{children:[e.jsx(b,{className:"text-sm font-medium",children:"Hotline"}),e.jsx(p,{children:e.jsx(E,{placeholder:"Nhập số hotline",value:l.value||"",onChange:l.onChange,onBlur:l.onBlur,name:l.name,ref:l.ref,disabled:a})}),e.jsx(g,{})]})}),e.jsx(x,{control:n.control,name:"email",render:({field:l})=>e.jsxs(u,{children:[e.jsx(b,{className:"text-sm font-medium",children:"Email nhận thông báo"}),e.jsx(p,{children:e.jsx(H,{value:l.value||[],onChange:l.onChange,placeholder:"Nhập email và ấn Enter",disabled:a})}),e.jsx(g,{})]})}),e.jsx(je,{onSave:d,isLoading:a,isSaving:t})]})]})}function fe({form:n,isLoading:a=!1}){return e.jsxs(S,{className:"shadow-sm",children:[e.jsx(I,{children:e.jsx(k,{className:"flex items-center gap-2 text-lg font-semibold text-gray-900",children:"CẤU HÌNH ĐẶT GIAO HÀNG"})}),e.jsxs(A,{className:"space-y-6 p-6",children:[e.jsx(x,{control:n.control,name:"representativeStore",render:({field:s})=>e.jsxs(u,{children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(b,{className:"w-full text-sm font-medium",children:"Nhà hàng đại diện"}),e.jsx(p,{children:e.jsxs(R,{value:s.value,onValueChange:s.onChange,disabled:a,children:[e.jsx(G,{className:"w-96",children:e.jsx(K,{placeholder:"A Hưng"})}),e.jsxs(L,{children:[e.jsx(O,{value:"a-hung",children:"A Hưng"}),e.jsx(O,{value:"other",children:"Khác"})]})]})})]}),e.jsx("div",{className:"w-96 text-xs text-gray-500",children:"Khách hàng truy cập các trang đặt hàng sẽ thấy thực đơn của nhà hàng đại diện này. Lưu ý: Các nhà hàng trong thương hiệu phải có thực đơn giống nhau."}),e.jsx(g,{})]})}),e.jsx(x,{control:n.control,name:"serviceCharge",render:({field:s})=>e.jsxs(u,{children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(b,{className:"w-32 text-sm font-medium",children:"Phí dịch vụ"}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(p,{children:e.jsx(E,{...s,type:"number",step:"0.1",disabled:a,className:"w-96",onChange:t=>s.onChange(parseFloat(t.target.value)||0)})}),e.jsx("span",{className:"text-sm",children:"%"})]})]}),e.jsx(g,{})]})}),e.jsx(x,{control:n.control,name:"vat",render:({field:s})=>e.jsxs(u,{children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(b,{className:"w-32 text-sm font-medium",children:"VAT"}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(p,{children:e.jsx(E,{...s,type:"number",step:"0.1",disabled:a,className:"w-96",onChange:t=>s.onChange(parseFloat(t.target.value)||0)})}),e.jsx("span",{className:"text-sm",children:"%"})]})]}),e.jsx(g,{})]})}),e.jsx("div",{className:"flex justify-start",children:e.jsx(y,{type:"button",disabled:a,children:"Lưu"})})]})]})}function ve({form:n,isLoading:a=!1}){return e.jsxs(S,{className:"shadow-sm",children:[e.jsx(I,{children:e.jsx(k,{className:"flex items-center gap-2 text-lg font-semibold text-gray-900",children:"CHƯƠNG TRÌNH THÀNH VIÊN"})}),e.jsxs(A,{className:"space-y-6 p-6",children:[e.jsx(x,{control:n.control,name:"enablePointAccumulation",render:({field:s})=>e.jsxs(u,{children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(b,{className:"w-full text-sm font-medium",children:"Làm tròn điểm tích lũy"}),e.jsx(p,{children:e.jsxs(_,{value:s.value?"yes":"no",onValueChange:t=>s.onChange(t==="yes"),className:"flex gap-6",disabled:a,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{value:"yes",id:"point-yes"}),e.jsx("label",{htmlFor:"point-yes",className:"text-sm",children:"Có"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{value:"no",id:"point-no"}),e.jsx("label",{htmlFor:"point-no",className:"text-sm",children:"Không"})]})]})})]}),e.jsx(g,{})]})}),e.jsx(x,{control:n.control,name:"enableAutoMemberUpgrade",render:({field:s})=>e.jsxs(u,{children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(b,{className:"w-full text-sm font-medium",children:"Chu kỳ xét duyệt thay đổi hạng thành viên tự động"}),e.jsx(p,{children:e.jsxs(_,{value:s.value?"yes":"no",onValueChange:t=>s.onChange(t==="yes"),className:"flex gap-6",disabled:a,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{value:"no",id:"upgrade-no"}),e.jsx("label",{htmlFor:"upgrade-no",className:"text-sm",children:"Không theo chu kỳ"})]}),e.jsxs("div",{className:"flex items-center justify-start",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{value:"yes",id:"upgrade-yes"}),e.jsx("label",{htmlFor:"upgrade-yes",className:"text-sm",children:"Chu kỳ"})]}),e.jsx("div",{children:e.jsx(x,{control:n.control,name:"autoUpgradeDays",render:({field:t})=>e.jsxs(u,{children:[e.jsxs("div",{className:"ml-48 flex items-center gap-4",children:[e.jsx(p,{children:e.jsx(E,{...t,type:"number",disabled:a,className:"w-96",onChange:d=>t.onChange(parseInt(d.target.value)||0)})}),e.jsx("span",{className:"text-sm",children:"ngày"})]}),e.jsx(g,{})]})})})]})]})})]}),e.jsx(g,{})]})}),e.jsx(x,{control:n.control,name:"resetPointsDays",render:({field:s})=>e.jsxs(u,{children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(b,{className:"w-full text-sm font-medium",children:"Chu kỳ reset điểm"}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(p,{children:e.jsx(E,{...s,type:"number",disabled:a,className:"w-96",onChange:t=>s.onChange(parseInt(t.target.value)||0)})}),e.jsx("span",{className:"text-sm",children:"ngày"})]})]}),e.jsx("div",{className:"text-xs text-gray-500",children:"Xóa điểm tích lũy của khách hàng sau 31 ngày chưa trở lại"}),e.jsx(g,{})]})}),e.jsx(x,{control:n.control,name:"excludeInvoiceOrigins",render:()=>e.jsxs(u,{children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(b,{className:"w-full text-sm font-medium",children:"Không tích điểm cho hóa đơn có nguồn đơn từ"}),e.jsx(p,{children:e.jsxs(R,{disabled:a,children:[e.jsx(G,{className:"w-96",children:e.jsx(K,{placeholder:"Chọn nguồn"})}),e.jsxs(L,{children:[e.jsx(O,{value:"pos",children:"POS"}),e.jsx(O,{value:"online",children:"Online"})]})]})})]}),e.jsx(g,{})]})}),e.jsx("div",{className:"flex justify-start",children:e.jsx(y,{type:"button",disabled:a,children:"Lưu"})})]})]})}function ye({form:n,isLoading:a=!1}){const s=n.watch("transactionEmailType"),{saveTransactionAlert:t,isLoading:d}=F(),m=async()=>{const r=n.getValues();await t({enableAccountBalanceAlert:r.enableAccountBalanceAlert,balanceThreshold:r.balanceThreshold,enableAccountBalanceAlertVND:r.enableAccountBalanceAlertVND,balanceThresholdVND:r.balanceThresholdVND,transactionEmailType:r.transactionEmailType,transactionCustomEmails:r.transactionCustomEmails})};return e.jsxs(S,{className:"shadow-sm",children:[e.jsx(I,{children:e.jsx(k,{className:"flex items-center gap-2 text-lg font-semibold text-gray-900",children:"CẢNH BÁO GIAO DỊCH BẤT THƯỜNG"})}),e.jsxs(A,{className:"space-y-6 p-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(b,{className:"w-full text-sm font-medium",children:"Thông báo danh sách tài khoản chi tiêu vượt"}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(x,{control:n.control,name:"balanceThreshold",render:({field:r})=>e.jsxs(u,{children:[e.jsx(p,{children:e.jsx(E,{...r,type:"number",disabled:a,className:"w-24",onChange:l=>r.onChange(parseInt(l.target.value)||0)})}),e.jsx(g,{})]})}),e.jsx(y,{variant:"outline",size:"sm",disabled:a,children:"Lần một ngày"})]})]}),e.jsx("div",{className:"flex flex-col gap-4",children:e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(x,{control:n.control,name:"balanceThresholdVND",render:({field:r})=>e.jsxs(u,{children:[e.jsx(p,{children:e.jsx(E,{...r,type:"number",disabled:a,className:"w-24",onChange:l=>r.onChange(parseInt(l.target.value)||0)})}),e.jsx(g,{})]})}),e.jsx(y,{variant:"outline",size:"sm",disabled:a,children:"VNĐ một ngày"})]})})]}),e.jsx(x,{control:n.control,name:"transactionEmailType",render:({field:r})=>e.jsxs(u,{children:[e.jsxs("div",{className:"flex w-full flex-col gap-4",children:[e.jsx(b,{className:"w-full text-sm font-medium",children:"Email nhận thông báo"}),e.jsx(p,{children:e.jsxs(_,{value:r.value,onValueChange:r.onChange,className:"flex gap-6",disabled:a,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{value:"regular",id:"transaction-regular"}),e.jsx("label",{htmlFor:"transaction-regular",className:"text-sm",children:"Email thường hiệu"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{value:"other",id:"transaction-other"}),e.jsx("label",{htmlFor:"transaction-other",className:"text-sm",children:"Email khác"})]})]})})]}),e.jsx(g,{})]})}),s==="other"&&e.jsx(x,{control:n.control,name:"transactionCustomEmails",render:({field:r})=>e.jsxs(u,{children:[e.jsx("div",{className:"flex flex-col gap-4",children:e.jsx(p,{children:e.jsx(H,{value:r.value||[],onChange:r.onChange,disabled:a,className:"w-96",placeholder:"Nhập email và ấn Enter"})})}),e.jsx(g,{})]})}),e.jsx("div",{className:"flex justify-start",children:e.jsx(y,{type:"button",disabled:a||d,onClick:m,children:d?"Đang lưu...":"Lưu"})})]})]})}function Ne({form:n,isLoading:a=!1}){const s=n.watch("onlineOrderEmailType"),{saveOnlineOrderAlert:t,isLoading:d}=F(),m=async()=>{const r=n.getValues();await t({onlineOrderEmailType:r.onlineOrderEmailType,onlineOrderCustomEmails:r.onlineOrderCustomEmails})};return e.jsxs(S,{className:"shadow-sm",children:[e.jsx(I,{children:e.jsx(k,{className:"flex items-center gap-2 text-lg font-semibold text-gray-900",children:"CẢNH BÁO ĐƠN HÀNG ONLINE GỬI XUỐNG POS LỖI"})}),e.jsxs(A,{className:"space-y-6 p-6",children:[e.jsx(x,{control:n.control,name:"onlineOrderEmailType",render:({field:r})=>e.jsxs(u,{children:[e.jsxs("div",{className:"flex w-full flex-col gap-4",children:[e.jsx(b,{className:"w-full text-sm font-medium",children:"Email nhận thông báo"}),e.jsx(p,{children:e.jsxs(_,{value:r.value,onValueChange:r.onChange,className:"flex gap-6",disabled:a,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{value:"regular",id:"online-regular"}),e.jsx("label",{htmlFor:"online-regular",className:"text-sm",children:"Email thường hiệu"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{value:"point",id:"online-point"}),e.jsx("label",{htmlFor:"online-point",className:"text-sm",children:"Email điểm bán"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{value:"other",id:"online-other"}),e.jsx("label",{htmlFor:"online-other",className:"text-sm",children:"Email khác"})]})]})})]}),e.jsx(g,{})]})}),s==="other"&&e.jsx(x,{control:n.control,name:"onlineOrderCustomEmails",render:({field:r})=>e.jsxs(u,{children:[e.jsx("div",{className:"flex flex-col gap-4",children:e.jsx(p,{children:e.jsx(H,{value:r.value||[],onChange:r.onChange,disabled:a,className:"w-96",placeholder:"Nhập email và ấn Enter"})})}),e.jsx(g,{})]})}),e.jsx("div",{className:"flex justify-start",children:e.jsx(y,{type:"button",disabled:a||d,onClick:m,children:d?"Đang lưu...":"Lưu"})})]})]})}const Ce=o.object({brandName:o.string().min(1,"Tên thương hiệu là bắt buộc"),bannerImage:o.string().optional(),logo:o.string().optional(),hotline:o.string().min(1,"Số điện thoại hotline là bắt buộc"),email:o.array(o.string().email("Email không hợp lệ")).min(1,"Ít nhất một email là bắt buộc"),representativeStore:o.string().min(1,"Nhà hàng đại diện là bắt buộc"),serviceCharge:o.number().min(0,"Phí dịch vụ phải lớn hơn hoặc bằng 0"),vat:o.number().min(0,"VAT phải lớn hơn hoặc bằng 0"),enablePointAccumulation:o.boolean(),enableAutoMemberUpgrade:o.boolean(),autoUpgradeDays:o.number().optional(),resetPointsDays:o.number(),excludeInvoiceOrigins:o.array(o.string()),enableAccountBalanceAlert:o.boolean(),balanceThreshold:o.number().optional(),enableAccountBalanceAlertVND:o.boolean(),balanceThresholdVND:o.number().optional(),transactionEmailType:o.enum(["regular","other"]),transactionCustomEmails:o.array(o.string().email("Email không hợp lệ")).optional(),onlineOrderEmailType:o.enum(["regular","point","other"]),onlineOrderCustomEmails:o.array(o.string().email("Email không hợp lệ")).optional()}),Ee={brandName:"Tutimi-Bình Lợi",bannerImage:"https://image.foodbook.vn/upload/********/1753667987575_blob.jpeg",logo:"https://image.foodbook.vn/upload/********/1755052729065_blob.jpeg",hotline:"33",email:["<EMAIL>","<EMAIL>","<EMAIL>"],representativeStore:"",serviceCharge:.0102,vat:.01,enablePointAccumulation:!0,enableAutoMemberUpgrade:!0,autoUpgradeDays:31,resetPointsDays:30,excludeInvoiceOrigins:["********","********"],enableAccountBalanceAlert:!0,balanceThreshold:3,enableAccountBalanceAlertVND:!0,balanceThresholdVND:3,transactionEmailType:"regular",transactionCustomEmails:[],onlineOrderEmailType:"other",onlineOrderCustomEmails:["<EMAIL>"]};function we(){return{form:le({resolver:me(Ce),defaultValues:Ee}),handleSubmit:async s=>{try{console.log("Saving CRM settings:",s)}catch(t){console.error("Error saving CRM settings:",t)}},isLoading:!1}}function Te(){const{form:n,handleSubmit:a,isLoading:s}=we();return e.jsxs(e.Fragment,{children:[e.jsxs(Z,{children:[e.jsx(se,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ae,{}),e.jsx(ne,{})]})]}),e.jsx(ee,{children:e.jsx("div",{className:"min-h-screen",children:e.jsx("div",{className:"container mx-auto px-6 py-8",children:e.jsx(re,{...n,children:e.jsxs("form",{id:"crm-settings-form",onSubmit:n.handleSubmit(a),className:"space-y-8",children:[e.jsx(be,{form:n,isLoading:s}),e.jsx(fe,{form:n,isLoading:s}),e.jsx(ve,{form:n,isLoading:s}),e.jsx(ye,{form:n,isLoading:s}),e.jsx(Ne,{form:n,isLoading:s})]})})})})})]})}const gn=Te;export{gn as component};
