import{r as w,j as c,R as X,u as R,a3 as $,a4 as f,B as U,l as ne,z as B}from"./index-CVQ6JZo2.js";import{u as ie}from"./use-dialog-state-Duzyky_e.js";import{u as Y}from"./useQuery-HgcIHxlE.js";import{Q as F}from"./query-keys-3lmd-xp6.js";import{b as x}from"./pos-api-mRg02iop.js";import"./vietqr-api-_ZZrmuU0.js";import{u as ae}from"./use-item-types-DnnQzY0g.js";import{u as se}from"./use-item-classes-uo7m9d9N.js";import{u as oe}from"./use-units-DPUE3WPd.js";import"./user-BJzEhOTa.js";import"./crm-api-CDzLLTww.js";import{C as W}from"./checkbox-BfLSzhzg.js";import{I as re}from"./input-Al6WtUZF.js";import{P as ce}from"./modal-DEESjk2b.js";import"./date-range-picker-CXbMaowj.js";import{u as le,F as de,a as ue,b as me,c as he,d as _e,e as ye}from"./form-CzmGigtT.js";import{C as V}from"./chevron-right-CxgpqvrH.js";import{C as J}from"./select-BFhNE0YE.js";import{s as ge}from"./zod-ByV4TDQ9.js";import{D as pe,a as fe,b as Ce,c as be,e as Ie,f as xe}from"./dialog-DDrduXt3.js";import{C as we}from"./combobox-Kh9YbXHg.js";import{E as Fe}from"./exceljs.min-CsFn5HPa.js";import{u as A}from"./useMutation-ZsyDznMu.js";const Z=X.createContext(null);function lt({children:e}){const[n,t]=ie(null),[i,a]=w.useState(null);return c.jsx(Z,{value:{open:n,setOpen:t,currentRow:i,setCurrentRow:a},children:e})}const dt=()=>{const e=X.useContext(Z);if(!e)throw new Error("useItemsInCity has to be used within <ItemsInCityContext>");return e},ee=()=>{const e=new Fe.Workbook;return e.creator="POS System",e.lastModifiedBy="POS System",e.created=new Date,e.modified=new Date,e},ve=()=>["ID","Mã món","Thành phố","Tên","Giá","Trạng thái","Mã barcode","Món ăn kèm","Không cập nhật số lượng món ăn kèm","Đơn vị","Nhóm","Tên nhóm","Loại món","Tên loại","Mô tả","SKU","VAT (%)","Thời gian chế biến (phút)","Cho phép sửa giá khi bán","Cấu hình món ảo","Cấu hình món dịch vụ","Cấu hình món ăn là vé buffet","Giờ","Ngày","Thứ tự","Hình ảnh","Công thức inQR cho máy pha trà"],te=e=>{const n=ve(),t=e.addWorksheet("Menu");return t.addRow(n).eachCell(s=>{s.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},s.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},s.alignment={horizontal:"center",vertical:"middle"},s.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((s,u)=>{t.getColumn(u+1).width=s}),t},Te=()=>["ID","Mã món","Thành phố","Tên","Giá (Mặc định 0)","Trạng thái (Mặc định 1)","Mã barcode (Tối đa 13)","Món ăn kèm (Mặc định 0)","Không cập nhật số lượng món ăn kèm (Mặc định 1)","Đơn vị (Mặc định MON)","Nhóm (Mặc định LOẠI KHÁC)","Tên nhóm","Loại món (Mặc định rỗng)","Tên loại","Mô tả","SKU (Tối đa 50)","VAT (%) (Mặc định 0)","Thời gian chế biến (phút) (Mặc định 0)","Cho phép sửa giá khi bán (Mặc định 0)","Cấu hình món ảo (Mặc định 0)","Cấu hình món dịch vụ (Mặc định 0)","Cấu hình món ăn là vé buffet (Mặc định 0)","Giờ (Mặc định 0)","Ngày (Mặc định 0)","Thứ tự (Mặc định 0)","Hình ảnh","Công thức inQR cho máy pha trà"],Se=()=>[["d9692391-3f3f-4754-9416-878d2d8b52ce","ITEM-3279","Hồ Chí Minh","Cà Phê Sữa (L)",0,1,"",0,1,"AM","ITEM_TYPE_OTHER","Uncategory","DA","Đồ ăn","không","",0,10,14,0,0,1,2064384,224,0,"https://image.foodbook.vn/images/20250829/1756431019051-anh-test-may-in-mau-chat-luong-hinh-2.jpg",""],["8119567b-f80e-43ac-8c85-012dd8f56b18","KHOAI","Hồ Chí Minh","pomato",0,1,"",1,1,"TRAI","MAK","MÓN ĂN KÈM","MA","manh","","",0,5,14,1,0,1,6355002,254,0,"https://image.foodbook.vn/images/20250829/1756450169874-vo_tri_2.jpg",""]],ke=()=>[["Chủ nhật","2"],["Thứ 2","4"],["Thứ 3","8"],["Thứ 4","16"],["Thứ 5","32"],["Thứ 6","64"],["Thứ 7","128"],["Ví dụ: CN, T2, T5 = 2 + 4 + 32","38"]],De=()=>[["0h","1"],["1h","2"],["2h","4"],["3h","8"],["4h","16"],["5h","32"],["6h","64"],["7h","128"],["8h","256"],["9h","512"],["10h","1024"],["11h","2048"],["12h","4096"],["13h","8192"],["14h","16384"],["15h","32768"],["16h","65536"],["17h","131072"],["18h","262144"],["19h","524288"],["20h","1048576"],["21h","2097152"],["22h","4194304"],["23h","8388608"],["Ví dụ: 0h, 1h, 3h = 1 + 2 + 8","11"]],Me=e=>{const n=e.filter(t=>t.active===1).map(t=>[t.item_type_id,t.item_type_name]);return n.length>0?n:[["Không có dữ liệu",""]]},Ne=e=>{const n=e.filter(t=>t.active===1).map(t=>[t.item_class_id,t.item_class_name]);return n.length>0?n:[["Không có dữ liệu",""]]},je=e=>{if(!e||e.length===0)return[["Không có dữ liệu",""]];const n=e.map(t=>[t.unit_id,t.unit_name]);return n.length>0?n:[["Không có dữ liệu",""]]},Ee=(e,n)=>{var b,T,P,q,k,D,E,S,K,Q;const t=Te(),i=e.addWorksheet("Template"),a=i.addRow(['Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet "Menu" để nhập dữ liệu.']);i.mergeCells(`A${a.number}:AA${a.number}`),a.getCell(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},a.getCell(1).font={color:{argb:"FFFFFFFF"},bold:!0,size:11},a.getCell(1).alignment={horizontal:"left",vertical:"middle"},a.getCell(1).border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}},i.addRow(t).eachCell(l=>{l.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},l.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},l.alignment={horizontal:"center",vertical:"middle"},l.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),Se().forEach(l=>{i.addRow(l).eachCell(g=>{g.font={size:10},g.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),i.addRow([]);const h=i.addRow(["BẢNG THAM CHIẾU NGÀY","","","BẢNG THAM CHIẾU GIỜ","","","BẢNG THAM CHIẾU NHÓM MÓN","","","BẢNG THAM CHIẾU LOẠI MÓN","","","BẢNG THAM CHIẾU ĐƠN VỊ",""]),r=h.number;i.mergeCells(`A${r}:B${r}`),i.mergeCells(`D${r}:E${r}`),i.mergeCells(`G${r}:H${r}`),i.mergeCells(`J${r}:K${r}`),i.mergeCells(`M${r}:N${r}`),h.eachCell(l=>{l.value&&l.value.toString().trim()!==""&&(l.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},l.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},l.alignment={horizontal:"center",vertical:"middle"},l.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})}),i.addRow(["Thời gian","Giá trị","","Thời gian","Giá trị","","Mã nhóm","Tên nhóm","","Mã loại món","Tên loại món","","Mã đơn vị","Tên đơn vị"]).eachCell(l=>{l.value&&l.value.toString().trim()!==""&&(l.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},l.font={bold:!0,size:10},l.alignment={horizontal:"center",vertical:"middle"},l.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})});const o=ke(),_=De(),y=n!=null&&n.itemTypes?Me(n.itemTypes):[["Không có dữ liệu",""]],p=n!=null&&n.itemClasses?Ne(n.itemClasses):[["Không có dữ liệu",""]],I=n!=null&&n.units?je(n.units):[["Không có dữ liệu",""]],v=Math.max(o.length,_.length,y.length,p.length,I.length),N=[];for(let l=0;l<v;l++){const d=[((b=o[l])==null?void 0:b[0])||"",((T=o[l])==null?void 0:T[1])||"","",((P=_[l])==null?void 0:P[0])||"",((q=_[l])==null?void 0:q[1])||"","",((k=y[l])==null?void 0:k[0])||"",((D=y[l])==null?void 0:D[1])||"","",((E=p[l])==null?void 0:E[0])||"",((S=p[l])==null?void 0:S[1])||"","",((K=I[l])==null?void 0:K[0])||"",((Q=I[l])==null?void 0:Q[1])||""];N.push(d)}return N.forEach(l=>{i.addRow(l).eachCell(g=>{g.value&&g.value.toString().trim()!==""&&(g.font={size:10},g.alignment={horizontal:"left",vertical:"middle"},g.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}})})}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((l,d)=>{i.getColumn(d+1).width=l}),i},ut=async(e,n)=>{try{const t=ee(),i=te(t);n&&n.length>0&&n.forEach(o=>{var v;const y=((v=(o.cities||[])[0])==null?void 0:v.city_name)||"",p=o.extra_data||{},I=[o.id||"",o.item_id||"",y,o.item_name||"",o.ots_price||0,o.active||1,o.item_id_barcode||"",o.is_eat_with||0,p.no_update_quantity_toping||1,o.unit_uid||"",o.item_type_uid||"","",o.item_class_uid||"","",o.description||"",o.sku||"",o.ots_tax||0,o.time_cooking||0,p.enable_edit_price||0,p.is_virtual_item||0,p.is_item_service||0,p.is_buffet_item||0,o.time_sale_hour_day||0,o.time_sale_date_week||0,o.list_order||0,o.image_path||"",p.formula_qrcode||""];i.addRow(I)}),Ee(t,e);const a=await t.xlsx.writeBuffer(),s=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),h=`items_import_template_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.xlsx`,r=window.URL.createObjectURL(s),m=document.createElement("a");return m.href=r,m.download=h,document.body.appendChild(m),m.click(),document.body.removeChild(m),window.URL.revokeObjectURL(r),Promise.resolve()}catch(t){return console.error("Error creating Excel file:",t),Promise.reject(t)}},Re=async e=>{try{const n=ee(),t=te(n);e.forEach(a=>{var m;const u=((m=(a.cities||[])[0])==null?void 0:m.city_name)||"",h=a.extra_data||{},r=[a.id||"",a.item_id||"",u,a.item_name||"",a.ots_price||0,a.active||1,a.item_id_barcode||"",a.is_eat_with||0,h.no_update_quantity_toping||1,a.unit_uid||"",a.item_type_uid||"","",a.item_class_uid||"","",a.description||"",a.sku||"",a.ots_tax||0,a.time_cooking||0,h.enable_edit_price||0,h.is_virtual_item||0,h.is_item_service||0,h.is_buffet_item||0,a.time_sale_hour_day||0,a.time_sale_date_week||0,a.list_order||0,a.image_path||"",h.formula_qrcode||""];t.addRow(r)});const i=await n.xlsx.writeBuffer();return new Blob([i],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})}catch(n){throw console.error("Error creating Excel blob:",n),n}},Ae=()=>{try{const e=localStorage.getItem("pos_cities_data");if(e)return JSON.parse(e).map(t=>t.id)}catch{}return[]},G=e=>{if(!e)return null;if(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e))return e;try{const t=localStorage.getItem("pos_cities_data");if(t){const a=JSON.parse(t).find(s=>s.city_name===e);return(a==null?void 0:a.id)||null}}catch{return null}return null},M=new Map,H=new Map,Pe=10*60*1e3;function z(e){return typeof e=="object"&&e!==null&&"response"in e}const C={getItemsInCity:async e=>{const n=e.city_uid&&e.city_uid!=="all"?G(e.city_uid):e.city_uid,t=e.active!==void 0?e.active.toString():"undefined",i=e.skip_limit?"true":"false",a=`${e.company_uid}-${e.brand_uid}-${e.page||1}-${n||"all"}-${e.list_city_uid||"all"}-${e.item_type_uid||"all"}-${e.time_sale_date_week||""}-${t}-${e.reverse||0}-${e.search||""}-${e.limit||50}-${i}`,s=M.get(a);if(s&&Date.now()-s.timestamp<Pe)return s.data;const u=H.get(a);if(u)return u;const h=(async()=>{try{const r=new URLSearchParams;if(r.append("company_uid",e.company_uid),r.append("brand_uid",e.brand_uid),e.page&&r.append("page",e.page.toString()),e.item_type_uid&&r.append("item_type_uid",e.item_type_uid),e.list_city_uid)r.append("list_city_uid",e.list_city_uid);else if(e.city_uid&&e.city_uid!=="all"){const _=G(e.city_uid);_&&r.append("city_uid",_)}else{const _=Ae();_.length>0&&r.append("list_city_uid",_.join(","))}e.time_sale_date_week&&r.append("time_sale_date_week",e.time_sale_date_week),e.reverse!==void 0&&r.append("reverse",e.reverse.toString()),e.search&&r.append("search",e.search),e.active!==void 0&&r.append("active",e.active.toString()),e.limit&&r.append("limit",e.limit.toString()),e.skip_limit&&r.append("skip_limit","true");const m=await x.get(`/mdata/v1/items?${r.toString()}`);if(!m.data||typeof m.data!="object")throw new Error("Invalid response format from items in city API");const o=m.data;return M.set(a,{data:o,timestamp:Date.now()}),o}finally{H.delete(a)}})();return H.set(a,h),h},deleteItemInCity:async e=>{var n;try{const t=new URLSearchParams;t.append("company_uid",e.company_uid),t.append("brand_uid",e.brand_uid),t.append("id",e.id),await x.delete(`/mdata/v1/item?${t.toString()}`),M.clear()}catch(t){throw z(t)&&((n=t.response)==null?void 0:n.status)===404?new Error("Item not found."):t}},deleteMultipleItemsInCity:async e=>{var n;try{const t=new URLSearchParams;t.append("company_uid",e.company_uid),t.append("brand_uid",e.brand_uid),t.append("list_item_uid",e.list_item_uid.join(",")),await x.delete(`/mdata/v1/items?${t.toString()}`),M.clear()}catch(t){throw z(t)&&((n=t.response)==null?void 0:n.status)===404?new Error("Items not found."):t}},downloadTemplate:async e=>{var s;const n=e.city_uid&&e.city_uid!=="all"?G(e.city_uid):null,t=new URLSearchParams({skip_limit:"true",company_uid:e.company_uid,brand_uid:e.brand_uid,...n&&{city_uid:n},...e.item_type_uid&&e.item_type_uid!=="all"&&{item_type_uid:e.item_type_uid},...e.active&&e.active!=="all"&&{active:e.active}}),i=await x.get(`/mdata/v1/items?${t}`),a=Array.isArray((s=i.data)==null?void 0:s.data)?i.data.data:[];return await Re(a)},fetchItemsData:async e=>{var a;const n=e.city_uid&&e.city_uid!=="all"?G(e.city_uid):null,t=new URLSearchParams({skip_limit:"true",company_uid:e.company_uid,brand_uid:e.brand_uid,...n&&{city_uid:n},...e.item_type_uid&&e.item_type_uid!=="all"&&{item_type_uid:e.item_type_uid},...e.active&&e.active!=="all"&&{active:e.active}}),i=await x.get(`/mdata/v1/items?${t}`);return Array.isArray((a=i.data)==null?void 0:a.data)?i.data.data:[]},createItemInCity:async e=>{var n,t;try{const i=await x.post("/mdata/v1/item",e);return M.clear(),i.data.data||i.data}catch(i){throw z(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((t=i.response.data)==null?void 0:t.message)||"Invalid data provided."):i}},updateItemInCity:async e=>{var n,t;try{const i=await x.put("/mdata/v1/item",e,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return M.clear(),i.data.data||i.data}catch(i){throw z(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((t=i.response.data)==null?void 0:t.message)||"Invalid data provided."):i}},getItemByListId:async e=>{var n,t;try{const i=new URLSearchParams({skip_limit:"true",company_uid:e.company_uid,brand_uid:e.brand_uid,is_all:"true",list_item_id:e.list_item_id}),a=await x.get(`/mdata/v1/items?${i}`,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4}),s=Array.isArray((n=a.data)==null?void 0:n.data)?a.data.data:[];if(!s.length)throw new Error("Item not found");return{data:s[0]}}catch(i){throw z(i)&&((t=i.response)==null?void 0:t.status)===404?new Error("Item not found."):i}},getItemById:async e=>{var n;try{const t=new URLSearchParams;t.append("id",e.id),e.company_uid&&t.append("company_uid",e.company_uid),e.brand_uid&&t.append("brand_uid",e.brand_uid);const i=await x.get(`/mdata/v1/item?${t.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from item detail API");return i.data}catch(t){throw z(t)&&((n=t.response)==null?void 0:n.status)===404?new Error("Item not found."):t}},importItems:async e=>(await x.post("/mdata/v1/items/import",{company_uid:e.company_uid,brand_uid:e.brand_uid,items:e.items})).data,updateItemStatus:async e=>{var n,t;try{const a={...(await C.getItemById({id:e.id})).data,active:e.active,company_uid:e.company_uid,brand_uid:e.brand_uid},s=await x.put("/mdata/v1/item",a,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return M.clear(),s.data.data||s.data}catch(i){throw z(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((t=i.response.data)==null?void 0:t.message)||"Invalid data provided."):i}},clearCache:()=>{M.clear(),H.clear()},getCacheStats:()=>({cacheSize:M.size,pendingRequests:H.size})},Le=(e={})=>{const{params:n={},enabled:t=!0}=e,{company:i,brands:a}=R(p=>p.auth),s=a==null?void 0:a[0],u={company_uid:(i==null?void 0:i.id)||"",brand_uid:(s==null?void 0:s.id)||"",page:1,reverse:1,limit:50,...n},h=!!(i!=null&&i.id&&(s!=null&&s.id)),r=Y({queryKey:[F.ITEMS_IN_CITY_LIST,JSON.stringify(u)],queryFn:async()=>(await C.getItemsInCity(u)).data||[],enabled:t&&h,staleTime:5*60*1e3,refetchInterval:10*60*1e3}),m={...u,page:(u.page||1)+1},o=Y({queryKey:[F.ITEMS_IN_CITY_LIST,"next",JSON.stringify(m)],queryFn:async()=>(await C.getItemsInCity(m)).data||[],enabled:t&&h&&(r.data?r.data.length>0:!1),staleTime:2*60*1e3,gcTime:5*60*1e3}),_=u.limit||50,y=(o.data?o.data.length>0:!1)||(r.data?r.data.length===_:!1);return{data:r.data,isLoading:r.isLoading,error:r.error,refetch:r.refetch,isFetching:r.isFetching,nextPageData:o.data||[],hasNextPage:y}},mt=(e,n=!0)=>Y({queryKey:[F.ITEMS_IN_CITY_DETAIL,e],queryFn:()=>C.getItemById({id:e}),enabled:n&&!!e,staleTime:5*60*1e3}),ht=(e={})=>{var r,m,o;const n=Le(e),t=(r=e.params)==null?void 0:r.city_uid,i=(m=e.params)==null?void 0:m.list_city_uid,{data:a=[]}=ae({skip_limit:!0,...t&&t!=="all"?{city_uid:t}:{},...i?{list_city_uid:i}:{}}),{data:s=[]}=se({skip_limit:!0}),{data:u=[]}=oe();return{data:((o=n.data)==null?void 0:o.map(_=>{var D,E;const y=_,I=((D=(y.cities||[])[0])==null?void 0:D.city_name)||"",v=y.item_type_uid?a.find(S=>S.id===y.item_type_uid):null,N=(v==null?void 0:v.item_type_name)||"",j=y.item_class_uid?s.find(S=>S.id===y.item_class_uid):null,b=(j==null?void 0:j.item_class_name)||"",T=y.unit_uid?u.find(S=>S.id===y.unit_uid):null,P=(T==null?void 0:T.unit_name)||"",k=y.is_eat_with===1||y.item_id_eat_with&&y.item_id_eat_with!==""?y.item_id_eat_with||"Món ăn kèm":"";return{..._,code:y.item_id||"",name:_.item_name,price:_.ots_price,vatPercent:_.ots_tax,cookingTime:_.time_cooking,categoryGroup:N,itemType:N,itemClass:b,unit:P,sideItems:k||void 0,city:I,buffetConfig:((E=y.extra_data)==null?void 0:E.is_buffet_item)===1?"Đã cấu hình":"Chưa cấu hình",customization:y.customization_uid||void 0,isActive:!!_.active,createdAt:typeof _.created_at=="number"?new Date(_.created_at*1e3):new Date(new Date(_.created_at).getTime())}}))||[],isLoading:n.isLoading,error:n.error,refetch:n.refetch,isFetching:n.isFetching,nextPageData:n.nextPageData,hasNextPage:n.hasNextPage}},_t=()=>{const e=$(),n=A({mutationFn:t=>C.createItemInCity(t),onSuccess:()=>{C.clearCache(),e.invalidateQueries({queryKey:[F.ITEMS_IN_CITY_LIST]}),f.success("Tạo món thành công!")},onError:t=>{f.error(t.message||"Có lỗi xảy ra khi tạo món")}});return{createItemAsync:n.mutateAsync,isPending:n.isPending}},ze=()=>{const e=$(),n=A({mutationFn:t=>C.updateItemInCity(t),onSuccess:()=>{C.clearCache(),e.invalidateQueries({queryKey:[F.ITEMS_IN_CITY_LIST]}),e.invalidateQueries({queryKey:[F.ITEMS_IN_CITY_DETAIL]}),f.success("Cập nhật món thành công!")},onError:t=>{f.error(t.message||"Có lỗi xảy ra khi cập nhật món")}});return{updateItemAsync:n.mutateAsync,isPending:n.isPending}},yt=()=>{const e=$(),{company:n,brands:t}=R(s=>s.auth),i=t==null?void 0:t[0],a=A({mutationFn:s=>{const u={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",id:s};return C.deleteItemInCity(u)},onSuccess:()=>{e.invalidateQueries({queryKey:[F.ITEMS_IN_CITY_LIST]}),f.success("Xóa món thành công!")},onError:s=>{f.error(s.message||"Có lỗi xảy ra khi xóa món")}});return{deleteItemAsync:a.mutateAsync,isPending:a.isPending}},gt=()=>{const e=$(),{company:n,brands:t}=R(s=>s.auth),i=t==null?void 0:t[0],a=A({mutationFn:s=>{const u={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",list_item_uid:s};return C.deleteMultipleItemsInCity(u)},onSuccess:()=>{e.invalidateQueries({queryKey:[F.ITEMS_IN_CITY_LIST]}),f.success("Xóa món ăn thành công")},onError:s=>{f.error((s==null?void 0:s.message)||"Có lỗi xảy ra khi xóa món ăn")}});return{deleteMultipleItemsAsync:a.mutateAsync,isPending:a.isPending}},pt=()=>{const e=$(),{company:n,brands:t}=R(s=>s.auth),i=t==null?void 0:t[0],a=A({mutationFn:s=>{const u={id:s.id,active:s.active,company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||""};return C.updateItemStatus(u)},onSuccess:()=>{C.clearCache(),e.invalidateQueries({queryKey:[F.ITEMS_IN_CITY_LIST]}),e.invalidateQueries({queryKey:[F.ITEMS_IN_CITY_DETAIL]}),f.success("Cập nhật trạng thái thành công!")},onError:s=>{f.error(s.message||"Có lỗi xảy ra khi cập nhật trạng thái")}});return{updateStatusAsync:a.mutateAsync,isPending:a.isPending}},ft=()=>{const{company:e,brands:n}=R(a=>a.auth),t=n==null?void 0:n[0],i=A({mutationFn:a=>{const s={company_uid:(e==null?void 0:e.id)||"",brand_uid:(t==null?void 0:t.id)||"",...a};return C.downloadTemplate(s)},onSuccess:a=>{const s=window.URL.createObjectURL(a),u=document.createElement("a");u.href=s,u.download=`items-template-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(s),f.success("Tải template thành công!")},onError:a=>{f.error(a.message||"Có lỗi xảy ra khi tải template")}});return{downloadTemplateAsync:i.mutateAsync,isPending:i.isPending}},Ct=()=>{const{company:e,brands:n}=R(a=>a.auth),t=n==null?void 0:n[0],i=A({mutationFn:a=>{const s={company_uid:(e==null?void 0:e.id)||"",brand_uid:(t==null?void 0:t.id)||"",...a};return C.fetchItemsData(s)},onError:a=>{f.error(a.message||"Có lỗi xảy ra khi tải dữ liệu")}});return{fetchItemsDataAsync:i.mutateAsync,isPending:i.isPending}},bt=()=>{const e=$(),{company:n,brands:t}=R(s=>s.auth),i=t==null?void 0:t[0],a=A({mutationFn:s=>{const u={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",items:s};return C.importItems(u)},onSuccess:()=>{e.invalidateQueries({queryKey:[F.ITEMS_IN_CITY_LIST]}),f.success("Import món thành công!")},onError:s=>{f.error(s.message||"Có lỗi xảy ra khi import món")}});return{importItemsAsync:a.mutateAsync,isPending:a.isPending}};function It({itemsBuffet:e,open:n,onOpenChange:t,onItemsChange:i,items:a,hide:s=!0,enable:u=!0,onEnableChange:h}){const[r,m]=w.useState(""),[o,_]=w.useState([]),[y,p]=w.useState(!1),[I,v]=w.useState(!1),[N,j]=w.useState(!1);w.useEffect(()=>{n&&(_(Array.isArray(e)?e:[]),j(u))},[e,n]);const b=w.useMemo(()=>r?a.filter(d=>{var g;return(g=d.item_name)==null?void 0:g.toLowerCase().includes(r.toLowerCase())}):a,[a,r]),T=w.useMemo(()=>b.length?b.filter(d=>o.includes(d.item_id||"")):[],[b,o]),P=w.useMemo(()=>b.length?b.filter(d=>!o.includes(d.item_id||"")):[],[b,o]),q=d=>{_(g=>g.includes(d)?g.filter(L=>L!==d):[...g,d])},k=T.length,D=b.length,E=D>0&&k===D,S=k>0&&k<D,K=()=>{if(E){const d=b.map(g=>g.item_id);_(g=>g.filter(L=>!d.includes(L)))}else{const d=b.map(g=>g.item_id);_(g=>{const L=[...g];return d.forEach(O=>{L.includes(O||"")||L.push(O||"")}),L})}},Q=()=>{i(o),t(!1)},l=()=>{_([]),t(!1)};return c.jsx(ce,{title:"Chọn danh sách món không đi kèm vé buffet",centerTitle:!0,open:n,onOpenChange:t,onCancel:l,onConfirm:Q,confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:c.jsxs("div",{className:"space-y-4",children:[!s&&c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(W,{id:"enable-buffet",checked:N,onCheckedChange:d=>{const g=!!d;j(g),h==null||h(g)}}),c.jsx("label",{htmlFor:"enable-buffet",className:"cursor-pointer text-blue-600",onClick:()=>j(d=>!d),children:"Cấu hình món ăn là vé buffet"})]}),(s||N)&&c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"flex items-center gap-2",children:c.jsx(re,{placeholder:"Tìm kiếm",value:r,onChange:d=>m(d.target.value),className:"w-full"})}),!s&&c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx(U,{type:"button",variant:"outline",className:"flex-1 justify-start",children:"Danh sách món không đi kèm vé buffet"}),c.jsx(U,{type:"button",variant:"link",className:"flex-1 justify-start text-blue-600",onClick:()=>{},children:"Danh sách vé buffet được upsize"})]}),c.jsxs("div",{className:"rounded-lg bg-green-50 p-3",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(W,{id:"select-all",checked:E,...S&&{"data-indeterminate":"true"},onCheckedChange:K,className:"data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600"}),c.jsxs("label",{htmlFor:"select-all",className:"cursor-pointer text-sm font-medium text-green-700",children:["Đã chọn ",k]}),c.jsx(U,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>p(!y),children:y?c.jsx(V,{className:"h-3 w-3"}):c.jsx(J,{className:"h-3 w-3"})})]}),!y&&T.length>0&&c.jsx("div",{className:"mt-3 space-y-2",children:T.map(d=>c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(W,{id:`selected-${d.item_id}`,checked:o.includes(d.item_id),onCheckedChange:()=>q(d.item_id)}),c.jsx("label",{htmlFor:`selected-${d.item_id}`,className:"flex-1 cursor-pointer text-sm",children:d.item_name})]},d.item_id))})]}),c.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsxs("div",{className:"text-sm font-medium text-gray-700",children:["Còn lại ",P.length]}),c.jsx(U,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>v(!I),children:I?c.jsx(V,{className:"h-3 w-3"}):c.jsx(J,{className:"h-3 w-3"})})]}),!I&&c.jsx("div",{className:"mt-3 max-h-60 space-y-2 overflow-y-auto",children:P.map(d=>c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(W,{id:d.item_id,checked:o.includes(d.item_id),onCheckedChange:()=>q(d.item_id)}),c.jsx("label",{htmlFor:d.item_id,className:"flex-1 cursor-pointer text-sm",children:d.item_name})]},d.item_id))})]})]})]})})}const qe=B.object({customization_uid:B.string().nullable()});function xt({item:e,customizations:n,open:t,onOpenChange:i}){const{updateItemAsync:a}=ze(),{company:s}=R(m=>m.auth),{selectedBrand:u}=ne(),h=le({resolver:ge(qe),defaultValues:{customization_uid:"none"}});w.useEffect(()=>{if(t)try{h.reset({customization_uid:(e==null?void 0:e.customization_uid)??null})}catch{f.error("Lỗi khi load customization data")}},[t,h,e]);const r=async m=>{try{if(!(e!=null&&e.id)||!(s!=null&&s.id)||!(u!=null&&u.id))throw new Error("Required data is missing");const o=m.customization_uid==="none"?null:m.customization_uid;await a({...e,customization_uid:o}),i(!1)}catch{f.error("Lỗi khi cập nhật customization")}};return e?c.jsx(pe,{open:t,onOpenChange:m=>{i(m),h.reset()},children:c.jsxs(fe,{className:"top-[20%] w-full max-w-4xl translate-y-[-50%]",children:[c.jsx(Ce,{children:c.jsx(be,{className:"text-center",children:"Cấu hình customization"})}),c.jsx(de,{...h,children:c.jsxs("form",{onSubmit:h.handleSubmit(r),className:"space-y-4",children:[c.jsx(ue,{control:h.control,name:"customization_uid",render:({field:m})=>c.jsxs(me,{children:[c.jsx(he,{children:"Customization áp dụng cho món"}),c.jsx(_e,{children:c.jsx(we,{value:m.value??"",onValueChange:o=>m.onChange(o===""?null:o),options:n.map(o=>({value:o.id,label:o.name})),placeholder:"Chọn customization...",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không có dữ liệu",className:"w-full"})}),c.jsx(ye,{})]})}),c.jsxs(Ie,{children:[c.jsx(xe,{asChild:!0,children:c.jsx(U,{variant:"outline",type:"button",children:"Hủy"})}),c.jsx(U,{type:"submit",disabled:h.formState.isSubmitting,children:h.formState.isSubmitting?"Đang lưu...":"Lưu"})]})]})})]})}):null}export{It as B,xt as C,lt as I,gt as a,ft as b,bt as c,Ct as d,yt as e,pt as f,ut as g,ze as h,ht as i,mt as j,_t as k,dt as u};
