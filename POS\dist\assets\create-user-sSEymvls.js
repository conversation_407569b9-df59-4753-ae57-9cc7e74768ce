import{h as C,r as y,j as e,B as j}from"./index-UcdZ5AHH.js";import"./date-range-picker-DxA68ufO.js";import{u as S,F as k,a as c,b as l,c as n,d,e as x}from"./form-D_U5B5Go.js";import{I as p}from"./input-CBpgGfUv.js";import{C as w}from"./checkbox-CDB9_T0n.js";import{c as E,u as P}from"./use-account-management-g4K9-VD1.js";import{s as V}from"./zod-5jr7PwGQ.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./select-DOexGcsG.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";function A(){const a=S({resolver:V(E),defaultValues:{username:"",email:"",password:"",permissions:[]}});return{form:a,resetForm:()=>{a.reset({username:"",email:"",password:"",permissions:[]})},isValid:a.formState.isValid,errors:a.formState.errors}}function M(){const a=C(),{form:t,resetForm:h}=A(),{createUser:f,permissionCategories:g,isLoading:u}=P(),[i,m]=y.useState([]),N=async s=>{try{await f({...s,permissions:i}),h(),m([]),a({to:"/general-setups/account"})}catch(r){console.error("Error creating account:",r)}},b=(s,r)=>{m(o=>r?[...o,s]:o.filter(F=>F!==s)),t.setValue("permissions",r?[...i,s]:i.filter(o=>o!==s))},v=()=>{h(),m([]),a({to:"/general-setups/account"})};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h1",{className:"text-2xl font-semibold",children:"Tạo tài khoản mới"})}),e.jsx("div",{className:"max-w-4xl",children:e.jsx(k,{...t,children:e.jsxs("form",{onSubmit:t.handleSubmit(N),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx(c,{control:t.control,name:"username",render:({field:s})=>e.jsxs(l,{children:[e.jsx(n,{children:"Tên người dùng *"}),e.jsx(d,{children:e.jsx(p,{placeholder:"Nhập tên người dùng",...s})}),e.jsx(x,{})]})}),e.jsx(c,{control:t.control,name:"email",render:({field:s})=>e.jsxs(l,{children:[e.jsx(n,{children:"Email"}),e.jsx(d,{children:e.jsx(p,{placeholder:"Nhập email",type:"email",...s})}),e.jsx(x,{})]})})]}),e.jsx(c,{control:t.control,name:"password",render:({field:s})=>e.jsxs(l,{children:[e.jsx(n,{children:"Mật khẩu *"}),e.jsx(d,{children:e.jsx(p,{placeholder:"Nhập mật khẩu",type:"password",...s})}),e.jsx(x,{})]})}),e.jsxs("div",{children:[e.jsx(n,{className:"text-base font-medium",children:"Cài đặt quyền cho tài khoản *"}),e.jsx("div",{className:"mt-4 space-y-6",children:g.map(s=>e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-sm font-medium",children:s.name}),e.jsx("div",{className:"grid grid-cols-1 gap-2 md:grid-cols-2",children:s.permissions.map(r=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(w,{id:r.id,checked:i.includes(r.id),onCheckedChange:o=>b(r.id,o)}),e.jsx("label",{htmlFor:r.id,className:"text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:r.name})]},r.id))})]},s.name))}),t.formState.errors.permissions&&e.jsx("p",{className:"text-destructive mt-2 text-sm",children:t.formState.errors.permissions.message})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx(j,{type:"button",variant:"outline",onClick:v,children:"Hủy"}),e.jsx(j,{type:"submit",disabled:u,children:u?"Đang tạo...":"Tạo tài khoản"})]})]})})})]})}const Z=M;export{Z as component};
