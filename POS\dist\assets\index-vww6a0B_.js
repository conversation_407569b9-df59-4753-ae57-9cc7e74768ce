import{h as xe,r,j as e,B as P,ay as Z,a4 as C,b as ue,l as pe,a3 as be}from"./index-CVQ6JZo2.js";import{a as Ne}from"./pos-api-mRg02iop.js";import"./vietqr-api-_ZZrmuU0.js";import{u as ee,a as ve,b as ye,c as we,d as Se,e as ne}from"./use-tables-C5Z7koKH.js";import{u as se,a as Ce}from"./use-areas-BEwI37-s.js";import"./user-BJzEhOTa.js";import"./crm-api-CDzLLTww.js";import{H as _e}from"./header-DiKooCuw.js";import{M as Te}from"./main-BD6dUgw2.js";import{P as ke}from"./profile-dropdown-BEO1YZeq.js";import{S as De,T as Ae}from"./search-bbGy26eb.js";import"./date-range-picker-CXbMaowj.js";import"./form-CzmGigtT.js";import{S as J,a as Q,b as W,c as G,d as H,C as Ie}from"./select-BFhNE0YE.js";import{D as Ee,a as Me,b as Fe,c as Y}from"./dropdown-menu-lhfGm_WJ.js";import{u as ze,c as Oe,e as Be,f as oe}from"./index-BCpVAkPF.js";import{D as Pe}from"./data-table-pagination-_F9gEREo.js";import{C as ge}from"./index-DFHgF59z.js";import{T as $e,a as Le,b as ae,c as Re,d as Ve,e as de}from"./table-BOc3nItc.js";import{T as fe}from"./trash-2-_PrOpZAV.js";import{C as me}from"./checkbox-BfLSzhzg.js";import{S as Ke}from"./status-badge-C8o-BQt9.js";import{u as Ue}from"./use-items-in-store-data-B90Qq0xn.js";import{P as te}from"./modal-DEESjk2b.js";import{D as je}from"./download-C9jrGxl1.js";import{U as le}from"./upload-Df52ST6B.js";import{u as He}from"./use-sales-channels-CDxRsdJ_.js";import{Q as he}from"./query-keys-3lmd-xp6.js";import{G as qe}from"./grip-vertical-B3Bm0NcF.js";import{X as Xe}from"./calendar-BszTCdZH.js";import{S as Je}from"./search-B4Rlb4i6.js";import{S as Qe}from"./square-pen-CIyK4qtR.js";import{A as We}from"./arrow-up-down-DeSUVWVI.js";import{S as Ge}from"./settings-58ClDTJ2.js";import"./useQuery-HgcIHxlE.js";import"./utils-km2FGkQ4.js";import"./useMutation-ZsyDznMu.js";import"./images-api-DZkYJB2_.js";import"./separator-BcoNozmD.js";import"./avatar-q0u2_bqW.js";import"./search-context-CkCLuJFL.js";import"./command-Nb4B17YQ.js";import"./dialog-DDrduXt3.js";import"./createReactComponent-DSXPaZ4c.js";import"./scroll-area-CGsZUbT-.js";import"./index-LVHINuqD.js";import"./IconChevronRight-LXWXuzjR.js";import"./IconSearch-DjIF9VGJ.js";import"./createLucideIcon-DKVxsQv7.js";import"./chevron-right-CxgpqvrH.js";import"./react-icons.esm-DMMA_g0o.js";import"./popover-DnoSPJNX.js";import"./index-CtK-wKtB.js";import"./isSameMonth-C8JQo-AN.js";import"./index-nc1u7392.js";import"./check-BE_j5GZD.js";import"./index-C34iUvGy.js";import"./badge-DIVCRE0k.js";import"./use-item-types-DnnQzY0g.js";import"./use-item-classes-uo7m9d9N.js";import"./use-units-DPUE3WPd.js";import"./use-removed-items-Yn7fGIc0.js";import"./items-in-store-api-C7Two_iQ.js";import"./xlsx-DkH2s96g.js";function Ye(){const f=xe(),i=r.useMemo(()=>{try{const I=localStorage.getItem("pos_stores_data");if(I){const s=JSON.parse(I);return Array.isArray(s)?s.filter(n=>n.active===1):[]}return[]}catch(I){return console.error("Error parsing pos_stores_data:",I),[]}},[]),[o,d]=r.useState(""),[m,p]=r.useState("all"),[l,j]=r.useState(!1),[u,g]=r.useState(!1),[h,t]=r.useState(!1),[x,S]=r.useState(!1);r.useEffect(()=>{i.length>0&&!o&&d(i[0].id)},[i.length,o]),r.useEffect(()=>{o||p("all")},[o]);const{data:_=[],isLoading:k}=se({storeUid:o||void 0,page:1,results_per_page:15e3}),{data:E=[],isLoading:D,error:v}=ee({storeUid:o,skip_limit:!0}),T=r.useMemo(()=>rs(o),[o]),w=r.useMemo(()=>m==="all"?E:E.filter(I=>I.area_uid===m),[E,m]),M=()=>{f({to:"/setting/table/detail"})},O=()=>{o&&j(!0)},$=()=>{j(!1)},L=()=>{j(!1)},a=()=>{o&&g(!0)},N=()=>{g(!1)},y=()=>{g(!1)},F=()=>{o&&t(!0)},V=()=>{t(!1)},R=()=>{t(!1)},q=()=>{o&&S(!0)},K=()=>{S(!1)},X=I=>{d(I),p("all")};return e.jsxs(e.Fragment,{children:[e.jsx(_e,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(De,{}),e.jsx(Ae,{}),e.jsx(ke,{})]})}),e.jsx(Te,{children:e.jsxs("div",{className:"container mx-auto space-y-6 py-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:"Danh sách bàn"}),e.jsxs("div",{className:"flex flex-1 gap-4",children:[e.jsxs(J,{value:o,onValueChange:X,children:[e.jsx(Q,{className:"w-[300px]",children:e.jsx(W,{placeholder:"Chọn cửa hàng"})}),e.jsx(G,{children:i.map(I=>e.jsx(H,{value:I.id,children:I.store_name},I.id))})]}),e.jsxs(J,{value:m,onValueChange:p,disabled:!o||k,children:[e.jsx(Q,{className:"w-[300px]",children:e.jsx(W,{placeholder:o?k?"Đang tải...":"Chọn khu vực":"Chọn cửa hàng trước"})}),e.jsx(G,{children:o&&e.jsxs(e.Fragment,{children:[e.jsx(H,{value:"all",children:"Tất cả khu vực"}),_.map(I=>e.jsx(H,{value:I.id,children:I.area_name},I.id))]})})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(Ee,{children:[e.jsx(Me,{asChild:!0,children:e.jsxs(P,{variant:"outline",children:["Tiện ích",e.jsx(Ie,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(Fe,{align:"end",className:"w-56",children:[e.jsxs(Y,{onClick:O,children:[e.jsx(le,{className:"mr-2 h-4 w-4"}),"Thêm bàn từ file"]}),e.jsxs(Y,{onClick:a,children:[e.jsx(Qe,{className:"mr-2 h-4 w-4"}),"Sửa thông tin bàn"]}),e.jsxs(Y,{onClick:F,children:[e.jsx(We,{className:"mr-2 h-4 w-4"}),"Sắp xếp bàn"]}),e.jsxs(Y,{onClick:q,children:[e.jsx(Ge,{className:"mr-2 h-4 w-4"}),"Cấu hình bàn"]})]})]}),e.jsx(P,{onClick:M,children:"Tạo bàn mới"})]})]}),D&&e.jsx("div",{className:"rounded-md border p-8 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Đang tải..."})}),v&&e.jsx("div",{className:"rounded-md border p-8 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Có lỗi xảy ra khi tải dữ liệu"})}),!D&&!v&&e.jsx(Ze,{columns:T,data:w,storeUid:o}),!o&&i.length>0&&e.jsx("div",{className:"bg-card rounded-lg border p-8 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Vui lòng chọn cửa hàng để xem danh sách bàn"})}),i.length===0&&e.jsx("div",{className:"bg-card rounded-lg border p-8 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Không có cửa hàng nào khả dụng"})}),e.jsx(is,{open:l,onOpenChange:j,onCancel:$,onSuccess:L}),e.jsx(os,{open:u,onOpenChange:g,onCancel:N,onSuccess:y}),e.jsx(ds,{open:h,onOpenChange:t,onCancel:V,onSuccess:R}),e.jsx(ms,{open:x,onOpenChange:S,onCancel:K})]})})]})}function Ze({columns:f,data:i,storeUid:o}){var D;const d=xe(),[m,p]=r.useState({}),[l,j]=r.useState(!1),{deleteTables:u,isDeleting:g}=ve(),h=ze({data:i,columns:f,state:{rowSelection:m},enableRowSelection:!0,onRowSelectionChange:p,getCoreRowModel:Be(),getPaginationRowModel:Oe(),initialState:{pagination:{pageSize:10}}}),t=h.getFilteredSelectedRowModel().rows,x=t.length,S=()=>{j(!0)},_=()=>{const v=t.map(T=>T.original.id);u({areaIds:v,storeUid:o}),p({}),j(!1)},k=()=>{j(!1)},E=(v,T)=>{const w=T.target;w.closest('input[type="checkbox"]')||w.closest("button")||w.closest('[role="button"]')||(console.log("🔍 Navigating to table detail:",{tableId:v.id,tableName:v.table_name,storeUid:o}),d({to:"/setting/table/detail/$areaId",params:{areaId:v.id},search:{store_uid:o}}))};return e.jsxs("div",{className:"space-y-4",children:[x>0&&e.jsx("div",{className:"justify-startr flex items-center",children:e.jsxs(P,{variant:"destructive",size:"sm",onClick:S,disabled:g,className:"h-8",children:[e.jsx(fe,{className:"h-4 w-4"}),g?"Đang xóa...":"Xóa bàn ("+x+")"]})}),e.jsx("div",{className:"rounded-md border",children:e.jsxs($e,{children:[e.jsx(Le,{children:h.getHeaderGroups().map(v=>e.jsx(ae,{children:v.headers.map(T=>e.jsx(Re,{style:{width:T.getSize()},children:T.isPlaceholder?null:oe(T.column.columnDef.header,T.getContext())},T.id))},v.id))}),e.jsx(Ve,{children:(D=h.getRowModel().rows)!=null&&D.length?h.getRowModel().rows.map(v=>{const T=v.original;return e.jsx(ae,{"data-state":v.getIsSelected()&&"selected",className:"cursor-pointer hover:bg-gray-50",onClick:w=>E(T,w),children:v.getVisibleCells().map(w=>e.jsx(de,{style:{width:w.column.getSize()},children:oe(w.column.columnDef.cell,w.getContext())},w.id))},v.id)}):e.jsx(ae,{children:e.jsx(de,{colSpan:f.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center justify-center space-y-2",children:[e.jsx("p",{className:"text-muted-foreground",children:"Không có bàn nào"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Hãy tạo bàn mới để bắt đầu quản lý"})]})})})})]})}),e.jsx(Pe,{table:h}),e.jsx(ge,{open:l,onOpenChange:j,title:"Lưu ý: Xóa bàn có thể ảnh hưởng đến dữ liệu đang vận hành tại POS",content:`Bạn có muốn xoá ${x} bàn đã chọn ?`,confirmText:"Xóa",cancelText:"Hủy",onConfirm:_,onCancel:k,isLoading:g})]})}function es(f){try{const i=localStorage.getItem("pos_stores_data");if(i){const d=JSON.parse(i).find(m=>m.id===f);return(d==null?void 0:d.store_name)||"Không xác định"}return"Không xác định"}catch{return"Không xác định"}}function ss({table:f}){const{toggleTableStatus:i,isToggling:o}=ye(),d=()=>{i(f)};return e.jsx("div",{className:"flex items-center justify-center gap-2",children:e.jsx("button",{onClick:d,disabled:o,className:"cursor-pointer disabled:cursor-not-allowed disabled:opacity-50",children:e.jsx(Ke,{isActive:f.active===1,activeText:"Active",inactiveText:"Deactive"})})})}function ts({table:f,storeUid:i}){const[o,d]=r.useState(!1),{deleteTable:m,isDeleting:p}=we(i),l=()=>{d(!0)},j=async()=>{try{await m(f.id),d(!1)}catch{}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(P,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-700",onClick:l,disabled:p,children:e.jsx(fe,{className:"h-4 w-4"})})}),e.jsx(ge,{open:o,onOpenChange:d,content:`Bạn có muốn xóa bàn "${f.table_name}"?`,confirmText:"Xóa",onConfirm:j,isLoading:p})]})}function as({table:f}){var m;const i=((m=f.extra_data)==null?void 0:m.order_list)||[],{data:o=[]}=Ue({params:{store_uid:f.store_uid},enabled:!!f.store_uid&&i.length>0});if(i.length===0)return e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-sm text-gray-500",children:"-"})});const d=i.map(p=>{const l=o.find(u=>u.code===p.item_id);return`${(l==null?void 0:l.name)||p.item_id} x ${p.quantity}`}).join(", ");return e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-sm",children:d})})}const rs=f=>[{id:"select",header:({table:i})=>e.jsx("div",{className:"flex justify-center",children:e.jsx(me,{checked:i.getIsAllPageRowsSelected()||i.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:o=>i.toggleAllPageRowsSelected(!!o),"aria-label":"Select all",className:"translate-y-[2px]"})}),cell:({row:i})=>e.jsx("div",{className:"flex justify-center",children:e.jsx(me,{checked:i.getIsSelected(),onCheckedChange:o=>i.toggleSelected(!!o),"aria-label":"Select row",className:"translate-y-[2px]"})}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:()=>e.jsx("div",{className:"text-center",children:"#"}),cell:({row:i})=>e.jsx("div",{className:"w-[50px] text-center font-medium",children:i.index+1}),enableSorting:!1,enableHiding:!1,size:60},{accessorKey:"table_name",header:()=>e.jsx("div",{className:"text-center",children:"Tên bàn"}),cell:({row:i})=>e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"font-medium",children:i.getValue("table_name")})}),size:200},{accessorKey:"table_id",header:()=>e.jsx("div",{className:"text-center",children:"Mã bàn"}),cell:({row:i})=>e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"font-mono text-sm",children:i.getValue("table_id")})}),size:150},{id:"applied_stores",header:()=>e.jsx("div",{className:"text-center",children:"Điểm áp dụng"}),cell:({row:i})=>{const o=i.original,d=es(o.store_uid||"");return e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-sm",children:d})})},enableSorting:!1,size:120},{id:"area_name",header:()=>e.jsx("div",{className:"text-center",children:"Khu vực"}),cell:({row:i})=>{var d;const o=i.original;return e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-sm",children:((d=o.area)==null?void 0:d.area_name)||"Không xác định"})})},enableSorting:!1,size:120},{id:"pre_orders",header:()=>e.jsx("div",{className:"text-center",children:"Món đặt trước"}),cell:({row:i})=>e.jsx(as,{table:i.original}),enableSorting:!1,size:150},{id:"actions",header:()=>e.jsx("div",{className:"text-center",children:"Thao tác"}),cell:({row:i})=>e.jsx(ss,{table:i.original}),enableSorting:!1,size:100},{id:"delete",header:()=>e.jsx("div",{className:"text-center"}),cell:({row:i})=>e.jsx(ts,{table:i.original,storeUid:f}),enableSorting:!1,size:60}];function ns(){return{parseExcelFile:async o=>{try{const d=await Z(()=>import("./xlsx-DkH2s96g.js"),[]),m=await o.arrayBuffer(),p=d.read(m,{type:"array"}),l=p.SheetNames[0],j=p.Sheets[l],u=d.utils.sheet_to_json(j,{header:1}),g=[];for(let h=1;h<u.length;h++){const t=u[h];if(!t||t.length===0||!t[0])continue;const x={table_name:String(t[0]||"").trim(),area_name:String(t[1]||"").trim(),source_name:t[2]?String(t[2]).trim():void 0,pre_order_items:t[3]?String(t[3]).trim():void 0,description:t[4]?String(t[4]).trim():void 0};if(!x.table_name){C.error(`Dòng ${h+1}: Tên bàn không được để trống`);continue}if(!x.area_name){C.error(`Dòng ${h+1}: Khu vực không được để trống`);continue}g.push(x)}if(g.length===0)throw C.error("Không tìm thấy dữ liệu hợp lệ trong file"),new Error("No valid data found");return g}catch(d){throw console.error("Error parsing Excel file:",d),d instanceof Error&&d.message!=="No valid data found"&&C.error("Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file."),d}},downloadTemplate:()=>{const o=[["Tên bàn","Khu vực","Nguồn","Món đặt trước","Mô tả"],["Bàn 01","Tầng 1","Tại chỗ","","Bàn gần cửa sổ"],["Bàn 02","Tầng 1","Tại chỗ","","Bàn góc"],["Bàn 03","Tầng 2","Grab Food","","Bàn VIP"]];Z(()=>import("./xlsx-DkH2s96g.js"),[]).then(d=>{const m=d.utils.book_new(),p=d.utils.aoa_to_sheet(o);d.utils.book_append_sheet(m,p,"Tables Template"),d.writeFile(m,"import_tables_template.xlsx"),C.success("Đã tải file mẫu thành công!")})}}}function ls(){const[f,i]=r.useState(!1),o=Ce();return{checkAndCreateAreas:async(p,l,j)=>{try{i(!0);const u=Array.from(new Set(l.map(x=>x.area_name.trim()).filter(Boolean))),g={},h=new Set;j.forEach(x=>{const S=x.area_name.trim();g[S]=x.id,h.add(S)});const t=u.filter(x=>!h.has(x)).map(x=>({area_name:x,description:"Khu vực được tạo tự động từ import bàn"}));return t.length>0&&(C.info(`Đang tạo ${t.length} khu vực mới...`),(await o.mutateAsync({storeUid:p,areas:t})).forEach((S,_)=>{const k=t[_].area_name;g[k]=S.id}),C.success(`Đã tạo thành công ${t.length} khu vực mới!`)),g}catch(u){throw console.error("Error creating areas:",u),C.error("Có lỗi xảy ra khi tạo khu vực. Vui lòng thử lại."),u}finally{i(!1)}},getAreasToCreate:(p,l)=>{const j=Array.from(new Set(p.map(g=>g.area_name.trim()).filter(Boolean))),u=new Set(l.map(g=>g.area_name.trim()));return j.filter(g=>!u.has(g))},isCreatingAreas:f||o.isPending}}function is({open:f,onOpenChange:i,onCancel:o,onSuccess:d}){const[m,p]=r.useState(null),[l,j]=r.useState([]),[u,g]=r.useState(1),h=r.useRef(null),[t,x]=r.useState(""),{parseExcelFile:S,downloadTemplate:_}=ns(),k=Se(),{checkAndCreateAreas:E,getAreasToCreate:D,isCreatingAreas:v}=ls(),{data:T=[]}=se({storeUid:t,page:1,results_per_page:15e3}),w=r.useMemo(()=>{try{const a=localStorage.getItem("pos_stores_data");if(a){const N=JSON.parse(a);return Array.isArray(N)?N.filter(y=>y.active===1):[]}return[]}catch(a){return console.error("Error parsing pos_stores_data:",a),[]}},[]);r.useMemo(()=>{w.length>0&&!t&&x(w[0].id)},[w,t]),r.useMemo(()=>{var a;f||(g(1),j([]),p(null),x(((a=w[0])==null?void 0:a.id)||""))},[f,w]);const M=()=>{_()},O=()=>{var a;(a=h.current)==null||a.click()},$=async a=>{var y;const N=(y=a.target.files)==null?void 0:y[0];if(N){p(N);try{const F=await S(N);j(F),g(2),C.success(`Đã phân tích ${F.length} bàn từ file!`)}catch{}}},L=async()=>{if(u===2&&t&&l.length>0)try{const a=await E(t,l,T),N=l.map(y=>{const F=a[y.area_name.trim()];if(!F)throw new Error(`Không tìm thấy khu vực: ${y.area_name}`);return{table_name:y.table_name,area_uid:F,description:y.description||""}});await k.mutateAsync({storeUid:t,tables:N}),C.success(`Đã tạo thành công ${l.length} bàn!`),d()}catch(a){a instanceof Error?C.error(a.message):C.error("Lỗi khi tạo bàn. Vui lòng thử lại.")}};return e.jsxs(te,{title:"Thêm bàn",open:f,onOpenChange:i,onCancel:o,onConfirm:u===2?L:()=>{},confirmText:u===2?"Tạo bàn":void 0,cancelText:"Đóng",centerTitle:!0,maxWidth:"sm:max-w-4xl",isLoading:k.isPending||v,hideButtons:u===1,confirmDisabled:u===2&&(!t||l.length===0),children:[e.jsxs("div",{className:"max-h-[70vh] space-y-6 overflow-y-auto",children:[e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsxs(P,{variant:"outline",size:"sm",onClick:M,className:"flex items-center gap-2",children:[e.jsx(je,{className:"h-4 w-4"}),"Tải xuống"]})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm bàn vào file"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"- Không sửa lại tên dòng tiêu đề"}),e.jsx("p",{children:"- Nhập thông tin bàn vào các dòng sau đó"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file bàn lên"}),e.jsxs(P,{variant:"outline",size:"sm",onClick:O,className:"flex items-center gap-2",children:[e.jsx(le,{className:"h-4 w-4"}),"Tải file lên"]})]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ ban bạn có thể tải file lên"}),m&&e.jsx("div",{className:"rounded-lg border bg-gray-50 p-3",children:e.jsxs("p",{className:"text-sm text-gray-700",children:["File đã chọn: ",e.jsx("span",{className:"font-medium",children:m.name})]})})]}),u===2&&l.length>0&&e.jsxs("div",{className:"space-y-4 border-t pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"text-md font-medium",children:"Chọn cửa hàng áp dụng"}),e.jsxs(J,{value:t,onValueChange:x,children:[e.jsx(Q,{className:"w-[200px]",children:e.jsx(W,{placeholder:"Chọn cửa hàng"})}),e.jsx(G,{children:w.map(a=>e.jsx(H,{value:a.id,children:a.store_name},a.id))})]})]}),D(l,T).length>0?e.jsx("div",{className:"rounded-lg bg-yellow-50 border border-yellow-200 p-3",children:e.jsx("p",{className:"text-sm text-yellow-800 text-center",children:"Các khu vực chưa tồn tại sẽ được tạo trước khi thêm bàn"})}):null,e.jsx("div",{className:"h-64 overflow-y-auto rounded-lg border",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"sticky top-0 z-10 bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"Tên bàn"}),e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"Khu vực"}),e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"Nguồn"}),e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"Món đặt trước"}),e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"Mô tả"})]})}),e.jsx("tbody",{children:l.map((a,N)=>e.jsxs("tr",{className:"border-t hover:bg-gray-50",children:[e.jsx("td",{className:"px-3 py-2 text-sm",children:a.table_name}),e.jsx("td",{className:"px-3 py-2 text-sm",children:a.area_name}),e.jsx("td",{className:"px-3 py-2 text-sm",children:a.source_name||"-"}),e.jsx("td",{className:"px-3 py-2 text-sm",children:a.pre_order_items||"-"}),e.jsx("td",{className:"px-3 py-2 text-sm",children:a.description||"-"})]},N))})]})}),e.jsxs("div",{className:"text-center text-sm text-gray-600",children:["Tổng cộng: ",e.jsx("span",{className:"font-medium",children:l.length})," bàn sẽ được tạo"]})]})]}),e.jsx("input",{ref:h,type:"file",accept:".xlsx,.xls",onChange:$,className:"hidden"})]})}function cs(){return{parseExcelFile:async o=>{try{const d=await Z(()=>import("./xlsx-DkH2s96g.js"),[]),m=await o.arrayBuffer(),p=d.read(m,{type:"array"}),l=p.SheetNames[0],j=p.Sheets[l],u=d.utils.sheet_to_json(j,{header:1}),g=[];for(let h=1;h<u.length;h++){const t=u[h];if(!t||t.length===0||!t[0])continue;const x={id:String(t[0]||"").trim(),table_name:String(t[1]||"").trim(),source_name:t[2]?String(t[2]).trim():void 0,pre_order_items:t[3]?String(t[3]).trim():void 0,description:t[4]?String(t[4]).trim():void 0};if(!x.id){C.error(`Dòng ${h+1}: ID không được để trống`);continue}if(!x.table_name){C.error(`Dòng ${h+1}: Tên bàn không được để trống`);continue}g.push(x)}if(g.length===0)throw C.error("Không tìm thấy dữ liệu hợp lệ trong file"),new Error("No valid data found");return g}catch(d){throw d instanceof Error&&d.message!=="No valid data found"&&C.error("Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file."),d}},downloadTablesData:async(o,d,m)=>{try{const p=await Z(()=>import("./xlsx-DkH2s96g.js"),[]),l=[["id","Tên bàn","Nguồn","Món đặt trước","Mô tả"],...d.map(x=>{var k,E;const S=m.find(D=>D.sourceId===x.source_id),_=(E=(k=x.extra_data)==null?void 0:k.order_list)!=null&&E.length?x.extra_data.order_list.map(D=>`${D.item_id}(${D.quantity})`).join(", "):"";return[x.id,x.table_name,(S==null?void 0:S.sourceName)||"",_,x.description||""]})],j=p.utils.book_new(),u=p.utils.aoa_to_sheet(l),g=[{wch:20},{wch:20},{wch:15},{wch:30},{wch:30}];u["!cols"]=g,p.utils.book_append_sheet(j,u,"Tables Data");const h=new Date().toISOString().slice(0,19).replace(/:/g,"-"),t=`tables_data_${o}_${h}.xlsx`;p.writeFile(j,t),C.success("Đã tải file dữ liệu bàn thành công!")}catch(p){throw C.error("Lỗi khi tải file dữ liệu bàn"),p}}}}function os({open:f,onOpenChange:i,onCancel:o,onSuccess:d}){const[m,p]=r.useState(null),[l,j]=r.useState([]),[u,g]=r.useState(1),h=r.useRef(null),[t,x]=r.useState(""),{parseExcelFile:S,downloadTablesData:_}=cs(),k=ne(),{company:E}=ue(),{selectedBrand:D}=pe(),v=r.useMemo(()=>{try{const a=localStorage.getItem("pos_stores_data");if(a){const N=JSON.parse(a);return Array.isArray(N)?N.filter(y=>y.active===1):[]}return[]}catch(a){return console.error("Error parsing pos_stores_data:",a),[]}},[]),{data:T=[]}=ee({storeUid:t}),{data:w=[]}=He({companyUid:E==null?void 0:E.id,brandUid:D==null?void 0:D.id,storeUid:t,skipLimit:!0});r.useMemo(()=>{v.length>0&&!t&&x(v[0].id)},[v,t]),r.useMemo(()=>{var a;f||(g(1),j([]),p(null),x(((a=v[0])==null?void 0:a.id)||""))},[f,v]);const M=async()=>{if(!t){C.error("Vui lòng chọn cửa hàng");return}try{await _(t,T,w)}catch{C.error("Lỗi khi tải dữ liệu bàn")}},O=()=>{var a;(a=h.current)==null||a.click()},$=async a=>{var y;const N=(y=a.target.files)==null?void 0:y[0];if(N){p(N);try{const F=await S(N);j(F),g(2),C.success(`Đã phân tích ${F.length} bàn từ file!`)}catch{}}},L=async()=>{if(u===2&&t&&l.length>0)try{const a=l.map(N=>{var V;const y=T.find(R=>R.id===N.id),F=w.find(R=>R.sourceName===N.source_name);if(!y)throw new Error(`Không tìm thấy bàn với ID "${N.id}" trong hệ thống.`);return{...y,table_name:N.table_name,source_id:(F==null?void 0:F.sourceId)||y.source_id||"",description:N.description||"",extra_data:{...y.extra_data,order_list:((V=y.extra_data)==null?void 0:V.order_list)||[]}}});await k.mutateAsync({storeUid:t,tables:a}),C.success(`Đã cập nhật thành công ${l.length} bàn!`),d()}catch(a){a instanceof Error?C.error(a.message):C.error("Lỗi khi cập nhật bàn. Vui lòng thử lại.")}};return e.jsxs(te,{title:"Sửa thông tin bàn",open:f,onOpenChange:i,onCancel:o,onConfirm:u===2?L:()=>{},confirmText:u===2?"Cập nhật":void 0,cancelText:"Đóng",centerTitle:!0,maxWidth:"sm:max-w-4xl",isLoading:k.isPending,hideButtons:u===1,confirmDisabled:u===2&&(!t||l.length===0),children:[e.jsxs("div",{className:"max-h-[70vh] space-y-6 overflow-y-auto",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Chọn cửa hàng, khu vực"}),e.jsxs(J,{value:t,onValueChange:x,children:[e.jsx(Q,{className:"w-full",children:e.jsx(W,{placeholder:"Chọn cửa hàng"})}),e.jsx(G,{children:v.map(a=>e.jsx(H,{value:a.id,children:a.store_name},a.id))})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu bàn đã có."}),e.jsxs(P,{variant:"outline",size:"sm",onClick:M,disabled:!t,className:"flex items-center gap-2",children:[e.jsx(je,{className:"h-4 w-4"}),"Tải xuống"]})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Không sửa các cột ",e.jsx("span",{className:"font-medium",children:"id"}),"."]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs(P,{variant:"outline",size:"sm",onClick:O,className:"flex items-center gap-2",children:[e.jsx(le,{className:"h-4 w-4"}),"Tải file lên"]})]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ ban bạn có thể tải file lên"}),m&&e.jsx("div",{className:"rounded-lg border bg-gray-50 p-3",children:e.jsxs("p",{className:"text-sm text-gray-700",children:["File đã chọn: ",e.jsx("span",{className:"font-medium",children:m.name})]})})]}),u===2&&l.length>0&&e.jsxs("div",{className:"space-y-4 border-t pt-4",children:[e.jsx("h4",{className:"text-md font-medium",children:"Xem trước dữ liệu cập nhật"}),e.jsx("div",{className:"h-64 overflow-y-auto rounded-lg border",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"sticky top-0 z-10 bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"ID"}),e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"Tên bàn"}),e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"Nguồn"}),e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"Món đặt trước"}),e.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium",children:"Mô tả"})]})}),e.jsx("tbody",{children:l.map((a,N)=>e.jsxs("tr",{className:"border-t hover:bg-gray-50",children:[e.jsx("td",{className:"px-3 py-2 text-sm",children:a.id}),e.jsx("td",{className:"px-3 py-2 text-sm",children:a.table_name}),e.jsx("td",{className:"px-3 py-2 text-sm",children:a.source_name||"-"}),e.jsx("td",{className:"px-3 py-2 text-sm",children:a.pre_order_items||"-"}),e.jsx("td",{className:"px-3 py-2 text-sm",children:a.description||"-"})]},N))})]})}),e.jsxs("div",{className:"text-center text-sm text-gray-600",children:["Tổng cộng: ",e.jsx("span",{className:"font-medium",children:l.length})," bàn sẽ được cập nhật"]})]})]}),e.jsx("input",{ref:h,type:"file",accept:".xlsx,.xls",onChange:$,className:"hidden"})]})}function ds({open:f,onOpenChange:i,onCancel:o,onSuccess:d}){const[m,p]=r.useState(""),[l,j]=r.useState(""),[u,g]=r.useState([]),[h,t]=r.useState(null),[x,S]=r.useState([]),[_,k]=r.useState(null),E=be(),{company:D}=ue(),{selectedBrand:v}=pe(),T=ne(),w=r.useMemo(()=>{try{const s=localStorage.getItem("pos_stores_data");if(s){const n=JSON.parse(s);return Array.isArray(n)?n.filter(c=>c.active===1):[]}return[]}catch{return[]}},[]),{data:M=[]}=se({storeUid:m,page:1,results_per_page:15e3}),{data:O=[]}=ee({storeUid:m});r.useEffect(()=>{f&&w.length>0&&!m&&p(w[0].id)},[f,w.length,m]),r.useEffect(()=>{M.length>0?S([...M].sort((s,n)=>s.sort-n.sort)):S([])},[M.length,M.map(s=>s.id).join(",")]),r.useEffect(()=>{if(l){const s=O.filter(n=>n.area_uid===l).sort((n,c)=>n.sort-c.sort);g(s)}else g([])},[l,O.length,O.map(s=>s.id).join(",")]),r.useEffect(()=>{if(!f){const s=setTimeout(()=>{p(""),j(""),g([]),S([]),k(null)},100);return()=>clearTimeout(s)}},[f]);const $=r.useCallback(s=>{p(s),j(""),g([])},[]),L=r.useCallback(s=>{j(s)},[]),a=r.useCallback((s,n)=>{k(n),s.dataTransfer.effectAllowed="move",s.dataTransfer.setData("text/html",n.toString())},[]),N=r.useCallback(s=>{s.preventDefault(),s.dataTransfer.dropEffect="move"},[]),y=r.useCallback((s,n)=>{if(s.preventDefault(),_===null||_===n){k(null);return}S(c=>{const b=Array.from(c),[A]=b.splice(_,1);return b.splice(n,0,A),b.map((z,B)=>({...z,sort:B}))}),k(null)},[_]),F=r.useCallback(()=>{k(null)},[]),V=r.useCallback((s,n)=>{t(n),s.dataTransfer.effectAllowed="move",s.dataTransfer.setData("text/html",n.toString())},[]),R=r.useCallback(s=>{s.preventDefault(),s.dataTransfer.dropEffect="move"},[]),q=r.useCallback((s,n)=>{if(s.preventDefault(),h===null||h===n){t(null);return}g(c=>{const b=Array.from(c),[A]=b.splice(h,1);return b.splice(n,0,A),b.map((z,B)=>({...z,sort:B+1}))}),t(null)},[h]),K=r.useCallback(()=>{t(null)},[]),X=async()=>{if(!m){C.error("Vui lòng chọn cửa hàng");return}const s=(D==null?void 0:D.id)||"",n=(v==null?void 0:v.id)||"";if(!s||!n){C.error("Thiếu thông tin công ty hoặc thương hiệu");return}try{if(x.length>0){const c=x.map(A=>({id:A.id,sort:A.sort,company_uid:s,brand_uid:n,store_uid:m}));console.log("Sending areas update data:",c);const b=await Ne.post("/pos/v1/area",c);if(console.log("Areas update response:",b),!b||b.status!==200)throw new Error("API response không thành công")}l&&u.length>0&&await T.mutateAsync({storeUid:m,tables:u}),await Promise.all([E.invalidateQueries({queryKey:[he.AREAS_LIST]}),E.invalidateQueries({queryKey:[he.TABLES_LIST]})]),await new Promise(c=>setTimeout(c,500)),C.success("Sắp xếp thành công!"),d()}catch(c){console.error("Error saving arrangement:",c),C.error("Lỗi khi sắp xếp. Vui lòng thử lại.")}},I=M.find(s=>s.id===l);return e.jsx(te,{title:"Sắp xếp bàn",open:f,onOpenChange:i,onCancel:o,onConfirm:X,confirmText:"Lưu",cancelText:"Đóng",centerTitle:!0,maxWidth:"sm:max-w-6xl",isLoading:T.isPending,confirmDisabled:!m||x.length===0,children:e.jsxs("div",{className:"space-y-4 max-h-[70vh] overflow-hidden",children:[e.jsx("div",{className:"space-y-2",children:e.jsxs(J,{value:m,onValueChange:$,children:[e.jsx(Q,{className:"w-full",children:e.jsx(W,{placeholder:"Chọn điểm áp dụng"})}),e.jsx(G,{children:w.map(s=>e.jsx(H,{value:s.id,children:s.store_name},s.id))})]})}),m&&e.jsxs("div",{className:"flex gap-4 h-[500px]",children:[e.jsxs("div",{className:"w-1/3 border rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 p-3 border-b",children:e.jsx("h4",{className:"font-medium text-sm",children:"Khu vực"})}),e.jsx("div",{className:"overflow-y-auto h-full",children:x.length===0?e.jsx("div",{className:"p-4 text-center text-gray-500 text-sm",children:"Vui lòng chọn cửa hàng"}):e.jsx("div",{className:"space-y-1 p-2",children:x.map((s,n)=>{const c=O.filter(z=>z.area_uid===s.id).length,b=_===n,A=l===s.id;return e.jsxs("div",{draggable:!0,onDragStart:z=>a(z,n),onDragOver:N,onDrop:z=>y(z,n),onDragEnd:F,onClick:()=>L(s.id),className:`w-full text-left p-3 rounded-md text-sm transition-all duration-200 flex items-center justify-between cursor-move ${b?"opacity-50 shadow-lg scale-105":A?"bg-blue-50 text-blue-700 border border-blue-200 shadow-md":"hover:bg-gray-50 hover:shadow-md border border-transparent"}`,children:[e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(qe,{className:"h-4 w-4 text-gray-400 flex-shrink-0"}),e.jsx("span",{className:"text-gray-400",children:"🏢"}),e.jsx("span",{className:"truncate",children:s.area_name})]}),e.jsx("div",{className:"flex items-center gap-2",children:c>0&&e.jsx("span",{className:"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full",children:c})})]},s.id)})})})]}),e.jsxs("div",{className:"flex-1 border rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 p-3 border-b",children:e.jsx("h4",{className:"font-medium text-sm",children:I?I.area_name:"Chọn khu vực"})}),e.jsx("div",{className:"p-4 overflow-y-auto h-full",children:l?u.length===0?e.jsx("div",{className:"flex items-center justify-center h-full text-gray-500 text-sm",children:"Không có bàn trong khu vực"}):e.jsx("div",{className:"grid grid-cols-4 gap-4 p-4",children:u.map((s,n)=>{var z;const c=((z=s.extra_data)==null?void 0:z.color)||"#3B82F6",A=(B=>{const U=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(B);return U?{r:parseInt(U[1],16),g:parseInt(U[2],16),b:parseInt(U[3],16)}:{r:59,g:130,b:246}})(c);return e.jsxs("div",{draggable:!0,onDragStart:B=>V(B,n),onDragOver:R,onDrop:B=>q(B,n),onDragEnd:K,className:`
                            relative cursor-move
                            ${h===n?"scale-105 opacity-70 z-10 rotate-2":""}
                          `,children:[e.jsxs("div",{className:`
                            relative bg-white border-2 rounded-xl shadow-md
                            overflow-hidden h-32 w-full
                            ${h===n?"shadow-blue-200":"border-gray-200"}
                          `,style:{borderColor:h===n?c:void 0},children:[e.jsx("div",{className:"absolute top-0 left-0 w-full h-1",style:{backgroundColor:c}}),e.jsx("div",{className:"p-4 h-full flex flex-col justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-800 mb-2",children:s.table_name}),e.jsxs("div",{className:"flex items-center justify-center gap-1 text-sm text-gray-500 mb-3",children:[e.jsx("span",{className:"w-2 h-2 rounded-full",style:{backgroundColor:c}}),e.jsxs("span",{children:["Thứ tự: ",n+1]})]}),e.jsxs("div",{className:"flex items-center justify-center gap-1.5 text-[10px] rounded-lg py-1.5 px-2",style:{color:c,backgroundColor:`rgba(${A.r}, ${A.g}, ${A.b}, 0.1)`},children:[e.jsx("svg",{className:"w-2.5 h-2.5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M7 7h10v3l4-4-4-4v3H5v6h2V7zm10 10H7v-3l-4 4 4 4v-3h12v-6h-2v4z"})}),e.jsx("span",{className:"font-medium whitespace-nowrap",children:"Kéo để sắp xếp"})]})]})})]}),e.jsx("div",{className:`
                            absolute -bottom-1 left-1/2 transform -translate-x-1/2
                            w-20 h-2 bg-gray-300 rounded-full opacity-20 blur-sm
                            ${h===n?"scale-110 opacity-40":""}
                          `})]},s.id)})}):e.jsx("div",{className:"flex items-center justify-center h-full text-gray-500 text-sm",children:"Vui lòng chọn khu vực"})})]})]})]})})}const re=["#1E40AF","#EA580C","#C026D3","#FCD34D","#6B7280","#000000","#FF4500","#32CD32","#00BFFF","#4169E1","#FF1493","#8B4513","#FFB6C1","#DDA0DD","#CD5C5C","#A0522D","#8B0000","#2F4F4F"],ms=({open:f,onOpenChange:i,onCancel:o})=>{var I;const d=()=>{p(""),j(""),g(""),t(new Set),S(!1),D(!1),T(!1),k({color:"#1E40AF",fontSize:"15"}),i(!1),o()},[m,p]=r.useState(""),[l,j]=r.useState(""),[u,g]=r.useState(""),[h,t]=r.useState(new Set),[x,S]=r.useState(!1),[_,k]=r.useState({color:"#1E40AF",fontSize:"15"}),[E,D]=r.useState(!1),[v,T]=r.useState(!1),w=r.useMemo(()=>{try{const s=localStorage.getItem("pos_stores_data");if(s){const n=JSON.parse(s);return Array.isArray(n)?n.filter(c=>c.active===1):[]}return[]}catch{return[]}},[]),{data:M=[]}=se({storeUid:m,page:1,results_per_page:1e3}),{data:O=[],isLoading:$}=ee({storeUid:m,page:1,limit:1e3});r.useEffect(()=>{M.length>0&&!l&&m&&j(M[0].id)},[M,l,m]);const{mutateAsync:L,isPending:a}=ne(),N=r.useMemo(()=>u?O.filter(s=>s.table_name.toLowerCase().includes(u.toLowerCase())):O,[O,u]),y=r.useMemo(()=>{const s={};return(l?N.filter(c=>c.area_uid===l):N).forEach(c=>{var A;const b=((A=c.area)==null?void 0:A.area_name)||"Không có khu vực";s[b]||(s[b]=[]),s[b].push(c)}),s},[N,l]),F=s=>{p(s),j(""),t(new Set),g(""),D(!1),T(!1)},V=s=>{const n=new Set(h);n.has(s)?n.delete(s):n.add(s),t(n)},R=s=>{var n,c;if(D(s),s&&l){const b=(n=M.find(A=>A.id===l))==null?void 0:n.area_name;if(b&&y[b]){const A=y[b].map(z=>z.id);t(new Set([...h,...A]))}}else if(!s&&l){const b=(c=M.find(A=>A.id===l))==null?void 0:c.area_name;if(b&&y[b]){const A=new Set(y[b].map(B=>B.id)),z=new Set([...h].filter(B=>!A.has(B)));t(z)}}},q=s=>{if(T(s),s){const n=O.map(c=>c.id);t(new Set(n))}else t(new Set)},K=s=>{k(n=>({...n,color:s})),S(!1)},X=async()=>{if(h.size!==0)try{const n=O.filter(c=>h.has(c.id)).map(c=>{var b;return{...c,store_uid:c.store_uid||m,company_uid:c.company_uid||"",brand_uid:c.brand_uid||"",source_id:c.source_id||"",area_uid:c.area_uid||"",sort:c.sort||1,description:c.description||"",extra_data:{...c.extra_data,color:_.color,font_size:_.fontSize,order_list:((b=c.extra_data)==null?void 0:b.order_list)||[]}}});await L({storeUid:m,tables:n}),d()}catch(s){console.error("Error updating tables:",s)}};return e.jsx(te,{title:"",open:f,onOpenChange:i,onCancel:d,onConfirm:()=>{},confirmText:"Lưu",cancelText:"Đóng",centerTitle:!0,maxWidth:"sm:max-w-7xl",isLoading:!1,confirmDisabled:!1,hideButtons:!0,children:e.jsxs("div",{className:"flex flex-col h-[85vh]",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Cấu hình bàn"}),e.jsx(P,{variant:"ghost",size:"sm",onClick:d,children:e.jsx(Xe,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"flex items-center gap-6 p-6 border-b bg-gray-50",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium whitespace-nowrap",children:"Chọn điểm áp dụng"}),e.jsxs("select",{value:m,onChange:s=>F(s.target.value),className:"px-3 py-2 border rounded-md bg-white min-w-[200px]",children:[e.jsx("option",{value:"",children:"Chọn cửa hàng"}),w.map(s=>e.jsx("option",{value:s.id,children:s.store_name},s.id))]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Màu chữ"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"w-8 h-8 rounded border cursor-pointer",style:{backgroundColor:_.color},onClick:()=>S(!x)}),x&&e.jsxs("div",{className:"absolute top-10 left-0 z-50 bg-white border rounded-lg shadow-lg p-4 w-80",children:[e.jsx("h3",{className:"text-sm font-medium mb-3",children:"Chọn ảnh hoặc màu"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-xs text-gray-600 mb-2",children:"Hoặc chọn màu dưới đây"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"grid grid-cols-6 gap-2",children:re.slice(0,6).map(s=>e.jsx("div",{className:`w-8 h-8 rounded cursor-pointer border-2 ${_.color===s?"border-blue-500":"border-gray-300"}`,style:{backgroundColor:s},onClick:()=>K(s)},s))}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:re.slice(6,12).map(s=>e.jsx("div",{className:`w-8 h-8 rounded cursor-pointer border-2 ${_.color===s?"border-blue-500":"border-gray-300"}`,style:{backgroundColor:s},onClick:()=>K(s)},s))}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:re.slice(12,18).map(s=>e.jsx("div",{className:`w-8 h-8 rounded cursor-pointer border-2 ${_.color===s?"border-blue-500":"border-gray-300"}`,style:{backgroundColor:s},onClick:()=>K(s)},s))})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(P,{variant:"outline",size:"sm",onClick:()=>S(!1),children:"Hủy"}),e.jsx(P,{size:"sm",onClick:()=>S(!1),children:"Xong"})]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Kích thước chữ (pixels)"}),e.jsx("input",{type:"number",min:"8",max:"50",value:_.fontSize,onChange:s=>k(n=>({...n,fontSize:s.target.value})),className:"w-20 px-2 py-1 border rounded-md text-center",placeholder:"15"}),e.jsx("span",{className:"text-sm text-gray-500",children:"px"})]})]}),e.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[e.jsx("div",{className:"w-80 border-r bg-gray-50 overflow-y-auto",children:$?e.jsx("div",{className:"p-4 text-center text-gray-500",children:"Đang tải dữ liệu bàn..."}):M.map(s=>{const n=O.filter(c=>c.area_uid===s.id).length;return e.jsx("div",{className:`p-3 border-b cursor-pointer hover:bg-gray-100 ${l===s.id?"bg-blue-50 border-blue-200":""}`,onClick:()=>{j(s.id),D(!1),g("")},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm",children:"📍"}),e.jsx("span",{className:"text-sm font-medium",children:s.area_name})]}),e.jsxs("span",{className:"text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded",children:[n," bàn"]})]})},s.id)})}),e.jsx("div",{className:"flex-1 flex flex-col",children:m?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"p-4 border-b space-y-3",children:[l&&e.jsxs("div",{className:"bg-gray-50 p-3 rounded-md",children:[e.jsx("h3",{className:"font-medium text-gray-900",children:((I=M.find(s=>s.id===l))==null?void 0:I.area_name)||"Khu vực"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Vui lòng chọn bàn"})]}),e.jsxs("div",{className:"flex items-center gap-4 flex-wrap",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:E,onChange:s=>R(s.target.checked),disabled:!l}),e.jsx("span",{className:"text-sm whitespace-nowrap",children:"Chọn tất cả bàn trong khu vực"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:v,onChange:s=>q(s.target.checked)}),e.jsx("span",{className:"text-sm whitespace-nowrap",children:"Áp dụng với toàn bộ bàn tại cửa hàng"})]}),e.jsxs("div",{className:"flex-1 relative min-w-[200px]",children:[e.jsx(Je,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Tìm kiếm tên bàn",value:u,onChange:s=>g(s.target.value),className:"w-full pl-10 pr-4 py-2 border rounded-md"})]})]})]}),e.jsx("div",{className:"flex-1 overflow-y-auto p-4",children:Object.keys(y).length===0?e.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:l?"Không có bàn nào trong khu vực này":"Vui lòng chọn khu vực để xem danh sách bàn"}):Object.entries(y).map(([s,n])=>e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3",children:s}),e.jsx("div",{className:"grid grid-cols-4 gap-4",children:n.map(c=>{var ie,ce;const b=h.has(c.id),A=((ie=c.extra_data)==null?void 0:ie.color)||"#6B7280",z=((ce=c.extra_data)==null?void 0:ce.font_size)||"15",B=b?_.color:A,U=b?_.fontSize:z;return e.jsxs("div",{className:`
                                  relative cursor-pointer rounded-lg transition-all shadow-sm border overflow-hidden
                                  ${b?"border-blue-500 bg-blue-50 shadow-md":"border-gray-200 bg-white hover:border-gray-300 hover:shadow-md"}
                                `,onClick:()=>V(c.id),children:[e.jsx("div",{className:"h-1 w-full",style:{backgroundColor:B}}),e.jsx("div",{className:"p-4 space-y-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"font-semibold text-lg transition-all",style:{color:B,fontSize:`${U}px`},children:c.table_name}),b&&e.jsx("div",{className:"w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white text-xs font-bold",children:"✓"})})]})})]},c.id)})})]},s))})]}):e.jsx("div",{className:"flex-1 flex items-center justify-center text-gray-500",children:"Vui lòng chọn cửa hàng"})})]}),e.jsxs("div",{className:"flex justify-end gap-3 p-6 border-t",children:[e.jsx(P,{variant:"outline",onClick:d,disabled:a,children:"Đóng"}),e.jsx(P,{onClick:X,disabled:h.size===0||a,children:a?"Đang lưu...":"Lưu"})]})]})})},yt=function(){return e.jsx(Ye,{})};export{yt as component};
