import{u as p}from"./useQuery-HgcIHxlE.js";import{u as l,a3 as i}from"./index-CVQ6JZo2.js";import{u as c}from"./useMutation-ZsyDznMu.js";import{a as s}from"./pos-api-mRg02iop.js";import{Q as r}from"./query-keys-3lmd-xp6.js";class m{async getRoles(e={}){return(await s.get("/accounts/v1/roles",{params:e})).data.data}async getRoleById(e){return(await s.get(`/accounts/v1/role?role_uid=${e}`)).data.data}async createRole(e){return(await s.post("/accounts/v1/roles",e)).data.data}async updateRole(e){return(await s.post("/accounts/v1/roles",e)).data.data}async deleteRole(e){await s.delete("/accounts/v1/role",{data:{id:e.id,company_uid:e.company_uid,brand_uid:e.brand_uid}})}async bulkDeleteRoles(e){await s.delete("/accounts/v1/roles",{data:{list_role_uid:e.ids,company_uid:e.company_uid,brand_uid:e.brand_uid}})}async copyRole(e,n){return(await s.post(`/accounts/v1/roles/${e}/copy`,{role_name:n})).data.data}}const u=new m;function v(t={}){const{enabled:e=!0,...n}=t,{company:o,brands:a}=l(R=>R.auth),d=a==null?void 0:a[0],y={company_uid:o==null?void 0:o.id,brand_uid:d==null?void 0:d.id,...n};return p({queryKey:[r.ROLES,y],queryFn:()=>u.getRoles(y),enabled:e,staleTime:5*60*1e3,gcTime:10*60*1e3})}function w(t,e=!0){return p({queryKey:[r.ROLES_DETAIL,t],queryFn:()=>u.getRoleById(t),enabled:e&&!!t,staleTime:5*60*1e3,gcTime:10*60*1e3})}function b(){const t=i();return c({mutationFn:e=>u.createRole(e),onSuccess:()=>{t.invalidateQueries({queryKey:[r.ROLES]})}})}function g(){const t=i();return c({mutationFn:e=>u.updateRole(e),onSuccess:()=>{t.invalidateQueries({queryKey:[r.ROLES]}),t.invalidateQueries({queryKey:[r.ROLES_DETAIL]})}})}function C(){const t=i(),{company:e,brands:n}=l(a=>a.auth),o=n==null?void 0:n[0];return c({mutationFn:a=>{if(!(e!=null&&e.id)||!(o!=null&&o.id))throw new Error("Company or brand information is missing");return u.deleteRole({id:a,company_uid:e.id,brand_uid:o.id})},onSuccess:()=>{t.invalidateQueries({queryKey:[r.ROLES]})}})}function L(){const t=i(),{company:e,brands:n}=l(a=>a.auth),o=n==null?void 0:n[0];return c({mutationFn:a=>{if(!(e!=null&&e.id)||!(o!=null&&o.id))throw new Error("Company or brand information is missing");return u.bulkDeleteRoles({ids:a,company_uid:e.id,brand_uid:o.id})},onSuccess:()=>{t.invalidateQueries({queryKey:[r.ROLES]})}})}export{C as a,L as b,w as c,b as d,g as e,v as u};
