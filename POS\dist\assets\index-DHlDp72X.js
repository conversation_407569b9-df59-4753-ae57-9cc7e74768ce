import{r as f,a as z,l as O,b as V,j as e,a4 as F,h as G,B as M}from"./index-CfbMU4Ye.js";import"./pos-api-BBB_ZiZD.js";import"./vietqr-api-BHQxfNzq.js";import{u as H}from"./use-sales-channels-xiPEaovV.js";import{u as K,a as R,b as X}from"./discount-form-context-DMuqloki.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import{H as W}from"./header-CiiJInbE.js";import{M as q}from"./main-B69tr6A0.js";import{P as J}from"./profile-dropdown-HjZ6UGjk.js";import{S as Q,T as Y}from"./search-Bbt2JnTN.js";import{u as Z,a as B}from"./use-discount-programs-MPHI6KML.js";import{P as ee}from"./modal-D_ZqQrH_.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{S,a as y,b,c as D,d as g,C as te}from"./select-_nXsh5SU.js";import{C as P}from"./checkbox-CSFn543p.js";import{C as se}from"./index-TKFSyVOw.js";import{B as $}from"./badge-DNJz5hg4.js";import{T as ae}from"./trash-2-BgCVQZay.js";import{D as ie,a as ne,b as le,c as oe}from"./dropdown-menu-8bnotEGr.js";import"./useQuery-BvDWg4vp.js";import"./utils-km2FGkQ4.js";import"./useMutation-C9PewMvL.js";import"./date-utils-DBbLjCz0.js";import"./query-keys-3lmd-xp6.js";import"./separator-DVvwOaSX.js";import"./avatar-CE3yFgmj.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-FztlF_ds.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./scroll-area-Bx6sgJqp.js";import"./index-D41EikqA.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";import"./error-utils-BYcz3jZ5.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./index-4DjKSQeL.js";function re({isOpen:x,onClose:C}){const[r,N]=f.useState(""),[o,j]=f.useState(new Set),[m,u]=f.useState(new Set),{currentBrandStores:v}=z(),{selectedBrand:d}=O(),{company:c}=V(),n=Z(),{data:i,isLoading:_}=B({companyUid:c==null?void 0:c.id,brandUid:d==null?void 0:d.id,storeUid:r,enabled:!!r}),a=(i==null?void 0:i.data)||[],p=v.filter(t=>t.id!==r),h=t=>{const l=new Set(m);l.has(t)?l.delete(t):l.add(t),u(l)},T=t=>{const l=new Set(o);l.has(t)?l.delete(t):l.add(t),j(l)},U=()=>{o.size===p.length?j(new Set):j(new Set(p.map(t=>t.id)))},A=async()=>{if(!(!(c!=null&&c.id)||!(d!=null&&d.id)||!r||o.size===0||m.size===0))try{const t=Array.from(m),l=Array.from(o);for(const I of l)await n.mutateAsync({companyUid:c.id,brandUid:d.id,listDiscountUid:t,sourceStore:r,targetStore:I});F.success("Sao chép thành công"),w()}catch(t){console.error("Error copying discount programs:",t)}},w=()=>{N(""),j(new Set),u(new Set),C()},k=a.filter(t=>m.has(t.id));return e.jsx(ee,{title:"Sao chép chương trình giảm giá",open:x,onOpenChange:w,onCancel:w,onConfirm:A,confirmText:"Sao chép",confirmDisabled:!r||o.size===0||m.size===0||n.isPending,maxWidth:"sm:max-w-6xl",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng nguồn"}),e.jsxs(S,{value:r,onValueChange:N,children:[e.jsx(y,{className:"mt-1",children:e.jsx(b,{placeholder:"Chọn cửa hàng nguồn"})}),e.jsx(D,{children:v.map(t=>e.jsx(g,{value:t.id,children:t.store_name},t.id))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chương trình giảm giá"}),e.jsx("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:_?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Đang tải..."}):a.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:r?"Không có chương trình giảm giá":"Chọn cửa hàng nguồn"}):e.jsx("div",{className:"space-y-3",children:a.map(t=>{var l;return e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(P,{id:t.id,checked:m.has(t.id),onCheckedChange:()=>h(t.id)}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-sm font-medium",children:((l=t.promotion)==null?void 0:l.promotion_name)||"N/A"}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Giảm"," ",t.discount_type==="AMOUNT"?`${(t.ta_discount||t.ots_discount||0).toLocaleString("vi-VN")} ₫`:`${t.ta_discount||t.ots_discount||0}%`]})]})]},t.id)})})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng đích"}),e.jsxs(S,{children:[e.jsx(y,{className:"mt-1",children:e.jsx(b,{placeholder:o.size===0?"Chọn cửa hàng đích":`Đã chọn ${o.size} cửa hàng`})}),e.jsx(D,{children:p.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chọn cửa hàng nguồn trước"}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-3 border-b px-2 py-1.5",children:[e.jsx(P,{id:"select-all-targets",checked:o.size===p.length&&p.length>0,onCheckedChange:U,onClick:t=>t.stopPropagation()}),e.jsx("label",{htmlFor:"select-all-targets",className:"flex-1 cursor-pointer text-sm font-medium",onClick:t=>{t.preventDefault(),U()},children:"Chọn tất cả"})]}),p.map(t=>e.jsxs("div",{className:"flex items-center space-x-3 px-2 py-1.5",children:[e.jsx(P,{id:`target-${t.id}`,checked:o.has(t.id),onCheckedChange:()=>T(t.id),onClick:l=>l.stopPropagation()}),e.jsx("label",{htmlFor:`target-${t.id}`,className:"flex-1 cursor-pointer text-sm font-medium",onClick:l=>{l.preventDefault(),T(t.id)},children:t.store_name})]},t.id))]})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chương trình được chọn"}),e.jsx("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:k.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chưa chọn chương trình nào"}):e.jsx("div",{className:"space-y-2",children:k.map(t=>{var l;return e.jsxs("div",{className:"bg-muted rounded-md p-2",children:[e.jsx("div",{className:"text-sm font-medium",children:((l=t.promotion)==null?void 0:l.promotion_name)||"N/A"}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:["Giảm"," ",t.discount_type==="AMOUNT"?`${(t.ta_discount||t.ots_discount||0).toLocaleString("vi-VN")} ₫`:`${((t.ta_discount||t.ots_discount||0)*100).toFixed(0)}%`]})]},t.id)})})})]})]})]})})}function de({discounts:x,isLoading:C,onToggleActive:r,onDeleteDiscount:N,isDeleting:o}){const j=G(),[m,u]=f.useState(!1),[v,d]=f.useState(null),c=a=>{d(a),u(!0)},n=()=>{v&&(N(v),u(!1),d(null))},i=()=>{u(!1),d(null)},_=(a,p)=>{const h=p.target;h.closest("button")||h.closest('[role="button"]')||h.closest(".badge")||h.tagName==="BUTTON"||(console.log("Navigating to discount detail:",a.id),j({to:"/sale-channel/discount/detail/$id",params:{id:a.id},search:{store_uid:a.storeUid}}))};return e.jsxs("div",{className:"rounded-md border",children:[e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-muted/50 border-b",children:[e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"#"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Kênh"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Cửa hàng"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Tuỳ chỉnh"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Giảm giá"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium",children:"Thao tác"}),e.jsx("th",{className:"text-muted-foreground h-12 px-4 text-left align-middle font-medium"})]})}),e.jsx("tbody",{children:C?e.jsx("tr",{children:e.jsx("td",{colSpan:7,className:"h-24 text-center",children:"Đang tải dữ liệu..."})}):x.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:7,className:"text-muted-foreground h-24 text-center",children:"Không có dữ liệu"})}):x.map((a,p)=>e.jsxs("tr",{className:"hover:bg-muted/50 cursor-pointer border-b",onClick:h=>_(a,h),children:[e.jsx("td",{className:"px-4 py-3",children:p+1}),e.jsx("td",{className:"px-4 py-3",children:a.source.sourceName}),e.jsx("td",{className:"px-4 py-3",children:a.storeName}),e.jsx("td",{className:"px-4 py-3",children:e.jsxs("div",{className:"space-y-1",children:[a.toDate<Date.now()?e.jsx("div",{className:"font-medium text-red-600",children:"Hết hạn"}):e.jsxs("div",{className:"text-sm",children:["Từ ",new Date(a.fromDate).toLocaleDateString("vi-VN")," đến"," ",new Date(a.toDate).toLocaleDateString("vi-VN")]}),e.jsxs("div",{className:"text-muted-foreground text-xs",children:[a.isAll===1&&"(Áp dụng cho tất cả)",a.isItem===1&&a.itemId&&`(Áp dụng cho ${a.itemId.split(",").length} món)`,a.isType===1&&a.typeId&&`(Áp dụng cho ${a.typeId.split(",").length} loại món)`,a.isCombo===1&&a.comboId&&`(Áp dụng cho ${a.comboId.split(",").length} combo)`]})]})}),e.jsx("td",{className:"px-4 py-3",children:a.discountType==="AMOUNT"?`${a.taDiscount.toLocaleString("vi-VN")}đ`:`${(a.taDiscount*100).toFixed(0)}%`}),e.jsx("td",{className:"px-4 py-3",children:a.active===1?e.jsx($,{variant:"default",className:"cursor-pointer bg-green-100 text-green-800 hover:bg-green-200",onClick:h=>{h.stopPropagation(),r(a)},children:"Active"}):e.jsx($,{variant:"destructive",className:"cursor-pointer bg-red-100 text-red-800 hover:bg-red-200",onClick:h=>{h.stopPropagation(),r(a)},children:"Deactive"})}),e.jsx("td",{className:"px-4 py-3",children:e.jsx(M,{variant:"ghost",size:"sm",onClick:()=>c(a.id),disabled:o,children:e.jsx(ae,{className:"h-4 w-4"})})})]},a.id))})]}),e.jsx(se,{open:m,onOpenChange:u,title:"Xác nhận xóa",content:"Bạn có chắc chắn muốn xóa chương trình giảm giá này? Hành động này không thể hoàn tác.",onConfirm:n,onCancel:i,confirmText:"Xóa",cancelText:"Hủy",isLoading:o})]})}function ce({title:x,selectedStoreId:C,selectedChannelId:r,selectedStatus:N,selectedExpiry:o,stores:j,salesChannels:m,onStoreChange:u,onChannelChange:v,onStatusChange:d,onExpiryChange:c,onCopyDiscount:n}){const i=G(),_=()=>{i({to:"/sale-channel/discount/detail"})};return e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold whitespace-nowrap",children:x}),e.jsx("div",{className:"min-w-[160px]",children:e.jsxs(S,{value:C,onValueChange:u,children:[e.jsx(y,{children:e.jsx(b,{placeholder:"Tất cả các điểm"})}),e.jsxs(D,{children:[e.jsx(g,{value:"all",children:"Tất cả các điểm"}),j.map(a=>e.jsx(g,{value:a.id,children:a.store_name},a.id))]})]})}),e.jsx("div",{className:"min-w-[160px]",children:e.jsxs(S,{value:r,onValueChange:v,children:[e.jsx(y,{children:e.jsx(b,{placeholder:"Tất cả kênh bán hàng"})}),e.jsxs(D,{children:[e.jsx(g,{value:"all",children:"Tất cả kênh bán hàng"}),m.map(a=>e.jsx(g,{value:a.id,children:a.sourceName},a.id))]})]})}),e.jsx("div",{className:"min-w-[130px]",children:e.jsxs(S,{value:N,onValueChange:d,children:[e.jsx(y,{children:e.jsx(b,{placeholder:"Tất cả trạng thái"})}),e.jsxs(D,{children:[e.jsx(g,{value:"all",children:"Tất cả trạng thái"}),e.jsx(g,{value:"1",children:"Active"}),e.jsx(g,{value:"0",children:"Deactive"})]})]})}),e.jsx("div",{className:"min-w-[150px]",children:e.jsxs(S,{value:o,onValueChange:c,children:[e.jsx(y,{children:e.jsx(b,{placeholder:"Tất cả ngày áp dụng"})}),e.jsxs(D,{children:[e.jsx(g,{value:"all",children:"Tất cả ngày áp dụng"}),e.jsx(g,{value:"expired",children:"Hết hạn"}),e.jsx(g,{value:"unexpired",children:"Chưa hết hạn"})]})]})}),e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsxs(ie,{children:[e.jsx(ne,{asChild:!0,children:e.jsxs(M,{variant:"outline",size:"sm",children:["Tiện ích ",e.jsx(te,{className:"ml-2 h-4 w-4"})]})}),e.jsx(le,{children:e.jsx(oe,{onClick:n,children:"Sao chép CTGG"})})]}),e.jsx(M,{size:"sm",onClick:_,children:"Tạo giảm giá"})]})]})}function me(){var L;const[x,C]=f.useState("all"),[r,N]=f.useState("all"),[o,j]=f.useState("all"),[m,u]=f.useState("all"),[v,d]=f.useState(!1),{currentBrandStores:c}=z(),{selectedBrand:n}=O(),{company:i}=V(),_=K(),a=R(),p=x==="all"?c.map(s=>s.id):[x],h=x==="all"?(L=c[0])==null?void 0:L.id:x,{data:T=[],isLoading:U}=H({companyUid:i==null?void 0:i.id,brandUid:n==null?void 0:n.id,storeUid:h,partnerConfig:1,skipLimit:!0}),{data:A=[],isLoading:w}=X({companyUid:i==null?void 0:i.id,brandUid:n==null?void 0:n.id,page:1,listStoreUid:p,promotionPartnerAutoGen:1,status:o==="0"?"unexpired":m==="all"?void 0:m,active:o==="all"?void 0:parseInt(o)}),k=r==="all"?A:A.filter(s=>s.sourceUid===r),t=U||w,l=s=>{!(i!=null&&i.id)||!(n!=null&&n.id)||_.mutate({companyUid:i.id,brandUid:n.id,id:s})},I=s=>{const E={id:s.id,created_at:s.createdAt,created_by:s.createdBy,updated_at:s.updatedAt,updated_by:s.updatedBy,deleted:s.deleted||!1,deleted_at:s.deletedAt||null,deleted_by:s.deletedBy||null,ta_discount:s.taDiscount,ots_discount:s.otsDiscount,is_all:s.isAll,is_type:s.isType,is_item:s.isItem,type_id:s.typeId,item_id:s.itemId,discount_type:s.discountType,from_date:s.fromDate,to_date:s.toDate,time_sale_hour_day:0,time_sale_date_week:0,description:null,extra_data:{combo_id:"",is_combo:0},active:s.active===1?0:1,revision:null,promotion_uid:s.promotionUid,brand_uid:(n==null?void 0:n.id)||"",company_uid:(i==null?void 0:i.id)||"",sort:1e3,store_uid:s.storeUid,discount_clone_id:null,source_uid:s.sourceUid,promotion:{id:s.promotionUid,sort:1e3,active:1,deleted:!1,is_fabi:1,revision:0,brand_uid:(n==null?void 0:n.id)||"",store_uid:s.storeUid,created_at:s.createdAt||Date.now(),created_by:"system",deleted_at:null,deleted_by:null,extra_data:{},source_uid:s.sourceUid,updated_at:s.updatedAt||Date.now(),updated_by:"system",company_uid:(i==null?void 0:i.id)||"",description:null,promotion_id:s.promotionId,promotion_name:s.promotionName,partner_auto_gen:s.partnerAutoGen},promotion_id:s.promotionId,partner_auto_gen:s.partnerAutoGen,store_name:s.storeName,source:{id:s.source.id,sort:1e3,is_fb:0,active:s.source.active,deleted:!1,is_fabi:1,revision:null,brand_uid:(n==null?void 0:n.id)||"",source_id:s.source.sourceId,store_uid:s.storeUid,created_at:s.createdAt||Date.now(),created_by:"system",deleted_at:null,deleted_by:null,extra_data:{},updated_at:s.updatedAt||Date.now(),updated_by:"system",company_uid:(i==null?void 0:i.id)||"",description:null,source_name:s.source.sourceName,source_type:s.source.sourceType,partner_config:1}};a.mutate(E)};return e.jsxs(e.Fragment,{children:[e.jsx(W,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Q,{}),e.jsx(Y,{}),e.jsx(J,{})]})}),e.jsx(q,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(ce,{title:"Chương trình giảm giá theo kênh",selectedStoreId:x,selectedChannelId:r,selectedStatus:o,selectedExpiry:m,stores:c,salesChannels:T,onStoreChange:C,onChannelChange:N,onStatusChange:j,onExpiryChange:u,onCopyDiscount:()=>d(!0)}),e.jsx(de,{discounts:k,isLoading:t,onToggleActive:I,onDeleteDiscount:l,isDeleting:_.isPending})]}),e.jsx(re,{isOpen:v,onClose:()=>d(!1)})]})})]})}const lt=me;export{lt as component};
