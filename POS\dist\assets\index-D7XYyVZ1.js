import{aR as m,j as i}from"./index-UcdZ5AHH.js";import"./pos-api-j20LMGrC.js";import"./vietqr-api-9FERZtmQ.js";import"./user-9ajIul7r.js";import"./crm-api-APQEjHWd.js";import"./header-CE1GZ327.js";import"./main-C1Ukb9JX.js";import"./search-context-DK2BgvuK.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{C as p}from"./create-table-form-r3KGETQQ.js";import"./separator-C5UQ7YqK.js";import"./command-DJT46NtT.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DmI079wB.js";import"./search-B6f_4BGP.js";import"./createReactComponent-C1S2Ujit.js";import"./scroll-area-DQUG4R9C.js";import"./index-MuNXZ_zP.js";import"./select-DOexGcsG.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";import"./IconChevronRight-CnyriCST.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./use-areas-DvC-z67k.js";import"./useQuery-B4yhTgGk.js";import"./utils-km2FGkQ4.js";import"./useMutation-q12VR5WX.js";import"./images-api-BMYin8XI.js";import"./query-keys-3lmd-xp6.js";import"./use-sales-channels-BiPb6QJ7.js";import"./use-tables--5pkDgd-.js";import"./input-CBpgGfUv.js";import"./checkbox-CDB9_T0n.js";import"./collapsible-Dz-Iaa-P.js";import"./use-items-in-store-data-CPIap05T.js";import"./use-item-types-Ba660Fo2.js";import"./use-item-classes-K5Si5Xyw.js";import"./use-units-DpGqz_uu.js";import"./use-removed-items-B2DPNXQs.js";import"./items-in-store-api-B-rwjDQy.js";import"./xlsx-DkH2s96g.js";import"./copy-CvzdUB3u.js";import"./plus-ioKLfxbk.js";import"./minus-D0JoVnU5.js";const mo=function(){const{store_uid:o,area_uid:t,tableLayout:r}=m.useSearch();return i.jsx(p,{storeUid:o,areaId:t,fromTableLayout:r})};export{mo as component};
