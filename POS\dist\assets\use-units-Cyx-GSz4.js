import{u as g}from"./useQuery-BvDWg4vp.js";import{b as l}from"./pos-api-BBB_ZiZD.js";import{Q as p}from"./query-keys-3lmd-xp6.js";const a=new Map,r=new Map,y=30*60*1e3,h={getUnits:async(t={})=>{const n=`units-${t.page||1}-${t.limit||50}-${t.search||""}-${t.active??1}-${t.sort||"sort"}`,s=a.get(n);if(s&&Date.now()-s.timestamp<y)return s.data;const i=r.get(n);if(i)return i;const c=(async()=>{try{const e=new URLSearchParams;t.page&&e.append("page",t.page.toString()),t.limit&&e.append("limit",t.limit.toString()),t.search&&e.append("search",t.search),t.active!==void 0&&e.append("active",t.active.toString()),t.sort&&e.append("sort",t.sort);const o=e.toString(),u=o?`/mdata/v1/units?${o}`:"/mdata/v1/units",d=await l.get(u);if(!d.data||typeof d.data!="object")throw new Error("Invalid response format from units API");const f=d.data;return a.set(n,{data:f,timestamp:Date.now()}),f}finally{r.delete(n)}})();return r.set(n,c),c},clearCache:()=>{a.clear(),r.clear()},getCacheStats:()=>({cacheSize:a.size,pendingRequests:r.size}),getUnitById:t=>{for(const n of a.values()){const s=n.data.data.find(i=>i.id===t);if(s)return s}},getUnitByUnitId:t=>{for(const n of a.values()){const s=n.data.data.find(i=>i.unit_id===t);if(s)return s}}},U=(t={})=>{const{params:n={},enabled:s=!0}=t,i={active:1,sort:"sort",...n};return g({queryKey:[p.UNITS,i],queryFn:async()=>(await h.getUnits(i)).data||[],enabled:s,staleTime:30*60*1e3,refetchInterval:60*60*1e3,retry:(c,e)=>{var o,u;return(o=e==null?void 0:e.message)!=null&&o.includes("401")||(u=e==null?void 0:e.message)!=null&&u.includes("403")?!1:c<3}})};export{U as u};
