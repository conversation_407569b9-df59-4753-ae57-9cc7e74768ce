import{R as v,d as ve,r as S,j as r,B as E}from"./index-CVQ6JZo2.js";import{C,d as T,a as ge,b as je}from"./card-DtE5r_AG.js";import{I as Ae}from"./input-Al6WtUZF.js";import{D as _e}from"./date-range-picker-o-N_0Uo2.js";import"./crm-api-CDzLLTww.js";import"./pos-api-mRg02iop.js";import"./user-BJzEhOTa.js";import{S as ne,a1 as Ne,f as J,L as K,d as Oe,A as Pe,l as G,m as we,s as de,V as Se,j as Z,a2 as De,i as ke,C as Ee,h as z,o as Ce,u as Te,G as ze,t as Ie,Z as re,y as $e,E as ue,X as me,Y as Q,F as Ke,R as Re,a0 as V,H as Be}from"./generateCategoricalChart-CbMKN4d7.js";import{A as Le}from"./Area-BR5iyjTM.js";import{L as pe}from"./Line-CE8nwEPj.js";import{a as ie,b as R}from"./subMonths-BRhS7Uii.js";import{s as B,f as se}from"./isSameMonth-C8JQo-AN.js";import"./popover-DnoSPJNX.js";import"./index-CtK-wKtB.js";import"./calendar-CYB-o1z2.js";import"./createLucideIcon-DKVxsQv7.js";import"./chevron-right-CxgpqvrH.js";import"./index-Chjiymov.js";function I(t){"@babel/helpers - typeof";return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},I(t)}function Fe(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function He(t,e){for(var n=0;n<e.length;n++){var a=e[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,he(a.key),a)}}function Me(t,e,n){return e&&He(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function We(t,e,n){return e=q(e),Ge(t,fe()?Reflect.construct(e,n||[],q(t).constructor):e.apply(t,n))}function Ge(t,e){if(e&&(I(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ve(t)}function Ve(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function fe(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(fe=function(){return!!t})()}function q(t){return q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},q(t)}function Ze(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ee(t,e)}function ee(t,e){return ee=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,i){return a.__proto__=i,a},ee(t,e)}function ye(t,e,n){return e=he(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function he(t){var e=qe(t,"string");return I(e)=="symbol"?e:e+""}function qe(t,e){if(I(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var a=n.call(t,e);if(I(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var U=function(t){function e(){return Fe(this,e),We(this,e,arguments)}return Ze(e,t),Me(e,[{key:"render",value:function(){return null}}])}(v.Component);ye(U,"displayName","ZAxis");ye(U,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"});var Ye=["option","isActive"];function L(){return L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},L.apply(this,arguments)}function Ue(t,e){if(t==null)return{};var n=Xe(t,e),a,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(i=0;i<o.length;i++)a=o[i],!(e.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(t,a)&&(n[a]=t[a])}return n}function Xe(t,e){if(t==null)return{};var n={};for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){if(e.indexOf(a)>=0)continue;n[a]=t[a]}return n}function Je(t){var e=t.option,n=t.isActive,a=Ue(t,Ye);return typeof e=="string"?v.createElement(ne,L({option:v.createElement(Ne,L({type:e},a)),isActive:n,shapeType:"symbols"},a)):v.createElement(ne,L({option:e,isActive:n,shapeType:"symbols"},a))}function $(t){"@babel/helpers - typeof";return $=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$(t)}function F(){return F=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},F.apply(this,arguments)}function oe(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,a)}return n}function _(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?oe(Object(n),!0).forEach(function(a){O(t,a,n[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oe(Object(n)).forEach(function(a){Object.defineProperty(t,a,Object.getOwnPropertyDescriptor(n,a))})}return t}function Qe(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function le(t,e){for(var n=0;n<e.length;n++){var a=e[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,be(a.key),a)}}function et(t,e,n){return e&&le(t.prototype,e),n&&le(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function tt(t,e,n){return e=Y(e),at(t,xe()?Reflect.construct(e,n||[],Y(t).constructor):e.apply(t,n))}function at(t,e){if(e&&($(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return nt(t)}function nt(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function xe(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(xe=function(){return!!t})()}function Y(t){return Y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},Y(t)}function rt(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&te(t,e)}function te(t,e){return te=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,i){return a.__proto__=i,a},te(t,e)}function O(t,e,n){return e=be(e),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function be(t){var e=it(t,"string");return $(e)=="symbol"?e:e+""}function it(t,e){if($(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var a=n.call(t,e);if($(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}var X=function(t){function e(){var n;Qe(this,e);for(var a=arguments.length,i=new Array(a),o=0;o<a;o++)i[o]=arguments[o];return n=tt(this,e,[].concat(i)),O(n,"state",{isAnimationFinished:!1}),O(n,"handleAnimationEnd",function(){n.setState({isAnimationFinished:!0})}),O(n,"handleAnimationStart",function(){n.setState({isAnimationFinished:!1})}),O(n,"id",Te("recharts-scatter-")),n}return rt(e,t),et(e,[{key:"renderSymbolsStatically",value:function(a){var i=this,o=this.props,x=o.shape,j=o.activeShape,h=o.activeIndex,b=J(this.props,!1);return a.map(function(c,d){var f=h===d,u=f?j:x,s=_(_({},b),c);return v.createElement(K,F({className:"recharts-scatter-symbol",key:"symbol-".concat(c==null?void 0:c.cx,"-").concat(c==null?void 0:c.cy,"-").concat(c==null?void 0:c.size,"-").concat(d)},Oe(i.props,c,d),{role:"img"}),v.createElement(Je,F({option:u,isActive:f,key:"symbol-".concat(d)},s)))})}},{key:"renderSymbolsWithAnimation",value:function(){var a=this,i=this.props,o=i.points,x=i.isAnimationActive,j=i.animationBegin,h=i.animationDuration,b=i.animationEasing,c=i.animationId,d=this.state.prevPoints;return v.createElement(Pe,{begin:j,duration:h,isActive:x,easing:b,from:{t:0},to:{t:1},key:"pie-".concat(c),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(f){var u=f.t,s=o.map(function(l,m){var y=d&&d[m];if(y){var g=G(y.cx,l.cx),p=G(y.cy,l.cy),A=G(y.size,l.size);return _(_({},l),{},{cx:g(u),cy:p(u),size:A(u)})}var N=G(0,l.size);return _(_({},l),{},{size:N(u)})});return v.createElement(K,null,a.renderSymbolsStatically(s))})}},{key:"renderSymbols",value:function(){var a=this.props,i=a.points,o=a.isAnimationActive,x=this.state.prevPoints;return o&&i&&i.length&&(!x||!we(x,i))?this.renderSymbolsWithAnimation():this.renderSymbolsStatically(i)}},{key:"renderErrorBar",value:function(){var a=this.props.isAnimationActive;if(a&&!this.state.isAnimationFinished)return null;var i=this.props,o=i.points,x=i.xAxis,j=i.yAxis,h=i.children,b=de(h,Se);return b?b.map(function(c,d){var f=c.props,u=f.direction,s=f.dataKey;return v.cloneElement(c,{key:"".concat(u,"-").concat(s,"-").concat(o[d]),data:o,xAxis:x,yAxis:j,layout:u==="x"?"vertical":"horizontal",dataPointFormatter:function(m,y){return{x:m.cx,y:m.cy,value:u==="x"?+m.node.x:+m.node.y,errorVal:Z(m,y)}}})}):null}},{key:"renderLine",value:function(){var a=this.props,i=a.points,o=a.line,x=a.lineType,j=a.lineJointType,h=J(this.props,!1),b=J(o,!1),c,d;if(x==="joint")c=i.map(function(p){return{x:p.cx,y:p.cy}});else if(x==="fitting"){var f=De(i),u=f.xmin,s=f.xmax,l=f.a,m=f.b,y=function(A){return l*A+m};c=[{x:u,y:y(u)},{x:s,y:y(s)}]}var g=_(_(_({},h),{},{fill:"none",stroke:h&&h.fill},b),{},{points:c});return v.isValidElement(o)?d=v.cloneElement(o,g):ke(o)?d=o(g):d=v.createElement(Ee,F({},g,{type:j})),v.createElement(K,{className:"recharts-scatter-line",key:"recharts-scatter-line"},d)}},{key:"render",value:function(){var a=this.props,i=a.hide,o=a.points,x=a.line,j=a.className,h=a.xAxis,b=a.yAxis,c=a.left,d=a.top,f=a.width,u=a.height,s=a.id,l=a.isAnimationActive;if(i||!o||!o.length)return null;var m=this.state.isAnimationFinished,y=ve("recharts-scatter",j),g=h&&h.allowDataOverflow,p=b&&b.allowDataOverflow,A=g||p,N=z(s)?this.id:s;return v.createElement(K,{className:y,clipPath:A?"url(#clipPath-".concat(N,")"):null},g||p?v.createElement("defs",null,v.createElement("clipPath",{id:"clipPath-".concat(N)},v.createElement("rect",{x:g?c:c-f/2,y:p?d:d-u/2,width:g?f:f*2,height:p?u:u*2}))):null,x&&this.renderLine(),this.renderErrorBar(),v.createElement(K,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!l||m)&&Ce.renderCallByParent(this.props,o))}}],[{key:"getDerivedStateFromProps",value:function(a,i){return a.animationId!==i.prevAnimationId?{prevAnimationId:a.animationId,curPoints:a.points,prevPoints:i.curPoints}:a.points!==i.curPoints?{curPoints:a.points}:null}}])}(S.PureComponent);O(X,"displayName","Scatter");O(X,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!ze.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"});O(X,"getComposedData",function(t){var e=t.xAxis,n=t.yAxis,a=t.zAxis,i=t.item,o=t.displayedData,x=t.xAxisTicks,j=t.yAxisTicks,h=t.offset,b=i.props.tooltipType,c=de(i.props.children,Ie),d=z(e.dataKey)?i.props.dataKey:e.dataKey,f=z(n.dataKey)?i.props.dataKey:n.dataKey,u=a&&a.dataKey,s=a?a.range:U.defaultProps.range,l=s&&s[0],m=e.scale.bandwidth?e.scale.bandwidth():0,y=n.scale.bandwidth?n.scale.bandwidth():0,g=o.map(function(p,A){var N=Z(p,d),H=Z(p,f),P=!z(u)&&Z(p,u)||"-",M=[{name:z(e.dataKey)?i.props.name:e.name||e.dataKey,unit:e.unit||"",value:N,payload:p,dataKey:d,type:b},{name:z(n.dataKey)?i.props.name:n.name||n.dataKey,unit:n.unit||"",value:H,payload:p,dataKey:f,type:b}];P!=="-"&&M.push({name:a.name||a.dataKey,unit:a.unit||"",value:P,payload:p,dataKey:u,type:b});var D=re({axis:e,ticks:x,bandSize:m,entry:p,index:A,dataKey:d}),k=re({axis:n,ticks:j,bandSize:y,entry:p,index:A,dataKey:f}),ae=P!=="-"?a.scale(P):l,W=Math.sqrt(Math.max(ae,0)/Math.PI);return _(_({},p),{},{cx:D,cy:k,x:D-W,y:k-W,xAxis:e,yAxis:n,zAxis:a,width:2*W,height:2*W,size:ae,node:{x:N,y:H,z:P},tooltipPayload:M,tooltipPosition:{x:D,y:k},payload:p},c&&c[A]&&c[A].props)});return _({points:g},h)});var st=$e({chartName:"ComposedChart",GraphicalChild:[pe,Le,ue,X],axisComponents:[{axisType:"xAxis",AxisComp:me},{axisType:"yAxis",AxisComp:Q},{axisType:"zAxis",AxisComp:U}],formatAxisMap:Ke});const w={number_of_bill:0,amount_after_discount:0,amount_average_bill:0,amount_discount:0,report_by_pos:[{store_name:"Cửa hàng A",amount_after_discount:5e6,amount_discount:5e5,number_of_bill:25,amount_average_bill:2e5},{store_name:"Cửa hàng B",amount_after_discount:35e5,amount_discount:35e4,number_of_bill:18,amount_average_bill:194444},{store_name:"Cửa hàng C",amount_after_discount:72e5,amount_discount:8e5,number_of_bill:40,amount_average_bill:18e4}]},ce=[];function ot(){const[t,e]=S.useState(null),[n,a]=S.useState(null),[i,o]=S.useState(""),[x,j]=S.useState(""),[h,b]=S.useState("7days");S.useEffect(()=>{d("7days")},[]);const c=(s,l)=>`${se(s,"dd/MM/yyyy")} - ${se(l,"dd/MM/yyyy")}`,d=s=>{const l=new Date;let m,y=ie(l);switch(s){case"today":m=B(l);break;case"yesterday":m=B(R(l,1)),y=ie(R(l,1));break;case"7days":m=B(R(l,6));break;case"15days":m=B(R(l,14));break;case"30days":m=B(R(l,29));break;default:return}e(m),a(y),o(c(m,y)),b(s)},f=s=>{o(s),b("");const l=/^(\d{2}\/\d{2}\/\d{4})\s*-\s*(\d{2}\/\d{2}\/\d{4})$/,m=s.match(l);if(m)try{const[,y,g]=m,[p,A,N]=y.split("/").map(Number),[H,P,M]=g.split("/").map(Number),D=new Date(N,A-1,p),k=new Date(M,P-1,H);!isNaN(D.getTime())&&!isNaN(k.getTime())&&(e(D),a(k))}catch(y){console.error("Invalid date format:",y)}},u=s=>`${s.toLocaleString("vi-VN")} VND`;return r.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[r.jsxs("div",{className:"mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Doanh thu thành viên"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Theo dõi và phân tích doanh thu từ các thành viên"})]}),r.jsxs("div",{className:"flex justify-end items-center gap-2 mb-6",children:[r.jsxs("div",{className:"flex gap-2",children:[r.jsx(E,{variant:"outline",size:"sm",className:`text-xs ${h==="today"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("today"),children:"Hôm nay"}),r.jsx(E,{variant:"outline",size:"sm",className:`text-xs ${h==="yesterday"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("yesterday"),children:"Hôm qua"}),r.jsx(E,{variant:"outline",size:"sm",className:`text-xs ${h==="7days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("7days"),children:"7 ngày trước"}),r.jsx(E,{variant:"outline",size:"sm",className:`text-xs ${h==="15days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("15days"),children:"15 ngày trước"}),r.jsx(E,{variant:"outline",size:"sm",className:`text-xs ${h==="30days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("30days"),children:"30 ngày trước"})]}),r.jsx(_e,{startDate:t,endDate:n,onDateChange:(s,l)=>{e(s),a(l),s&&l&&(o(c(s,l)),b(""))},dateRange:i,onDateRangeChange:f})]}),r.jsxs("div",{className:"grid grid-cols-4 gap-4 mb-6",children:[r.jsx(C,{children:r.jsxs(T,{className:"p-4 text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-gray-900",children:u(w.amount_after_discount)}),r.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"DOANH THU"})]})}),r.jsx(C,{children:r.jsxs(T,{className:"p-4 text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-gray-900",children:u(w.amount_discount)}),r.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"GIẢM GIÁ"})]})}),r.jsx(C,{children:r.jsxs(T,{className:"p-4 text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-gray-900",children:w.number_of_bill}),r.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"SỐ LƯỢNG HÓA ĐƠN"})]})}),r.jsx(C,{children:r.jsxs(T,{className:"p-4 text-center",children:[r.jsx("div",{className:"text-2xl font-bold text-gray-900",children:u(w.amount_average_bill)}),r.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"BÌNH QUÂN HÓA ĐƠN"})]})})]}),r.jsx(C,{className:"mb-6",children:r.jsxs(T,{className:"p-6",children:[r.jsxs("div",{className:"flex justify-center gap-6 mb-4",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-3 h-3 bg-green-500 rounded"}),r.jsx("span",{className:"text-sm text-gray-600",children:"Số lượng hóa đơn"})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded"}),r.jsx("span",{className:"text-sm text-gray-600",children:"Doanh thu"})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-3 h-3 bg-orange-500 rounded"}),r.jsx("span",{className:"text-sm text-gray-600",children:"Giảm giá"})]})]}),r.jsxs("div",{className:"h-[600px] relative",children:[r.jsx(Re,{width:"100%",height:"100%",children:r.jsxs(st,{data:ce,margin:{top:15,right:50,left:90,bottom:25},children:[[.1,.2,.3,.4,.5,.6,.7,.8,.9,1].map(s=>r.jsx(V,{y:s,stroke:"#e5e7eb",strokeDasharray:"none",yAxisId:"left"},`left-${s}`)),[.1,.2,.3,.4,.5,.6,.7,.8,.9,1].map(s=>r.jsx(V,{y:s,stroke:"#e5e7eb",strokeDasharray:"none",yAxisId:"right"},`right-${s}`)),r.jsx(V,{y:0,stroke:"#9ca3af",strokeWidth:1,yAxisId:"left"}),r.jsx(V,{y:0,stroke:"#9ca3af",strokeWidth:1,yAxisId:"right"}),r.jsx(me,{dataKey:"name",axisLine:!0,tickLine:!0,tick:{fontSize:14,fill:"#6b7280"},stroke:"#9ca3af",strokeWidth:1}),r.jsx(Q,{yAxisId:"left",domain:[0,1],ticks:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1],tickFormatter:s=>s.toFixed(1),axisLine:!0,tickLine:!0,tick:{fontSize:14,fill:"#6b7280"},width:40,stroke:"#9ca3af",strokeWidth:1}),r.jsx(Q,{yAxisId:"right",orientation:"right",domain:[0,1],ticks:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1],tickFormatter:s=>s.toFixed(1),axisLine:!0,tickLine:!0,tick:{fontSize:14,fill:"#6b7280"},width:40,stroke:"#9ca3af",strokeWidth:1}),r.jsx(Be,{wrapperStyle:{fontSize:"16px",paddingTop:"20px"},iconType:"rect"}),ce.length>0&&r.jsxs(r.Fragment,{children:[r.jsx(ue,{yAxisId:"left",dataKey:"total_amount",fill:"#3b82f6",name:"Doanh thu",barSize:60}),r.jsx(pe,{yAxisId:"right",type:"monotone",dataKey:"number_of_bills",stroke:"#22c55e",strokeWidth:3,dot:{fill:"#22c55e",strokeWidth:2,r:6}})]})]})}),r.jsx("div",{className:"absolute left-2 top-1/2 transform -translate-y-1/2 -rotate-90",children:r.jsx("span",{className:"text-sm text-gray-600 whitespace-nowrap",children:"DOANH THU/GIẢM GIÁ"})}),r.jsx("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 rotate-90",children:r.jsx("span",{className:"text-sm text-gray-600 whitespace-nowrap",children:"HÓA ĐƠN"})})]})]})}),r.jsxs(C,{children:[r.jsxs(ge,{className:"flex flex-row items-center justify-between",children:[r.jsx(E,{variant:"outline",size:"sm",className:"bg-blue-500 text-white hover:bg-blue-600",children:"Xuất file"}),r.jsx(je,{className:"text-base font-medium text-gray-700 flex-1 text-center",children:"DOANH THU THEO CỬA HÀNG"}),r.jsx(Ae,{placeholder:"Tìm kiếm nhanh",value:x,onChange:s=>j(s.target.value),className:"w-64"})]}),r.jsx(T,{children:r.jsx("div",{className:"overflow-hidden",children:r.jsxs("table",{className:"w-full",children:[r.jsx("thead",{children:r.jsxs("tr",{className:"border-b bg-gray-50",children:[r.jsx("th",{className:"text-left p-3 text-sm font-medium text-gray-600",children:"Cửa hàng"}),r.jsx("th",{className:"text-left p-3 text-sm font-medium text-gray-600",children:"Doanh thu"}),r.jsx("th",{className:"text-left p-3 text-sm font-medium text-gray-600",children:"Giảm giá"}),r.jsx("th",{className:"text-left p-3 text-sm font-medium text-gray-600",children:"Số lượng hóa đơn"}),r.jsx("th",{className:"text-left p-3 text-sm font-medium text-gray-600",children:"Bình quân hóa đơn"})]})}),r.jsx("tbody",{children:w.report_by_pos&&w.report_by_pos.length>0?w.report_by_pos.filter(s=>{var l;return!x||((l=s.store_name)==null?void 0:l.toLowerCase().includes(x.toLowerCase()))}).map((s,l)=>r.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[r.jsx("td",{className:"p-3 text-sm text-gray-900",children:s.store_name||"N/A"}),r.jsx("td",{className:"p-3 text-sm text-gray-900",children:u(s.amount_after_discount||0)}),r.jsx("td",{className:"p-3 text-sm text-gray-900",children:u(s.amount_discount||0)}),r.jsx("td",{className:"p-3 text-sm text-gray-900",children:s.number_of_bill||0}),r.jsx("td",{className:"p-3 text-sm text-gray-900",children:u(s.amount_average_bill||0)})]},l)):r.jsx("tr",{children:r.jsx("td",{colSpan:5,className:"text-center p-8 text-gray-500",children:"No data available in table"})})})]})})})]})]})}const Pt=ot;export{Pt as component};
