import{a3 as Ye,h as Qe,i as xs,u as Ke,r as g,a4 as K,j as e,B as I,c as ie,aA as gs,e as ys}from"./index-UcdZ5AHH.js";import{C as js}from"./index-Bz1ZLo3n.js";import{b as Ee,a as Me}from"./pos-api-j20LMGrC.js";import"./date-range-picker-DxA68ufO.js";import{L as h}from"./form-D_U5B5Go.js";import{u as Ns}from"./useCanGoBack-CgcmcCI3.js";import{g as je}from"./error-utils-DVDyynMb.js";import{c as bs,u as _s,d as fs}from"./use-devices-CfIGwCOc.js";import{u as vs}from"./use-stores-ypC_BTQC.js";import{u as Cs}from"./use-item-types-Ba660Fo2.js";import{d as Ss}from"./use-combos-CvqC-2D5.js";import"./vietqr-api-9FERZtmQ.js";import{u as Se}from"./useQuery-B4yhTgGk.js";import{u as Xe}from"./useMutation-q12VR5WX.js";import{Q as Te}from"./query-keys-3lmd-xp6.js";import"./user-9ajIul7r.js";import"./crm-api-APQEjHWd.js";import{u as Ts}from"./use-printer-positions-data-B_9EXgyP.js";import{C as ae,a as re,b as le,c as ce,d as oe,e as de}from"./command-DJT46NtT.js";import{P as me,a as he,b as pe}from"./popover-BeZit_vZ.js";import{D as Re,a as ze,b as Ve,c as Ge,e as ws}from"./dialog-DmI079wB.js";import{I as f}from"./input-CBpgGfUv.js";import{S as Is,a as Ps,b as ks,c as As,d as Ue,C as Ne}from"./select-DOexGcsG.js";import{C as G}from"./checkbox-CDB9_T0n.js";import{R as Ce,a as V}from"./radio-group-B_FdzlMv.js";import{C as ue}from"./chevrons-up-down-BkDfU9b3.js";import{C as xe}from"./check-vnaEv-AC.js";import{D as Ls}from"./date-range-picker-BDtmAj1a.js";import{P as Os}from"./modal-DNIlBRJT.js";import{T as Ms,a as Ks,c as Fe,b as Be}from"./tabs-0XXLAiGI.js";import{C as be,a as _e,b as fe}from"./collapsible-Dz-Iaa-P.js";import{C as ve}from"./chevron-right-Dup7TmpK.js";import{X as We}from"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./react-icons.esm-DpPH1mSm.js";import"./isSameMonth-C8JQo-AN.js";import"./stores-api-CmxnE7jq.js";import"./utils-km2FGkQ4.js";import"./printer-position-api-CvF0O4CM.js";import"./search-B6f_4BGP.js";import"./index-DPUGtNbb.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./index-iiVug-md.js";import"./calendar-DZLqW2ag.js";const ne=new Map,te=new Map,Es=2*60*1e3,Rs={getOrderLogs:async s=>{const n=`${s.company_uid}-${s.brand_uid}-${s.store_uid}-${s.device_code}-${s.start_date}-${s.end_date}-${s.search||""}`,a=ne.get(n);if(a&&Date.now()-a.timestamp<Es)return a.data;const c=te.get(n);if(c)return c;const r=(async()=>{try{const o=new URLSearchParams;o.append("company_uid",s.company_uid),o.append("brand_uid",s.brand_uid),o.append("store_uid",s.store_uid),o.append("device_code",s.device_code),o.append("start_date",s.start_date.toString()),o.append("end_date",s.end_date.toString()),s.search&&o.append("search",s.search);const y=await Ee.get(`/v3/pos-cms/sale-change-log?${o.toString()}`,{headers:{Accept:"application/json, text/plain, */*","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});if(!y.data||typeof y.data!="object")throw new Error("Invalid response format from order logs API");const j=y.data;return ne.set(n,{data:j,timestamp:Date.now()}),j}finally{te.delete(n)}})();return te.set(n,r),r},clearCache:()=>{ne.clear(),te.clear()},getCacheStats:()=>({cacheSize:ne.size,pendingRequests:te.size}),getOrderLogByTranId:s=>{for(const n of ne.values()){const a=n.data.data.find(c=>c.tran_id===s);if(a)return a}}},zs=(s={})=>{const{enabled:n=!0,...a}=s,c=Date.now(),o={company_uid:"595e8cb4-674c-49f7-adec-826b211a7ce3",brand_uid:"d43a01ec-2f38-4430-a7ca-9b3324f7d39e",store_uid:"ba6e0a44-080d-4ae4-aba0-c29b79e95ab3",device_code:"",start_date:c-30*24*60*60*1e3,end_date:c,...a};return Se({queryKey:[Te.ORDER_LOGS,o],queryFn:async()=>o.device_code?await Rs.getOrderLogs(o):{data:[]},enabled:n&&!!o.device_code,staleTime:2*60*1e3,gcTime:5*60*1e3,select:y=>y.data||[]})},$e={getPrinters:async s=>{const n=new URLSearchParams({pos_device_code:s.pos_device_code,results_per_page:(s.results_per_page||15e3).toString()});return(await Me.get(`/v3/pos-cms/printer?${n.toString()}`)).data},createPrinter:async s=>(await Me.post("/v3/pos-cms/printer",s,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"}})).data.data,deletePrinter:async s=>{await Me.delete(`/v3/pos-cms/printer?id=${s}`,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"}})}},Vs=(s={})=>{const{params:n={},enabled:a=!0}=s;return Se({queryKey:[Te.PRINTERS,n.pos_device_code],queryFn:async()=>{if(!n.pos_device_code)throw new Error("pos_device_code is required");const c={pos_device_code:n.pos_device_code,results_per_page:n.results_per_page||15e3};return await $e.getPrinters(c)},enabled:a&&!!n.pos_device_code,staleTime:5*60*1e3,gcTime:10*60*1e3})},Gs=()=>{const s=Ye();return Xe({mutationFn:n=>$e.createPrinter(n),onSuccess:(n,a)=>{s.invalidateQueries({queryKey:[Te.PRINTERS,a.device_code]})}})},$s=()=>{const s=Ye();return Xe({mutationFn:n=>$e.deletePrinter(n),onSuccess:()=>{s.invalidateQueries({queryKey:[Te.PRINTERS]})}})},Je=[{value:"POS",label:"POS",apiValue:"POS"},{value:"POS_MINI",label:"POS MINI",apiValue:"POS_MINI"},{value:"PDA",label:"PDA",apiValue:"PDA"},{value:"KDS",label:"KDS",apiValue:"KDS"},{value:"KDS_ORDER_CONTROL",label:"KDS ORDER CONTROL",apiValue:"KDS_ORDER_CONTROL"},{value:"KDS_MAKER",label:"KDS MAKER",apiValue:"KDS_MAKER"}],He=[{value:"None",label:"None",apiValue:0},{value:"Máy chủ",label:"Máy chủ",apiValue:1},{value:"Máy trạm",label:"Máy trạm",apiValue:2}],Ze=[{value:"Cho phép",label:"Cho phép",apiValue:1},{value:"Không cho phép",label:"Không cho phép",apiValue:0}],qe=[{value:"Không hiển thị",label:"Không hiển thị",apiValue:0},{value:"Nhận thông báo từ KDS với order từ thiết bị",label:"Nhận thông báo từ KDS với order từ thiết bị",apiValue:1},{value:"Nhận tất cả thông báo từ KDS",label:"Nhận tất cả thông báo từ KDS",apiValue:2}],Hs={lastUpdate:"",deviceName:"",deviceCode:"",deviceType:"POS",storeLocation:"",localIpAddress:"",version:"",timezone:"Asia/Ho_Chi_Minh",isActive:!0,enableTabManagement:!1,displayColumns:"5",enableScreen2:!1,useItemInStore:!1,kdsNotificationConfig:"Không hiển thị",enablePosNutMode:!1,typeAllowConnectPos:"Cho phép",enableComboGroup:!1,enableTabDisplay:!0,deviceTypeLocal:"None",newIpAddress:"",specialConfigType:""},De=s=>{const n=Je.find(a=>a.value===s);return(n==null?void 0:n.apiValue)??"POS"},qs=s=>{const n=He.find(a=>a.apiValue===s);return(n==null?void 0:n.value)??"None"},Us=s=>{const n=He.find(a=>a.value===s);return(n==null?void 0:n.apiValue)??0},Fs=s=>{const n=Ze.find(a=>a.apiValue===s);return(n==null?void 0:n.value)??"Cho phép"},Bs=s=>{const n=Ze.find(a=>a.value===s);return(n==null?void 0:n.apiValue)??0},Ws=s=>{const n=qe.find(a=>a.apiValue===s);return(n==null?void 0:n.value)??"Không hiển thị"},Ys=s=>{const n=qe.find(a=>a.value===s);return(n==null?void 0:n.apiValue)??0},Qs=(s,n)=>({deviceName:(s==null?void 0:s.device_name)||(s==null?void 0:s.name)||"",deviceCode:(s==null?void 0:s.device_code)||(s==null?void 0:s.serialNumber)||"",deviceType:(s==null?void 0:s.type)||"",storeLocation:(s==null?void 0:s.storeName)||"N/A",localIpAddress:(s==null?void 0:s.my_ip_local)||"N/A",version:(s==null?void 0:s.version_app)||(s==null?void 0:s.version)||"N/A",timezone:(s==null?void 0:s.time_zone)||"N/A",lastUpdate:(()=>{const a=(s==null?void 0:s.updated_at)||(s==null?void 0:s.lastUpdate)||(s==null?void 0:s.updatedAt)||(n==null?void 0:n.updated_at);if(!a)return"N/A";const c=a instanceof Date?a:new Date(a);if(isNaN(c.getTime()))return String(a);const r=c.getDate().toString().padStart(2,"0"),o=(c.getMonth()+1).toString().padStart(2,"0"),y=c.getFullYear(),j=c.getHours().toString().padStart(2,"0"),t=c.getMinutes().toString().padStart(2,"0");return`${r}/${o}/${y} ${j}:${t}`})(),isActive:(s==null?void 0:s.active)===1,enableTabManagement:(n==null?void 0:n.area_manager_enable)===1,displayColumns:(n==null?void 0:n.column_table)??"5",enableScreen2:(n==null?void 0:n.dual_screen_enable)===1,useItemInStore:(n==null?void 0:n.allow_print_label)===1,kdsNotificationConfig:Ws((n==null?void 0:n.enable_tab_kds)??0),enablePosNutMode:(n==null?void 0:n.enable_cash_drawer)===1,typeAllowConnectPos:Fs((n==null?void 0:n.type_allow_connect_pos)??0),enableComboGroup:Array.isArray(n==null?void 0:n.item_type_ignore)&&n.item_type_ignore.length>0,enableTabDisplay:(n==null?void 0:n.enable_tab_order_ta)===1,deviceTypeLocal:qs((n==null?void 0:n.device_type_local)??0),newIpAddress:"",specialConfigType:(n==null?void 0:n.special_character)||""}),Xs=(s,n,a,c,r,o)=>{const y={...n,device_name:s.deviceName,type:De(s.deviceType),active:s.isActive?1:0,company_uid:n.company_uid,brand_uid:n.brand_uid,store_uid:n.store_uid,extra_data:{...n.extra_data,area_manager_enable:s.enableTabManagement?1:0,enable_tab_management:s.enableTabManagement?1:0,column_table:s.displayColumns||"6",display_columns:s.displayColumns||"6",dual_screen_enable:s.enableScreen2?1:0,enable_screen_2:s.enableScreen2?1:0,allow_print_label:s.useItemInStore?1:0,use_item_in_store:s.useItemInStore?1:0,enable_cash_drawer:s.enablePosNutMode?1:0,enable_tab_order_ta:s.enableTabDisplay?1:0,enable_tab_kds:Ys(s.kdsNotificationConfig),type_allow_connect_pos:Bs(s.typeAllowConnectPos),device_type_local:Us(s.deviceTypeLocal),special_character:s.specialConfigType||"",new_restaurant_interface:s.enableTabDisplay?1:0,item_type_ignore:(()=>{var j,t;if((j=s.extra_data)!=null&&j.item_type_ignore&&Array.isArray(s.extra_data.item_type_ignore))return s.extra_data.item_type_ignore;if(a&&c&&r&&o){const u=[];return a.forEach(S=>{const _=r.find(C=>C.id===S);_&&_.item_type_id&&u.push(_.item_type_id)}),c.forEach(S=>{const _=o.find(C=>C.id===S);_&&_.package_id&&u.push(_.package_id)}),u}return s.enableComboGroup?((t=s.extra_data)==null?void 0:t.item_type_ignore)||[]:[]})()}};return s.timezone&&s.timezone!=="N/A"&&(y.extra_data.timezone=s.timezone),s.newIpAddress&&s.newIpAddress.trim()&&(y.address=s.newIpAddress.trim()),y},Js=(s,n,a,c)=>({deviceName:s.deviceName,storeId:n,deviceType:De(s.deviceType),brandUid:""}),Zs=(s,n)=>{const a=Qe(),c=xs(),r=Ns(),{auth:o}=Ke(),y=!!s,[j,t]=g.useState(Hs),[u,S]=g.useState(!1),[_,C]=g.useState(!1),[k,p]=g.useState(!1),[x,d]=g.useState(!1),[N,l]=g.useState(null),[m,M]=g.useState(null),[P,E]=g.useState(!1),[A,$]=g.useState(!1),[L,H]=g.useState(!1),[T,R]=g.useState(new Set),[O,q]=g.useState(new Set),[i,b]=g.useState(""),[v,w]=g.useState(!0),[Y,se]=g.useState(!0),[ge,J]=g.useState(!1),[we,ye]=g.useState(!1),[Ie,Z]=g.useState(!1),[Pe,z]=g.useState(!1),[ke,Ae]=g.useState(!1),[es,ss]=g.useState(!1),[ns,ts]=g.useState(!1),{mutate:is,isPending:as}=bs(),{mutate:rs,isPending:ls}=_s(),{mutate:cs,isPending:Le}=$s(),{data:U,isLoading:os}=fs(s||"",y),{data:ds}=vs(),{data:ms,isLoading:hs}=Vs({params:{pos_device_code:U==null?void 0:U.device_code},enabled:y&&!!(U!=null&&U.device_code)}),{data:D}=Cs({enabled:y}),{data:ee}=Ss({enabled:y});return g.useEffect(()=>{if(y&&U){const F=U.extra_data||{},Q=Qs(U,F);if(t(Q),Array.isArray(F.item_type_ignore)){const B=new Set,W=new Set;F.item_type_ignore.forEach(X=>{const ps=D==null?void 0:D.some(Oe=>Oe.id===X),us=ee==null?void 0:ee.some(Oe=>Oe.id===X);ps&&B.add(X),us&&W.add(X)}),R(B),q(W)}}},[y,U,D,ee]),{formData:j,setFormData:t,allowEditIpServer:u,setAllowEditIpServer:S,showDeleteModal:_,setShowDeleteModal:C,showPrinterModal:k,setShowPrinterModal:p,showDeletePrinterModal:x,setShowDeletePrinterModal:d,printerToDelete:N,setPrinterToDelete:l,printerToEdit:m,setPrinterToEdit:M,isPrinterEditMode:P,setIsPrinterEditMode:E,showOrderLogModal:A,setShowOrderLogModal:$,showGroupSelectionModal:L,setShowGroupSelectionModal:H,selectedGroups:T,setSelectedGroups:R,selectedCombos:O,setSelectedCombos:q,groupSearchTerm:i,setGroupSearchTerm:b,selectedSectionOpen:v,setSelectedSectionOpen:w,remainingSectionOpen:Y,setRemainingSectionOpen:se,openDeviceType:ge,setOpenDeviceType:J,openDeviceStatus:we,setOpenDeviceStatus:ye,openStoreLocation:Ie,setOpenStoreLocation:Z,openTimezone:Pe,setOpenTimezone:z,openDeviceTypeLocal:ke,setOpenDeviceTypeLocal:Ae,openTypeAllowConnectPos:es,setOpenTypeAllowConnectPos:ss,openKdsNotification:ns,setOpenKdsNotification:ts,deviceData:U,storesData:ds,printersData:ms,itemTypesData:D,combosData:ee,isLoadingDevice:os,isLoadingPrinters:hs,isUpdating:as,isCreating:ls,isDeletingPrinter:Le,handleSave:async()=>{var F,Q;try{if(y){const B=Xs(j,U,T,O,D,ee);is({id:s,...B},{onSuccess:()=>{K.success("Cập nhật thiết bị thành công!"),a({to:"/devices/list"})},onError:W=>{const X=je(W);K.error(X)}})}else{if(!n){K.error("Không tìm thấy thông tin cửa hàng");return}const B={...Js(j,n,T,O),brandUid:((Q=(F=o==null?void 0:o.brands)==null?void 0:F[0])==null?void 0:Q.id)||""};rs(B,{onSuccess:()=>{K.success("Tạo thiết bị thành công!"),a({to:"/devices/list"})},onError:W=>{const X=je(W);K.error(X)}})}}catch(B){const W=je(B);K.error(W)}},handleDeletePrinter:F=>{Le||(l(F),d(!0))},confirmDeletePrinter:()=>{!N||Le||cs(N.id,{onSuccess:()=>{K.success(`Xóa máy in "${N.printer_name}" thành công!`),d(!1),l(null)},onError:F=>{const Q=je(F);K.error(Q)}})},handleBack:()=>{r?c.history.back():a({to:"/devices/list"})},handleGroupSelectionConfirm:()=>{const F=Array.from(T),Q=Array.from(O),B=[...F,...Q];t(W=>({...W,enableComboGroup:B.length>0,extra_data:{...W.extra_data,item_type_ignore:B}})),H(!1)},isEditMode:y}};function Ds({open:s,onOpenChange:n,onSave:a,deviceCode:c,printerData:r,isEditMode:o=!1}){const{mutate:y}=Gs(),{data:j=[]}=Ts(),[t,u]=g.useState({name:"",connectionType:"USB",productId:"",vendorId:"",ipAddress:"",port:"9100",printerType:"In hóa đơn",printerPosition:"Chọn vị trí máy in",paperSize:"580",sheetNumber:1,printOrder:!1,enableSpecialPrint:!1,kdsDeviceType:"",labelWidth:38,labelHeight:30,labelGap:3,labelContentHeight:220,rotatePaper:!1}),[S,_]=g.useState(!1),[C,k]=g.useState(!1),p=[{value:"In hóa đơn",label:"In hóa đơn",apiValue:"PRINT_HS"},{value:"In order",label:"In order",apiValue:"PRINT_HS"},{value:"In tem",label:"In tem",apiValue:"PRINT_LABLE"}];g.useEffect(()=>{var l,m,M,P,E,A,$,L,H,T,R,O,q;if(s&&o&&r){const i=r.type_printer==="Usb"?"USB":r.type_printer==="Wifi"?"LAN":r.type_printer||"USB",b=r.type==="PRINT_HS"?r.print_order===!0?"In order":"In hóa đơn":r.type==="PRINT_LABLE"?"In tem":"In hóa đơn";u({name:r.printer_name||"",connectionType:i,productId:((l=r.product_id)==null?void 0:l.toString())||"",vendorId:((m=r.vendor_id)==null?void 0:m.toString())||"",ipAddress:r.ip||"",port:((M=r.port)==null?void 0:M.toString())||"9100",printerType:b,printerPosition:((P=j.find(v=>{var w;return v.printer_position_id===((w=r.extra_data)==null?void 0:w.printer_position)}))==null?void 0:P.printer_position_name)||"Chọn vị trí máy in",paperSize:((A=(E=r.extra_data)==null?void 0:E.screen_width)==null?void 0:A.toString())||"580",sheetNumber:(($=r.extra_data)==null?void 0:$.sheet_number)||1,printOrder:r.print_order||!1,enableSpecialPrint:((L=r.extra_data)==null?void 0:L.no_print_switch_table)===1,kdsDeviceType:"",labelWidth:parseInt((H=r.extra_data)==null?void 0:H.w_size_label)||38,labelHeight:parseInt((T=r.extra_data)==null?void 0:T.h_size_label)||30,labelGap:parseInt((R=r.extra_data)==null?void 0:R.gab_label)||3,labelContentHeight:parseInt((O=r.extra_data)==null?void 0:O.he_label)||220,rotatePaper:((q=r.extra_data)==null?void 0:q.rotate_paper)===180})}},[s,o,r,j]);const x=()=>{u({name:"In hóa đơn",connectionType:"USB",productId:"",vendorId:"",ipAddress:"",port:"9100",printerType:"In hóa đơn",printerPosition:"Chọn vị trí máy in",paperSize:"580",sheetNumber:1,printOrder:!1,enableSpecialPrint:!1,kdsDeviceType:"",labelWidth:38,labelHeight:30,labelGap:3,labelContentHeight:220,rotatePaper:!1})};g.useEffect(()=>{s&&!o&&x()},[s,o]),g.useEffect(()=>{let l=t.printerType;t.printerType!=="In hóa đơn"&&t.printerPosition&&t.printerPosition!=="Chọn vị trí máy in"&&(l=`${t.printerType} ${t.printerPosition}`),u(m=>({...m,name:l}))},[t.printerType,t.printerPosition]);const d=()=>{var M,P,E,A,$,L,H,T;if(t.connectionType==="USB"&&(!((M=t.productId)!=null&&M.trim())||!((P=t.vendorId)!=null&&P.trim()))){K.error("Product ID và Vendor ID không được để trống khi chọn USB");return}if(t.connectionType==="LAN"&&!((E=t.ipAddress)!=null&&E.trim())){K.error("Địa chỉ IP không được để trống khi chọn LAN");return}if(t.connectionType==="KDS"){if(!((A=t.kdsDeviceType)!=null&&A.trim())){K.error("Thiết bị KDS không được để trống khi chọn KDS");return}if(!t.printerPosition||t.printerPosition==="Chọn vị trí máy in"){K.error("Vị trí máy in không được để trống khi chọn KDS");return}}if(!c){K.error("Device code không hợp lệ");return}const l=`PRINTER_DEVICE_ID_${Date.now()}`,m={type_printer:t.connectionType==="USB"?"Usb":t.connectionType==="LAN"?"Wifi":t.connectionType,port:parseInt(t.port||"9100"),ip:t.connectionType==="LAN"?t.ipAddress:null,extra_data:{hash_order:!1,size_label:"LABEL4x3",w_size_label:t.printerType==="In tem"&&(($=t.labelWidth)==null?void 0:$.toString())||"38",h_size_label:t.printerType==="In tem"&&((L=t.labelHeight)==null?void 0:L.toString())||"30",gab_label:t.printerType==="In tem"&&((H=t.labelGap)==null?void 0:H.toString())||"3",he_label:t.printerType==="In tem"&&((T=t.labelContentHeight)==null?void 0:T.toString())||"220",sheet_number:t.sheetNumber,list_categories:[],printer_position:t.printerPosition==="Chọn vị trí máy in"?null:t.printerPosition||null,screen_width:parseInt(t.paperSize),rotate_paper:t.rotatePaper?180:0,no_print_switch_table:0,device_name:null},product_id:t.connectionType==="USB"?t.productId||"2":0,vendor_id:t.connectionType==="USB"?t.vendorId||"2":0,device_code:c,printer_name:t.name,printer_device_id:l,type:t.printerType==="In tem"?"PRINT_LABLE":"PRINT_HS",print_order:t.printerType==="In order"};y(m,{onSuccess:()=>{K.success(`Tạo máy in "${t.name}" thành công!`),a(t),n(!1),x()},onError:()=>{K.error("Có lỗi xảy ra khi tạo máy in")}})},N=()=>{n(!1),x()};return e.jsx(Re,{open:s,onOpenChange:n,children:e.jsxs(ze,{children:[e.jsx(Ve,{children:e.jsx(Ge,{className:"text-center text-2xl font-medium",children:o?"Chi tiết máy in":"Tạo máy in mới"})}),e.jsxs("div",{className:"space-y-3 py-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Chọn kết nối máy in"}),e.jsxs(Ce,{value:t.connectionType,onValueChange:l=>u(m=>({...m,connectionType:l})),className:"flex space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"USB",id:"usb"}),e.jsx(h,{htmlFor:"usb",children:"USB"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"LAN",id:"lan"}),e.jsx(h,{htmlFor:"lan",children:"LAN"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"Sunmi",id:"sunmi"}),e.jsx(h,{htmlFor:"sunmi",children:"Sunmi"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"KDS",id:"kds"}),e.jsx(h,{htmlFor:"kds",children:"KDS"})]})]})]}),t.connectionType==="USB"&&e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Product ID *"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:t.productId,onChange:l=>u(m=>({...m,productId:l.target.value})),placeholder:"Nhập Product ID"})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Vendor ID *"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:t.vendorId,onChange:l=>u(m=>({...m,vendorId:l.target.value})),placeholder:"Nhập Vendor ID"})})]})]}),t.connectionType==="LAN"&&e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Địa chỉ IP"}),e.jsxs("div",{className:"col-span-2 flex gap-2",children:[e.jsx(f,{value:t.ipAddress,onChange:l=>u(m=>({...m,ipAddress:l.target.value})),placeholder:"Nhập địa chỉ IP",className:"flex-1"}),e.jsx(f,{value:t.port,onChange:l=>u(m=>({...m,port:l.target.value})),placeholder:"9100",className:"w-20"})]})]})}),t.connectionType==="KDS"&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Thiết bị KDS"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(Is,{value:t.kdsDeviceType,onValueChange:l=>u(m=>({...m,kdsDeviceType:l})),children:[e.jsx(Ps,{children:e.jsx(ks,{placeholder:"Chọn loại máy in"})}),e.jsxs(As,{children:[e.jsx(Ue,{value:"kds-type-1",children:"KDS Type 1"}),e.jsx(Ue,{value:"kds-type-2",children:"KDS Type 2"})]})]})})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Kiểu máy in"}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Loại máy in"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(me,{open:S,onOpenChange:_,children:[e.jsx(he,{asChild:!0,children:e.jsxs(I,{variant:"outline",role:"combobox","aria-expanded":S,className:"w-full justify-between",children:[t.printerType||"Chọn loại máy in",e.jsx(ue,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(pe,{className:"w-full p-0",children:e.jsxs(ae,{children:[e.jsx(re,{placeholder:"Tìm kiếm loại máy in..."}),e.jsxs(le,{children:[e.jsx(ce,{children:"Không tìm thấy loại máy in."}),e.jsx(oe,{children:p.map(l=>e.jsxs(de,{value:l.value,onSelect:()=>{u(m=>({...m,printerType:l.value})),_(!1)},children:[e.jsx(xe,{className:ie("mr-2 h-4 w-4",t.printerType===l.value?"opacity-100":"opacity-0")}),l.label]},l.value))})]})]})})]})})]}),t.printerType==="In order"&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Vị trí máy in"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(me,{open:C,onOpenChange:k,children:[e.jsx(he,{asChild:!0,children:e.jsxs(I,{variant:"outline",role:"combobox","aria-expanded":C,className:"w-full justify-between",children:[t.printerPosition||"Chọn vị trí máy in",e.jsx(ue,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(pe,{className:"w-full p-0",children:e.jsxs(ae,{children:[e.jsx(re,{placeholder:"Tìm kiếm vị trí máy in..."}),e.jsxs(le,{children:[e.jsx(ce,{children:"Không tìm thấy vị trí máy in."}),e.jsx(oe,{children:j.map(l=>e.jsxs(de,{value:l.printer_position_name,onSelect:()=>{u(m=>({...m,printerPosition:l.printer_position_name})),k(!1)},children:[e.jsx(xe,{className:ie("mr-2 h-4 w-4",t.printerPosition===l.printer_position_name?"opacity-100":"opacity-0")}),l.printer_position_name]},l.id))})]})]})})]})})]}),(t.printerType==="In hóa đơn"||t.printerType==="In order")&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Khổ giấy in"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(Ce,{value:t.paperSize,onValueChange:l=>u(m=>({...m,paperSize:l})),className:"flex space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"350",id:"size-350"}),e.jsx(h,{htmlFor:"size-350",children:"350 (K58)"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"500",id:"size-500"}),e.jsx(h,{htmlFor:"size-500",children:"500 (K80)"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"580",id:"size-580"}),e.jsx(h,{htmlFor:"size-580",children:"580 (K80)"})]})]})})]}),t.printerType==="In tem"&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Khổ giấy in tem"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(Ce,{value:t.paperSize==="350"?"4x3":"5x3",onValueChange:l=>u(m=>({...m,paperSize:l==="4x3"?"350":"500"})),className:"flex space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"4x3",id:"label-4x3"}),e.jsx(h,{htmlFor:"label-4x3",children:"4x3"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"5x3",id:"label-5x3"}),e.jsx(h,{htmlFor:"label-5x3",children:"5x3"})]})]})})]}),(t.printerType==="In hóa đơn"||t.printerType==="In order")&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Số liên"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{type:"number",value:t.sheetNumber,onChange:l=>u(m=>({...m,sheetNumber:parseInt(l.target.value)||1})),placeholder:"1",min:"1"})})]}),t.printerType==="In order"&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Bấm order"}),e.jsx("div",{className:"col-span-2",children:e.jsx(G,{checked:t.printOrder,onCheckedChange:l=>u(m=>({...m,printOrder:!!l}))})})]}),t.printerType==="In tem"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Chiều rộng tem (mm) *"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{type:"number",value:t.labelWidth,onChange:l=>u(m=>({...m,labelWidth:parseInt(l.target.value)||38})),placeholder:"38",min:"1"})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Chiều cao tem (mm) *"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{type:"number",value:t.labelHeight,onChange:l=>u(m=>({...m,labelHeight:parseInt(l.target.value)||30})),placeholder:"30",min:"1"})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Khoảng cách giữa 2 tem (mm) *"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{type:"number",value:t.labelGap,onChange:l=>u(m=>({...m,labelGap:parseInt(l.target.value)||3})),placeholder:"3",min:"0"})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Chiều cao nội dung tem (px) *"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{type:"number",value:t.labelContentHeight,onChange:l=>u(m=>({...m,labelContentHeight:parseInt(l.target.value)||220})),placeholder:"220",min:"1"})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Xoay ngược giấy"}),e.jsx("div",{className:"col-span-2",children:e.jsx(G,{checked:t.rotatePaper,onCheckedChange:l=>u(m=>({...m,rotatePaper:!!l}))})})]})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-left font-medium text-gray-700",children:"Máy in này ko thực hiện in phiếu chuyển bàn"}),e.jsx("div",{className:"col-span-2",children:e.jsx(G,{checked:t.enableSpecialPrint,onCheckedChange:l=>u(m=>({...m,enableSpecialPrint:!!l}))})})]})]})]}),e.jsxs(ws,{children:[e.jsx(I,{variant:"outline",onClick:N,children:"Hủy"}),e.jsx(I,{onClick:d,children:"Lưu"})]})]})})}function en({open:s,onOpenChange:n,deviceCode:a}){const[c,r]=g.useState(""),[o,y]=g.useState(()=>{const d=new Date;return new Date(d.getFullYear(),d.getMonth(),1)}),[j,t]=g.useState(()=>{const d=new Date;return new Date(d.getFullYear(),d.getMonth()+1,0)}),u=g.useMemo(()=>o.getMonth()===j.getMonth()&&o.getFullYear()===j.getFullYear(),[o,j]),{startTimestamp:S,endTimestamp:_}=g.useMemo(()=>{const d=o.getTime(),N=j.getTime()+24*60*60*1e3-1;return{startTimestamp:d,endTimestamp:N}},[o,j]),{data:C,isLoading:k}=zs({device_code:a||"",start_date:S,end_date:_,search:c,enabled:s&&!!a&&u}),x=g.useMemo(()=>C?C.map(d=>{var N,l,m,M,P,E;return{id:d.tran_id,orderNumber:`#${d.change_data.tran_no||d.tran_id.slice(-6)}`,customerName:d.table_name||d.change_data.table_name||"Không xác định",orderTime:new Date(d.change_data.start_date||d.start_date).toLocaleString("vi-VN"),status:d.change_data.sale_note||"Hoàn thành",total:d.change_data.total_amount||0,items:((N=d.change_data.sale_detail)==null?void 0:N.map(A=>({name:A.item_name,quantity:A.quantity,price:A.amount})))||[],subtotal:d.change_data.total_amount||0,discount:((l=d.change_data.extra_data)==null?void 0:l.deposit_amount)||0,finalTotal:d.change_data.total_amount||0,customerCount:((m=d.change_data.extra_data)==null?void 0:m.peo_count)||0,customerType:((M=d.change_data.extra_data)==null?void 0:M.customer_name)||"",phoneNumber:((P=d.change_data.extra_data)==null?void 0:P.customer_phone)||"",notes:((E=d.change_data.extra_data)==null?void 0:E.note_deposit)||d.change_data.sale_note||""}}):[],[C]).filter(d=>d.orderNumber.toLowerCase().includes(c.toLowerCase()));return e.jsx(Re,{open:s,onOpenChange:n,children:e.jsxs(ze,{className:"sm:max-w-4xl",children:[e.jsx(Ve,{children:e.jsx(Ge,{className:"text-center text-2xl font-medium",children:"Nhật ký order"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(f,{placeholder:"Tìm kiếm theo mã hóa đơn",value:c,onChange:d=>r(d.target.value)})}),e.jsx("div",{className:"w-64",children:e.jsx(Ls,{startDate:o,endDate:j,onStartDateChange:d=>d&&y(d),onEndDateChange:d=>d&&t(d),placeholder:"Chọn khoảng thời gian"})})]}),!u&&e.jsx("div",{className:"rounded bg-red-50 p-2 text-sm text-red-600",children:"⚠️ Ngày bắt đầu và ngày kết thúc phải cùng tháng"}),e.jsx("div",{className:"max-h-96 overflow-y-auto",children:k?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"Đang tải dữ liệu..."}):x.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"Không tìm thấy order nào"}):e.jsx("div",{className:"space-y-3",children:x.map(d=>e.jsxs("div",{className:"rounded-lg border bg-white p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between border-b pb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium text-blue-600",children:d.orderNumber}),e.jsx("span",{className:"text-sm text-gray-500",children:"- Bàn 1 - 32131 - TN: CÔNG TY CỔ PHẦN TTMI - STT: 1"})]}),e.jsx("div",{className:"text-sm text-gray-600",children:d.orderTime})]}),e.jsx("div",{className:"mb-2 text-sm text-gray-600",children:"Ghi chú: 222222"}),e.jsx("div",{className:"space-y-2",children:d.items&&d.items.length>0?d.items.map((N,l)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:N.name}),N.time&&e.jsxs("span",{className:"text-xs text-gray-500",children:["⏰ ",N.time]})]}),e.jsx("span",{className:"font-medium",children:N.quantity})]},l)):e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Không có dữ liệu món ăn"})})]},d.id))})})]})]})})}function sn({open:s,onOpenChange:n,storeName:a,storeId:c}){const[r,o]=g.useState(""),y=Qe(),j=()=>{c&&(y({to:`/setting/store/detail/${c}`,hash:"manageDeviceCode"}),n(!1))},t=()=>{n(!1)},u=()=>{o(""),n(!1)};return e.jsx(Re,{open:s,onOpenChange:n,children:e.jsxs(ze,{className:"sm:max-w-md",children:[e.jsx(Ve,{children:e.jsx(Ge,{className:"text-center text-xl font-semibold",children:"Hủy thiết bị"})}),e.jsxs("div",{className:"space-y-6 py-4",children:[e.jsx("div",{className:"text-center text-gray-700",children:e.jsxs("p",{children:["Bạn cần nhập mã quản lý thiết bị của cửa hàng"," ",e.jsx("span",{className:"font-semibold",children:a||"Tutimi-Bình Lợi"})," để xác nhận hủy thiết bị."]})}),e.jsx("div",{className:"flex justify-center",children:e.jsx(I,{type:"button",variant:"secondary",onClick:j,className:"bg-gray-500 text-white hover:bg-gray-600",children:"Lấy mã quản lý thiết bị"})}),e.jsx("div",{className:"space-y-2",children:e.jsx(f,{placeholder:"Nhập mã quản lý thiết bị",value:r,onChange:S=>o(S.target.value),className:"text-center"})}),e.jsxs("div",{className:"flex justify-between gap-4",children:[e.jsx(I,{type:"button",variant:"outline",onClick:u,className:"flex-1",children:"Hủy"}),e.jsx(I,{type:"button",onClick:t,disabled:!r.trim(),className:"flex-1 bg-red-500 text-white hover:bg-red-600 disabled:bg-gray-300 disabled:text-gray-500",children:"Xác nhận"})]})]})]})})}function nn({storeUid:s}){const{company:n,brands:a}=Ke(r=>r.auth),c=a==null?void 0:a[0];return Se({queryKey:["device-item-types",n==null?void 0:n.id,c==null?void 0:c.id,s],queryFn:async()=>{var o;return!(n!=null&&n.id)||!(c!=null&&c.id)||!s?[]:((o=(await Ee.get(`/mdata/v1/item-types?skip_limit=true&company_uid=${n.id}&brand_uid=${c.id}&store_uid=${s}`)).data)==null?void 0:o.data)||[]},enabled:!!(n!=null&&n.id&&(c!=null&&c.id)&&s)})}function tn({storeUid:s}){const{company:n,brands:a}=Ke(r=>r.auth),c=a==null?void 0:a[0];return Se({queryKey:["device-packages",n==null?void 0:n.id,c==null?void 0:c.id,s],queryFn:async()=>{var o;return!(n!=null&&n.id)||!(c!=null&&c.id)||!s?[]:((o=(await Ee.get(`/mdata/v1/packages?skip_limit=true&company_uid=${n.id}&brand_uid=${c.id}&store_uid=${s}`)).data)==null?void 0:o.data)||[]},enabled:!!(n!=null&&n.id&&(c!=null&&c.id)&&s)})}function an({open:s,onOpenChange:n,selectedGroups:a,setSelectedGroups:c,selectedCombos:r,setSelectedCombos:o,onConfirm:y,storeUid:j,deviceData:t}){const[u,S]=g.useState("groups"),[_,C]=g.useState(""),[k,p]=g.useState(!0),[x,d]=g.useState(!0),{data:N=[]}=nn({storeUid:j}),{data:l=[]}=tn({storeUid:j});g.useEffect(()=>{var i;if(s&&((i=t==null?void 0:t.extra_data)!=null&&i.item_type_ignore)&&Array.isArray(N)&&Array.isArray(l)&&N.length>0&&l.length>0&&a.size===0&&r.size===0){const b=t.extra_data.item_type_ignore||[],v=new Set,w=new Set;b.forEach(Y=>{N.find(J=>J.item_type_id===Y)&&v.add(Y),l.find(J=>J.package_id===Y)&&w.add(Y)}),c(v),o(w)}},[s,t,N,l,a.size,r.size,c,o]);const m=N||[],M=m.filter(i=>i.item_type_name.toLowerCase().includes(_.toLowerCase())),P=l||[],E=P.filter(i=>(i.package_name||i.name||"").toLowerCase().includes(_.toLowerCase())),A=()=>u==="groups"?{allItems:M.map(i=>({id:i.id,item_type_id:i.item_type_id,name:i.item_type_name,type:"group"})),selectedItems:a,setSelectedItems:c}:{allItems:E.map(i=>({id:i.id,package_id:i.package_id,name:i.package_name,type:"combo"})),selectedItems:r,setSelectedItems:o},{allItems:$,selectedItems:L,setSelectedItems:H}=A(),T=i=>{if(u==="groups"){const b=m.find(v=>v.id===i.id);return(b==null?void 0:b.item_type_id)||i.id}else{const b=P.find(v=>v.id===i.id);return(b==null?void 0:b.package_id)||i.id}},R=$.filter(i=>L.has(T(i))),O=$.filter(i=>!L.has(T(i))),q=i=>{let b=i;if(u==="groups"){const v=m.find(w=>w.id===i);v&&v.item_type_id&&(b=v.item_type_id)}else{const v=P.find(w=>w.id===i);v&&v.package_id&&(b=v.package_id)}H(v=>{const w=new Set(v);return w.has(b)?w.delete(b):w.add(b),w})};return e.jsx(Os,{title:"Chọn nhóm hoặc combo không hiển thị trên thiết bị",centerTitle:!0,open:s,onOpenChange:n,onCancel:()=>n(!1),onConfirm:y,confirmText:"Xác nhận",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(f,{placeholder:"Tìm kiếm nhóm hoặc combo",value:_,onChange:i=>C(i.target.value),className:"w-full"}),e.jsxs(Ms,{value:u,onValueChange:S,children:[e.jsxs(Ks,{className:"grid w-full grid-cols-2",children:[e.jsx(Fe,{value:"groups",children:"Nhóm món"}),e.jsx(Fe,{value:"combos",children:"Combo"})]}),e.jsxs(Be,{value:"groups",className:"space-y-4",children:[e.jsxs(be,{open:k,onOpenChange:p,children:[e.jsxs(_e,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Đã chọn (",R.length,")"]}),k?e.jsx(Ne,{className:"h-4 w-4"}):e.jsx(ve,{className:"h-4 w-4"})]}),e.jsx(fe,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:R.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Chưa có nhóm món nào được chọn"}):R.map(i=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(G,{checked:L.has(T(i)),onCheckedChange:()=>q(i.id)}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"text-sm font-medium",children:i.name})})]},i.id))})})]}),e.jsxs(be,{open:x,onOpenChange:d,children:[e.jsxs(_e,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Còn lại (",O.length,")"]}),x?e.jsx(Ne,{className:"h-4 w-4"}):e.jsx(ve,{className:"h-4 w-4"})]}),e.jsx(fe,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:O.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Không có nhóm món nào"}):O.map(i=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(G,{checked:L.has(T(i)),onCheckedChange:()=>q(i.id)}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"text-sm font-medium",children:i.name})})]},i.id))})})]})]}),e.jsxs(Be,{value:"combos",className:"space-y-4",children:[e.jsxs(be,{open:k,onOpenChange:p,children:[e.jsxs(_e,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Đã chọn (",R.length,")"]}),k?e.jsx(Ne,{className:"h-4 w-4"}):e.jsx(ve,{className:"h-4 w-4"})]}),e.jsx(fe,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:R.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Chưa có combo nào được chọn"}):R.map(i=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(G,{checked:L.has(T(i)),onCheckedChange:()=>q(i.id)}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"text-sm font-medium",children:i.name})})]},i.id))})})]}),e.jsxs(be,{open:x,onOpenChange:d,children:[e.jsxs(_e,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Còn lại (",O.length,")"]}),x?e.jsx(Ne,{className:"h-4 w-4"}):e.jsx(ve,{className:"h-4 w-4"})]}),e.jsx(fe,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:O.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Không có combo nào"}):O.map(i=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(G,{checked:L.has(T(i)),onCheckedChange:()=>q(i.id)}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"text-sm font-medium",children:i.name})})]},i.id))})})]})]})]})]})})}function rn({id:s,storeUid:n}){const{formData:a,setFormData:c,allowEditIpServer:r,setAllowEditIpServer:o,showDeleteModal:y,setShowDeleteModal:j,showPrinterModal:t,setShowPrinterModal:u,showDeletePrinterModal:S,setShowDeletePrinterModal:_,printerToDelete:C,printerToEdit:k,setPrinterToEdit:p,isPrinterEditMode:x,setIsPrinterEditMode:d,showOrderLogModal:N,setShowOrderLogModal:l,showGroupSelectionModal:m,setShowGroupSelectionModal:M,selectedGroups:P,setSelectedGroups:E,selectedCombos:A,setSelectedCombos:$,openDeviceType:L,setOpenDeviceType:H,openDeviceTypeLocal:T,setOpenDeviceTypeLocal:R,openKdsNotification:O,setOpenKdsNotification:q,deviceData:i,printersData:b,isLoadingDevice:v,isUpdating:w,isCreating:Y,isDeletingPrinter:se,handleSave:ge,handleDeletePrinter:J,confirmDeletePrinter:we,handleBack:ye,handleGroupSelectionConfirm:Ie,isEditMode:Z}=Zs(s,n),Pe=()=>{};return v?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-8",children:e.jsx("div",{className:"mb-4 flex items-center justify-between",children:e.jsxs(I,{variant:"ghost",size:"sm",onClick:ye,className:"flex items-center",children:[e.jsx(We,{className:"h-4 w-4"}),e.jsx("span",{className:"ml-2",children:"Quay lại"})]})})}),e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-center",children:e.jsx("div",{className:"text-lg font-medium text-gray-900",children:"Đang tải..."})})})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(I,{variant:"ghost",size:"sm",onClick:ye,className:"flex items-center",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("h2",{className:"text-3xl font-medium text-gray-900",children:Z?"Chi tiết thiết bị":"Tạo thiết bị mới"}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsx(I,{variant:"default",type:"button",onClick:ge,disabled:w||Y,children:w||Y?"Đang lưu...":"Lưu"})})]})}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsxs("form",{onSubmit:z=>z.preventDefault(),className:"space-y-8",children:[e.jsx(ln,{formData:a,setFormData:c,isEditMode:Z,openDeviceType:L,setOpenDeviceType:H}),e.jsx(cn,{formData:a,setFormData:c,allowEditIpServer:r,setAllowEditIpServer:o,deviceData:i,isEditMode:Z,openDeviceTypeLocal:T,setOpenDeviceTypeLocal:R,openKdsNotification:O,setOpenKdsNotification:q,selectedGroups:P,selectedCombos:A,onOpenGroupSelection:()=>M(!0)}),Z&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Danh sách máy in"}),e.jsx(I,{type:"button",variant:"default",onClick:()=>u(!0),className:"flex items-center",children:e.jsx("span",{children:"Tạo máy in mới"})})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-white",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-4 border-b border-gray-200 bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700",children:[e.jsx("div",{children:"#"}),e.jsx("div",{children:"Tên máy in"}),e.jsx("div",{children:"Kiểu kết nối"}),e.jsx("div",{children:"Loại máy in"})]}),e.jsx("div",{className:"divide-y divide-gray-200",children:b&&b.data&&b.data.length>0?b.data.map((z,ke)=>e.jsxs("div",{className:"grid cursor-pointer grid-cols-4 gap-4 px-4 py-3 text-sm hover:bg-gray-50",onClick:()=>{p(z),d(!0),u(!0)},children:[e.jsx("div",{className:"text-gray-700",children:ke+1}),e.jsx("div",{className:"text-gray-700",children:z.printer_name}),e.jsx("div",{className:"text-gray-700",children:z.type_printer=="Wifi"?"LAN":z.type_printer.toUpperCase()}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-700",children:z.type==="PRINT_HS"?z.print_order===!0?"In order":"In hoá đơn":z.type==="PRINT_LABLE"?"In tem":z.type}),e.jsx(I,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 text-gray-400 hover:text-red-600",onClick:Ae=>{Ae.stopPropagation(),J(z)},disabled:se,children:"✕"})]})]},z.id)):e.jsx("div",{className:"px-4 py-8 text-center text-sm text-gray-500",children:"Chưa có máy in nào được cấu hình"})})]})]}),Z&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-8",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Nhật ký order của thiết bị"}),e.jsx("div",{className:"w-full text-center",children:e.jsx(I,{type:"button",variant:"outline",onClick:()=>l(!0),children:"Xem nhật ký order"})})]}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-gray-900",children:"Hủy thiết bị"}),e.jsx("p",{className:"mb-4 text-sm text-gray-600",children:"Thiết bị đã hủy sẽ không thể sử dụng lại."}),e.jsx(I,{type:"button",variant:"destructive",onClick:()=>j(!0),children:"Hủy thiết bị"})]})]})]})}),e.jsx(sn,{open:y,onOpenChange:j,storeName:i==null?void 0:i.storeName,storeId:(i==null?void 0:i.store_uid)||(i==null?void 0:i.storeId)}),e.jsx(js,{open:S,onOpenChange:_,content:`Bạn có muốn xóa máy in ${(C==null?void 0:C.printer_name)||"In hoá đơn"}?`,confirmText:"Xóa",onConfirm:we,isLoading:se}),e.jsx(Ds,{open:t,onOpenChange:u,onSave:Pe,deviceCode:i==null?void 0:i.device_code,printerData:k,isEditMode:x}),e.jsx(en,{open:N,onOpenChange:l,deviceCode:i==null?void 0:i.device_code}),e.jsx(an,{open:m,onOpenChange:M,selectedGroups:P,setSelectedGroups:E,selectedCombos:A,setSelectedCombos:$,onConfirm:Ie,storeUid:(i==null?void 0:i.store_uid)||(i==null?void 0:i.storeId),deviceData:i})]})}function ln({formData:s,setFormData:n,isEditMode:a,openDeviceType:c,setOpenDeviceType:r}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Chi tiết"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Tên thiết bị"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:s.deviceName,onChange:o=>n(y=>({...y,deviceName:o.target.value})),placeholder:"Nhập tên thiết bị"})})]}),a&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Device code"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:s.deviceCode,className:"cursor-not-allowed bg-gray-50 font-medium text-gray-600"})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Loại thiết bị"}),e.jsx("div",{className:"col-span-2",children:a?e.jsx(f,{value:s.deviceType,className:"cursor-not-allowed bg-gray-50 font-medium text-blue-500"}):e.jsxs(me,{open:c,onOpenChange:r,children:[e.jsx(he,{asChild:!0,children:e.jsxs(I,{variant:"outline",role:"combobox","aria-expanded":c,className:"w-full justify-between",children:[s.deviceType||"Chọn loại thiết bị",e.jsx(ue,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(pe,{className:"w-full p-0",children:e.jsxs(ae,{children:[e.jsx(re,{placeholder:"Tìm kiếm loại thiết bị..."}),e.jsxs(le,{children:[e.jsx(ce,{children:"Không tìm thấy loại thiết bị."}),e.jsx(oe,{children:Je.map(o=>e.jsxs(de,{value:o.value,onSelect:()=>{n(y=>({...y,deviceType:o.value})),r(!1)},children:[e.jsx(xe,{className:ie("mr-2 h-4 w-4",s.deviceType===o.value?"opacity-100":"opacity-0")}),o.label]},o.value))})]})]})})]})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Điểm bán hàng"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:s.storeLocation,disabled:!0,className:"cursor-not-allowed bg-gray-50 font-medium text-gray-600"})})]}),a&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Địa chỉ IP local"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:s.localIpAddress,disabled:!0,className:"cursor-not-allowed bg-gray-50 font-medium text-gray-600"})})]}),a&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Phiên bản"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:s.version,disabled:!0,className:"cursor-not-allowed bg-gray-50 font-medium text-gray-600"})})]}),a&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Múi giờ tại POS"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:s.timezone,disabled:!0,className:"cursor-not-allowed bg-gray-50 font-medium text-gray-600"})})]}),a&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Thời gian cập nhật"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:s.lastUpdate,className:"cursor-not-allowed bg-gray-50 font-medium text-gray-600"})})]})})]})]})}function cn({formData:s,setFormData:n,allowEditIpServer:a,setAllowEditIpServer:c,deviceData:r,isEditMode:o,openDeviceTypeLocal:y,setOpenDeviceTypeLocal:j,openKdsNotification:t,setOpenKdsNotification:u,selectedGroups:S,selectedCombos:_,onOpenGroupSelection:C}){const k=()=>{var p,x;return S.size===0&&_.size===0?((x=(p=r==null?void 0:r.extra_data)==null?void 0:p.item_type_ignore)==null?void 0:x.length)||0:S.size+_.size};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình thiết bị"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium",children:"Loại máy"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(me,{open:y,onOpenChange:j,children:[e.jsx(he,{asChild:!0,children:e.jsxs(I,{variant:"outline",role:"combobox","aria-expanded":y,className:"w-full justify-between text-blue-500",children:[s.deviceTypeLocal||"Chọn loại máy",e.jsx(ue,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(pe,{className:"w-full p-0",children:e.jsxs(ae,{children:[e.jsx(re,{placeholder:"Tìm kiếm loại máy..."}),e.jsxs(le,{children:[e.jsx(ce,{children:"Không tìm thấy loại máy."}),e.jsx(oe,{children:He.map(p=>e.jsxs(de,{className:"text-blue-500",value:p.value,onSelect:()=>{n(x=>({...x,deviceTypeLocal:p.value})),j(!1)},children:[e.jsx(xe,{className:ie("mr-2 h-4 w-4",s.deviceTypeLocal===p.value?"opacity-100":"opacity-0")}),p.label]},p.value))})]})]})})]})})]}),s.deviceTypeLocal!=="Máy chủ"&&s.deviceTypeLocal!=="None"&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Địa chỉ IP máy chủ"}),e.jsxs("div",{className:"col-span-2 flex items-center gap-3",children:[o?e.jsx(f,{value:a?s.newIpAddress:(r==null?void 0:r.ip_local_server)||(r==null?void 0:r.ipAddress)||"",disabled:!a,onChange:p=>n(x=>({...x,newIpAddress:p.target.value})),className:a?"":"cursor-not-allowed bg-gray-50 font-medium text-gray-600",placeholder:a?"Nhập địa chỉ IP máy chủ mới":""}):e.jsx(f,{value:s.newIpAddress,onChange:p=>n(x=>({...x,newIpAddress:p.target.value})),placeholder:"Nhập địa chỉ IP máy chủ"}),o&&e.jsx(G,{checked:a,onCheckedChange:p=>{c(!!p),n(p?x=>({...x,newIpAddress:""}):x=>({...x,newIpAddress:(r==null?void 0:r.ip_local_server)||(r==null?void 0:r.ipAddress)||""}))}})]})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Hiển thị tab quản lý khu vực"}),e.jsx("div",{className:"col-span-2",children:e.jsx(G,{checked:s.enableTabManagement,onCheckedChange:p=>n(x=>({...x,enableTabManagement:!!p}))})})]}),s.enableTabManagement&&e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Cấu hình hiển thị khu vực"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(Ce,{value:s.displayColumns,onValueChange:p=>n(x=>({...x,displayColumns:p})),className:"flex space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"5",id:"col5"}),e.jsx(h,{htmlFor:"col5",children:"5 cột"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"6",id:"col6"}),e.jsx(h,{htmlFor:"col6",children:"6 cột"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"7",id:"col7"}),e.jsx(h,{htmlFor:"col7",children:"7 cột"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(V,{value:"8",id:"col8"}),e.jsx(h,{htmlFor:"col8",children:"8 cột"})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Kích hoạt màn hình 2"}),e.jsx("div",{className:"col-span-2",children:e.jsx(G,{checked:s.enableScreen2,onCheckedChange:p=>n(x=>({...x,enableScreen2:!!p}))})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Dùng mẫu in tem của máy chủ"}),e.jsx("div",{className:"col-span-2",children:e.jsx(G,{checked:s.useItemInStore,onCheckedChange:p=>n(x=>({...x,useItemInStore:!!p}))})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-left font-medium",children:"Cấu hình hiện thông báo KDS"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(me,{open:t,onOpenChange:u,children:[e.jsx(he,{asChild:!0,children:e.jsxs(I,{variant:"outline",role:"combobox","aria-expanded":t,className:"w-full justify-between text-blue-500",children:[s.kdsNotificationConfig||"Chọn cấu hình thông báo KDS",e.jsx(ue,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(pe,{className:"w-full p-0",children:e.jsxs(ae,{children:[e.jsx(re,{placeholder:"Tìm kiếm cấu hình..."}),e.jsxs(le,{children:[e.jsx(ce,{children:"Không tìm thấy cấu hình."}),e.jsx(oe,{children:qe.map(p=>e.jsxs(de,{className:"text-blue-500",value:p.value,onSelect:()=>{n(x=>({...x,kdsNotificationConfig:p.value})),u(!1)},children:[e.jsx(xe,{className:ie("mr-2 h-4 w-4",s.kdsNotificationConfig===p.value?"opacity-100":"opacity-0")}),p.label]},p.value))})]})]})})]})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Hiển thị nút mở két ở POS"}),e.jsx("div",{className:"col-span-2",children:e.jsx(G,{checked:s.enablePosNutMode,onCheckedChange:p=>n(x=>({...x,enablePosNutMode:!!p}))})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Cấu hình ký tự đặc biệt"}),e.jsx("div",{className:"col-span-2",children:e.jsx(f,{value:s.specialConfigType,onChange:p=>n(x=>({...x,specialConfigType:p.target.value})),placeholder:"Nhập cấu hình ký tự đặc biệt"})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-left font-medium",children:"Ẩn nhóm hoặc combo không hiển thị trên thiết bị"}),e.jsx("div",{className:"col-span-2",children:e.jsx(I,{type:"button",variant:"outline",onClick:C,className:"w-full justify-start text-left text-blue-500",children:k()>0?`${k()} nhóm hoặc combo`:"0 nhóm hoặc combo"})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(h,{className:"text-right font-medium text-gray-700",children:"Giao diện mới ở tab nhà hàng"}),e.jsx("div",{className:"col-span-2",children:e.jsx(G,{checked:s.enableTabDisplay,onCheckedChange:p=>n(x=>({...x,enableTabDisplay:!!p}))})})]})]})]})}function on(){const{id:s}=gs({from:"/_authenticated/devices/detail/$id"}),{store_uid:n}=ys({from:"/_authenticated/devices/detail/$id"});return e.jsx(rn,{id:s,storeUid:n})}const rt=on;export{rt as component};
