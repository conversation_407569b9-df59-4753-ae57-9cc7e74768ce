import{u as m}from"./useQuery-B4yhTgGk.js";import{u as p}from"./index-UcdZ5AHH.js";import{p as P}from"./printer-position-api-CvF0O4CM.js";import{Q as y}from"./query-keys-3lmd-xp6.js";const h=(e={})=>{const{params:o={},enabled:u=!0}=e,{company:t,brands:a}=p(r=>r.auth),i=a==null?void 0:a[0],s={company_uid:(t==null?void 0:t.id)||"",brand_uid:(i==null?void 0:i.id)||"",page:1,limit:1e3,...o},n=!!(t!=null&&t.id&&(i!=null&&i.id));return m({queryKey:[y.PRINTER_POSITIONS,s],queryFn:async()=>{const r=await P.getPrinterPositions(s);return Array.isArray(r.data)?r.data:[]},enabled:u&&n,staleTime:0,gcTime:0,retry:!1,initialData:[]})};export{h as u};
