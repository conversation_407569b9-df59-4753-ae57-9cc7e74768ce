import{r as p,j as e,c as f,B as t,z as n,L as g}from"./index-UcdZ5AHH.js";import{C as b,a as w,b as N,c as v,d as y,f as P}from"./card-ulE1yKb5.js";import{A as C}from"./auth-layout-BesYdG-f.js";import{u as F,F as I,a as i,b as l,c,d,e as m}from"./form-D_U5B5Go.js";import{s as S}from"./zod-5jr7PwGQ.js";import{I as L}from"./input-CBpgGfUv.js";import{P as u}from"./password-input-DBRTkDCn.js";import{I as k,a as B}from"./IconBrandGithub-BU85VZho.js";import"./createReactComponent-C1S2Ujit.js";const A=n.object({email:n.string().min(1,{message:"Please enter your email"}).email({message:"Invalid email address"}),password:n.string().min(1,{message:"Please enter your password"}).min(7,{message:"Password must be at least 7 characters long"}),confirmPassword:n.string()}).refine(a=>a.password===a.confirmPassword,{message:"Passwords don't match.",path:["confirmPassword"]});function E({className:a,...h}){const[o,x]=p.useState(!1),r=F({resolver:S(A),defaultValues:{email:"",password:"",confirmPassword:""}});function j(s){x(!0),console.log(s),setTimeout(()=>{x(!1)},3e3)}return e.jsx(I,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(j),className:f("grid gap-3",a),...h,children:[e.jsx(i,{control:r.control,name:"email",render:({field:s})=>e.jsxs(l,{children:[e.jsx(c,{children:"Email"}),e.jsx(d,{children:e.jsx(L,{placeholder:"<EMAIL>",...s})}),e.jsx(m,{})]})}),e.jsx(i,{control:r.control,name:"password",render:({field:s})=>e.jsxs(l,{children:[e.jsx(c,{children:"Password"}),e.jsx(d,{children:e.jsx(u,{placeholder:"********",...s})}),e.jsx(m,{})]})}),e.jsx(i,{control:r.control,name:"confirmPassword",render:({field:s})=>e.jsxs(l,{children:[e.jsx(c,{children:"Confirm Password"}),e.jsx(d,{children:e.jsx(u,{placeholder:"********",...s})}),e.jsx(m,{})]})}),e.jsx(t,{className:"mt-2",disabled:o,children:"Create Account"}),e.jsxs("div",{className:"relative my-2",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("span",{className:"w-full border-t"})}),e.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:e.jsx("span",{className:"bg-background text-muted-foreground px-2",children:"Or continue with"})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs(t,{variant:"outline",className:"w-full",type:"button",disabled:o,children:[e.jsx(k,{className:"h-4 w-4"})," GitHub"]}),e.jsxs(t,{variant:"outline",className:"w-full",type:"button",disabled:o,children:[e.jsx(B,{className:"h-4 w-4"})," Facebook"]})]})]})})}function T(){return e.jsx(C,{children:e.jsxs(b,{className:"gap-4",children:[e.jsxs(w,{children:[e.jsx(N,{className:"text-lg tracking-tight",children:"Create an account"}),e.jsxs(v,{children:["Enter your email and password to create an account. ",e.jsx("br",{}),"Already have an account?"," ",e.jsx(g,{to:"/sign-in",className:"hover:text-primary underline underline-offset-4",children:"Sign In"})]})]}),e.jsx(y,{children:e.jsx(E,{})}),e.jsx(P,{children:e.jsxs("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["By creating an account, you agree to our"," ",e.jsx("a",{href:"/terms",className:"hover:text-primary underline underline-offset-4",children:"Terms of Service"})," ","and"," ",e.jsx("a",{href:"/privacy",className:"hover:text-primary underline underline-offset-4",children:"Privacy Policy"}),"."]})})]})})}const q=T;export{q as component};
