import{r as j,j as t,c as u}from"./index-UcdZ5AHH.js";import{B as L,H as E,R as T}from"./generateCategoricalChart-DLKIEKdV.js";const I={light:"",dark:".dark"},w=j.createContext(null);function C(){const c=j.useContext(w);if(!c)throw new Error("useChart must be used within a <ChartContainer />");return c}function K({id:c,className:r,children:n,config:s,...l}){const h=j.useId(),o=`chart-${c||h.replace(/:/g,"")}`;return t.jsx(w.Provider,{value:{config:s},children:t.jsxs("div",{"data-slot":"chart","data-chart":o,className:u("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",r),...l,children:[t.jsx(P,{id:o,config:s}),t.jsx(T,{children:n})]})})}const P=({id:c,config:r})=>{const n=Object.entries(r).filter(([,s])=>s.theme||s.color);return n.length?t.jsx("style",{dangerouslySetInnerHTML:{__html:Object.entries(I).map(([s,l])=>`
${l} [data-chart=${c}] {
${n.map(([h,o])=>{var a;const i=((a=o.theme)==null?void 0:a[s])||o.color;return i?`  --color-${h}: ${i};`:null}).join(`
`)}
}
`).join(`
`)}}):null},M=L;function R({active:c,payload:r,className:n,indicator:s="dot",hideLabel:l=!1,hideIndicator:h=!1,label:o,labelFormatter:i,labelClassName:a,formatter:_,color:N,nameKey:$,labelKey:b}){const{config:g}=C(),k=j.useMemo(()=>{var f;if(l||!(r!=null&&r.length))return null;const[e]=r,p=`${b||(e==null?void 0:e.dataKey)||(e==null?void 0:e.name)||"value"}`,x=m(g,e,p),d=!b&&typeof o=="string"?((f=g[o])==null?void 0:f.label)||o:x==null?void 0:x.label;return i?t.jsx("div",{className:u("font-medium",a),children:i(d,r)}):d?t.jsx("div",{className:u("font-medium",a),children:d}):null},[o,i,r,l,a,g,b]);if(!c||!(r!=null&&r.length))return null;const v=r.length===1&&s!=="dot";return t.jsxs("div",{className:u("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",n),children:[v?null:k,t.jsx("div",{className:"grid gap-1.5",children:r.map((e,p)=>{const x=`${$||e.name||e.dataKey||"value"}`,d=m(g,e,x),f=N||e.payload.fill||e.color;return t.jsx("div",{className:u("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5",s==="dot"&&"items-center"),children:_&&(e==null?void 0:e.value)!==void 0&&e.name?_(e.value,e.name,e,p,e.payload):t.jsxs(t.Fragment,{children:[d!=null&&d.icon?t.jsx(d.icon,{}):!h&&t.jsx("div",{className:u("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":s==="dot","w-1":s==="line","w-0 border-[1.5px] border-dashed bg-transparent":s==="dashed","my-0.5":v&&s==="dashed"}),style:{"--color-bg":f,"--color-border":f}}),t.jsxs("div",{className:u("flex flex-1 justify-between leading-none",v?"items-end":"items-center"),children:[t.jsxs("div",{className:"grid gap-1.5",children:[v?k:null,t.jsx("span",{className:"text-muted-foreground",children:(d==null?void 0:d.label)||e.name})]}),e.value&&t.jsx("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}const O=E;function q({className:c,hideIcon:r=!1,payload:n,verticalAlign:s="bottom",nameKey:l}){const{config:h}=C();return n!=null&&n.length?t.jsx("div",{className:u("flex items-center justify-center gap-4",s==="top"?"pb-3":"pt-3",c),children:n.map(o=>{const i=`${l||o.dataKey||"value"}`,a=m(h,o,i);return t.jsxs("div",{className:u("[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3"),children:[a!=null&&a.icon&&!r?t.jsx(a.icon,{}):t.jsx("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:o.color}}),a==null?void 0:a.label]},o.value)})}):null}function m(c,r,n){if(typeof r!="object"||r===null)return;const s="payload"in r&&typeof r.payload=="object"&&r.payload!==null?r.payload:void 0;let l=n;return n in r&&typeof r[n]=="string"?l=r[n]:s&&n in s&&typeof s[n]=="string"&&(l=s[n]),l in c?c[l]:c[n]}export{K as C,M as a,R as b,O as c,q as d};
