import{j as r}from"./index-UcdZ5AHH.js";import{C as e,a as t,b as o,c as i,d as s,f as a}from"./card-ulE1yKb5.js";import{A as n}from"./auth-layout-BesYdG-f.js";import{U as m}from"./user-auth-form-DHqhTrLQ.js";import"./form-D_U5B5Go.js";import"./zod-5jr7PwGQ.js";import"./use-auth-Ds6PM7xW.js";import"./useMutation-q12VR5WX.js";import"./utils-km2FGkQ4.js";import"./pos-api-j20LMGrC.js";import"./input-CBpgGfUv.js";import"./password-input-DBRTkDCn.js";import"./createReactComponent-C1S2Ujit.js";import"./IconBrandGithub-BU85VZho.js";function c(){return r.jsx(n,{children:r.jsxs(e,{className:"gap-4",children:[r.jsxs(t,{children:[r.jsx(o,{className:"text-lg tracking-tight",children:"Login"}),r.jsxs(i,{children:["Enter your email and password below to ",r.jsx("br",{}),"log into your account"]})]}),r.jsx(s,{children:r.jsx(m,{})}),r.jsx(a,{children:r.jsxs("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["By clicking login, you agree to our"," ",r.jsx("a",{href:"/terms",className:"hover:text-primary underline underline-offset-4",children:"Terms of Service"})," ","and"," ",r.jsx("a",{href:"/privacy",className:"hover:text-primary underline underline-offset-4",children:"Privacy Policy"}),"."]})})]})})}const A=c;export{A as component};
