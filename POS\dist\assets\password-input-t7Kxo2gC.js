import{r as o,j as e,c as l,B as p}from"./index-CfbMU4Ye.js";import{c as a}from"./createReactComponent-CVG1We1Z.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var u=a("outline","eye-off","IconEyeOff",[["path",{d:"M10.585 10.587a2 2 0 0 0 2.829 2.828",key:"svg-0"}],["path",{d:"M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87",key:"svg-1"}],["path",{d:"M3 3l18 18",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var f=a("outline","eye","IconEye",[["path",{d:"M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-0"}],["path",{d:"M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6",key:"svg-1"}]]);const m=o.forwardRef(({className:r,disabled:s,...n},d)=>{const[t,i]=o.useState(!1);return e.jsxs("div",{className:l("relative rounded-md",r),children:[e.jsx("input",{type:t?"text":"password",className:"border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-xs transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50",ref:d,disabled:s,...n}),e.jsx(p,{type:"button",size:"icon",variant:"ghost",disabled:s,className:"text-muted-foreground absolute top-1/2 right-1 h-6 w-6 -translate-y-1/2 rounded-md",onClick:()=>i(c=>!c),children:t?e.jsx(f,{size:18}):e.jsx(u,{size:18})})]})});m.displayName="PasswordInput";export{m as P};
