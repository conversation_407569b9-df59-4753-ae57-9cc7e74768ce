import{r as g,j as l,B as h,a4 as e}from"./index-UcdZ5AHH.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";function f({isActive:r,onToggle:n,disabled:a=!1,className:o=""}){const[t,s]=g.useState(!1),i=async()=>{if(!(a||t)){s(!0);try{await n(),e.success(r?"Đã vô hiệu hóa chương trình giảm giá":"<PERSON><PERSON> kích hoạt chương trình giảm giá")}catch(c){e.error("Có lỗi xảy ra khi cập nhật trạng thái"),console.error("Toggle error:",c)}finally{s(!1)}}};return l.jsxs(h,{type:"button",variant:r?"destructive":"default",size:"sm",onClick:i,disabled:a||t,className:`min-w-[100px] ${o}`,children:[t&&"<PERSON>ang cập nhật...",!t&&(r?"Deactivate":"Activate")]})}export{f as D};
