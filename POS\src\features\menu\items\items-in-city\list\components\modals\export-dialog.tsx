'use client'

import { useState, useRef } from 'react'

import { DownloadIcon, UploadIcon } from '@radix-ui/react-icons'

import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { useItemTypesData, useCitiesData } from '@/hooks/api'

import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

import { Combobox } from '@/components/pos/combobox'

import { useDownloadItemsTemplate } from '../../../hooks'
import { ExcelPreviewExportDialog, type ExportItem } from './excel-preview-export-dialog'

interface ExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ExportDialog({ open, onOpenChange }: ExportDialogProps) {
  const [selectedCity, setSelectedCity] = useState<string>('all')
  const [selectedItemType, setSelectedItemType] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [uploadedData, setUploadedData] = useState<ExportItem[]>([])
  const [showPreviewDialog, setShowPreviewDialog] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { data: itemTypesData = [] } = useItemTypesData()
  const { data: citiesData = [] } = useCitiesData()
  const { downloadTemplateAsync, isPending } = useDownloadItemsTemplate()

  const itemTypeOptions = [
    { label: 'Tất cả nhóm món', value: 'all' },
    ...itemTypesData
      .filter(itemType => itemType.active === 1)
      .map(itemType => ({
        label: itemType.item_type_name,
        value: itemType.id
      }))
  ]

  const cityOptions = [
    { label: 'Tất cả thành phố', value: 'all' },
    ...citiesData
      .filter(city => city.active === 1)
      .map(city => ({
        label: city.city_name,
        value: city.id
      }))
  ]

  const statusOptions = [
    { label: 'Tất cả trạng thái', value: 'all' },
    { label: 'Active', value: '1' },
    { label: 'Deactive', value: '0' }
  ]

  const handleDownloadTemplate = async () => {
    try {
      const blob = await downloadTemplateAsync({
        city_uid: selectedCity !== 'all' ? selectedCity : undefined,
        item_type_uid: selectedItemType !== 'all' ? selectedItemType : undefined,
        active: selectedStatus !== 'all' ? selectedStatus : undefined
      })

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `danh-sach-mon-${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success('Tải xuống template thành công!')
    } catch (_error) {
      toast.error('Lỗi khi tải template')
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        if (jsonData.length > 0) {
          // Convert raw Excel data to ExportItem format
          const rawData = jsonData as (string | number)[][]
          const headers = rawData[0] || []
          const rows = rawData.slice(1)

          const exportItems: ExportItem[] = rows.map((row, index) => {
            const item: any = { id: `temp_${index}` }
            headers.forEach((header, headerIndex) => {
              const key = String(header).toLowerCase().replace(/\s+/g, '_')
              item[key] = row[headerIndex] || ''
            })
            return item as ExportItem
          })

          setUploadedData(exportItems)
          setShowPreviewDialog(true)
          toast.success('File uploaded successfully')
        }
      } catch (_error) {
        toast.error('Error parsing file')
      }
    }
    reader.readAsArrayBuffer(file)
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }



  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-w-2xl lg:max-w-xl'>
          <DialogHeader>
            <div className='flex items-center justify-between'>
              <DialogTitle className='text-xl font-semibold'>Xuất, sửa thực đơn</DialogTitle>
            </div>
          </DialogHeader>

          <div className='space-y-6'>
            <div className='space-y-4'>
              <h3 className='text-lg font-medium'>Bước 1. Chỉnh bộ lọc để xuất file</h3>
              <div className='flex gap-4'>
                <Combobox
                  options={cityOptions}
                  value={selectedCity}
                  onValueChange={setSelectedCity}
                  placeholder='Tất cả thành phố'
                  searchPlaceholder='Tìm thành phố...'
                  className='flex-1'
                />

                <Combobox
                  options={itemTypeOptions}
                  value={selectedItemType}
                  onValueChange={setSelectedItemType}
                  placeholder='Tất cả nhóm món'
                  searchPlaceholder='Tìm nhóm món...'
                  className='flex-1'
                />

                <Combobox
                  options={statusOptions}
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                  placeholder='Tất cả trạng thái'
                  searchPlaceholder='Tìm trạng thái...'
                  className='flex-1'
                />
              </div>
            </div>

            <div className='space-y-4'>
              <h3 className='text-lg font-medium'>Bước 2. Tải file dữ liệu</h3>
              <div className='flex items-center justify-between'>
                <span className='text-muted-foreground text-sm'>Tải xuống</span>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleDownloadTemplate}
                  disabled={isPending}
                  className='flex items-center gap-2'
                >
                  <DownloadIcon className='h-4 w-4' />
                  {isPending && 'Đang tải...'}
                </Button>
              </div>
            </div>

            <div className='space-y-4'>
              <h3 className='text-lg font-medium'>Bước 3. Thêm cấu hình vào file</h3>
              <div className='space-y-2'>
                <p className='text-muted-foreground text-sm'>Không sửa các cột :</p>
                <p className='font-mono text-sm text-blue-600'>ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại.</p>
              </div>
            </div>

            <div className='space-y-4'>
              <h3 className='text-lg font-medium'>Bước 4. Tải file lên</h3>
              <div className='flex items-center justify-between'>
                <span className='text-muted-foreground text-sm'>Sau khi đã điền đầy đủ bạn có thể tải file lên</span>
                <Button variant='outline' size='sm' onClick={handleUploadClick} className='flex items-center gap-2'>
                  Tải file lên
                  <UploadIcon className='h-4 w-4' />
                </Button>
                <input
                  ref={fileInputRef}
                  type='file'
                  accept='.xlsx,.xls'
                  onChange={handleFileUpload}
                  style={{ display: 'none' }}
                />
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <ExcelPreviewExportDialog
        open={showPreviewDialog}
        onOpenChange={setShowPreviewDialog}
        data={uploadedData}
      />
    </>
  )
}
