import{h as u,b as h,j as t,B as x}from"./index-UcdZ5AHH.js";import{c as C}from"./crm-api-APQEjHWd.js";import{a as f}from"./pos-api-j20LMGrC.js";import{u as k}from"./useMutation-q12VR5WX.js";import{b as g}from"./use-auth-Ds6PM7xW.js";import{u as j}from"./use-pos-data-nGOqH6A5.js";import{M as y}from"./main-C1Ukb9JX.js";import{C as b,a as M,b as N,c as v,d as R}from"./card-ulE1yKb5.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{c as w}from"./createReactComponent-C1S2Ujit.js";import{I}from"./IconLoader2-BDeN03hy.js";import"./utils-km2FGkQ4.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./select-DOexGcsG.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var P=w("outline","link","IconLink",[["path",{d:"M9 15l6 -6",key:"svg-0"}],["path",{d:"M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464",key:"svg-1"}],["path",{d:"M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463",key:"svg-2"}]]);const _=async e=>f.post("/pos/v1/connect-crm",e),K=async(e,r)=>{try{const n=await C.post("/api/auth-by-token",{token:e});return n.data&&n.data.token&&(localStorage.setItem("crm_token",n.data.token),r&&r()),n.data}catch(n){throw console.error("CRM authentication failed:",n),n}};function L(){return k({mutationFn:async e=>(await _(e)).data})}function T(){const e=u(),r=L(),{brands:n}=j(),{user:a}=g(),{company:o}=h(),d=i=>{try{return new URL(i).searchParams.get("token")}catch{return null}},p=async()=>{var i;if(!(!(a!=null&&a.email)||!(o!=null&&o.id)||!a.company_uid))try{const s={brand_uid:n[0].id,company_uid:o.id,email:a.email},c=await r.mutateAsync(s);if((i=c.data)!=null&&i.link){const l=d(c.data.link);if(l){const m=await K(l,()=>{e({to:"/crm/customer-list"})});m.status==="OK"&&m.token&&(localStorage.setItem("crm_token",m.token),e({to:"/crm/customer-list"}))}}}catch(s){console.error("Error connecting to CRM:",s)}};return t.jsx(y,{children:t.jsx("div",{className:"container mx-auto flex min-h-screen items-center justify-center py-6",children:t.jsxs("div",{className:"space-y-4 text-center",children:[t.jsxs("div",{children:[t.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:"Kết nối CRM"}),t.jsx("p",{className:"text-muted-foreground",children:"Thiết lập kết nối với hệ thống CRM để đồng bộ dữ liệu"})]}),t.jsxs(b,{className:"max-w-md",children:[t.jsxs(M,{className:"pb-3",children:[t.jsxs(N,{className:"flex items-center justify-center gap-2 text-lg font-semibold",children:[t.jsx(P,{className:"size-5 text-blue-600"}),"Kết nối hệ thống"]}),t.jsx(v,{children:"Nhấn nút bên dưới để bắt đầu quá trình kết nối CRM"})]}),t.jsx(R,{className:"pt-0",children:t.jsxs(x,{onClick:p,disabled:r.isPending,className:"w-full",size:"lg",children:[r.isPending&&t.jsxs(t.Fragment,{children:[t.jsx(I,{className:"mr-2 size-4 animate-spin"}),"Đang kết nối..."]}),!r.isPending&&"Kết nối CRM"]})})]})]})})})}const at=T;export{at as component};
