import{R as t,j as e,c as m}from"./index-CfbMU4Ye.js";import{S as l}from"./separator-DVvwOaSX.js";import{n as i}from"./date-range-picker-FRR8J6T3.js";const p=({className:a,fixed:s,children:o,...n})=>{const[c,d]=t.useState(0);return t.useEffect(()=>{const r=()=>{d(document.body.scrollTop||document.documentElement.scrollTop)};return document.addEventListener("scroll",r,{passive:!0}),()=>document.removeEventListener("scroll",r)},[]),e.jsxs("header",{className:m("bg-background flex h-16 items-center gap-3 p-4 sm:gap-4",s&&"header-fixed peer/header fixed z-50 w-[inherit] rounded-md",c>10&&s?"shadow-sm":"shadow-none",a),...n,children:[e.jsx(i,{variant:"outline",className:"scale-125 sm:scale-100"}),e.jsx(l,{orientation:"vertical",className:"h-6"}),o]})};p.displayName="Header";export{p as H};
