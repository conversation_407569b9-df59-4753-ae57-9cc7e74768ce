import{r as s,j as n,B as b}from"./index-UcdZ5AHH.js";import{C as f,d as x}from"./card-ulE1yKb5.js";import{C as v,o as u,p as k,q as D,r as S}from"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{D as j,a as N,b as T,c as y}from"./dialog-DmI079wB.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./select-DOexGcsG.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";function I({open:o,onOpenChange:m,device:t}){const[h,g]=s.useState(0),[e,r]=s.useState();if(s.useEffect(()=>{if(!e)return;const i=()=>{g(e.selectedScrollSnap())};return e.on("select",i),i(),()=>{e.off("select",i)}},[e]),!t)return null;const c=t.name.replace("Thiết bị ",""),l=i=>{e&&e.scrollTo(i)};return n.jsx(j,{open:o,onOpenChange:m,children:n.jsxs(N,{className:"!h-[600px] !max-h-[600px] !w-[1400px] !max-w-[1400px] overflow-hidden",children:[n.jsx(T,{children:n.jsx(y,{className:"text-center text-2xl font-bold",children:c})}),n.jsxs("div",{className:"mt-6 grid h-[500px] grid-cols-2 gap-8",children:[n.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[n.jsxs(v,{setApi:r,className:"!w-[700px]",children:[n.jsx(u,{children:t.images.map((i,a)=>n.jsx(k,{children:n.jsx("div",{className:"h-[400px] w-[700px] overflow-hidden rounded-lg border bg-gray-50",children:n.jsx("img",{src:i,alt:`${c} - Image ${a+1}`,className:"h-full w-full object-contain p-4",onError:p=>{const d=p.target;d.src="/placeholder-image.jpg"}})})},a))}),t.images.length>1&&n.jsxs(n.Fragment,{children:[n.jsx(D,{className:"top-1/2 left-2 -translate-y-1/2"}),n.jsx(S,{className:"top-1/2 right-2 -translate-y-1/2"})]})]}),t.images.length>1&&n.jsx("div",{className:"flex space-x-2",children:t.images.map((i,a)=>n.jsx("button",{onClick:()=>l(a),className:`h-3 w-3 rounded-full transition-all duration-200 ${h===a?"scale-110 bg-blue-500":"bg-gray-300 hover:bg-gray-400"}`,"aria-label":`Go to image ${a+1}`},a))})]}),n.jsx("div",{className:"h-full overflow-y-auto pr-4",children:n.jsx("ul",{className:"space-y-3 text-lg leading-relaxed text-gray-700",children:(t.detailInformation||[t.description]).map((i,a)=>n.jsxs("li",{className:"flex items-start",children:[n.jsx("span",{className:"mt-1 mr-2 text-blue-500",children:"•"}),n.jsx("span",{children:i})]},a))})})]})]})})}const O=[{id:"pos",name:"Thiết bị POS",description:"Thiết bị bán hàng đáp ứng các nghiệp vụ kinh doanh nhà hàng.",image:"https://image.foodbook.vn/images/20210114/1610594463910-iAP200-600x600.jpg",images:["https://image.foodbook.vn/images/20210114/1610594463910-iAP200-600x600.jpg","https://image.foodbook.vn/images/20210115/1610679782642-POS1.png","https://image.foodbook.vn/images/20210115/1610679798609-POS2.png","https://image.foodbook.vn/images/20210115/1610679809909-POS3.png","https://image.foodbook.vn/images/20210115/1610679823366-POS4.png"],detailInformation:["Thiết bị bán hàng đáp ứng các nghiệp vụ kinh doanh nhà hàng."]},{id:"pos-mini",name:"Thiết bị POS MINI",description:"Thiết bị bán hàng cầm tay nhỏ gọn, phù hợp cho nhà hàng vừa và nhỏ.",image:"https://image.foodbook.vn/images/20210114/1610594509383-B06-2-600x600.jpg",images:["https://image.foodbook.vn/images/20210114/1610594509383-B06-2-600x600.jpg","https://image.foodbook.vn/images/20210114/1610618634292-POS_MINI_0.png","https://image.foodbook.vn/images/20210114/1610618649865-POS_MINI_1.png","https://image.foodbook.vn/images/20210114/1610618664516-POS_MINI_2.png"],detailInformation:["Thiết bị bán hàng cầm tay nhỏ gọn, phù hợp cho nhà hàng vừa và nhỏ.","Giao diện được tối ưu theo chiều dọc."]},{id:"pda",name:"Thiết bị PDA",description:"Thiết bị cầm tay cá nhân được tích hợp chức năng order giúp nhân viên thuận tiện đặt món cho khách.",image:"https://image.foodbook.vn/images/20210114/1610594699308-TAB-A8-1-1-600x600.png",images:["https://image.foodbook.vn/images/20210114/1610594699308-TAB-A8-1-1-600x600.png","https://image.foodbook.vn/images/20210115/1610679414089-PDA1.png","https://image.foodbook.vn/images/20210115/1610679436448-PDA2.png"],detailInformation:["Thiết bị cầm tay cá nhân được tích hợp chức năng order giúp nhân viên thuận tiện đặt món cho khách.","PDA Fabi chỉ dùng để oder không có chức năng thanh toán.","Để sử dụng PDA cần phải chọn 1 POS làm máy chủ."]},{id:"kds",name:"Thiết bị KDS",description:"Thiết bị hiển thị danh sách món order dành cho bếp, dùng để thay thế máy in order, theo dõi tình hình chế biến món ăn trực quan, tức thời.",image:"https://image.foodbook.vn/images/20210114/1610596408975-KDS.png",images:["https://image.foodbook.vn/images/20210114/1610596408975-KDS.png","https://image.foodbook.vn/images/20210115/1610677692502-KDS1.png","https://image.foodbook.vn/images/20210115/1610677784222-KDS2.png","https://image.foodbook.vn/images/20210115/1610677754310-KDS3.png","https://image.foodbook.vn/images/20210115/1610677857417-KDS4.png"],detailInformation:["Thiết bị hiển thị danh sách món order dành cho bếp, dùng để thay thế máy in order, theo dõi tình hình chế biến món ăn trực quan, tức thời."]},{id:"kds-maker",name:"Thiết bị KDS MAKER",description:"Thiết bị giúp đầu bếp, nhân viên pha chế nhận đồ và chế biến, kết hợp với in tem, in phiếu trả đồ.",image:"https://image.foodbook.vn/images/20210114/1610596408975-KDS.png",images:["https://image.foodbook.vn/images/20210114/1610596408975-KDS.png","https://image.foodbook.vn/images/20210115/1610678602088-KDS5.png"],detailInformation:["Thiết bị giúp đầu bếp, nhân viên pha chế nhận đồ và chế biến, kết hợp với in tem, in phiếu trả đồ.","KDS MAKER kết hợp với KDS ORDER CONTROL để hiển thị món, hoá đơn đã hoàn thành."]},{id:"kds-order-control",name:"Thiết bị KDS ORDER CONTROL",description:"Thiết bị phục vụ cho việc quản lý trả đồ hoặc thay thế máy in order, phù hợp cho mô hình bán mang về.",image:"https://image.foodbook.vn/images/20210114/1610596408975-KDS.png",images:["https://image.foodbook.vn/images/20210114/1610596408975-KDS.png","https://image.foodbook.vn/images/20210115/1610678602088-KDS5.png"],detailInformation:["Thiết bị phục vụ cho việc quản lý trả đồ hoặc thay thế máy in order, phù hợp cho mô hình bán mang về.","Thường sử dụng kết hợp KDS ORDER CONTROL với KDS MAKER cho mô hình bán mang về, kitchen cloud."]},{id:"self-order",name:"Thiết bị SELF ORDER",description:"Thiết bị hỗ trợ khách hàng có thể tự đặt đồ, lựa chọn sản phẩm và thanh toán.",image:"https://image.foodbook.vn/images/20210114/1610594725602-may-self-order-co-chan-PNG.png",images:["https://image.foodbook.vn/images/20210114/1610594725602-may-self-order-co-chan-PNG.png","https://image.foodbook.vn/images/20210115/1610678935514-SO-1.png"],detailInformation:["Thiết bị hỗ trợ khách hàng có thể tự đặt đồ, lựa chọn sản phẩm và thanh toán.","Nhờ hình thức này, nhà hàng/quán café có thể tiết kiệm chi phí nhân sự, tối ưu thời gian phục vụ."]}];function C(){const[o,m]=s.useState(null),[t,h]=s.useState(!1),g=e=>{m(e),h(!0)};return n.jsxs("div",{className:"container mx-auto px-4 py-8",children:[n.jsx("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:O.map(e=>n.jsxs(f,{className:"flex h-full flex-col overflow-hidden",children:[n.jsx("div",{className:"relative h-48 bg-gray-100",children:n.jsx("div",{className:"absolute inset-0 bg-contain bg-center bg-no-repeat",style:{backgroundImage:`url('${e.image}')`}})}),n.jsxs(x,{className:"flex flex-1 flex-col p-6 text-center",children:[n.jsx("h5",{className:"mb-2 text-lg font-semibold",children:e.name}),n.jsx("p",{className:"text-muted-foreground mb-4 line-clamp-2 flex-1 text-sm leading-relaxed",children:e.description}),n.jsx(b,{variant:"default",size:"sm",className:"px-3 py-1 text-xs",onClick:()=>g(e),children:"Chi tiết"})]})]},e.id))}),n.jsx(I,{open:t,onOpenChange:h,device:o?{id:o.id,name:o.name,description:o.description,images:o.images||[o.image],detailInformation:o.detailInformation}:null})]})}const J=C;export{J as component};
