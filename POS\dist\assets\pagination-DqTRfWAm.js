import{e as N,h as g,r as t,j as s,c as S,B as u}from"./index-CfbMU4Ye.js";import{b as I,c as M,d as T,e as R}from"./react-icons.esm-DefBGHOQ.js";import{S as A,a as D,b as E,c as V,d as $}from"./select-_nXsh5SU.js";function q({currentPage:e,totalPages:c,pageSize:l,totalItems:i,onPageChange:r,onPageSizeChange:o,showPageSizeSelector:a=!0,showItemCount:v=!0,showFirstLastButtons:p=!0,pageSizeOptions:b=[10,20,50,100],className:w,disabled:m=!1}){const x=g(),d=N({strict:!1}),h=t.useCallback(n=>{r?r(n):x({search:{...d,page:n}})},[r,x,d]),C=t.useCallback(n=>{o?o(n):x({search:{...d,page:1,pageSize:n}})},[o,x,d]),f=e>1,j=e<c,k=i===0?0:(e-1)*l+1,y=Math.min(e*l,i);return s.jsxs("div",{className:S("flex items-center justify-between overflow-clip px-2 py-2",w),style:{overflowClipMargin:1},children:[v&&s.jsxs("div",{className:"text-muted-foreground hidden flex-1 text-sm sm:block",children:["Hiển thị ",k," - ",y," / ",i]}),s.jsxs("div",{className:"flex items-center sm:space-x-6 lg:space-x-8",children:[a&&s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("p",{className:"hidden text-sm font-medium sm:block",children:"Số hàng mỗi trang"}),s.jsxs(A,{value:`${l}`,onValueChange:n=>C(Number(n)),disabled:m,children:[s.jsx(D,{className:"h-8 w-[70px]",children:s.jsx(E,{placeholder:l})}),s.jsx(V,{side:"top",children:b.map(n=>s.jsx($,{value:`${n}`,children:n},n))})]})]}),s.jsxs("div",{className:"flex w-[100px] items-center justify-center text-sm font-medium",children:["Trang ",e," / ",c]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[p&&s.jsxs(u,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>h(1),disabled:!f||m,children:[s.jsx("span",{className:"sr-only",children:"Đi đến trang đầu"}),s.jsx(I,{className:"h-4 w-4"})]}),s.jsxs(u,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>h(e-1),disabled:!f||m,children:[s.jsx("span",{className:"sr-only",children:"Trang trước"}),s.jsx(M,{className:"h-4 w-4"})]}),s.jsxs(u,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>h(e+1),disabled:!j||m,children:[s.jsx("span",{className:"sr-only",children:"Trang sau"}),s.jsx(T,{className:"h-4 w-4"})]}),p&&s.jsxs(u,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>h(c),disabled:!j||m,children:[s.jsx("span",{className:"sr-only",children:"Đi đến trang cuối"}),s.jsx(R,{className:"h-4 w-4"})]})]})]})]})}function z(){const e=N({strict:!1}),c=g(),l=t.useMemo(()=>{const a=e==null?void 0:e.page;return typeof a=="number"&&a>0?a:1},[e==null?void 0:e.page]),i=t.useMemo(()=>{const a=e==null?void 0:e.pageSize;return typeof a=="number"&&a>0?a:10},[e==null?void 0:e.pageSize]),r=t.useCallback(a=>{c({search:{...e,page:a}})},[c,e]),o=t.useCallback(a=>{c({search:{...e,page:1,pageSize:a}})},[c,e]);return{currentPage:l,pageSize:i,setPage:r,setPageSize:o}}export{q as P,z as u};
