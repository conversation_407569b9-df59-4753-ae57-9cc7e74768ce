import{u as l}from"./useQuery-HgcIHxlE.js";import{u as y,a3 as p,a4 as h}from"./index-CVQ6JZo2.js";import{u as g}from"./useMutation-ZsyDznMu.js";import{p as _}from"./payment-methods-api-CVNkcw7x.js";import{Q as m}from"./query-keys-3lmd-xp6.js";const T=e=>({id:e.id,code:e.payment_method_id,name:e.payment_method_name,cardProcessingFee:e.payment_fee_extra,feeType:e.payment_fee_type,description:e.description,imagePath:e.image_path,isActive:e.active===1,storeCount:e.stores,storeUids:e.list_payment_method_uid||[],createdAt:new Date(e.created_at*1e3),updatedAt:new Date(e.updated_at*1e3),createdBy:e.created_by,updatedBy:e.updated_by,brandUid:e.brand_uid,companyUid:e.company_uid,sort:e.sort,extraData:e.extra_data?{requireTraceno:e.extra_data.require_traceno===1}:void 0}),E=(e={})=>{const{params:a={},enabled:o=!0}=e,{company:n,brands:i}=y(s=>s.auth),r=i==null?void 0:i[0],u={...{company_uid:(n==null?void 0:n.id)||"",brand_uid:(r==null?void 0:r.id)||"",page:1},...a},c=!!(n!=null&&n.id&&(r!=null&&r.id));return l({queryKey:[m.PAYMENT_METHODS_LIST,u],queryFn:async()=>{var d;return((d=(await _.getPaymentMethods(u)).data)==null?void 0:d.map(T))||[]},enabled:o&&c,staleTime:10*60*1e3,refetchInterval:5*60*1e3})},S=(e={})=>{var n;const a=E({params:{store_uid:e.storeUid},enabled:!!e.storeUid}),o=(n=a.data)==null?void 0:n.filter(i=>{if(e.searchTerm){const r=e.searchTerm.toLowerCase();if(!(i.name.toLowerCase().includes(r)||i.code.toLowerCase().includes(r)||i.description.toLowerCase().includes(r)))return!1}return!(e.isActive!==void 0&&i.isActive!==e.isActive)});return{...a,data:o||[]}},b=()=>{const e=p(),{company:a,brands:o}=y(t=>t.auth),n=o==null?void 0:o[0],{mutate:i,isPending:r}=g({mutationFn:async t=>{const u=(a==null?void 0:a.id)||"",c=(n==null?void 0:n.id)||"";if(!u||!c)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");let s="";t.logoFile&&(s=(await _.uploadImage(t.logoFile)).data.image_url);const d={payment_method_name:t.payment_method_name,payment_method_id:t.payment_method_id||`PAYMENT_METHOD-${Date.now()}`,payment_fee_extra:t.payment_fee_extra,payment_fee_type:t.payment_fee_type,payment_type:0,stores:t.stores,config_keys:{},company_uid:u,brand_uid:c,background:s,image_path:s,extra_data:t.extra_data};return await _.createPaymentMethod(d)},onSuccess:()=>{e.invalidateQueries({queryKey:[m.PAYMENT_METHODS_LIST]}),h.success("Tạo phương thức thanh toán thành công")},onError:t=>{var c,s;const u=((s=(c=t==null?void 0:t.response)==null?void 0:c.data)==null?void 0:s.message)||"Có lỗi xảy ra khi tạo phương thức thanh toán";h.error(u)}});return{createPaymentMethod:i,isCreating:r}},v=()=>{const e=p(),{company:a,brands:o}=y(t=>t.auth),n=o==null?void 0:o[0],{mutate:i,isPending:r}=g({mutationFn:async t=>{const u=(a==null?void 0:a.id)||"",c=(n==null?void 0:n.id)||"";if(!u||!c)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");let s="";t.logoFile&&(s=(await _.uploadImage(t.logoFile)).data.image_url);const d={id:t.id,payment_method_id:t.payment_method_id,payment_method_name:t.payment_method_name,payment_type:0,sort:20,is_fb:0,extra_data:t.extra_data,active:1,revision:0,brand_uid:c,company_uid:u,is_fabi:1,payment_fee_extra:t.payment_fee_extra,payment_fee_type:t.payment_fee_type,stores:t.stores,config_keys:{}};return s&&(d.background=s,d.image_path=s),await _.updatePaymentMethod(d)},onSuccess:()=>{e.invalidateQueries({queryKey:[m.PAYMENT_METHODS_LIST]}),e.invalidateQueries({queryKey:[m.PAYMENT_METHODS_DETAIL]}),h.success("Cập nhật phương thức thanh toán thành công")},onError:t=>{var c,s;const u=((s=(c=t==null?void 0:t.response)==null?void 0:c.data)==null?void 0:s.message)||"Có lỗi xảy ra khi cập nhật phương thức thanh toán";h.error(u)}});return{updatePaymentMethod:i,isUpdating:r}},C=()=>{const e=p(),{company:a,brands:o}=y(t=>t.auth),n=o==null?void 0:o[0],{mutate:i,isPending:r}=g({mutationFn:async t=>{const u=(a==null?void 0:a.id)||"",c=(n==null?void 0:n.id)||"";if(!u||!c)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");await _.deletePaymentMethod({id:t,company_uid:u,brand_uid:c})},onSuccess:()=>{e.invalidateQueries({queryKey:[m.PAYMENT_METHODS_LIST]}),h.success("Xóa phương thức thanh toán thành công")},onError:t=>{var c,s;const u=((s=(c=t==null?void 0:t.response)==null?void 0:c.data)==null?void 0:s.message)||"Có lỗi xảy ra khi xóa phương thức thanh toán";h.error(u)}});return{deletePaymentMethod:i,isDeleting:r}},q=e=>{const{company:a,brands:o}=y(i=>i.auth),n=o==null?void 0:o[0];return l({queryKey:[m.PAYMENT_METHODS_DETAIL,e],queryFn:async()=>{const i=(a==null?void 0:a.id)||"",r=(n==null?void 0:n.id)||"";if(!i||!r||!e)throw new Error("Thiếu thông tin cần thiết");return await _.getPaymentMethodDetail(e,i,r)},enabled:!!(a!=null&&a.id&&(n!=null&&n.id)&&e),staleTime:5*60*1e3})};export{S as a,b,q as c,v as d,C as u};
