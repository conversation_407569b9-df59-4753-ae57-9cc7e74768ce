import{c as d}from"./createReactComponent-DSXPaZ4c.js";import{j as e}from"./index-CVQ6JZo2.js";import{S as o}from"./skeleton-CzG-rpGS.js";import{T as p,a as f,b as n,c as u,d as g,e as x}from"./table-BOc3nItc.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var y=d("outline","file-import","IconFileImport",[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M5 13v-8a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2h-5.5m-9.5 -2h7m-3 -3l3 3l-3 3",key:"svg-1"}]]);function S({rows:r=5,columns:a=4,showHeader:t=!0,className:c="",variant:h="default"}){const s=(()=>{switch(h){case"compact":return"h-3";case"spacious":return"h-5";default:return"h-4"}})();return e.jsx("div",{className:`rounded-md border ${c}`,children:e.jsxs(p,{children:[t&&e.jsx(f,{children:e.jsx(n,{children:Array.from({length:a}).map((i,l)=>e.jsx(u,{children:e.jsx(o,{className:`${s} w-full`})},l))})}),e.jsx(g,{children:Array.from({length:r}).map((i,l)=>e.jsx(n,{children:Array.from({length:a}).map((T,m)=>e.jsx(x,{children:e.jsx(o,{className:`${s} w-full`})},m))},l))})]})})}const H=()=>{const r="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let a="";for(let t=0;t<4;t++)a+=r.charAt(Math.floor(Math.random()*r.length));return`ITEM_TYPE-${a}`};export{y as I,S,H as g};
