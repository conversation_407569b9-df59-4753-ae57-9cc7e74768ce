import{j as e,B,r as h,l as F}from"./index-CVQ6JZo2.js";import{H as I}from"./header-DiKooCuw.js";import{P as z}from"./profile-dropdown-BEO1YZeq.js";import{S as U,T as X}from"./search-bbGy26eb.js";import"./date-range-picker-CXbMaowj.js";import"./form-CzmGigtT.js";import{S as C,a as R,b as M,c as L,d as b}from"./select-BFhNE0YE.js";import{D as V}from"./date-range-picker-CDswWfuX.js";import{S as $}from"./search-B4Rlb4i6.js";import{u as Z,c as J,e as Q,f as k}from"./index-BCpVAkPF.js";import{D as W}from"./data-table-pagination-_F9gEREo.js";import{T as ee,a as te,b as T,c as ae,d as se,e as D}from"./table-BOc3nItc.js";import{c as re}from"./crm-api-CDzLLTww.js";import"./pos-api-mRg02iop.js";import{f as v}from"./isSameMonth-C8JQo-AN.js";import"./separator-BcoNozmD.js";import"./avatar-q0u2_bqW.js";import"./dropdown-menu-lhfGm_WJ.js";import"./index-LVHINuqD.js";import"./index-CtK-wKtB.js";import"./index-C34iUvGy.js";import"./check-BE_j5GZD.js";import"./createLucideIcon-DKVxsQv7.js";import"./search-context-CkCLuJFL.js";import"./command-Nb4B17YQ.js";import"./calendar-BszTCdZH.js";import"./dialog-DDrduXt3.js";import"./createReactComponent-DSXPaZ4c.js";import"./scroll-area-CGsZUbT-.js";import"./IconChevronRight-LXWXuzjR.js";import"./IconSearch-DjIF9VGJ.js";import"./chevron-right-CxgpqvrH.js";import"./react-icons.esm-DMMA_g0o.js";import"./popover-DnoSPJNX.js";import"./index-nc1u7392.js";import"./calendar-CYB-o1z2.js";const N={FROM:new Date(2025,6,29),TO:new Date(2025,7,13)},O={getSystemLogs:async a=>{try{const s=new URLSearchParams({start_time:a.start_time,end_time:a.end_time,pos_parent:a.pos_parent});a.page&&s.set("page",a.page.toString()),a.limit&&s.set("limit",a.limit.toString());const i=await re.get(`/settings/system-log?${s.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from system log API");return i.data}catch(s){throw console.error("Error fetching system logs:",s),s}}},q=a=>v(a,"yyyy-MM-dd 00:00:00"),E=a=>v(a,"yyyy-MM-dd 23:59:59");function ne({filters:a,onFilterChange:s,onDateRangeChange:i,onSearch:m,availableRequestPaths:c,availableUsers:u,isLoading:r}){const n=t=>{i(t||null,a.dateRange.to)},p=t=>{i(a.dateRange.from,t||null)};return e.jsx("div",{className:"space-y-4 rounded-lg border bg-white p-4",children:e.jsxs("div",{className:"flex items-center justify-start gap-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Thời gian"}),e.jsx(V,{startDate:a.dateRange.from||void 0,endDate:a.dateRange.to||void 0,onStartDateChange:n,onEndDateChange:p,placeholder:"Chọn khoảng thời gian",disabled:r})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Thao tác"}),e.jsxs(C,{value:a.request_path,onValueChange:t=>s("request_path",t),children:[e.jsx(R,{children:e.jsx(M,{placeholder:"Chọn đường dẫn API"})}),e.jsxs(L,{children:[e.jsx(b,{value:"all",children:"Tất cả"}),c.map(t=>e.jsx(b,{value:t.value,children:t.label},t.value))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"User"}),e.jsxs(C,{value:a.user_name,onValueChange:t=>s("user_name",t),children:[e.jsx(R,{children:e.jsx(M,{placeholder:"Chọn user"})}),e.jsxs(L,{children:[e.jsx(b,{value:"all",children:"Tất cả"}),u.map(t=>e.jsx(b,{value:t.value,children:t.label},t.value))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"."}),e.jsxs(B,{onClick:m,disabled:r,className:"w-full",children:[e.jsx($,{className:"mr-2 h-4 w-4"}),r?"Đang tìm...":"Lọc"]})]})]})})}const oe={"/crm/setting/crm_config/update":"SYSTEM_LOG:crm:settingupdate","/crm/setting/update_pos_parent":"Cập nhật thông tin thương hiệu","/crm/setting/update-setting-config":"SYSTEM_LOG:crm:settingupdate","/crm/setting/update-setting-loyalty":"Cập nhật chương trình thành viên","/crm/setting/update-cheat-config":"Cập nhật cảnh báo giao dịch","/crm/loyalty/membership_type/update":"SYSTEM_LOG.crmloyaltymembership_typeupdate","/crm/loyalty/membership_type/create":"SYSTEM_LOG.crmloyaltymembership_typecreate","/crm/maketing/update_campaign":"Cập nhật CTKM","/crm/setting/register_form/config":"SYSTEM_LOG.crmsettingregister_formconfig","/crm/loyalty/reset_point":"Reset điểm hội viên","/crm/loyalty/membership_type_extra_rate/update":"SYSTEM_LOG.crmloyaltymembership_type_extra_rateupdate","/crm/loyalty/membership_type_extra_rate/create":"SYSTEM_LOG.crmloyaltymembership_type_extra_ratecreate","/crm/maketing/export_vouchers":"Xuất danh sách mã voucher","/crm/setting/sync":"Đồng bộ","/crm/setting/update_warning_cheat_config":"SYSTEM_LOG.crmsettingupdate_warning_cheat_config","/crm/loyalty/loyalty_config/update":"SYSTEM_LOG.crmloyaltyloyalty_configupdate","/crm/report/sale_manager":"SYSTEM_LOG.crmreportsale_manager","/crm/setting/delivery/config":"SYSTEM_LOG.crmsettingdeliveryconfig","/crm/maketing/create_trigger":"Tạo trigger","/crm/loyalty/company_membership/update/point":"SYSTEM_LOG.crmloyaltymembership_typecreate","/crm/filter/bulk_update/add_tag":"SYSTEM_LOG.crmloyaltycompany_membershipupdatemembership_type","/crm/member/update":"SYSTEM_LOG.crmmemberupdate","/crm/loyalty/company_membership/update/membership_type":"SYSTEM_LOG.crmloyaltycompany_membershipupdatemembership_type","/crm/common/update_marketing_metric":"SYSTEM_LOG.crmloyaltymembership_typeupdate","/crm/account/update":"SYSTEM_LOG.crmloyaltymembership_typeupdate","/crm/customer-care/scenario/reopen":"Mở lại kịch bản CSKH","/crm/customer-care/schedule/create":"Tạo lịch CSKH","/crm/marketing/voucher/export-history":"Xuất lịch sử mã voucher từ CTKM","/crm/marketing/broadcast/send":"Gửi broadcast","/crm/customer-care/scenario/update":"Cập nhật kịch bản CSKH","/crm/marketing/voucher/reactivate":"Kích hoạt lại mã voucher","/crm/o2o/config/create":"Tạo cấu hình O2O","/crm/o2o/config/update":"Cập nhật cấu hình O2O","/crm/online-sales/time-config/create":"Thêm cấu hình thời gian bán online","/crm/menu/parent-item/create":"Tạo món cha","/crm/online-sales/time-config/delete":"Xóa thời gian bán online","/crm/facebook/page/connect":"Kết nối Facebook page","/crm/facebook/config/save":"Lưu cấu hình Facebook","/crm/menu/sync":"Đồng bộ món","/crm/zalo/config/update":"Cập nhật cấu hình Zalo","/crm/online-sales/time-config/update":"Cập nhật thời gian bán online"};function w(a){return oe[a]||a}const ce=["Reset điểm hội viên","Mở lại kịch bản CSKH","Tạo lịch CSKH","Tạo trigger","Xuất lịch sử mã voucher từ CTKM","Xuất danh sách mã voucher","Gửi broadcast","Cập nhật CTKM","Cập nhật kịch bản CSKH","Kích hoạt lại mã voucher","Tạo cấu hình O2O","Cập nhật cấu hình O2O","Thêm cấu hình thời gian bán online","Tạo món cha","Xóa thời gian bán online","Kết nối Facebook page","Đồng bộ","Lưu cấu hình Facebook","Đồng bộ món","Cập nhật cấu hình Zalo","Cập nhật thông tin thương hiệu","Cập nhật thời gian bán online"];function ie({logs:a,isLoading:s,formatTimestamp:i}){var u;const m=[{id:"index",header:"#",cell:({row:r})=>e.jsx("div",{className:"w-[50px]",children:r.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"request_path",header:"Thao tác",size:500,cell:({getValue:r})=>{const n=r();return w(n)}},{accessorKey:"user_name",header:"User",size:250},{accessorKey:"request_at",header:"Thời gian",size:250,cell:({getValue:r})=>{const n=r();return i(n)}}],c=Z({data:a,columns:m,getCoreRowModel:Q(),getPaginationRowModel:J(),initialState:{pagination:{pageSize:20}}});return s?e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải dữ liệu nhật ký..."})}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(ee,{children:[e.jsx(te,{children:c.getHeaderGroups().map(r=>e.jsx(T,{children:r.headers.map(n=>e.jsx(ae,{style:{width:n.getSize()},children:n.isPlaceholder?null:k(n.column.columnDef.header,n.getContext())},n.id))},r.id))}),e.jsx(se,{children:(u=c.getRowModel().rows)!=null&&u.length?c.getRowModel().rows.map(r=>e.jsx(T,{"data-state":r.getIsSelected()&&"selected",children:r.getVisibleCells().map(n=>e.jsx(D,{children:k(n.column.columnDef.cell,n.getContext())},n.id))},r.id)):e.jsx(T,{children:e.jsx(D,{colSpan:m.length,className:"h-24 text-center",children:"Không có dữ liệu nhật ký"})})})]})}),e.jsx(W,{table:c})]})}const me=a=>s=>i=>{if(!s||s==="all")return!0;const m=i[a];return typeof m!="string"?!1:m.toLowerCase().includes(s.toLowerCase())},le=[{id:"689bf187de86ec0001ef91b8",pos_parent:"BRAND-953H",user_id:"92930",user_name:"<EMAIL>",request_path:"/crm/setting/crm_config/update",request_data:"params: pos_parent=BRAND-953H&access_token=&session_token=",request_at:"2025-08-13 08:59:34",response_at:"2025-08-13 08:59:35",response:"250813-085934-076016"},{id:"689bf160de86ec0001ef91b7",pos_parent:"BRAND-953H",user_id:"92930",user_name:"<EMAIL>",request_path:"/crm/setting/crm_config/update",request_data:"params: pos_parent=BRAND-953H&access_token=&session_token=",request_at:"2025-08-13 08:58:56",response_at:"2025-08-13 08:58:56",response:"250813-085856-321062"},{id:"689bf160de86ec0001ef91b5",pos_parent:"BRAND-953H",user_id:"92930",user_name:"<EMAIL>",request_path:"/crm/setting/update_pos_parent",request_data:"pos_parent=BRAND-953H&access_token=&session_token=",request_at:"2025-08-13 08:58:56",response_at:"2025-08-13 08:58:56",response:"1"},{id:"689bf160de86ec0001ef91b6",pos_parent:"BRAND-953H",user_id:"92930",user_name:"<EMAIL>",request_path:"/crm/setting/update_pos_parent",request_data:"params: pos_parent=BRAND-953H&access_token=&session_token=&pos_parent=BRAND-953H&access_token=&session_token=d32367a00bbe63be56920bfe28fbe35204447296",request_at:"2025-08-13 08:58:56",response_at:"2025-08-13 08:58:56",response:"250813-085856-215811"},{id:"689bf1449c46170001a9c7d4",pos_parent:"BRAND-953H",user_id:"92930",user_name:"<EMAIL>",request_path:"/crm/setting/update_pos_parent",request_data:"pos_parent=BRAND-953H&access_token=&session_token=",request_at:"2025-08-13 08:58:28",response_at:"2025-08-13 08:58:28",response:"1"},{id:"689bf1449c46170001a9c7d5",pos_parent:"BRAND-953H",user_id:"92930",user_name:"<EMAIL>",request_path:"/crm/setting/update_pos_parent",request_data:"params: pos_parent=BRAND-953H&access_token=&session_token=&pos_parent=BRAND-953H&access_token=&session_token=d32367a00bbe63be56920bfe28fbe35204447296",request_at:"2025-08-13 08:58:28",response_at:"2025-08-13 08:58:28",response:"250813-085828-326940"}],pe=a=>{const s=["/crm/setting/crm_config/update","/crm/setting/update_pos_parent","/crm/setting/update-setting-config","/crm/setting/update-setting-loyalty","/crm/setting/update-cheat-config"],i=[{id:"92930",name:"<EMAIL>"},{id:"92931",name:"<EMAIL>"},{id:"92932",name:"<EMAIL>"},{id:"92933",name:"<EMAIL>"}],m=[];for(let c=0;c<a;c++){const u=s[Math.floor(Math.random()*s.length)],r=i[Math.floor(Math.random()*i.length)],p=new Date(Date.now()-Math.random()*30*24*60*60*1e3).toISOString().slice(0,19).replace("T"," ");m.push({id:`mock-${c+7}`,pos_parent:"BRAND-953H",user_id:r.id,user_name:r.name,request_path:u,request_data:"params: pos_parent=BRAND-953H&access_token=&session_token=",request_at:p,response_at:p,response:`${p.replace(/[-:\s]/g,"").slice(2)}-${Math.floor(Math.random()*999999).toString().padStart(6,"0")}`})}return m},S=[...le,...pe(136)];function de(){const[a,s]=h.useState(!1),[i,m]=h.useState(null),[c,u]=h.useState([]),[r,n]=h.useState(0),{selectedBrand:p}=F(),[t,f]=h.useState({dateRange:{from:N.FROM,to:N.TO},request_path:"all",user_name:""}),j=h.useMemo(()=>{let l=[...c.length>0?c:S];t.request_path&&t.request_path!=="all"&&(l=l.filter(_=>w(_.request_path)===t.request_path));const d=me("user_name");return l=l.filter(d(t.user_name)),l.sort((_,x)=>new Date(x.request_at).getTime()-new Date(_.request_at).getTime())},[t,c]),A=(o,l)=>{f(d=>({...d,[o]:l}))},H=(o,l)=>{f(d=>({...d,dateRange:{from:o,to:l}}))},y=h.useCallback(async()=>{if(!(!t.dateRange.from||!t.dateRange.to)){s(!0),m(null);try{const o=await O.getSystemLogs({start_time:q(t.dateRange.from),end_time:E(t.dateRange.to),pos_parent:(p==null?void 0:p.brandId)||"",page:1});if(!o.data||!o.data.data){u([]),n(0);return}let l=[...o.data.data];const d=o.data.totalPage||1;if(n(o.data.count||0),d>1){const _=[];for(let g=2;g<=d;g++)_.push(O.getSystemLogs({start_time:q(t.dateRange.from),end_time:E(t.dateRange.to),pos_parent:(p==null?void 0:p.brandId)||"",page:g}));(await Promise.all(_)).forEach(g=>{g.data&&g.data.data&&l.push(...g.data.data)})}u(l)}catch(o){console.error("Error fetching system logs:",o),m("Có lỗi xảy ra khi tải nhật ký hệ thống"),u(S),n(S.length)}finally{s(!1)}}},[t.dateRange.from,t.dateRange.to]),G=h.useCallback(async()=>{await y()},[y]);h.useEffect(()=>{y()},[y]);const P=h.useMemo(()=>ce.map(o=>({value:o,label:o})),[]),K=h.useMemo(()=>{const o=c.length>0?c:S;return Array.from(new Set(o.map(d=>d.user_name))).map(d=>({value:d,label:d}))},[c]),Y=h.useCallback(o=>{try{const l=new Date(o);return v(l,"dd/MM/yyyy HH:mm:ss")}catch{return o}},[]);return{logs:j,isLoading:a,error:i,filters:t,updateFilter:A,updateDateRange:H,searchLogs:G,availableRequestPaths:P,availableUsers:K,formatTimestamp:Y,totalCount:r||j.length}}function ue(){const{logs:a,isLoading:s,error:i,filters:m,updateFilter:c,updateDateRange:u,searchLogs:r,availableRequestPaths:n,availableUsers:p,formatTimestamp:t,totalCount:f}=de();return i?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:i})})}):e.jsxs(e.Fragment,{children:[e.jsxs(I,{children:[e.jsx(U,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(X,{}),e.jsx(z,{})]})]}),e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-6",children:e.jsx(ne,{filters:m,onFilterChange:c,onDateRangeChange:u,onSearch:r,availableRequestPaths:n,availableUsers:p,isLoading:s})}),e.jsx("div",{className:"mb-4",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Tổng ",f," kết quả"]})}),e.jsx(ie,{logs:a,isLoading:s,formatTimestamp:t})]})]})}const Ze=ue;export{Ze as component};
