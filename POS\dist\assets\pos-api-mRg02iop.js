import{y as n,u as o}from"./index-CVQ6JZo2.js";const p="https://posapi.ipos.vn/api",c="5c885b2ef8c34fb7b1d1fad11eef7bec",a=n.create({baseURL:p,timeout:3e4,headers:{"Content-Type":"application/json;charset=UTF-8",Accept:"application/json, text/plain, */*",Connection:"keep-alive",Origin:"https://fabi.ipos.vn",Referer:"https://fabi.ipos.vn/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-site","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept-language":"vi",access_token:c,fabi_type:"pos-cms","sec-ch-ua":'"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"macOS"',"x-client-timezone":"25200000"}});a.interceptors.request.use(e=>{const{accessToken:t,jwtToken:s}=o.getState().auth,i=t||c;return e.headers.set("access_token",i),s&&e.headers.set("Authorization",`${s}`),e},e=>Promise.reject(e));a.interceptors.response.use(e=>e,e=>{var t;return((t=e.response)==null?void 0:t.status)===401&&o.getState().auth.reset(),Promise.reject(e.response||e)});const h={get:(e,t)=>a.get(e,t),post:(e,t,s)=>a.post(e,t,s),put:(e,t,s)=>a.put(e,t,s),patch:(e,t,s)=>a.patch(e,t,s),delete:(e,t)=>a.delete(e,t)};export{a,h as b};
