import{aZ as r,j as t}from"./index-CfbMU4Ye.js";import{S as i}from"./index-BM0B6Gu-.js";import"./use-service-charge-form-BYsz60u5.js";import"./date-utils-DBbLjCz0.js";import"./useQuery-BvDWg4vp.js";import"./utils-km2FGkQ4.js";import"./useMutation-C9PewMvL.js";import"./pos-api-BBB_ZiZD.js";import"./query-keys-3lmd-xp6.js";import"./discount-toggle-button-CgZWL_PV.js";import"./date-range-picker-FRR8J6T3.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./select-_nXsh5SU.js";import"./index-D41EikqA.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./form-DPp_Bp7A.js";import"./input-D8TU6hMD.js";import"./tabs-BqFFB_d7.js";import"./index-4DjKSQeL.js";import"./textarea-BQNTa5Sn.js";import"./checkbox-CSFn543p.js";import"./modal-D_ZqQrH_.js";import"./dialog-FztlF_ds.js";import"./collapsible-BVDeq-Zm.js";import"./calendar-BqbXMtoi.js";import"./circle-help-a_rpWlnp.js";import"./switch-DgMQ7bQC.js";const G=function(){const{id:o}=r.useParams();return console.log("🔥 Service Charge Detail Page - URL Params:"),console.log("🔥 serviceChargeId:",o),t.jsx(i,{serviceChargeId:o})};export{G as component};
