import{aY as t,j as i}from"./index-CfbMU4Ye.js";import"./pos-api-BBB_ZiZD.js";import"./header-CiiJInbE.js";import"./main-B69tr6A0.js";import"./search-context-DXPkaUlN.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{A as m}from"./area-form-DRlh6Qx_.js";import"./separator-DVvwOaSX.js";import"./command-Jt-qPT7s.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-FztlF_ds.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./scroll-area-Bx6sgJqp.js";import"./index-D41EikqA.js";import"./select-_nXsh5SU.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./IconChevronRight-1SGwHwL2.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./images-api-RS3EYfrE.js";import"./use-areas-CyxUzBoL.js";import"./useQuery-BvDWg4vp.js";import"./utils-km2FGkQ4.js";import"./useMutation-C9PewMvL.js";import"./query-keys-3lmd-xp6.js";import"./input-D8TU6hMD.js";const B=function(){const{areaId:o}=t.useParams(),{store_uid:r}=t.useSearch();return console.log("🔍 AreaDetailPage rendered"),console.log("📍 Route params:",{areaId:o}),console.log("🔍 Route search:",{store_uid:r}),console.log("📊 Full URL:",window.location.href),i.jsx(m,{areaId:o,storeUid:r})};export{B as component};
