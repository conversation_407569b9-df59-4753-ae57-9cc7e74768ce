import{r as m}from"./index-UcdZ5AHH.js";import{b as l}from"./use-auth-Ds6PM7xW.js";const S=()=>{const{user:u,userRole:i,company:n,brands:a,cities:s,stores:r,activeBrands:o,activeStores:d,isAuthenticated:c}=l(),g=m.useMemo(()=>({getBrandById:e=>a.find(t=>t.id===e||t.brand_id===e),getStoreById:e=>r.find(t=>t.id===e||t.store_id===e),getCityById:e=>s.find(t=>t.id===e||t.city_id===e),getStoresByBrand:e=>r.filter(t=>t.brand_uid===e),getStoresByCity:e=>r.filter(t=>t.city_uid===e),getActiveStoresByBrand:e=>r.filter(t=>t.brand_uid===e&&t.active===1),hasStoreAccess:e=>c&&r.some(t=>t.id===e||t.store_id===e),getPrimaryBrand:()=>o[0],getPrimaryStore:()=>d[0],getStats:()=>({totalBrands:a.length,activeBrands:o.length,totalStores:r.length,activeStores:d.length,totalCities:s.length,userRole:i==null?void 0:i.role_name,companyName:n==null?void 0:n.company_name}),exportData:()=>({user:u,userRole:i,company:n,brands:a,cities:s,stores:r,timestamp:new Date().toISOString()})}),[a,r,s,o,d,c,i,n,u]);return{user:u,userRole:i,company:n,brands:a,cities:s,stores:r,activeBrands:o,activeStores:d,isAuthenticated:c,...g}};export{S as u};
