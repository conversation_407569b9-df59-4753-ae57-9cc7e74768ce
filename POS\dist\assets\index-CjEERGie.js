import{r as u,h as Ne,j as e,B as y,c as _e,T as Ue,o as Ae,p as Le,q as Ke,R as ye,a4 as B,u as Ee,l as qe}from"./index-CfbMU4Ye.js";import"./pos-api-BBB_ZiZD.js";import"./vietqr-api-BHQxfNzq.js";import{u as $e}from"./use-customizations-CZoEvMkV.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import{H as Xe}from"./header-CiiJInbE.js";import{M as Ge}from"./main-B69tr6A0.js";import{P as We}from"./profile-dropdown-HjZ6UGjk.js";import{S as Je,T as Qe}from"./search-Bbt2JnTN.js";import{u as se,a as Ye,b as Ce,c as Ze,g as es,d as ss,I as ts,e as as,f as is,h as ls,C as ns,B as cs}from"./customization-dialog-BgorFKjZ.js";import"./exceljs.min-C3DV5hpd.js";import{h as rs,x as Y,G as he,H as os,J as ds,K as ms,i as hs,A as xs,a as us,C as ps,B as fs}from"./react-icons.esm-DefBGHOQ.js";import{D as gs,a as js,b as _s,c as $}from"./dropdown-menu-8bnotEGr.js";import{D as P}from"./data-table-column-header-DxTOtvid.js";import{B as we}from"./badge-DNJz5hg4.js";import{S as ys}from"./status-badge-BBBMKdZV.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{C as be}from"./checkbox-CSFn543p.js";import{S as Q}from"./settings-DfmLInNB.js";import{I as ws}from"./IconCopy-zUX983_j.js";import{I as bs}from"./IconTrash-NV_v0NzY.js";import{u as ke,g as vs,a as Ns,b as Cs,d as ks,e as Se,f as Z}from"./index-DrO-sOnq.js";import{S as Te,a as me}from"./scroll-area-Bx6sgJqp.js";import{T as xe,a as ue,b as U,c as ee,d as pe,e as X}from"./table-C3v-r6-e.js";import{C as Ie}from"./confirm-dialog-dvjeaHx8.js";import{a as Ss,C as Ts}from"./chevron-right-BwGWQXH2.js";import{u as fe}from"./use-item-types-mN8TSC7t.js";import{u as ge}from"./use-removed-items-DWRDzX0n.js";import{I as Is}from"./input-D8TU6hMD.js";import{S as ne,a as ce,b as re,c as oe,d as W}from"./select-_nXsh5SU.js";import{M as Ds}from"./multi-select-DH0FdNoK.js";import{T as De}from"./trash-2-BgCVQZay.js";import{I as Ms}from"./IconFilter-y_BvGZMb.js";import{X as Rs}from"./calendar-DmzcYdpW.js";import{S as d}from"./skeleton-HVXRv3hO.js";import{read as Me,utils as Re}from"./xlsx-DkH2s96g.js";import{u as ze}from"./use-item-classes-DJrzbexi.js";import{u as Be}from"./use-units-Cyx-GSz4.js";import{D as te,a as ae,b as ie,c as le}from"./dialog-FztlF_ds.js";import{C as de}from"./combobox-B1W092_-.js";import"./useQuery-BvDWg4vp.js";import"./utils-km2FGkQ4.js";import"./useMutation-C9PewMvL.js";import"./query-keys-3lmd-xp6.js";import"./separator-DVvwOaSX.js";import"./avatar-CE3yFgmj.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./search-N2Gpb9W7.js";import"./createLucideIcon-BH-J_-vM.js";import"./createReactComponent-CVG1We1Z.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";import"./use-dialog-state-CkZCkUo8.js";import"./modal-D_ZqQrH_.js";import"./zod-BFJv4_uG.js";import"./index-D41EikqA.js";import"./index-CBP3KeI0.js";import"./index-4DjKSQeL.js";import"./check-C1W3FWto.js";import"./popover-C4SSkcaE.js";import"./isSameMonth-C8JQo-AN.js";import"./index-Cqf6DKEV.js";import"./alert-dialog-Bac0_q2y.js";import"./circle-x-BmLiWp0R.js";import"./chevrons-up-down-BYVIbgdq.js";function zs(){const[n,s]=u.useState(!1),[i,c]=u.useState(!1),[a,o]=u.useState(null),[m,g]=u.useState(!1),[j,M]=u.useState([]),[I,C]=u.useState("all"),[k,x]=u.useState("all"),[h,f]=u.useState([]),[S,b]=u.useState("all");return{isCustomizationDialogOpen:n,isBuffetItem:i,selectedMenuItem:a,isBuffetConfigModalOpen:m,selectedBuffetMenuItem:j,selectedItemTypeUid:I,selectedCityUid:k,selectedDaysOfWeek:h,selectedStatus:S,setIsCustomizationDialogOpen:s,setIsBuffetItem:c,setSelectedMenuItem:o,setIsBuffetConfigModalOpen:g,setSelectedBuffetMenuItem:M,setSelectedItemTypeUid:C,setSelectedCityUid:x,setSelectedDaysOfWeek:f,setSelectedStatus:b}}function Bs(){const{setOpen:n}=se(),s=Ne(),i=()=>{s({to:"/menu/items/items-in-city/detail"})},c=()=>{n("export-dialog")},a=()=>{n("import")},o=()=>{},m=()=>{},g=()=>{},j=()=>{};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(gs,{children:[e.jsx(js,{asChild:!0,children:e.jsxs(y,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(rs,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(_s,{align:"end",className:"w-56",children:[e.jsxs($,{onClick:c,children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Xuất, sửa thực đơn"]}),e.jsxs($,{onClick:a,children:[e.jsx(he,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]}),e.jsxs($,{onClick:o,children:[e.jsx(os,{className:"mr-2 h-4 w-4"}),"Cấu hình giá theo nguồn"]}),e.jsxs($,{onClick:m,children:[e.jsx(ds,{className:"mr-2 h-4 w-4"}),"Sắp xếp thực đơn"]}),e.jsxs($,{onClick:g,children:[e.jsx(ms,{className:"mr-2 h-4 w-4"}),"Sao chép thực đơn"]}),e.jsxs($,{onClick:j,children:[e.jsx(hs,{className:"mr-2 h-4 w-4"}),"Cấu hình khung thời gian"]})]})]}),e.jsx(y,{variant:"default",size:"sm",onClick:i,children:"Tạo món"})]})})}function ve({column:n,title:s,className:i,defaultSort:c="desc"}){if(!n.getCanSort())return e.jsx("div",{className:_e(i),children:s});const a=()=>{const o=n.getIsSorted();o?o==="desc"?n.toggleSorting(!1):n.toggleSorting(!0):n.toggleSorting(c==="desc")};return e.jsx("div",{className:_e("flex items-center space-x-2",i),children:e.jsxs(y,{variant:"ghost",size:"sm",className:"-ml-3 h-8 hover:bg-accent",onClick:a,children:[e.jsx("span",{children:s}),n.getIsSorted()==="desc"?e.jsx(xs,{className:"ml-2 h-4 w-4"}):n.getIsSorted()==="asc"?e.jsx(us,{className:"ml-2 h-4 w-4"}):e.jsx(ps,{className:"ml-2 h-4 w-4"})]})})}const Os=({onBuffetConfigClick:n})=>[{id:"select",header:({table:s})=>e.jsx(be,{checked:s.getIsAllPageRowsSelected(),onCheckedChange:i=>s.toggleAllPageRowsSelected(!!i),"aria-label":"Select all"}),cell:({row:s})=>e.jsx(be,{checked:s.getIsSelected(),onCheckedChange:i=>s.toggleSelected(!!i),"aria-label":"Select row",onClick:i=>i.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:s})=>e.jsx("div",{className:"w-[50px]",children:s.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:s})=>e.jsx(P,{column:s,title:"Mã món"}),cell:({row:s})=>e.jsx("div",{className:"text-sm font-medium",children:s.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:s})=>e.jsx(P,{column:s,title:"Tên món"}),cell:({row:s})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm font-medium",children:s.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"price",header:({column:s})=>e.jsx(P,{column:s,title:"Giá"}),cell:({row:s})=>{const i=s.getValue("price");return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(i)})},enableSorting:!1,enableHiding:!0},{accessorKey:"vatPercent",header:({column:s})=>e.jsx(P,{column:s,title:"VAT (%)"}),cell:({row:s})=>{const i=s.getValue("vatPercent");return e.jsx("div",{className:"text-right text-sm",children:i*100})},enableSorting:!1,enableHiding:!0},{accessorKey:"itemType",header:({column:s})=>e.jsx(P,{column:s,title:"Nhóm món"}),cell:({row:s})=>e.jsx(we,{variant:"outline",className:"text-xs",children:s.getValue("itemType")}),enableSorting:!1,enableHiding:!0},{accessorKey:"itemClass",header:({column:s})=>e.jsx(P,{column:s,title:"Loại món"}),cell:({row:s})=>s.getValue("itemClass")&&e.jsx(we,{variant:"outline",className:"text-center text-xs",children:s.getValue("itemClass")}),enableSorting:!1,enableHiding:!0},{accessorKey:"unit",header:({column:s})=>e.jsx(P,{column:s,title:"Đơn vị tính"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("unit")}),enableSorting:!1,enableHiding:!0},{accessorKey:"sideItems",header:({column:s})=>e.jsx(ve,{column:s,title:"Món ăn kèm",defaultSort:"desc"}),cell:({row:s})=>{const i=s.getValue("sideItems");if(!i)return e.jsx("div",{children:"Món chính"});const c=i==="Món ăn kèm"?"Món ăn kèm":i;return e.jsx(Ue,{children:e.jsxs(Ae,{children:[e.jsx(Le,{asChild:!0,children:e.jsx("div",{className:"max-w-[120px] cursor-help truncate text-sm",children:c})}),e.jsx(Ke,{children:e.jsx("p",{className:"max-w-[300px]",children:c})})]})})},enableSorting:!0,enableHiding:!0},{accessorKey:"city",header:({column:s})=>e.jsx(P,{column:s,title:"Thành phố"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("city")}),enableSorting:!1,enableHiding:!0},{accessorKey:"buffetConfig",header:({column:s})=>e.jsx(P,{column:s,title:"Cấu hình buffet"}),cell:({row:s})=>{var a;const i=s.original;return((a=i.extra_data)==null?void 0:a.is_buffet_item)===1?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Đã cấu hình"}),e.jsx(y,{variant:"outline",size:"sm",onClick:()=>n(i),className:"h-6 px-2 text-xs",children:e.jsx(Q,{className:"h-3 w-3"})})]}):e.jsxs(y,{variant:"outline",size:"sm",onClick:()=>n(i),className:"h-7 px-2 text-xs",children:[e.jsx(Q,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{accessorKey:"customization",header:({column:s})=>e.jsx(P,{column:s,title:"Customization"}),cell:({row:s,table:i})=>{var g;const c=s.original,a=i.options.meta,o=c.customization_uid,m=(g=a==null?void 0:a.customizations)==null?void 0:g.find(j=>j.id===o);return m?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:m.name}),e.jsx(y,{variant:"outline",size:"sm",onClick:()=>{var j;return(j=a==null?void 0:a.onCustomizationClick)==null?void 0:j.call(a,c)},className:"h-6 px-2 text-xs",children:e.jsx(Q,{className:"h-3 w-3"})})]}):e.jsxs(y,{variant:"outline",size:"sm",onClick:()=>{var j;return(j=a==null?void 0:a.onCustomizationClick)==null?void 0:j.call(a,c)},className:"h-7 px-2 text-xs",children:[e.jsx(Q,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{id:"copy",header:"Sao chép tạo món mới",cell:({row:s,table:i})=>{const c=s.original,a=i.options.meta;return e.jsxs(y,{variant:"ghost",size:"sm",className:"ml-14 h-8 w-8",onClick:o=>{var m;o.stopPropagation(),(m=a==null?void 0:a.onCopyClick)==null||m.call(a,c)},children:[e.jsx(ws,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",c.item_name]})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"isActive",header:({column:s})=>e.jsx(ve,{column:s,title:"Thao tác",defaultSort:"desc"}),enableSorting:!0,cell:({row:s,table:i})=>{const c=s.original,a=s.getValue("isActive"),o=i.options.meta;return e.jsx("div",{onClick:m=>{var g;m.stopPropagation(),(g=o==null?void 0:o.onToggleStatus)==null||g.call(o,c)},className:"cursor-pointer",children:e.jsx(ys,{isActive:a,activeText:"Active",inactiveText:"Deactive"})})},enableHiding:!0},{id:"actions",cell:({row:s,table:i})=>{const c=s.original,a=i.options.meta;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(y,{variant:"ghost",size:"sm",onClick:o=>{var m;o.stopPropagation(),(m=a==null?void 0:a.onDeleteClick)==null||m.call(a,c)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(bs,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa món ",c.item_name]})]})})},enableSorting:!1,enableHiding:!1,size:80}],Fs=Os;function Ps({currentPage:n,onPageChange:s,hasNextPage:i}){const c=()=>{n>1&&s(n-1)},a=()=>{i&&s(n+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(y,{variant:"outline",size:"sm",onClick:c,disabled:n===1,className:"flex items-center gap-2",children:[e.jsx(Ss,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:n}),e.jsxs(y,{variant:"outline",size:"sm",onClick:a,disabled:!i,className:"flex items-center gap-2",children:["Sau",e.jsx(Ts,{className:"h-4 w-4"})]})]})}const Vs=[{label:"Thứ 2",value:"2"},{label:"Thứ 3",value:"3"},{label:"Thứ 4",value:"4"},{label:"Thứ 5",value:"5"},{label:"Thứ 6",value:"6"},{label:"Thứ 7",value:"7"},{label:"Chủ Nhật",value:"1"}],Hs=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}];function Us({table:n,selectedItemTypeUid:s="all",onItemTypeChange:i,selectedCityUid:c="all",onCityChange:a,selectedDaysOfWeek:o=[],onDaysOfWeekChange:m,selectedStatus:g="all",onStatusChange:j,onDeleteSelected:M}){var r;const[I,C]=u.useState(!1),{data:k=[]}=fe(),{data:x=[]}=ge(),h=x.filter(t=>t.active===1),f=h.map(t=>({label:t.city_name,value:t.id})),S=h.map(t=>t.id).join(",");u.useEffect(()=>{c==="all"&&S&&a&&a(S)},[c,S,a]);const b=n.getState().columnFilters.length>0,v=n.getFilteredSelectedRowModel().rows.length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[v>0&&e.jsxs(y,{variant:"destructive",size:"sm",onClick:M,className:"h-9",children:[e.jsx(De,{}),"Xóa món (",v,")"]}),e.jsx(Is,{placeholder:"Tìm kiếm món ăn...",value:((r=n.getColumn("name"))==null?void 0:r.getFilterValue())??"",onChange:t=>{var l;return(l=n.getColumn("name"))==null?void 0:l.setFilterValue(t.target.value)},className:"h-9 w-[150px] lg:w-[250px]"}),e.jsxs(ne,{value:c,onValueChange:t=>{a&&a(t)},children:[e.jsx(ce,{className:"h-10 w-[180px]",children:e.jsx(re,{placeholder:"Chọn thành phố"})}),e.jsxs(oe,{children:[e.jsx(W,{value:S,children:"Tất cả thành phố"}),f.map(t=>e.jsx(W,{value:t.value,children:t.label},t.value))]})]}),e.jsxs(ne,{value:g,onValueChange:j,children:[e.jsx(ce,{className:"h-10 w-[180px]",children:e.jsx(re,{placeholder:"Chọn Trạng thái"})}),e.jsx(oe,{children:Hs.map(t=>e.jsx(W,{value:t.value,children:t.label},t.value))})]}),e.jsxs(y,{variant:"outline",size:"sm",onClick:()=>C(!I),className:"h-9",children:[e.jsx(Ms,{className:"h-4 w-4"}),"Nâng cao"]}),b&&e.jsxs(y,{variant:"ghost",onClick:()=>n.resetColumnFilters(),className:"h-10 px-2 lg:px-3",children:["Reset",e.jsx(Rs,{className:"ml-2 h-4 w-4"})]})]})}),I&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(ne,{value:s,onValueChange:i,children:[e.jsx(ce,{className:"h-10 w-[180px]",children:e.jsx(re,{placeholder:"Chọn loại món"})}),e.jsxs(oe,{children:[e.jsx(W,{value:"all",children:"Tất cả nhóm món"}),k.filter(t=>t.active===1).map(t=>({label:t.item_type_name,value:t.id})).map(t=>e.jsx(W,{value:t.value,children:t.label},t.value))]})]}),e.jsx(Ds,{options:Vs,value:o,onValueChange:m||(()=>{}),placeholder:"Chọn ngày trong tuần",className:"min-h-9 w-[300px]",maxCount:1})]})]})}function As({columns:n,data:s,onCustomizationClick:i,onCopyClick:c,onToggleStatus:a,onRowClick:o,onDeleteClick:m,customizations:g,selectedItemTypeUid:j,onItemTypeChange:M,selectedCityUid:I,onCityChange:C,selectedDaysOfWeek:k,onDaysOfWeekChange:x,selectedStatus:h,onStatusChange:f,hasNextPageOverride:S,currentPage:b,onPageChange:v}){var q;const[r,t]=u.useState({}),[l,p]=u.useState({}),[z,V]=u.useState([]),[H,_]=u.useState([]),[w,D]=u.useState(!1),{deleteMultipleItemsAsync:A}=Ye(),L=()=>{D(!0)},K=async()=>{try{const T=O.getFilteredSelectedRowModel().rows.map(F=>F.original.id);await A(T),D(!1),O.resetRowSelection()}catch{}},E=(R,T)=>{const F=T.target;F.closest('input[type="checkbox"]')||F.closest("button")||F.closest('[role="button"]')||F.closest(".badge")||F.tagName==="BUTTON"||o==null||o(R)},O=ke({data:s,columns:n,state:{sorting:H,columnVisibility:l,rowSelection:r,columnFilters:z},enableRowSelection:!0,onRowSelectionChange:t,onSortingChange:_,onColumnFiltersChange:V,onColumnVisibilityChange:p,getCoreRowModel:Se(),getFilteredRowModel:ks(),getSortedRowModel:Cs(),getFacetedRowModel:Ns(),getFacetedUniqueValues:vs(),meta:{onCustomizationClick:i,onCopyClick:c,onToggleStatus:a,onDeleteClick:m,customizations:g}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(Us,{table:O,selectedItemTypeUid:j,onItemTypeChange:M,selectedCityUid:I,onCityChange:C,selectedDaysOfWeek:k,onDaysOfWeekChange:x,selectedStatus:h,onStatusChange:f,onDeleteSelected:L}),e.jsxs(Te,{className:"rounded-md border",children:[e.jsxs(xe,{className:"relative",children:[e.jsx(ue,{children:O.getHeaderGroups().map(R=>e.jsx(U,{children:R.headers.map(T=>e.jsx(ee,{colSpan:T.colSpan,children:T.isPlaceholder?null:Z(T.column.columnDef.header,T.getContext())},T.id))},R.id))}),e.jsx(pe,{children:(q=O.getRowModel().rows)!=null&&q.length?O.getRowModel().rows.map(R=>e.jsx(U,{"data-state":R.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:T=>E(R.original,T),children:R.getVisibleCells().map(T=>e.jsx(X,{children:Z(T.column.columnDef.cell,T.getContext())},T.id))},R.id)):e.jsx(U,{children:e.jsx(X,{colSpan:n.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(me,{orientation:"horizontal"})]}),e.jsx(Ps,{currentPage:b??1,onPageChange:R=>v&&v(R),hasNextPage:!!S}),e.jsx(Ie,{open:w,onOpenChange:D,title:`Bạn có chắc muốn xóa ${O.getFilteredSelectedRowModel().rows.length} món đã chọn`,desc:"Hành động này không thể hoàn tác.",confirmText:"Xóa",cancelBtnText:"Hủy",className:"top-[30%] translate-y-[-50%]",handleConfirm:K,destructive:!0})]})}function Ls(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(d,{className:"h-8 w-[250px]"}),e.jsx(d,{className:"h-8 w-[100px]"}),e.jsx(d,{className:"h-8 w-[100px]"}),e.jsx(d,{className:"h-8 w-[100px]"})]}),e.jsx(d,{className:"h-8 w-[100px]"})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsx("div",{className:"border-b p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(d,{className:"h-4 w-8"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-32"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-16"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-16"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-16"})]})}),Array.from({length:10}).map((n,s)=>e.jsx("div",{className:"border-b p-4 last:border-b-0",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(d,{className:"h-4 w-8"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-32"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-16"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-16"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-16"})]})},s))]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{className:"h-8 w-[200px]"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(d,{className:"h-8 w-[100px]"}),e.jsx(d,{className:"h-8 w-8"}),e.jsx(d,{className:"h-8 w-8"})]})]})]})}function Ks({open:n,onOpenChange:s,data:i,onSave:c}){var k;const[a,o]=u.useState(i);ye.useEffect(()=>{o(i)},[i]);const m=()=>{c(a),B.success("Data saved successfully"),s(!1)},g=()=>{s(!1)},j=ye.useCallback(x=>{const h=a.filter((f,S)=>S!==x);o(h)},[a]),{tableData:M,columns:I}=u.useMemo(()=>{if(!a||a.length===0)return{tableData:[],columns:[]};const x=a[0]||[],h=a.slice(1),f=[{id:"actions",header:"-",cell:({row:b})=>e.jsx(y,{variant:"ghost",size:"sm",onClick:()=>j(b.original._originalIndex),className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:e.jsx(fs,{className:"h-4 w-4"})}),enableSorting:!1,enableHiding:!1,size:50,meta:{className:"w-12 text-center sticky left-0 bg-background z-20 border-r"}},...x.map((b,v)=>({id:`col_${v}`,accessorKey:`col_${v}`,header:String(b),cell:({row:r})=>e.jsx("div",{className:"min-w-[150px] whitespace-nowrap",children:r.getValue(`col_${v}`)}),enableSorting:!1,enableHiding:!1,meta:{className:"min-w-[150px] px-4 whitespace-nowrap"}}))];return{tableData:h.map((b,v)=>{const r={_originalIndex:v+1};return b.forEach((t,l)=>{r[`col_${l}`]=t}),r}),columns:f}},[a,j]),C=ke({data:M,columns:I,getCoreRowModel:Se()});return!a||a.length===0?null:e.jsx(te,{open:n,onOpenChange:s,children:e.jsx(ae,{className:"h-[525px] w-[1140px] !max-w-[1140px] overflow-hidden p-0",children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx(ie,{className:"shrink-0 border-b px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(le,{className:"text-xl font-semibold",children:"Thêm món từ file"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(y,{variant:"outline",size:"sm",onClick:g,children:"Đóng"}),e.jsx(y,{size:"sm",className:"bg-green-600 hover:bg-green-700",onClick:m,children:"Lưu"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden p-6",children:e.jsx("div",{className:"bg-background h-full w-full overflow-auto rounded-lg border",children:e.jsx("div",{className:"min-w-max",children:e.jsxs(xe,{children:[e.jsx(ue,{className:"bg-muted/50 sticky top-0 z-10",children:C.getHeaderGroups().map(x=>e.jsx(U,{children:x.headers.map(h=>{var f;return e.jsx(ee,{className:((f=h.column.columnDef.meta)==null?void 0:f.className)||"",children:h.isPlaceholder?null:Z(h.column.columnDef.header,h.getContext())},h.id)})},x.id))}),e.jsx(pe,{children:(k=C.getRowModel().rows)!=null&&k.length?C.getRowModel().rows.map(x=>e.jsx(U,{className:"hover:bg-muted/50",children:x.getVisibleCells().map(h=>{var f;return e.jsx(X,{className:((f=h.column.columnDef.meta)==null?void 0:f.className)||"",children:Z(h.column.columnDef.cell,h.getContext())},h.id)})},x.id)):e.jsx(U,{children:e.jsx(X,{colSpan:I.length,className:"h-24 text-center",children:"No data."})})})]})})})})]})})})}function Es(){const{open:n,setOpen:s}=se(),[i,c]=u.useState(!1),[a,o]=u.useState([]),m=u.useRef(null),{downloadTemplateAsync:g,isPending:j}=Ce(),M=async()=>{try{const x=await g({city_uid:"all",item_type_uid:"all",active:"all"}),h=window.URL.createObjectURL(x),f=document.createElement("a");f.href=h,f.download=`items-template-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(f),f.click(),document.body.removeChild(f),window.URL.revokeObjectURL(h),B.success("Tải template thành công")}catch{B.error("Lỗi khi tải template")}},I=()=>{var x;(x=m.current)==null||x.click()},C=x=>{var S;const h=(S=x.target.files)==null?void 0:S[0];if(!h)return;const f=new FileReader;f.onload=b=>{var v;try{const r=new Uint8Array((v=b.target)==null?void 0:v.result),t=Me(r,{type:"array"}),l=t.SheetNames[0],p=t.Sheets[l],z=Re.sheet_to_json(p,{header:1,defval:"",raw:!1});if(z.length===0){B.error("File không có dữ liệu");return}o(z),s(null),c(!0),m.current&&(m.current.value="")}catch{B.error("Lỗi khi đọc file. Vui lòng kiểm tra định dạng file.")}},f.readAsArrayBuffer(h)},k=()=>{B.success("Dữ liệu đã được lưu thành công!"),c(!1),s(null)};return e.jsxs(e.Fragment,{children:[e.jsx(te,{open:n==="import",onOpenChange:x=>s(x?"import":null),children:e.jsxs(ae,{className:"max-w-2xl",children:[e.jsx(ie,{children:e.jsx(le,{children:"Thêm món"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsx(y,{variant:"outline",size:"sm",onClick:M,disabled:j,className:"flex items-center gap-2",children:j?"Đang tải...":e.jsxs(e.Fragment,{children:["Tải xuống",e.jsx(Y,{className:"h-4 w-4"})]})})]})}),e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Không được để trống các cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Tên, Thành phố"}),"."]}),e.jsxs("p",{children:["Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị đã có vào cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Nhóm, Loại món"}),"."]}),e.jsxs("p",{children:["Mã đơn vị món, mã thành phố có thể xem trong sheet"," ",e.jsx("span",{className:"font-mono text-blue-600",children:"Guide"})," của file mẫu."]})]})]}),e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"})]}),e.jsxs(y,{variant:"outline",size:"sm",onClick:I,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(he,{className:"h-4 w-4"})]})]})})]})]})}),e.jsx("input",{ref:m,type:"file",accept:".xlsx,.xls",onChange:C,style:{display:"none"}}),e.jsx(Ks,{open:i,onOpenChange:c,data:a,onSave:k})]})}function qs({open:n,onOpenChange:s,data:i}){const[c,a]=u.useState(i),[o,m]=u.useState(!1),{user:g,company:j}=Ee(r=>r.auth),{selectedBrand:M}=qe(),{importItemsAsync:I,isPending:C}=Ze(),{data:k=[]}=fe({skip_limit:!0}),{data:x=[]}=ze({skip_limit:!0}),{data:h=[]}=Be(),{data:f=[]}=ge();u.useEffect(()=>{a(i)},[i]);const S=r=>{a(t=>t.filter((l,p)=>p!==r))},b=async()=>{if(!j||!M){B.error("Thiếu thông tin cần thiết để cập nhật");return}m(!0);const r=c.map(t=>{const l=h.find(w=>w.unit_id===t.unit_id),p=f.find(w=>w.city_name===t.city_name),z=k.find(w=>w.item_type_id===t.item_type_id||w.item_type_name===t.item_type_name),V=x.find(w=>w.item_class_id===t.item_class_id||w.item_class_name===t.item_class_name),H=h.find(w=>w.unit_id==="MON"),_=k.find(w=>w.item_type_name==="LOẠI KHÁC");return{id:t.id,item_id:t.item_id,item_name:t.item_name,description:t.description||"",ots_price:t.ots_price||0,ots_tax:(t.ots_tax||0)/100,ta_price:t.ots_price||0,ta_tax:(t.ots_tax||0)/100,time_sale_hour_day:Number(t.time_sale_hour_day??0),time_sale_date_week:Number(t.time_sale_date_week??0),allow_take_away:1,is_eat_with:t.is_eat_with||0,image_path:t.image_path||"",image_path_thumb:t.image_path?`${t.image_path}?width=185`:"",item_color:"",list_order:t.list_order||0,is_service:0,is_material:0,active:t.active||1,user_id:"",is_foreign:0,quantity_default:0,price_change:t.price_change||0,currency_type_id:"",point:0,is_gift:0,is_fc:0,show_on_web:0,show_price_on_web:0,cost_price:0,is_print_label:0,quantity_limit:0,is_kit:0,time_cooking:t.time_cooking||0,item_id_barcode:t.item_id_barcode||"",process_index:0,is_allow_discount:0,quantity_per_day:0,item_id_eat_with:"",is_parent:0,is_sub:0,item_id_mapping:String(t.sku||""),effective_date:0,expire_date:0,sort:t.list_order||1,extra_data:{formula_qrcode:t.inqr_formula||"",is_buffet_item:t.is_buffet_item||0,up_size_buffet:[],is_item_service:t.is_item_service||0,is_virtual_item:t.is_virtual_item||0,price_by_source:[],enable_edit_price:t.price_change||0,exclude_items_buffet:[],no_update_quantity_toping:t.no_update_quantity_toping||0},revision:0,unit_uid:(l==null?void 0:l.id)||(H==null?void 0:H.id)||"",unit_secondary_uid:null,item_type_uid:(z==null?void 0:z.id)||(_==null?void 0:_.id)||"",item_class_uid:(V==null?void 0:V.id)||void 0,source_uid:null,brand_uid:M.id,company_uid:j.id,customization_uid:"",is_fabi:1,deleted:!1,created_by:(g==null?void 0:g.email)||"",updated_by:(g==null?void 0:g.email)||"",deleted_by:null,created_at:Math.floor(Date.now()/1e3),updated_at:Math.floor(Date.now()/1e3),deleted_at:null,apply_with_store:2,cities:p?[{id:p.id,city_id:p.city_id||"",fb_city_id:p.fb_city_id||"",city_name:p.city_name,image_path:p.image_path,description:p.description||"",active:p.active||1,extra_data:p.extra_data,revision:p.revision||0,sort:p.sort||0,created_by:p.created_by,updated_by:p.updated_by,deleted_by:p.deleted_by,created_at:p.created_at||0,updated_at:p.updated_at||0,deleted_at:p.deleted_at,items_cities:{item_uid:t.id,city_uid:p.id}}]:[],status_trigger_disabled:!1}});try{await I(r),m(!1),s(!1)}catch(t){console.error("Error updating items:",t),B.error(`Có lỗi xảy ra khi cập nhật món ăn: ${t}`),m(!1)}},v=[{key:"item_id",label:"Mã món",width:"120px"},{key:"city_name",label:"Thành phố",width:"120px"},{key:"item_name",label:"Tên",width:"200px"},{key:"ots_price",label:"Giá",width:"100px"},{key:"active",label:"Trạng thái",width:"100px"},{key:"item_id_barcode",label:"Mã barcode",width:"120px"},{key:"is_eat_with",label:"Món ăn kèm",width:"120px"},{key:"no_update_quantity_toping",label:"Không cập nhật số lượng",width:"180px"},{key:"unit_name",label:"Đơn vị",width:"100px"},{key:"item_type_id",label:"Nhóm",width:"120px"},{key:"item_type_name",label:"Tên nhóm",width:"150px"},{key:"item_class_id",label:"Loại món",width:"120px"},{key:"item_class_name",label:"Tên loại",width:"150px"},{key:"description",label:"Mô tả",width:"200px"},{key:"sku",label:"SKU",width:"100px"},{key:"ots_tax",label:"VAT (%)",width:"80px"},{key:"time_cooking",label:"Thời gian chế biến (phút)",width:"180px"},{key:"price_change",label:"Cho phép sửa giá khi bán",width:"180px"},{key:"is_virtual_item",label:"Cấu hình món ảo",width:"150px"},{key:"is_item_service",label:"Cấu hình món dịch vụ",width:"180px"},{key:"is_buffet_item",label:"Cấu hình món ăn là vé buffet",width:"200px"},{key:"time_sale_hour_day",label:"Giờ",width:"80px"},{key:"time_sale_date_week",label:"Ngày",width:"80px"},{key:"list_order",label:"Thứ tự",width:"80px"},{key:"image_path",label:"Hình ảnh",width:"120px"},{key:"inqr_formula",label:"Công thức inQR cho máy pha trà",width:"220px"}];return e.jsx(te,{open:n,onOpenChange:s,children:e.jsxs(ae,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(ie,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(le,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(Te,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(xe,{children:[e.jsx(ue,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(U,{children:[e.jsx(ee,{className:"w-12"}),v.map(r=>e.jsx(ee,{style:{width:r.width},children:r.label},r.key))]})}),e.jsx(pe,{children:c.map((r,t)=>e.jsxs(U,{children:[e.jsx(X,{children:e.jsx(y,{variant:"ghost",size:"icon",onClick:()=>S(t),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(De,{className:"h-4 w-4"})})}),v.map(l=>{var p;return e.jsxs(X,{style:{width:l.width},children:[l.key==="ots_price"&&e.jsxs("span",{className:"text-right",children:[(p=r[l.key])==null?void 0:p.toLocaleString("vi-VN")," ₫"]}),l.key==="active"&&e.jsx("span",{children:r[l.key]}),(l.key==="item_id"||l.key==="item_id_barcode")&&e.jsx("span",{className:"font-mono text-sm",children:r[l.key]}),l.key==="item_name"&&e.jsx("span",{className:"font-medium",children:r[l.key]}),(l.key==="is_eat_with"||l.key==="no_update_quantity_toping"||l.key==="price_change"||l.key==="is_virtual_item"||l.key==="is_item_service"||l.key==="is_buffet_item")&&e.jsx("span",{className:"text-center",children:r[l.key]}),l.key!=="ots_price"&&l.key!=="active"&&l.key!=="item_id"&&l.key!=="item_id_barcode"&&l.key!=="item_name"&&l.key!=="is_eat_with"&&l.key!=="no_update_quantity_toping"&&l.key!=="price_change"&&l.key!=="is_virtual_item"&&l.key!=="is_item_service"&&l.key!=="is_buffet_item"&&e.jsx("span",{children:r[l.key]||""})]},l.key)})]},t))})]}),e.jsx(me,{orientation:"horizontal"}),e.jsx(me,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(y,{variant:"outline",onClick:()=>s(!1),children:"Đóng"}),e.jsx(y,{onClick:b,disabled:o||C,children:o||C?"Đang lưu...":"Lưu"})]})]})]})})}function $s({open:n,onOpenChange:s}){const[i,c]=u.useState("all"),[a,o]=u.useState("all"),[m,g]=u.useState("all"),[j,M]=u.useState([]),[I,C]=u.useState(!1),k=u.useRef(null),{data:x=[]}=fe(),{data:h=[]}=ze(),{data:f=[]}=Be(),{data:S=[]}=ge(),{downloadTemplateAsync:b,isPending:v}=Ce(),r=[{label:"Tất cả nhóm món",value:"all"},...x.filter(_=>_.active===1).map(_=>({label:_.item_type_name,value:_.id}))],t=[{label:"Tất cả thành phố",value:"all"},...S.filter(_=>_.active===1).map(_=>({label:_.city_name,value:_.id}))],l=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}],p=async()=>{try{const _=await b({city_uid:i!=="all"?i:void 0,item_type_uid:a!=="all"?a:void 0,active:m!=="all"?m:void 0}),w=window.URL.createObjectURL(_),D=document.createElement("a");D.href=w,D.download=`danh-sach-mon-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(D),D.click(),document.body.removeChild(D),window.URL.revokeObjectURL(w),B.success("Tải xuống template thành công!")}catch{B.error("Lỗi khi tải template")}},z=async()=>{try{await es({itemTypes:x,itemClasses:h,units:f}),B.success("Tải xuống template mẫu thành công!")}catch{B.error("Lỗi khi tải template mẫu")}},V=_=>{var A;const w=(A=_.target.files)==null?void 0:A[0];if(!w)return;const D=new FileReader;D.onload=L=>{var K;try{const E=new Uint8Array((K=L.target)==null?void 0:K.result),O=Me(E,{type:"array"}),q=O.SheetNames[0],R=O.Sheets[q],T=Re.sheet_to_json(R,{header:1});if(T.length>0){const F=T,N=F[0]||[],J=F.slice(1).map((Oe,Fe)=>{const je={id:`temp_${Fe}`};return N.forEach((Pe,Ve)=>{const He=String(Pe).toLowerCase().replace(/\s+/g,"_");je[He]=Oe[Ve]||""}),je});M(J),C(!0),B.success("File uploaded successfully")}}catch{B.error("Error parsing file")}},D.readAsArrayBuffer(w)},H=()=>{var _;(_=k.current)==null||_.click()};return e.jsxs(e.Fragment,{children:[e.jsx(te,{open:n,onOpenChange:s,children:e.jsxs(ae,{className:"max-w-2xl lg:max-w-xl",children:[e.jsx(ie,{children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(le,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(de,{options:t,value:i,onValueChange:c,placeholder:"Tất cả thành phố",searchPlaceholder:"Tìm thành phố...",className:"flex-1"}),e.jsx(de,{options:r,value:a,onValueChange:o,placeholder:"Tất cả nhóm món",searchPlaceholder:"Tìm nhóm món...",className:"flex-1"}),e.jsx(de,{options:l,value:m,onValueChange:g,placeholder:"Tất cả trạng thái",searchPlaceholder:"Tìm trạng thái...",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải xuống dữ liệu hiện tại"}),e.jsxs(y,{variant:"outline",size:"sm",onClick:p,disabled:v,className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),v?"Đang tải...":"Tải dữ liệu"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải template mẫu (với header mới)"}),e.jsxs(y,{variant:"outline",size:"sm",onClick:z,className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),"Tải template mẫu"]})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột :"}),e.jsx("p",{className:"font-mono text-sm text-blue-600",children:"ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại."})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs(y,{variant:"outline",size:"sm",onClick:H,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(he,{className:"h-4 w-4"})]}),e.jsx("input",{ref:k,type:"file",accept:".xlsx,.xls",onChange:V,style:{display:"none"}})]})]})]})]})}),e.jsx(qs,{open:I,onOpenChange:C,data:j})]})}function Xs(){const{open:n,setOpen:s,currentRow:i,setCurrentRow:c}=se(),{deleteItemAsync:a}=ss();return e.jsxs(e.Fragment,{children:[e.jsx($s,{open:n==="export-dialog",onOpenChange:()=>s(null)}),e.jsx(Es,{}),i&&e.jsx(e.Fragment,{children:e.jsx(Ie,{destructive:!0,open:n==="delete",onOpenChange:o=>{o||(s(null),setTimeout(()=>{c(null)},500))},handleConfirm:async()=>{s(null),setTimeout(()=>{c(null)},500),await a(i.id||"")},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")})]})}function Gs(){const n=Ne(),[s,i]=u.useState(1),{setOpen:c,setCurrentRow:a}=se(),{updateStatusAsync:o}=as(),{updateItemAsync:m}=is(),{isCustomizationDialogOpen:g,isBuffetItem:j,isBuffetConfigModalOpen:M,setIsCustomizationDialogOpen:I,setIsBuffetItem:C,selectedMenuItem:k,setSelectedMenuItem:x,setIsBuffetConfigModalOpen:h,selectedBuffetMenuItem:f,setSelectedBuffetMenuItem:S,selectedItemTypeUid:b,setSelectedItemTypeUid:v,selectedCityUid:r,setSelectedCityUid:t,selectedDaysOfWeek:l,setSelectedDaysOfWeek:p,selectedStatus:z,setSelectedStatus:V}=zs(),H=u.useMemo(()=>({...b!=="all"&&{item_type_uid:b},...r!=="all"&&{city_uid:r},...l.length>0&&{time_sale_date_week:l.join(",")},...z!=="all"&&{active:parseInt(z,10)},page:s}),[b,r,l,z,s]);u.useEffect(()=>{i(1)},[b,r,l,z]);const{data:_=[],isLoading:w,error:D,hasNextPage:A}=ls({params:H}),{data:L=[]}=$e({skip_limit:!0,list_city_uid:r!=="all"?[r]:void 0}),K=N=>{x(N),I(!0)},E=N=>{var G,J;x(N),S(((G=N==null?void 0:N.extra_data)==null?void 0:G.exclude_items_buffet)||[]),C(((J=N==null?void 0:N.extra_data)==null?void 0:J.is_buffet_item)===1),h(!0)},O=N=>{n({to:"/menu/items/items-in-city/detail",search:{id:N.id||""}})},q=N=>{a(N),c("delete")},R=N=>{n({to:"/menu/items/items-in-city/detail/$id",params:{id:N.id||""}})},T=async N=>{const G=N.active?0:1;await o({id:N.id||"",active:G})},F=Fs({onBuffetConfigClick:E});return D?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:D&&`Món ăn: ${(D==null?void 0:D.message)||"Lỗi không xác định"}`})]})}):e.jsxs(e.Fragment,{children:[e.jsx(Xe,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Je,{}),e.jsx(Qe,{}),e.jsx(We,{})]})}),e.jsxs(Ge,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Món ăn tại thành phố"})}),e.jsx(Bs,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[w&&e.jsx(Ls,{}),!w&&e.jsx(As,{columns:F,data:_,onCustomizationClick:K,onCopyClick:O,onToggleStatus:T,onRowClick:R,onDeleteClick:q,customizations:L,selectedItemTypeUid:b,onItemTypeChange:v,selectedCityUid:r,onCityChange:t,selectedDaysOfWeek:l,onDaysOfWeekChange:p,selectedStatus:z,onStatusChange:V,hasNextPageOverride:A,currentPage:s,onPageChange:i})]})]}),e.jsx(Xs,{}),g&&k&&e.jsx(ns,{open:g,onOpenChange:I,item:k,customizations:L}),M&&f&&e.jsx(cs,{itemsBuffet:f,open:M,onOpenChange:h,onItemsChange:async N=>{await m({...k,extra_data:{is_buffet_item:j?1:0,exclude_items_buffet:N}})},items:_,hide:!1,enable:j,onEnableChange:C})]})}function Ws(){return e.jsx(ts,{children:e.jsx(Gs,{})})}const da=Ws;export{da as component};
