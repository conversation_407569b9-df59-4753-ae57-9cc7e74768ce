import{b as u}from"./pos-api-j20LMGrC.js";const c=new Map,D=new Map,m=2*60*1e3,g=new Map,l=new Map,y=2*60*1e3,w={getRevenueSummary:async t=>{var n;const s=`${t.companyUid}-${t.brandUid}-${t.startDate}-${t.endDate}-${((n=t.storeUids)==null?void 0:n.join(","))||"all"}`,a=D.get(s);if(a&&Date.now()-a.timestamp<m)return a.data;if(c.has(s))return c.get(s);const e=(async()=>{try{const i=new URLSearchParams({company_uid:t.companyUid,brand_uid:t.brandUid,start_date:t.startDate.toString(),end_date:t.endDate.toString(),limit:(t.limit||1e3).toString(),by_days:(t.byDays||1).toString(),store_open_at:"0"});t.storeUids&&t.storeUids.length>0&&i.set("list_store_uid",t.storeUids.join(","));const o=await u.get(`/v1/reports/sale-summary/stores?${i.toString()}`);return D.set(s,{data:o.data,timestamp:Date.now()}),o.data}finally{c.delete(s)}})();return c.set(s,e),e},processRevenueSummary:t=>{const s=t.reduce((o,r)=>o+r.revenue_gross,0),a=t.reduce((o,r)=>o+r.total_sales,0),e=t.length,n=new Map;t.forEach(o=>{o.list_data.forEach(r=>{const d=n.get(r.date);d?(d.revenue+=r.revenue_gross,d.orders+=r.total_sales):n.set(r.date,{revenue:r.revenue_gross,orders:r.total_sales})})});const i=Array.from(n.entries()).map(([o,r])=>({date:o,revenue:r.revenue,orders:r.orders})).sort((o,r)=>o.date.localeCompare(r.date));return{totalRevenue:s,totalOrders:a,storeCount:e,dailyData:i}},getPaymentMethodsSummary:async t=>{var i;const s=`${t.companyUid}-${t.brandUid}-${t.startDate}-${t.endDate}-${((i=t.storeUids)==null?void 0:i.join(","))||"all"}-${t.sourceId||"all-sources"}`,a=l.get(s);if(a&&Date.now()-a.timestamp<y)return a.data;const e=g.get(s);if(e)return e;const n=(async()=>{try{const o=new URLSearchParams({company_uid:t.companyUid,brand_uid:t.brandUid,start_date:t.startDate.toString(),end_date:t.endDate.toString(),limit:(t.limit||5).toString(),store_open_at:"0"});t.storeUids&&t.storeUids.length>0&&o.set("list_store_uid",t.storeUids.join(",")),t.sourceId&&t.sourceId!=="all-sources"&&o.set("source_id",t.sourceId);const r=await u.get(`/v1/reports/sale-summary/payment-methods?${o.toString()}`);return l.set(s,{data:r.data,timestamp:Date.now()}),r.data}finally{g.delete(s)}})();return g.set(s,n),n},formatCurrency:t=>new Intl.NumberFormat("vi-VN").format(t),getDateRange:t=>{const s=new Date,a=new Date(s.getFullYear(),s.getMonth(),s.getDate());switch(t){case"today":return{startDate:a.getTime(),endDate:a.getTime()+24*60*60*1e3-1};case"yesterday":{const e=new Date(a);return e.setDate(e.getDate()-1),{startDate:e.getTime(),endDate:e.getTime()+24*60*60*1e3-1}}case"this-week":{const e=new Date(a);return e.setDate(a.getDate()-a.getDay()),{startDate:e.getTime(),endDate:s.getTime()}}case"last-week":{const e=new Date(a);e.setDate(a.getDate()-a.getDay());const n=new Date(e);n.setDate(e.getDate()-7);const i=new Date(e);return i.setTime(i.getTime()-1),{startDate:n.getTime(),endDate:i.getTime()}}case"this-month":return{startDate:new Date(a.getFullYear(),a.getMonth(),1).getTime(),endDate:s.getTime()};case"last-month":{const e=new Date(a.getFullYear(),a.getMonth()-1,1),n=new Date(a.getFullYear(),a.getMonth(),0,23,59,59,999);return{startDate:e.getTime(),endDate:n.getTime()}}case"this-quarter":{const e=Math.floor(a.getMonth()/3);return{startDate:new Date(a.getFullYear(),e*3,1).getTime(),endDate:s.getTime()}}case"last-quarter":{const e=Math.floor(a.getMonth()/3),n=e===0?3:e-1,i=e===0?a.getFullYear()-1:a.getFullYear(),o=new Date(i,n*3,1),r=new Date(i,(n+1)*3,0,23,59,59,999);return{startDate:o.getTime(),endDate:r.getTime()}}case"this-year":return{startDate:new Date(a.getFullYear(),0,1).getTime(),endDate:s.getTime()};case"last-year":{const e=new Date(a.getFullYear()-1,0,1),n=new Date(a.getFullYear()-1,11,31,23,59,59,999);return{startDate:e.getTime(),endDate:n.getTime()}}case"last-7-days":{const e=new Date(a);return e.setDate(a.getDate()-7),{startDate:e.getTime(),endDate:s.getTime()}}case"last-30-days":{const e=new Date(a);return e.setDate(a.getDate()-30),{startDate:e.getTime(),endDate:s.getTime()}}case"last-90-days":{const e=new Date(a);return e.setDate(a.getDate()-90),{startDate:e.getTime(),endDate:s.getTime()}}default:return{startDate:new Date(a.getFullYear(),a.getMonth(),1).getTime(),endDate:s.getTime()}}}};export{w as r};
