import{r as u,h as Ne,j as e,B as y,c as _e,T as Ae,o as Le,p as Ke,q as Ee,R as ye,a4 as z,u as qe,l as $e}from"./index-UcdZ5AHH.js";import"./pos-api-j20LMGrC.js";import"./vietqr-api-9FERZtmQ.js";import{u as Xe}from"./use-customizations-DjbYm2qv.js";import"./user-9ajIul7r.js";import"./crm-api-APQEjHWd.js";import{H as Ge}from"./header-CE1GZ327.js";import{M as We}from"./main-C1Ukb9JX.js";import{P as Je}from"./profile-dropdown-CFMwD8wA.js";import{S as Qe,T as Ye}from"./search-CgdyS-kQ.js";import{u as se,a as Ze,b as Ce,c as es,d as ss,g as ts,e as as,I as is,f as ls,h as ns,i as cs,C as rs,B as os}from"./customization-dialog-CBVIYqux.js";import"./exceljs.min-DdGS1m1w.js";import{h as ds,x as Y,G as he,H as ms,J as hs,K as xs,i as us,A as ps,a as fs,C as gs,B as js}from"./react-icons.esm-DpPH1mSm.js";import{D as _s,a as ys,b as ws,c as $}from"./dropdown-menu-D3XvynCv.js";import{D as V}from"./data-table-column-header-CZeJA5fJ.js";import{B as we}from"./badge-BlAal7b-.js";import{S as vs}from"./status-badge-sjK-a3X7.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{C as ve}from"./checkbox-CDB9_T0n.js";import{S as Q}from"./settings-DBM6wTh3.js";import{I as bs}from"./IconCopy-DqHGIjT3.js";import{I as Ns}from"./IconTrash-BBImt48g.js";import{u as ke,g as Cs,a as ks,b as Ss,d as Ts,e as Se,f as Z}from"./index-Bfo4u1qA.js";import{S as Te,a as me}from"./scroll-area-DQUG4R9C.js";import{T as xe,a as ue,b as L,c as ee,d as pe,e as X}from"./table-DHWQVnPn.js";import{C as Ie}from"./confirm-dialog-geNSWVL7.js";import{a as Is,C as Ds}from"./chevron-right-Dup7TmpK.js";import{u as fe}from"./use-item-types-Ba660Fo2.js";import{u as ge}from"./use-removed-items-B2DPNXQs.js";import{I as Ms}from"./input-CBpgGfUv.js";import{S as ne,a as ce,b as re,c as oe,d as W}from"./select-DOexGcsG.js";import{M as Rs}from"./multi-select-CznXmmsw.js";import{T as De}from"./trash-2-fGkpHALB.js";import{I as zs}from"./IconFilter-C9L0RJjp.js";import{X as Bs}from"./calendar-BZ1UqQsL.js";import{S as m}from"./skeleton-B5wLl279.js";import{read as Me,utils as Re}from"./xlsx-DkH2s96g.js";import{u as ze}from"./use-item-classes-K5Si5Xyw.js";import{u as Be}from"./use-units-DpGqz_uu.js";import{D as te,a as ae,b as ie,c as le}from"./dialog-DmI079wB.js";import{C as de}from"./combobox-TkEauTt9.js";import"./useQuery-B4yhTgGk.js";import"./utils-km2FGkQ4.js";import"./useMutation-q12VR5WX.js";import"./query-keys-3lmd-xp6.js";import"./separator-C5UQ7YqK.js";import"./avatar-BOI9P1fI.js";import"./search-context-DK2BgvuK.js";import"./command-DJT46NtT.js";import"./search-B6f_4BGP.js";import"./createLucideIcon-D7O7McKr.js";import"./createReactComponent-C1S2Ujit.js";import"./IconChevronRight-CnyriCST.js";import"./IconSearch-Ca5dfxyj.js";import"./use-dialog-state-D3rbBEhX.js";import"./modal-DNIlBRJT.js";import"./zod-5jr7PwGQ.js";import"./index-MuNXZ_zP.js";import"./index-DPUGtNbb.js";import"./index-iiVug-md.js";import"./check-vnaEv-AC.js";import"./popover-BeZit_vZ.js";import"./isSameMonth-C8JQo-AN.js";import"./index-BKS-UfoD.js";import"./alert-dialog-DeDOQ4ck.js";import"./circle-x-DouBnnMt.js";import"./chevrons-up-down-BkDfU9b3.js";function Fs(){const[n,s]=u.useState(!1),[i,c]=u.useState(!1),[a,o]=u.useState(null),[d,j]=u.useState(!1),[_,M]=u.useState([]),[I,N]=u.useState("all"),[C,x]=u.useState("all"),[h,p]=u.useState([]),[k,w]=u.useState("all");return{isCustomizationDialogOpen:n,isBuffetItem:i,selectedMenuItem:a,isBuffetConfigModalOpen:d,selectedBuffetMenuItem:_,selectedItemTypeUid:I,selectedCityUid:C,selectedDaysOfWeek:h,selectedStatus:k,setIsCustomizationDialogOpen:s,setIsBuffetItem:c,setSelectedMenuItem:o,setIsBuffetConfigModalOpen:j,setSelectedBuffetMenuItem:M,setSelectedItemTypeUid:N,setSelectedCityUid:x,setSelectedDaysOfWeek:p,setSelectedStatus:w}}function Os(){const{setOpen:n}=se(),s=Ne(),i=()=>{s({to:"/menu/items/items-in-city/detail"})},c=()=>{n("export-dialog")},a=()=>{n("import")},o=()=>{},d=()=>{},j=()=>{},_=()=>{};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(_s,{children:[e.jsx(ys,{asChild:!0,children:e.jsxs(y,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(ds,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(ws,{align:"end",className:"w-56",children:[e.jsxs($,{onClick:c,children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Xuất, sửa thực đơn"]}),e.jsxs($,{onClick:a,children:[e.jsx(he,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]}),e.jsxs($,{onClick:o,children:[e.jsx(ms,{className:"mr-2 h-4 w-4"}),"Cấu hình giá theo nguồn"]}),e.jsxs($,{onClick:d,children:[e.jsx(hs,{className:"mr-2 h-4 w-4"}),"Sắp xếp thực đơn"]}),e.jsxs($,{onClick:j,children:[e.jsx(xs,{className:"mr-2 h-4 w-4"}),"Sao chép thực đơn"]}),e.jsxs($,{onClick:_,children:[e.jsx(us,{className:"mr-2 h-4 w-4"}),"Cấu hình khung thời gian"]})]})]}),e.jsx(y,{variant:"default",size:"sm",onClick:i,children:"Tạo món"})]})})}function be({column:n,title:s,className:i,defaultSort:c="desc"}){if(!n.getCanSort())return e.jsx("div",{className:_e(i),children:s});const a=()=>{const o=n.getIsSorted();o?o==="desc"?n.toggleSorting(!1):n.toggleSorting(!0):n.toggleSorting(c==="desc")};return e.jsx("div",{className:_e("flex items-center space-x-2",i),children:e.jsxs(y,{variant:"ghost",size:"sm",className:"-ml-3 h-8 hover:bg-accent",onClick:a,children:[e.jsx("span",{children:s}),n.getIsSorted()==="desc"?e.jsx(ps,{className:"ml-2 h-4 w-4"}):n.getIsSorted()==="asc"?e.jsx(fs,{className:"ml-2 h-4 w-4"}):e.jsx(gs,{className:"ml-2 h-4 w-4"})]})})}const Ps=({onBuffetConfigClick:n})=>[{id:"select",header:({table:s})=>e.jsx(ve,{checked:s.getIsAllPageRowsSelected(),onCheckedChange:i=>s.toggleAllPageRowsSelected(!!i),"aria-label":"Select all"}),cell:({row:s})=>e.jsx(ve,{checked:s.getIsSelected(),onCheckedChange:i=>s.toggleSelected(!!i),"aria-label":"Select row",onClick:i=>i.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:s})=>e.jsx("div",{className:"w-[50px]",children:s.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:s})=>e.jsx(V,{column:s,title:"Mã món"}),cell:({row:s})=>e.jsx("div",{className:"text-sm font-medium",children:s.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:s})=>e.jsx(V,{column:s,title:"Tên món"}),cell:({row:s})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm font-medium",children:s.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"price",header:({column:s})=>e.jsx(V,{column:s,title:"Giá"}),cell:({row:s})=>{const i=s.getValue("price");return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(i)})},enableSorting:!1,enableHiding:!0},{accessorKey:"vatPercent",header:({column:s})=>e.jsx(V,{column:s,title:"VAT (%)"}),cell:({row:s})=>{const i=s.getValue("vatPercent");return e.jsx("div",{className:"text-right text-sm",children:i*100})},enableSorting:!1,enableHiding:!0},{accessorKey:"itemType",header:({column:s})=>e.jsx(V,{column:s,title:"Nhóm món"}),cell:({row:s})=>e.jsx(we,{variant:"outline",className:"text-xs",children:s.getValue("itemType")}),enableSorting:!1,enableHiding:!0},{accessorKey:"itemClass",header:({column:s})=>e.jsx(V,{column:s,title:"Loại món"}),cell:({row:s})=>s.getValue("itemClass")&&e.jsx(we,{variant:"outline",className:"text-center text-xs",children:s.getValue("itemClass")}),enableSorting:!1,enableHiding:!0},{accessorKey:"unit",header:({column:s})=>e.jsx(V,{column:s,title:"Đơn vị tính"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("unit")}),enableSorting:!1,enableHiding:!0},{accessorKey:"sideItems",header:({column:s})=>e.jsx(be,{column:s,title:"Món ăn kèm",defaultSort:"desc"}),cell:({row:s})=>{const i=s.getValue("sideItems");if(!i)return e.jsx("div",{children:"Món chính"});const c=i==="Món ăn kèm"?"Món ăn kèm":i;return e.jsx(Ae,{children:e.jsxs(Le,{children:[e.jsx(Ke,{asChild:!0,children:e.jsx("div",{className:"max-w-[120px] cursor-help truncate text-sm",children:c})}),e.jsx(Ee,{children:e.jsx("p",{className:"max-w-[300px]",children:c})})]})})},enableSorting:!0,enableHiding:!0},{accessorKey:"city",header:({column:s})=>e.jsx(V,{column:s,title:"Thành phố"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("city")}),enableSorting:!1,enableHiding:!0},{accessorKey:"buffetConfig",header:({column:s})=>e.jsx(V,{column:s,title:"Cấu hình buffet"}),cell:({row:s})=>{var a;const i=s.original;return((a=i.extra_data)==null?void 0:a.is_buffet_item)===1?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Đã cấu hình"}),e.jsx(y,{variant:"outline",size:"sm",onClick:()=>n(i),className:"h-6 px-2 text-xs",children:e.jsx(Q,{className:"h-3 w-3"})})]}):e.jsxs(y,{variant:"outline",size:"sm",onClick:()=>n(i),className:"h-7 px-2 text-xs",children:[e.jsx(Q,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{accessorKey:"customization",header:({column:s})=>e.jsx(V,{column:s,title:"Customization"}),cell:({row:s,table:i})=>{var j;const c=s.original,a=i.options.meta,o=c.customization_uid,d=(j=a==null?void 0:a.customizations)==null?void 0:j.find(_=>_.id===o);return d?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:d.name}),e.jsx(y,{variant:"outline",size:"sm",onClick:()=>{var _;return(_=a==null?void 0:a.onCustomizationClick)==null?void 0:_.call(a,c)},className:"h-6 px-2 text-xs",children:e.jsx(Q,{className:"h-3 w-3"})})]}):e.jsxs(y,{variant:"outline",size:"sm",onClick:()=>{var _;return(_=a==null?void 0:a.onCustomizationClick)==null?void 0:_.call(a,c)},className:"h-7 px-2 text-xs",children:[e.jsx(Q,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{id:"copy",header:"Sao chép tạo món mới",cell:({row:s,table:i})=>{const c=s.original,a=i.options.meta;return e.jsxs(y,{variant:"ghost",size:"sm",className:"ml-14 h-8 w-8",onClick:o=>{var d;o.stopPropagation(),(d=a==null?void 0:a.onCopyClick)==null||d.call(a,c)},children:[e.jsx(bs,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",c.item_name]})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"isActive",header:({column:s})=>e.jsx(be,{column:s,title:"Thao tác",defaultSort:"desc"}),enableSorting:!0,cell:({row:s,table:i})=>{const c=s.original,a=s.getValue("isActive"),o=i.options.meta;return e.jsx("div",{onClick:d=>{var j;d.stopPropagation(),(j=o==null?void 0:o.onToggleStatus)==null||j.call(o,c)},className:"cursor-pointer",children:e.jsx(vs,{isActive:a,activeText:"Active",inactiveText:"Deactive"})})},enableHiding:!0},{id:"actions",cell:({row:s,table:i})=>{const c=s.original,a=i.options.meta;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(y,{variant:"ghost",size:"sm",onClick:o=>{var d;o.stopPropagation(),(d=a==null?void 0:a.onDeleteClick)==null||d.call(a,c)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(Ns,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa món ",c.item_name]})]})})},enableSorting:!1,enableHiding:!1,size:80}],Vs=Ps;function Hs({currentPage:n,onPageChange:s,hasNextPage:i}){const c=()=>{n>1&&s(n-1)},a=()=>{i&&s(n+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(y,{variant:"outline",size:"sm",onClick:c,disabled:n===1,className:"flex items-center gap-2",children:[e.jsx(Is,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:n}),e.jsxs(y,{variant:"outline",size:"sm",onClick:a,disabled:!i,className:"flex items-center gap-2",children:["Sau",e.jsx(Ds,{className:"h-4 w-4"})]})]})}const Us=[{label:"Thứ 2",value:"2"},{label:"Thứ 3",value:"3"},{label:"Thứ 4",value:"4"},{label:"Thứ 5",value:"5"},{label:"Thứ 6",value:"6"},{label:"Thứ 7",value:"7"},{label:"Chủ Nhật",value:"1"}],As=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}];function Ls({table:n,selectedItemTypeUid:s="all",onItemTypeChange:i,selectedCityUid:c="all",onCityChange:a,selectedDaysOfWeek:o=[],onDaysOfWeekChange:d,selectedStatus:j="all",onStatusChange:_,onDeleteSelected:M}){var r;const[I,N]=u.useState(!1),{data:C=[]}=fe(),{data:x=[]}=ge(),h=x.filter(t=>t.active===1),p=h.map(t=>({label:t.city_name,value:t.id})),k=h.map(t=>t.id).join(",");u.useEffect(()=>{c==="all"&&k&&a&&a(k)},[c,k,a]);const w=n.getState().columnFilters.length>0,b=n.getFilteredSelectedRowModel().rows.length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[b>0&&e.jsxs(y,{variant:"destructive",size:"sm",onClick:M,className:"h-9",children:[e.jsx(De,{}),"Xóa món (",b,")"]}),e.jsx(Ms,{placeholder:"Tìm kiếm món ăn...",value:((r=n.getColumn("name"))==null?void 0:r.getFilterValue())??"",onChange:t=>{var l;return(l=n.getColumn("name"))==null?void 0:l.setFilterValue(t.target.value)},className:"h-9 w-[150px] lg:w-[250px]"}),e.jsxs(ne,{value:c,onValueChange:t=>{a&&a(t)},children:[e.jsx(ce,{className:"h-10 w-[180px]",children:e.jsx(re,{placeholder:"Chọn thành phố"})}),e.jsxs(oe,{children:[e.jsx(W,{value:k,children:"Tất cả thành phố"}),p.map(t=>e.jsx(W,{value:t.value,children:t.label},t.value))]})]}),e.jsxs(ne,{value:j,onValueChange:_,children:[e.jsx(ce,{className:"h-10 w-[180px]",children:e.jsx(re,{placeholder:"Chọn Trạng thái"})}),e.jsx(oe,{children:As.map(t=>e.jsx(W,{value:t.value,children:t.label},t.value))})]}),e.jsxs(y,{variant:"outline",size:"sm",onClick:()=>N(!I),className:"h-9",children:[e.jsx(zs,{className:"h-4 w-4"}),"Nâng cao"]}),w&&e.jsxs(y,{variant:"ghost",onClick:()=>n.resetColumnFilters(),className:"h-10 px-2 lg:px-3",children:["Reset",e.jsx(Bs,{className:"ml-2 h-4 w-4"})]})]})}),I&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(ne,{value:s,onValueChange:i,children:[e.jsx(ce,{className:"h-10 w-[180px]",children:e.jsx(re,{placeholder:"Chọn loại món"})}),e.jsxs(oe,{children:[e.jsx(W,{value:"all",children:"Tất cả nhóm món"}),C.filter(t=>t.active===1).map(t=>({label:t.item_type_name,value:t.id})).map(t=>e.jsx(W,{value:t.value,children:t.label},t.value))]})]}),e.jsx(Rs,{options:Us,value:o,onValueChange:d||(()=>{}),placeholder:"Chọn ngày trong tuần",className:"min-h-9 w-[300px]",maxCount:1})]})]})}function Ks({columns:n,data:s,onCustomizationClick:i,onCopyClick:c,onToggleStatus:a,onRowClick:o,onDeleteClick:d,customizations:j,selectedItemTypeUid:_,onItemTypeChange:M,selectedCityUid:I,onCityChange:N,selectedDaysOfWeek:C,onDaysOfWeekChange:x,selectedStatus:h,onStatusChange:p,hasNextPageOverride:k,currentPage:w,onPageChange:b}){var K;const[r,t]=u.useState({}),[l,f]=u.useState({}),[R,H]=u.useState([]),[U,P]=u.useState([]),[S,g]=u.useState(!1),{deleteMultipleItemsAsync:A}=Ze(),B=()=>{g(!0)},q=async()=>{try{const T=F.getFilteredSelectedRowModel().rows.map(O=>O.original.id);await A(T),g(!1),F.resetRowSelection()}catch{}},G=(D,T)=>{const O=T.target;O.closest('input[type="checkbox"]')||O.closest("button")||O.closest('[role="button"]')||O.closest(".badge")||O.tagName==="BUTTON"||o==null||o(D)},F=ke({data:s,columns:n,state:{sorting:U,columnVisibility:l,rowSelection:r,columnFilters:R},enableRowSelection:!0,onRowSelectionChange:t,onSortingChange:P,onColumnFiltersChange:H,onColumnVisibilityChange:f,getCoreRowModel:Se(),getFilteredRowModel:Ts(),getSortedRowModel:Ss(),getFacetedRowModel:ks(),getFacetedUniqueValues:Cs(),meta:{onCustomizationClick:i,onCopyClick:c,onToggleStatus:a,onDeleteClick:d,customizations:j}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(Ls,{table:F,selectedItemTypeUid:_,onItemTypeChange:M,selectedCityUid:I,onCityChange:N,selectedDaysOfWeek:C,onDaysOfWeekChange:x,selectedStatus:h,onStatusChange:p,onDeleteSelected:B}),e.jsxs(Te,{className:"rounded-md border",children:[e.jsxs(xe,{className:"relative",children:[e.jsx(ue,{children:F.getHeaderGroups().map(D=>e.jsx(L,{children:D.headers.map(T=>e.jsx(ee,{colSpan:T.colSpan,children:T.isPlaceholder?null:Z(T.column.columnDef.header,T.getContext())},T.id))},D.id))}),e.jsx(pe,{children:(K=F.getRowModel().rows)!=null&&K.length?F.getRowModel().rows.map(D=>e.jsx(L,{"data-state":D.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:T=>G(D.original,T),children:D.getVisibleCells().map(T=>e.jsx(X,{children:Z(T.column.columnDef.cell,T.getContext())},T.id))},D.id)):e.jsx(L,{children:e.jsx(X,{colSpan:n.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(me,{orientation:"horizontal"})]}),e.jsx(Hs,{currentPage:w??1,onPageChange:D=>b&&b(D),hasNextPage:!!k}),e.jsx(Ie,{open:S,onOpenChange:g,title:`Bạn có chắc muốn xóa ${F.getFilteredSelectedRowModel().rows.length} món đã chọn`,desc:"Hành động này không thể hoàn tác.",confirmText:"Xóa",cancelBtnText:"Hủy",className:"top-[30%] translate-y-[-50%]",handleConfirm:q,destructive:!0})]})}function Es(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(m,{className:"h-8 w-[250px]"}),e.jsx(m,{className:"h-8 w-[100px]"}),e.jsx(m,{className:"h-8 w-[100px]"}),e.jsx(m,{className:"h-8 w-[100px]"})]}),e.jsx(m,{className:"h-8 w-[100px]"})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsx("div",{className:"border-b p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(m,{className:"h-4 w-8"}),e.jsx(m,{className:"h-4 w-20"}),e.jsx(m,{className:"h-4 w-32"}),e.jsx(m,{className:"h-4 w-20"}),e.jsx(m,{className:"h-4 w-16"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-20"}),e.jsx(m,{className:"h-4 w-16"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-20"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-16"})]})}),Array.from({length:10}).map((n,s)=>e.jsx("div",{className:"border-b p-4 last:border-b-0",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(m,{className:"h-4 w-8"}),e.jsx(m,{className:"h-4 w-20"}),e.jsx(m,{className:"h-4 w-32"}),e.jsx(m,{className:"h-4 w-20"}),e.jsx(m,{className:"h-4 w-16"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-20"}),e.jsx(m,{className:"h-4 w-16"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-20"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-24"}),e.jsx(m,{className:"h-4 w-16"})]})},s))]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(m,{className:"h-8 w-[200px]"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(m,{className:"h-8 w-[100px]"}),e.jsx(m,{className:"h-8 w-8"}),e.jsx(m,{className:"h-8 w-8"})]})]})]})}function qs({open:n,onOpenChange:s,data:i,onSave:c}){var C;const[a,o]=u.useState(i);ye.useEffect(()=>{o(i)},[i]);const d=()=>{c(a),z.success("Data saved successfully"),s(!1)},j=()=>{s(!1)},_=ye.useCallback(x=>{const h=a.filter((p,k)=>k!==x);o(h)},[a]),{tableData:M,columns:I}=u.useMemo(()=>{if(!a||a.length===0)return{tableData:[],columns:[]};const x=a[0]||[],h=a.slice(1),p=[{id:"actions",header:"-",cell:({row:w})=>e.jsx(y,{variant:"ghost",size:"sm",onClick:()=>_(w.original._originalIndex),className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:e.jsx(js,{className:"h-4 w-4"})}),enableSorting:!1,enableHiding:!1,size:50,meta:{className:"w-12 text-center sticky left-0 bg-background z-20 border-r"}},...x.map((w,b)=>({id:`col_${b}`,accessorKey:`col_${b}`,header:String(w),cell:({row:r})=>e.jsx("div",{className:"min-w-[150px] whitespace-nowrap",children:r.getValue(`col_${b}`)}),enableSorting:!1,enableHiding:!1,meta:{className:"min-w-[150px] px-4 whitespace-nowrap"}}))];return{tableData:h.map((w,b)=>{const r={_originalIndex:b+1};return w.forEach((t,l)=>{r[`col_${l}`]=t}),r}),columns:p}},[a,_]),N=ke({data:M,columns:I,getCoreRowModel:Se()});return!a||a.length===0?null:e.jsx(te,{open:n,onOpenChange:s,children:e.jsx(ae,{className:"h-[525px] w-[1140px] !max-w-[1140px] overflow-hidden p-0",children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx(ie,{className:"shrink-0 border-b px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(le,{className:"text-xl font-semibold",children:"Thêm món từ file"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(y,{variant:"outline",size:"sm",onClick:j,children:"Đóng"}),e.jsx(y,{size:"sm",className:"bg-green-600 hover:bg-green-700",onClick:d,children:"Lưu"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden p-6",children:e.jsx("div",{className:"bg-background h-full w-full overflow-auto rounded-lg border",children:e.jsx("div",{className:"min-w-max",children:e.jsxs(xe,{children:[e.jsx(ue,{className:"bg-muted/50 sticky top-0 z-10",children:N.getHeaderGroups().map(x=>e.jsx(L,{children:x.headers.map(h=>{var p;return e.jsx(ee,{className:((p=h.column.columnDef.meta)==null?void 0:p.className)||"",children:h.isPlaceholder?null:Z(h.column.columnDef.header,h.getContext())},h.id)})},x.id))}),e.jsx(pe,{children:(C=N.getRowModel().rows)!=null&&C.length?N.getRowModel().rows.map(x=>e.jsx(L,{className:"hover:bg-muted/50",children:x.getVisibleCells().map(h=>{var p;return e.jsx(X,{className:((p=h.column.columnDef.meta)==null?void 0:p.className)||"",children:Z(h.column.columnDef.cell,h.getContext())},h.id)})},x.id)):e.jsx(L,{children:e.jsx(X,{colSpan:I.length,className:"h-24 text-center",children:"No data."})})})]})})})})]})})})}function $s(){const{open:n,setOpen:s}=se(),[i,c]=u.useState(!1),[a,o]=u.useState([]),d=u.useRef(null),{downloadTemplateAsync:j,isPending:_}=Ce(),M=async()=>{try{const x=await j({city_uid:"all",item_type_uid:"all",active:"all"}),h=window.URL.createObjectURL(x),p=document.createElement("a");p.href=h,p.download=`items-template-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(p),p.click(),document.body.removeChild(p),window.URL.revokeObjectURL(h),z.success("Tải template thành công")}catch{z.error("Lỗi khi tải template")}},I=()=>{var x;(x=d.current)==null||x.click()},N=x=>{var k;const h=(k=x.target.files)==null?void 0:k[0];if(!h)return;const p=new FileReader;p.onload=w=>{var b;try{const r=new Uint8Array((b=w.target)==null?void 0:b.result),t=Me(r,{type:"array"}),l=t.SheetNames[0],f=t.Sheets[l],R=Re.sheet_to_json(f,{header:1,defval:"",raw:!1});if(R.length===0){z.error("File không có dữ liệu");return}o(R),s(null),c(!0),d.current&&(d.current.value="")}catch{z.error("Lỗi khi đọc file. Vui lòng kiểm tra định dạng file.")}},p.readAsArrayBuffer(h)},C=()=>{z.success("Dữ liệu đã được lưu thành công!"),c(!1),s(null)};return e.jsxs(e.Fragment,{children:[e.jsx(te,{open:n==="import",onOpenChange:x=>s(x?"import":null),children:e.jsxs(ae,{className:"max-w-2xl",children:[e.jsx(ie,{children:e.jsx(le,{children:"Thêm món"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsx(y,{variant:"outline",size:"sm",onClick:M,disabled:_,className:"flex items-center gap-2",children:_?"Đang tải...":e.jsxs(e.Fragment,{children:["Tải xuống",e.jsx(Y,{className:"h-4 w-4"})]})})]})}),e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Không được để trống các cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Tên, Thành phố"}),"."]}),e.jsxs("p",{children:["Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị đã có vào cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Nhóm, Loại món"}),"."]}),e.jsxs("p",{children:["Mã đơn vị món, mã thành phố có thể xem trong sheet"," ",e.jsx("span",{className:"font-mono text-blue-600",children:"Guide"})," của file mẫu."]})]})]}),e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"})]}),e.jsxs(y,{variant:"outline",size:"sm",onClick:I,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(he,{className:"h-4 w-4"})]})]})})]})]})}),e.jsx("input",{ref:d,type:"file",accept:".xlsx,.xls",onChange:N,style:{display:"none"}}),e.jsx(qs,{open:i,onOpenChange:c,data:a,onSave:C})]})}function Xs({open:n,onOpenChange:s,data:i}){const[c,a]=u.useState(i),[o,d]=u.useState(!1),{user:j,company:_}=qe(r=>r.auth),{selectedBrand:M}=$e(),{importItemsAsync:I,isPending:N}=es(),{data:C=[]}=fe({skip_limit:!0}),{data:x=[]}=ze({skip_limit:!0}),{data:h=[]}=Be(),{data:p=[]}=ge();u.useEffect(()=>{a(i)},[i]);const k=r=>{a(t=>t.filter((l,f)=>f!==r))},w=async()=>{if(!_||!M){z.error("Thiếu thông tin cần thiết để cập nhật");return}d(!0);const r=c.map(t=>{const l=h.find(S=>S.unit_id===t.unit_id),f=p.find(S=>S.city_name===t.city_name),R=C.find(S=>S.item_type_id===t.item_type_id||S.item_type_name===t.item_type_name),H=x.find(S=>S.item_class_id===t.item_class_id||S.item_class_name===t.item_class_name),U=h.find(S=>S.unit_id==="MON"),P=C.find(S=>S.item_type_name==="LOẠI KHÁC");return{id:t.id,item_id:t.item_id,item_name:t.item_name,description:t.description||"",ots_price:t.ots_price||0,ots_tax:(t.ots_tax||0)/100,ta_price:t.ots_price||0,ta_tax:(t.ots_tax||0)/100,time_sale_hour_day:Number(t.time_sale_hour_day??0),time_sale_date_week:Number(t.time_sale_date_week??0),allow_take_away:1,is_eat_with:t.is_eat_with||0,image_path:t.image_path||"",image_path_thumb:t.image_path?`${t.image_path}?width=185`:"",item_color:"",list_order:t.list_order||0,is_service:0,is_material:0,active:t.active||1,user_id:"",is_foreign:0,quantity_default:0,price_change:t.price_change||0,currency_type_id:"",point:0,is_gift:0,is_fc:0,show_on_web:0,show_price_on_web:0,cost_price:0,is_print_label:0,quantity_limit:0,is_kit:0,time_cooking:t.time_cooking||0,item_id_barcode:t.item_id_barcode||"",process_index:0,is_allow_discount:0,quantity_per_day:0,item_id_eat_with:"",is_parent:0,is_sub:0,item_id_mapping:String(t.sku||""),effective_date:0,expire_date:0,sort:t.list_order||1,extra_data:{formula_qrcode:t.inqr_formula||"",is_buffet_item:t.is_buffet_item||0,up_size_buffet:[],is_item_service:t.is_item_service||0,is_virtual_item:t.is_virtual_item||0,price_by_source:[],enable_edit_price:t.price_change||0,exclude_items_buffet:[],no_update_quantity_toping:t.no_update_quantity_toping||0},revision:0,unit_uid:(l==null?void 0:l.id)||(U==null?void 0:U.id)||"",unit_secondary_uid:null,item_type_uid:(R==null?void 0:R.id)||(P==null?void 0:P.id)||"",item_class_uid:(H==null?void 0:H.id)||void 0,source_uid:null,brand_uid:M.id,company_uid:_.id,customization_uid:"",is_fabi:1,deleted:!1,created_by:(j==null?void 0:j.email)||"",updated_by:(j==null?void 0:j.email)||"",deleted_by:null,created_at:Math.floor(Date.now()/1e3),updated_at:Math.floor(Date.now()/1e3),deleted_at:null,apply_with_store:2,cities:f?[{id:f.id,city_id:f.city_id||"",fb_city_id:f.fb_city_id||"",city_name:f.city_name,image_path:f.image_path,description:f.description||"",active:f.active||1,extra_data:f.extra_data,revision:f.revision||0,sort:f.sort||0,created_by:f.created_by,updated_by:f.updated_by,deleted_by:f.deleted_by,created_at:f.created_at||0,updated_at:f.updated_at||0,deleted_at:f.deleted_at,items_cities:{item_uid:t.id,city_uid:f.id}}]:[],status_trigger_disabled:!1}});try{await I(r),d(!1),s(!1)}catch(t){console.error("Error updating items:",t),z.error(`Có lỗi xảy ra khi cập nhật món ăn: ${t}`),d(!1)}},b=[{key:"item_id",label:"Mã món",width:"120px"},{key:"city_name",label:"Thành phố",width:"120px"},{key:"item_name",label:"Tên",width:"200px"},{key:"ots_price",label:"Giá",width:"100px"},{key:"active",label:"Trạng thái",width:"100px"},{key:"item_id_barcode",label:"Mã barcode",width:"120px"},{key:"is_eat_with",label:"Món ăn kèm",width:"120px"},{key:"no_update_quantity_toping",label:"Không cập nhật số lượng",width:"180px"},{key:"unit_name",label:"Đơn vị",width:"100px"},{key:"item_type_id",label:"Nhóm",width:"120px"},{key:"item_type_name",label:"Tên nhóm",width:"150px"},{key:"item_class_id",label:"Loại món",width:"120px"},{key:"item_class_name",label:"Tên loại",width:"150px"},{key:"description",label:"Mô tả",width:"200px"},{key:"sku",label:"SKU",width:"100px"},{key:"ots_tax",label:"VAT (%)",width:"80px"},{key:"time_cooking",label:"Thời gian chế biến (phút)",width:"180px"},{key:"price_change",label:"Cho phép sửa giá khi bán",width:"180px"},{key:"is_virtual_item",label:"Cấu hình món ảo",width:"150px"},{key:"is_item_service",label:"Cấu hình món dịch vụ",width:"180px"},{key:"is_buffet_item",label:"Cấu hình món ăn là vé buffet",width:"200px"},{key:"time_sale_hour_day",label:"Giờ",width:"80px"},{key:"time_sale_date_week",label:"Ngày",width:"80px"},{key:"list_order",label:"Thứ tự",width:"80px"},{key:"image_path",label:"Hình ảnh",width:"120px"},{key:"inqr_formula",label:"Công thức inQR cho máy pha trà",width:"220px"}];return e.jsx(te,{open:n,onOpenChange:s,children:e.jsxs(ae,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(ie,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(le,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(Te,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(xe,{children:[e.jsx(ue,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(L,{children:[e.jsx(ee,{className:"w-12"}),b.map(r=>e.jsx(ee,{style:{width:r.width},children:r.label},r.key))]})}),e.jsx(pe,{children:c.map((r,t)=>e.jsxs(L,{children:[e.jsx(X,{children:e.jsx(y,{variant:"ghost",size:"icon",onClick:()=>k(t),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(De,{className:"h-4 w-4"})})}),b.map(l=>{var f;return e.jsxs(X,{style:{width:l.width},children:[l.key==="ots_price"&&e.jsxs("span",{className:"text-right",children:[(f=r[l.key])==null?void 0:f.toLocaleString("vi-VN")," ₫"]}),l.key==="active"&&e.jsx("span",{children:r[l.key]}),(l.key==="item_id"||l.key==="item_id_barcode")&&e.jsx("span",{className:"font-mono text-sm",children:r[l.key]}),l.key==="item_name"&&e.jsx("span",{className:"font-medium",children:r[l.key]}),(l.key==="is_eat_with"||l.key==="no_update_quantity_toping"||l.key==="price_change"||l.key==="is_virtual_item"||l.key==="is_item_service"||l.key==="is_buffet_item")&&e.jsx("span",{className:"text-center",children:r[l.key]}),l.key!=="ots_price"&&l.key!=="active"&&l.key!=="item_id"&&l.key!=="item_id_barcode"&&l.key!=="item_name"&&l.key!=="is_eat_with"&&l.key!=="no_update_quantity_toping"&&l.key!=="price_change"&&l.key!=="is_virtual_item"&&l.key!=="is_item_service"&&l.key!=="is_buffet_item"&&e.jsx("span",{children:r[l.key]||""})]},l.key)})]},t))})]}),e.jsx(me,{orientation:"horizontal"}),e.jsx(me,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(y,{variant:"outline",onClick:()=>s(!1),children:"Đóng"}),e.jsx(y,{onClick:w,disabled:o||N,children:o||N?"Đang lưu...":"Lưu"})]})]})]})})}function Gs({open:n,onOpenChange:s}){const[i,c]=u.useState("all"),[a,o]=u.useState("all"),[d,j]=u.useState("all"),[_,M]=u.useState([]),[I,N]=u.useState(!1),C=u.useRef(null),{data:x=[]}=fe(),{data:h=[]}=ze(),{data:p=[]}=Be(),{data:k=[]}=ge(),{downloadTemplateAsync:w,isPending:b}=Ce(),{fetchItemsDataAsync:r,isPending:t}=ss(),l=[{label:"Tất cả nhóm món",value:"all"},...x.filter(g=>g.active===1).map(g=>({label:g.item_type_name,value:g.id}))],f=[{label:"Tất cả thành phố",value:"all"},...k.filter(g=>g.active===1).map(g=>({label:g.city_name,value:g.id}))],R=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}],H=async()=>{try{const g=await w({city_uid:i!=="all"?i:void 0,item_type_uid:a!=="all"?a:void 0,active:d!=="all"?d:void 0,referenceData:{itemTypes:x,itemClasses:h,units:p}}),A=window.URL.createObjectURL(g),B=document.createElement("a");B.href=A,B.download=`danh-sach-mon-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(B),B.click(),document.body.removeChild(B),window.URL.revokeObjectURL(A),z.success("Tải xuống template thành công!")}catch{z.error("Lỗi khi tải template")}},U=async()=>{try{const g=await r({city_uid:i!=="all"?i:void 0,item_type_uid:a!=="all"?a:void 0,active:d!=="all"?d:void 0});await ts({itemTypes:x,itemClasses:h,units:p},g),z.success("Tải xuống template mẫu thành công!")}catch{z.error("Lỗi khi tải template mẫu")}},P=g=>{var q;const A=(q=g.target.files)==null?void 0:q[0];if(!A)return;const B=new FileReader;B.onload=G=>{var F;try{const K=new Uint8Array((F=G.target)==null?void 0:F.result),D=Me(K,{type:"array"}),T=D.SheetNames[0],O=D.Sheets[T],v=Re.sheet_to_json(O,{header:1});if(v.length>0){const E=v,J=E[0]||[],Fe=E.slice(1).map((Oe,Pe)=>{const je={id:`temp_${Pe}`};return J.forEach((Ve,He)=>{const Ue=String(Ve).toLowerCase().replace(/\s+/g,"_");je[Ue]=Oe[He]||""}),je});M(Fe),N(!0),z.success("File uploaded successfully")}}catch{z.error("Error parsing file")}},B.readAsArrayBuffer(A)},S=()=>{var g;(g=C.current)==null||g.click()};return e.jsxs(e.Fragment,{children:[e.jsx(te,{open:n,onOpenChange:s,children:e.jsxs(ae,{className:"max-w-2xl lg:max-w-xl",children:[e.jsx(ie,{children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(le,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(de,{options:f,value:i,onValueChange:c,placeholder:"Tất cả thành phố",searchPlaceholder:"Tìm thành phố...",className:"flex-1"}),e.jsx(de,{options:l,value:a,onValueChange:o,placeholder:"Tất cả nhóm món",searchPlaceholder:"Tìm nhóm món...",className:"flex-1"}),e.jsx(de,{options:R,value:d,onValueChange:j,placeholder:"Tất cả trạng thái",searchPlaceholder:"Tìm trạng thái...",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải xuống dữ liệu hiện tại"}),e.jsxs(y,{variant:"outline",size:"sm",onClick:H,disabled:b,className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),b?"Đang tải...":"Tải dữ liệu"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải template mẫu (với header mới)"}),e.jsxs(y,{variant:"outline",size:"sm",onClick:U,disabled:t,className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),t?"Đang tải...":"Tải template mẫu"]})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột :"}),e.jsx("p",{className:"font-mono text-sm text-blue-600",children:"ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại."})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs(y,{variant:"outline",size:"sm",onClick:S,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(he,{className:"h-4 w-4"})]}),e.jsx("input",{ref:C,type:"file",accept:".xlsx,.xls",onChange:P,style:{display:"none"}})]})]})]})]})}),e.jsx(Xs,{open:I,onOpenChange:N,data:_})]})}function Ws(){const{open:n,setOpen:s,currentRow:i,setCurrentRow:c}=se(),{deleteItemAsync:a}=as();return e.jsxs(e.Fragment,{children:[e.jsx(Gs,{open:n==="export-dialog",onOpenChange:()=>s(null)}),e.jsx($s,{}),i&&e.jsx(e.Fragment,{children:e.jsx(Ie,{destructive:!0,open:n==="delete",onOpenChange:o=>{o||(s(null),setTimeout(()=>{c(null)},500))},handleConfirm:async()=>{s(null),setTimeout(()=>{c(null)},500),await a(i.id||"")},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")})]})}function Js(){const n=Ne(),[s,i]=u.useState(1),{setOpen:c,setCurrentRow:a}=se(),{updateStatusAsync:o}=ls(),{updateItemAsync:d}=ns(),{isCustomizationDialogOpen:j,isBuffetItem:_,isBuffetConfigModalOpen:M,setIsCustomizationDialogOpen:I,setIsBuffetItem:N,selectedMenuItem:C,setSelectedMenuItem:x,setIsBuffetConfigModalOpen:h,selectedBuffetMenuItem:p,setSelectedBuffetMenuItem:k,selectedItemTypeUid:w,setSelectedItemTypeUid:b,selectedCityUid:r,setSelectedCityUid:t,selectedDaysOfWeek:l,setSelectedDaysOfWeek:f,selectedStatus:R,setSelectedStatus:H}=Fs(),U=u.useMemo(()=>({...w!=="all"&&{item_type_uid:w},...r!=="all"&&{city_uid:r},...l.length>0&&{time_sale_date_week:l.join(",")},...R!=="all"&&{active:parseInt(R,10)},page:s}),[w,r,l,R,s]);u.useEffect(()=>{i(1)},[w,r,l,R]);const{data:P=[],isLoading:S,error:g,hasNextPage:A}=cs({params:U}),{data:B=[]}=Xe({skip_limit:!0,list_city_uid:r!=="all"?[r]:void 0}),q=v=>{x(v),I(!0)},G=v=>{var E,J;x(v),k(((E=v==null?void 0:v.extra_data)==null?void 0:E.exclude_items_buffet)||[]),N(((J=v==null?void 0:v.extra_data)==null?void 0:J.is_buffet_item)===1),h(!0)},F=v=>{n({to:"/menu/items/items-in-city/detail",search:{id:v.id||""}})},K=v=>{a(v),c("delete")},D=v=>{n({to:"/menu/items/items-in-city/detail/$id",params:{id:v.id||""}})},T=async v=>{const E=v.active?0:1;await o({id:v.id||"",active:E})},O=Vs({onBuffetConfigClick:G});return g?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:g&&`Món ăn: ${(g==null?void 0:g.message)||"Lỗi không xác định"}`})]})}):e.jsxs(e.Fragment,{children:[e.jsx(Ge,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Qe,{}),e.jsx(Ye,{}),e.jsx(Je,{})]})}),e.jsxs(We,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Món ăn tại thành phố"})}),e.jsx(Os,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[S&&e.jsx(Es,{}),!S&&e.jsx(Ks,{columns:O,data:P,onCustomizationClick:q,onCopyClick:F,onToggleStatus:T,onRowClick:D,onDeleteClick:K,customizations:B,selectedItemTypeUid:w,onItemTypeChange:b,selectedCityUid:r,onCityChange:t,selectedDaysOfWeek:l,onDaysOfWeekChange:f,selectedStatus:R,onStatusChange:H,hasNextPageOverride:A,currentPage:s,onPageChange:i})]})]}),e.jsx(Ws,{}),j&&C&&e.jsx(rs,{open:j,onOpenChange:I,item:C,customizations:B}),M&&p&&e.jsx(os,{itemsBuffet:p,open:M,onOpenChange:h,onItemsChange:async v=>{await d({...C,extra_data:{is_buffet_item:_?1:0,exclude_items_buffet:v}})},items:P,hide:!1,enable:_,onEnableChange:N})]})}function Qs(){return e.jsx(is,{children:e.jsx(Js,{})})}const xa=Qs;export{xa as component};
