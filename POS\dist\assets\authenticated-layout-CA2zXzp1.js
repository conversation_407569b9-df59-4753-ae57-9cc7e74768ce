import{k as T,r as h,j as e,i as k,L as x,l as V,m as U,c as R,O as q}from"./index-UcdZ5AHH.js";import{s as F,S as W}from"./search-context-DK2BgvuK.js";import{u as f,S as Z,a as J,b,c as m,d as g,e as K,f as M,g as v,h as Q,i as X,j as Y,k as I,l as ee,m as ae}from"./date-range-picker-DxA68ufO.js";import{u as se}from"./useLocation-DMLaS369.js";import{C as D,a as A,b as _}from"./collapsible-Dz-Iaa-P.js";import{B as te}from"./badge-BlAal7b-.js";import{D as w,a as S,b as C,d as N,e as j,c as o,f as z,g as re}from"./dropdown-menu-D3XvynCv.js";import{C as y}from"./chevron-right-Dup7TmpK.js";import{a as ne}from"./use-auth-Ds6PM7xW.js";import{A as $,a as L,b as B}from"./avatar-BOI9P1fI.js";import{u as ie}from"./use-pos-data-nGOqH6A5.js";import{c as u}from"./createLucideIcon-D7O7McKr.js";import{S as le}from"./store-C2nYewOZ.js";import{C as O}from"./chevrons-up-down-BkDfU9b3.js";import"./form-D_U5B5Go.js";import{P as ce}from"./plus-ioKLfxbk.js";import"./pos-api-j20LMGrC.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=[["path",{d:"M2 13a2 2 0 0 0 2-2V7a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0V4a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0v-4a2 2 0 0 1 2-2",key:"57tc96"}]],P=u("audio-waveform",oe);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const de=[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]],xe=u("badge-check",de);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ue=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],he=u("bell",ue);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pe=[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]],E=u("command",pe);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],ge=u("credit-card",me);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["path",{d:"M7 2h10",key:"nczekb"}],["path",{d:"M5 6h14",key:"u2x4p"}],["rect",{width:"18",height:"12",x:"3",y:"10",rx:"2",key:"l0tzu3"}]],G=u("gallery-vertical-end",je);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fe=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],be=u("log-out",fe);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],ve=u("sparkles",Ne),ye={name:"satnaing",email:"<EMAIL>",avatar:"/avatars/shadcn.jpg"},ke=[{name:"POS TTM",logo:E,plan:"Work Hard. Have Fun. Make History"},{name:"Acme Inc",logo:G,plan:"Enterprise"},{name:"Acme Corp.",logo:P,plan:"Startup"}],we=()=>{const{isAuthenticated:a,brands:t,getActiveBrands:i}=T(),r=i();return h.useMemo(()=>!a||t.length===0?ke.map((c,l)=>({id:`fallback-${l}`,name:c.name,logo:c.logo,plan:c.plan,brandId:`fallback-${l}`,currency:"VND",active:!0})):r.map((n,c)=>({id:n.id,name:n.brand_name,logo:Se(c),plan:`${n.currency} • ${n.brand_id}`,brandId:n.brand_id,currency:n.currency,active:n.active===1})),[t,r,a])},Se=a=>{const t=[le,E,G,P];return t[a%t.length]},Ce=()=>{const{user:a,isAuthenticated:t}=ie();return h.useMemo(()=>!t||!a?ye:{name:a.full_name||a.email,email:a.email,avatar:"/avatars/shadcn.jpg"},[a,t])};function Me({title:a,items:t}){const{state:i}=f(),r=se({select:s=>s.href});return e.jsxs(Z,{children:[e.jsx(J,{children:a}),e.jsx(b,{children:t.map(s=>{const n=`${s.title}-${s.url}`;return s.items?i==="collapsed"?e.jsx(_e,{item:s,href:r},n):e.jsx(Ae,{item:s,href:r},n):e.jsx(De,{item:s,href:r},n)})})]})}const p=({children:a})=>e.jsx(te,{className:"rounded-full px-1 py-0 text-xs",children:a}),De=({item:a,href:t})=>{const{setOpenMobile:i}=f(),r=k(),s=n=>{if(a.guard==="crm"&&!localStorage.getItem("crm_token")){n.preventDefault(),r.navigate({to:"/crm/connect-crm"});return}i(!1)};return e.jsx(m,{children:e.jsx(g,{asChild:!0,isActive:d(t,a),tooltip:a.title,children:e.jsxs(x,{to:a.url,onClick:s,children:[a.icon&&e.jsx(a.icon,{}),e.jsx("span",{className:"truncate whitespace-nowrap",title:a.title,children:a.title}),a.badge&&e.jsx(p,{children:a.badge})]})})})},Ae=({item:a,href:t})=>{const{setOpenMobile:i}=f(),r=k();return e.jsx(D,{asChild:!0,defaultOpen:d(t,a,!0),className:"group/collapsible",children:e.jsxs(m,{children:[e.jsx(A,{asChild:!0,children:e.jsxs(g,{tooltip:a.title,children:[a.icon&&e.jsx(a.icon,{}),e.jsx("span",{className:"truncate whitespace-nowrap",title:a.title,children:a.title}),a.badge&&e.jsx(p,{children:a.badge}),e.jsx(y,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),e.jsx(_,{className:"CollapsibleContent",children:e.jsx(K,{children:a.items.map(s=>s.items?e.jsx(M,{children:e.jsx(D,{asChild:!0,defaultOpen:d(t,s,!0),className:"group/sub-collapsible",children:e.jsxs("div",{children:[e.jsx(A,{asChild:!0,children:e.jsxs(v,{children:[s.icon&&e.jsx(s.icon,{}),e.jsx("span",{className:"truncate whitespace-nowrap",title:s.title,children:s.title}),s.badge&&e.jsx(p,{children:s.badge}),e.jsx(y,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/sub-collapsible:rotate-90"})]})}),e.jsx(_,{className:"CollapsibleContent",children:e.jsx("div",{className:"mt-1 ml-4 space-y-1",children:s.items.map(n=>e.jsx(v,{asChild:!0,isActive:d(t,n),className:"text-sm",children:e.jsxs(x,{to:n.url,onClick:c=>{if(n.guard==="crm"&&!localStorage.getItem("crm_token")){c.preventDefault(),r.navigate({to:"/crm/connect-crm"});return}i(!1)},children:[n.icon&&e.jsx(n.icon,{}),e.jsx("span",{className:"truncate whitespace-nowrap",title:n.title,children:n.title}),n.badge&&e.jsx(p,{children:n.badge})]})},n.title))})})]})})},s.title):e.jsx(M,{children:e.jsx(v,{asChild:!0,isActive:d(t,s),children:e.jsxs(x,{to:s.url,onClick:n=>{if(s.guard==="crm"&&!localStorage.getItem("crm_token")){n.preventDefault(),r.navigate({to:"/crm/connect-crm"});return}i(!1)},children:[s.icon&&e.jsx(s.icon,{}),e.jsx("span",{className:"truncate whitespace-nowrap",title:s.title,children:s.title}),s.badge&&e.jsx(p,{children:s.badge})]})})},s.title))})})]})})},_e=({item:a,href:t})=>{const i=k();return e.jsx(m,{children:e.jsxs(w,{children:[e.jsx(S,{asChild:!0,children:e.jsxs(g,{tooltip:a.title,isActive:d(t,a),children:[a.icon&&e.jsx(a.icon,{}),e.jsx("span",{className:"truncate whitespace-nowrap",children:a.title}),a.badge&&e.jsx(p,{children:a.badge}),e.jsx(y,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),e.jsxs(C,{side:"right",align:"start",sideOffset:4,children:[e.jsxs(N,{children:[a.title," ",a.badge?`(${a.badge})`:""]}),e.jsx(j,{}),a.items.map(r=>r.items?e.jsxs("div",{children:[e.jsx(N,{className:"text-muted-foreground px-2 py-1 text-xs font-medium",children:r.title}),r.items.map(s=>e.jsx(o,{asChild:!0,children:e.jsxs(x,{to:s.url,className:`${d(t,s)?"bg-secondary":""} ml-4`,children:[s.icon&&e.jsx(s.icon,{}),e.jsx("span",{className:"max-w-52 text-wrap",children:s.title}),s.badge&&e.jsx("span",{className:"ml-auto text-xs",children:s.badge})]})},`${s.title}-${s.url}`))]},r.title):e.jsx(o,{asChild:!0,children:e.jsxs(x,{to:r.url,className:`${d(t,r)?"bg-secondary":""}`,onClick:s=>{if(r.guard==="crm"&&!localStorage.getItem("crm_token")){s.preventDefault(),i.navigate({to:"/crm/connect-crm"});return}},children:[r.icon&&e.jsx(r.icon,{}),e.jsx("span",{className:"max-w-52 text-wrap",children:r.title}),r.badge&&e.jsx("span",{className:"ml-auto text-xs",children:r.badge})]})},`${r.title}-${r.url}`))]})]})})};function d(a,t,i=!1){var r,s;return a===t.url||a.split("?")[0]===t.url||!!((r=t==null?void 0:t.items)!=null&&r.filter(n=>n.url===a).length)||i&&a.split("/")[1]!==""&&a.split("/")[1]===((s=t==null?void 0:t.url)==null?void 0:s.split("/")[1])}function ze({user:a}){const{isMobile:t}=f(),i=Ce(),r=ne(),s=i||a||{name:"Guest",email:"<EMAIL>",avatar:"/avatars/shadcn.jpg"},n=()=>{r.mutate()};return e.jsx(b,{children:e.jsx(m,{children:e.jsxs(w,{children:[e.jsx(S,{asChild:!0,children:e.jsxs(g,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[e.jsxs($,{className:"h-8 w-8 rounded-lg",children:[e.jsx(L,{src:s.avatar,alt:s.name}),e.jsx(B,{className:"rounded-lg",children:"SN"})]}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx("span",{className:"truncate font-semibold",children:s.name}),e.jsx("span",{className:"truncate text-xs",children:s.email})]}),e.jsx(O,{className:"ml-auto size-4"})]})}),e.jsxs(C,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:t?"bottom":"right",align:"end",sideOffset:4,children:[e.jsx(N,{className:"p-0 font-normal",children:e.jsxs("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[e.jsxs($,{className:"h-8 w-8 rounded-lg",children:[e.jsx(L,{src:s.avatar,alt:s.name}),e.jsx(B,{className:"rounded-lg",children:"SN"})]}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx("span",{className:"truncate font-semibold",children:s.name}),e.jsx("span",{className:"truncate text-xs",children:s.email})]})]})}),e.jsx(j,{}),e.jsx(z,{children:e.jsxs(o,{children:[e.jsx(ve,{}),"Upgrade to Pro"]})}),e.jsx(j,{}),e.jsxs(z,{children:[e.jsx(o,{asChild:!0,children:e.jsxs(x,{to:"/settings/account",children:[e.jsx(xe,{}),"Account"]})}),e.jsx(o,{asChild:!0,children:e.jsxs(x,{to:"/settings",children:[e.jsx(ge,{}),"Billing"]})}),e.jsx(o,{asChild:!0,children:e.jsxs(x,{to:"/settings/notifications",children:[e.jsx(he,{}),"Notifications"]})})]}),e.jsx(j,{}),e.jsxs(o,{onClick:n,disabled:r.isPending,children:[e.jsx(be,{}),r.isPending?"Logging out...":"Log out"]})]})]})})})}function $e(){const{isMobile:a}=f(),t=we(),{selectedBrand:i,setSelectedBrand:r}=V(),[s,n]=h.useState(!1);if(h.useEffect(()=>{!i&&t.length>0&&r(t[0])},[i,t,r]),!i)return e.jsx(b,{children:e.jsx(m,{children:e.jsxs(g,{size:"lg",disabled:!0,children:[e.jsx("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:e.jsx("div",{className:"size-4 animate-pulse rounded bg-current"})}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx("span",{className:"truncate font-semibold",children:"Loading..."}),e.jsx("span",{className:"truncate text-xs",children:"Selecting brand"})]})]})})});const c=l=>{n(!0),r(l),setTimeout(()=>{n(!1)},200)};return e.jsx(b,{children:e.jsx(m,{children:e.jsxs(w,{children:[e.jsx(S,{asChild:!0,children:e.jsxs(g,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",disabled:s,children:[e.jsx("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:i.logo&&typeof i.logo=="function"?h.createElement(i.logo,{className:"size-4"}):e.jsx("div",{className:"size-4 rounded bg-current"})}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx("span",{className:"truncate font-semibold",children:s?"Switching...":i.name}),e.jsx("span",{className:"truncate text-xs",children:i.plan})]}),e.jsx(O,{className:"ml-auto"})]})}),e.jsxs(C,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"start",side:a?"bottom":"right",sideOffset:4,children:[e.jsx(N,{className:"text-muted-foreground text-xs",children:"Brands"}),t.map((l,H)=>e.jsxs(o,{onClick:()=>c(l),className:"gap-2 p-2",disabled:s,children:[e.jsx("div",{className:"flex size-6 items-center justify-center rounded-sm border",children:l.logo&&typeof l.logo=="function"?h.createElement(l.logo,{className:"size-4 shrink-0"}):e.jsx("div",{className:"size-4 rounded bg-current"})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:l.name}),l.currency&&e.jsxs("span",{className:"text-muted-foreground text-xs",children:[l.currency," • ",l.brandId||"ID"]})]}),e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[(i==null?void 0:i.id)===l.id&&e.jsx("div",{className:"bg-primary size-2 rounded-full"}),e.jsxs(re,{children:["⌘",H+1]})]})]},l.id||l.name)),e.jsx(j,{}),e.jsxs(o,{className:"gap-2 p-2",children:[e.jsx("div",{className:"bg-background flex size-6 items-center justify-center rounded-md border",children:e.jsx(ce,{className:"size-4"})}),e.jsx("div",{className:"text-muted-foreground font-medium",children:"Add brand"})]})]})]})})})}function Le({...a}){return e.jsxs(Q,{collapsible:"icon",variant:"floating",...a,children:[e.jsx(X,{children:e.jsx($e,{})}),e.jsx(Y,{children:F.navGroups.map(t=>e.jsx(Me,{...t},t.title))}),e.jsx(I,{children:e.jsx(ze,{})}),e.jsx(ee,{})]})}const Be=()=>e.jsx("a",{className:"bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring fixed left-44 z-999 -translate-y-52 px-4 py-2 text-sm font-medium whitespace-nowrap opacity-95 shadow-sm transition focus:translate-y-3 focus:transform focus-visible:ring-1",href:"#content",children:"Skip to Main"});function Ye({children:a}){const t=U.get("sidebar_state")!=="false";return e.jsx(W,{children:e.jsxs(ae,{defaultOpen:t,children:[e.jsx(Be,{}),e.jsx(Le,{}),e.jsx("div",{id:"content",className:R("ml-auto w-full max-w-full","peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)]","peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]","sm:transition-[width] sm:duration-200 sm:ease-linear","flex h-svh flex-col","group-data-[scroll-locked=1]/body:h-full","has-[main.fixed-main]:group-data-[scroll-locked=1]/body:h-svh"),children:a||e.jsx(q,{})})]})})}export{Ye as A};
