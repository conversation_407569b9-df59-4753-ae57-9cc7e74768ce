import{aZ as r,j as t}from"./index-CVQ6JZo2.js";import{S as i}from"./index-Dn1YclZO.js";import"./use-service-charge-form-Be66dHnR.js";import"./date-utils-DBbLjCz0.js";import"./useQuery-HgcIHxlE.js";import"./utils-km2FGkQ4.js";import"./useMutation-ZsyDznMu.js";import"./pos-api-mRg02iop.js";import"./query-keys-3lmd-xp6.js";import"./discount-toggle-button-D2fQjrQB.js";import"./date-range-picker-CXbMaowj.js";import"./calendar-BszTCdZH.js";import"./createLucideIcon-DKVxsQv7.js";import"./index-CtK-wKtB.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-CxgpqvrH.js";import"./react-icons.esm-DMMA_g0o.js";import"./popover-DnoSPJNX.js";import"./select-BFhNE0YE.js";import"./index-LVHINuqD.js";import"./index-nc1u7392.js";import"./check-BE_j5GZD.js";import"./form-CzmGigtT.js";import"./input-Al6WtUZF.js";import"./tabs-Bk_fz3zz.js";import"./index-C34iUvGy.js";import"./textarea-DQwjUKcg.js";import"./checkbox-BfLSzhzg.js";import"./modal-DEESjk2b.js";import"./dialog-DDrduXt3.js";import"./collapsible-CRaCKnru.js";import"./calendar-CYB-o1z2.js";import"./circle-help-CHqJBYbr.js";import"./switch-DKe-2oYu.js";const G=function(){const{id:o}=r.useParams();return console.log("🔥 Service Charge Detail Page - URL Params:"),console.log("🔥 serviceChargeId:",o),t.jsx(i,{serviceChargeId:o})};export{G as component};
