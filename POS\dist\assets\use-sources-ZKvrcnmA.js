import{u as i}from"./useQuery-B4yhTgGk.js";import{u as p}from"./index-UcdZ5AHH.js";import{s}from"./sources-api-D61PDxyv.js";import{Q as a}from"./query-keys-3lmd-xp6.js";function l(u={}){const{enabled:m=!0,...c}=u,{company:r,brands:t}=p(n=>n.auth),o=t==null?void 0:t[0],e={company_uid:(r==null?void 0:r.id)||"",brand_uid:(o==null?void 0:o.id)||"",city_uid:"",skip_limit:!0,...c};return i({queryKey:[a.SOURCES,e],queryFn:()=>s.getSources(e),enabled:m&&!!e.company_uid&&!!e.brand_uid,staleTime:5*60*1e3,gcTime:10*60*1e3})}function _(u=!0){return i({queryKey:[a.SOURCES,"autocomplete","is_fb=1"],queryFn:()=>s.getSourcesForAutocomplete(),enabled:u,staleTime:10*60*1e3,gcTime:30*60*1e3})}export{_ as a,l as u};
