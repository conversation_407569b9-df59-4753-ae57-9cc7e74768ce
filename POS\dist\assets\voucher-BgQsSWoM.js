import{r as m,j as e,B as y}from"./index-CfbMU4Ye.js";import{C as p,d as N,a as F,b as Y}from"./card-Dq-aHO9v.js";import{I as U}from"./input-D8TU6hMD.js";import{P as B,a as q,b as X}from"./popover-C4SSkcaE.js";import{C as J}from"./calendar-BqbXMtoi.js";import{a as Q,C as Z}from"./chevron-right-BwGWQXH2.js";import{s as ee,e as te,i as E,a as G,b as P}from"./subMonths-BRhS7Uii.js";import{f as b,g as se,r as W,v as ae,l as ne,q as R,s as D}from"./isSameMonth-C8JQo-AN.js";import{R as V,X as z,Y as $,a0 as A}from"./generateCategoricalChart-qSn9hoZi.js";import{L as K}from"./LineChart-U2cypp8l.js";import{L as T}from"./Line-yhXG0L-j.js";import"./index-CBP3KeI0.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-Chjiymov.js";function re({startDate:g,endDate:i,onDateChange:k,dateRange:S,onDateRangeChange:H}){const[_,w]=m.useState(!1),[h,u]=m.useState(new Date),[a,v]=m.useState(null),[r,f]=m.useState(null),s=t=>{w(t),t||(v(null),f(null))},n=t=>{!a||a&&i?(v(t),f(null),k(t,null)):(t>=a?k(a,t):k(t,a),v(null),f(null),w(!1))},c=()=>{const t=ae(h),o=ne(h),d=new Date(t);d.setDate(d.getDate()-t.getDay()+1);const j=new Date(o);return j.setDate(j.getDate()+(6-o.getDay())),te({start:d,end:j})},x=t=>{if(g&&i)return E(t,{start:g,end:i});if(a&&r){const o=a<r?a:r,d=a<r?r:a;return E(t,{start:o,end:d})}return!1},L=t=>g&&R(t,g)?"start":i&&R(t,i)?"end":a&&R(t,a)?"temp":!1,I=t=>{const o=L(t),d=x(t),j=W(t,h),O=R(t,new Date),M=r&&R(t,r);let l="h-8 w-8 p-0 text-xs font-normal transition-colors ";return j?l+="text-gray-900 ":l+="text-gray-400 ",o==="start"||o==="end"?l+="bg-blue-500 text-white hover:bg-blue-600 ":o==="temp"?l+="bg-blue-400 text-white hover:bg-blue-500 ":M&&a?l+="bg-blue-400 text-white ":d?l+="bg-blue-100 text-blue-900 hover:bg-blue-200 ":l+="hover:bg-gray-100 ",O&&!o&&!d&&!M&&(l+="text-red-500 font-bold "),l};return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(U,{value:S,onChange:t=>H(t.target.value),placeholder:"dd/mm/yyyy - dd/mm/yyyy",className:"w-64 text-xs"}),e.jsxs(B,{open:_,onOpenChange:s,children:[e.jsx(q,{asChild:!0,children:e.jsx(J,{className:"h-4 w-4 text-gray-500 cursor-pointer hover:text-gray-700"})}),e.jsx(X,{className:"w-auto p-4",align:"end",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(y,{variant:"outline",size:"sm",onClick:()=>u(ee(h)),className:"h-7 w-7 p-0",children:e.jsx(Q,{className:"h-4 w-4"})}),e.jsx("div",{className:"text-sm font-medium",children:b(h,"MMMM yyyy")}),e.jsx(y,{variant:"outline",size:"sm",onClick:()=>u(se(h,1)),className:"h-7 w-7 p-0",children:e.jsx(Z,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-1",children:[["T2","T3","T4","T5","T6","T7","CN"].map(t=>e.jsx("div",{className:"text-center text-xs font-medium text-gray-500 p-2",children:t},t)),c().map((t,o)=>e.jsx("div",{className:"text-center",children:e.jsx(y,{variant:"ghost",className:I(t),onClick:()=>n(t),onMouseEnter:()=>{a&&!i&&W(t,h)&&f(t)},onMouseLeave:()=>{a&&!i&&f(null)},children:t.getDate()})},o))]}),e.jsx("div",{className:"text-xs text-gray-500 text-center border-t pt-3",children:a&&r?e.jsxs("span",{className:"text-blue-600 font-medium",children:[b(a<r?a:r,"dd/MM/yyyy")," - ",b(a<r?r:a,"dd/MM/yyyy")]}):a?"Chọn ngày kết thúc":g&&i?e.jsxs("span",{className:"text-blue-600 font-medium",children:[b(g,"dd/MM/yyyy")," - ",b(i,"dd/MM/yyyy")]}):"Chọn ngày bắt đầu"})]})})]})]})}const C={amount_before_discount:0,amount_discount:0,report_by_pos:[],number_of_voucher_used:0,number_of_voucher_published:0},ie=[{name:"1","Doanh thu":0,"Giảm giá":0},{name:"2","Doanh thu":0,"Giảm giá":0},{name:"3","Doanh thu":0,"Giảm giá":0},{name:"4","Doanh thu":0,"Giảm giá":0},{name:"5","Doanh thu":0,"Giảm giá":0},{name:"6","Doanh thu":0,"Giảm giá":0},{name:"7","Doanh thu":0,"Giảm giá":0}],oe=[{name:"1","Phát hành":0,"Sử dụng":0},{name:"2","Phát hành":0,"Sử dụng":0},{name:"3","Phát hành":0,"Sử dụng":0},{name:"4","Phát hành":0,"Sử dụng":0},{name:"5","Phát hành":0,"Sử dụng":0},{name:"6","Phát hành":0,"Sử dụng":0},{name:"7","Phát hành":0,"Sử dụng":0}];function ce(){const[g,i]=m.useState(null),[k,S]=m.useState(null),[H,_]=m.useState(""),[w,h]=m.useState(""),[u,a]=m.useState("7days");m.useEffect(()=>{r("7days")},[]);const v=(s,n)=>`${b(s,"dd/MM/yyyy")} - ${b(n,"dd/MM/yyyy")}`,r=s=>{const n=new Date;let c,x=G(n);switch(s){case"today":c=D(n);break;case"yesterday":c=D(P(n,1)),x=G(P(n,1));break;case"7days":c=D(P(n,6));break;case"15days":c=D(P(n,14));break;case"30days":c=D(P(n,29));break;default:return}i(c),S(x),_(v(c,x)),a(s)},f=s=>{_(s),a("");const n=/(\d{2}\/\d{2}\/\d{4})\s*-\s*(\d{2}\/\d{2}\/\d{4})/,c=s.match(n);if(c)try{const[,x,L]=c,[I,t,o]=x.split("/"),[d,j,O]=L.split("/"),M=new Date(parseInt(o),parseInt(t)-1,parseInt(I)),l=new Date(parseInt(O),parseInt(j)-1,parseInt(d));!isNaN(M.getTime())&&!isNaN(l.getTime())&&(i(D(M)),S(G(l)))}catch(x){console.error("Error parsing date range:",x)}};return e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"BÁO CÁO VOUCHER"})}),e.jsxs("div",{className:"flex items-center justify-end gap-4",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(y,{variant:"outline",size:"sm",className:`text-xs ${u==="today"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>r("today"),children:"Hôm nay"}),e.jsx(y,{variant:"outline",size:"sm",className:`text-xs ${u==="yesterday"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>r("yesterday"),children:"Hôm qua"}),e.jsx(y,{variant:"outline",size:"sm",className:`text-xs ${u==="7days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>r("7days"),children:"7 ngày trước"}),e.jsx(y,{variant:"outline",size:"sm",className:`text-xs ${u==="15days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>r("15days"),children:"15 ngày trước"}),e.jsx(y,{variant:"outline",size:"sm",className:`text-xs ${u==="30days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>r("30days"),children:"30 ngày trước"})]}),e.jsx(re,{startDate:g,endDate:k,onDateChange:(s,n)=>{i(s),S(n),s&&n&&(_(v(s,n)),a(""))},dateRange:H,onDateRangeChange:f})]}),e.jsxs("div",{className:"grid grid-cols-4 gap-4 mb-6",children:[e.jsx(p,{children:e.jsxs(N,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:C.number_of_voucher_published}),e.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"VOUCHER PHÁT HÀNH"})]})}),e.jsx(p,{children:e.jsxs(N,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:C.number_of_voucher_used}),e.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"VOUCHER SỬ DỤNG"})]})}),e.jsx(p,{children:e.jsxs(N,{className:"p-4 text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-gray-900",children:[C.amount_before_discount.toLocaleString()," VND"]}),e.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"DOANH THU TRƯỚC GIẢM GIÁ"})]})}),e.jsx(p,{children:e.jsxs(N,{className:"p-4 text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-gray-900",children:[C.amount_discount.toLocaleString()," VND"]}),e.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"CHI PHÍ GIẢM GIÁ"})]})})]}),e.jsx(p,{className:"mb-6",children:e.jsxs(N,{className:"p-6",children:[e.jsxs("div",{className:"flex justify-center gap-6 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Doanh thu"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-orange-500 rounded"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Giảm giá"})]})]}),e.jsx("div",{className:"h-120",children:e.jsx(V,{width:"100%",height:"100%",children:e.jsxs(K,{data:ie,margin:{top:20,right:30,left:20,bottom:20},children:[e.jsx(z,{dataKey:"name",hide:!0}),e.jsx($,{domain:[0,1],ticks:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1],tickFormatter:s=>s===0?"0":s.toFixed(1),axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},width:40}),[.1,.2,.3,.4,.5,.6,.7,.8,.9,1].map(s=>e.jsx(A,{y:s,stroke:"#e5e7eb",strokeDasharray:"none"},s)),e.jsx(T,{type:"monotone",dataKey:"Doanh thu",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2}}),e.jsx(T,{type:"monotone",dataKey:"Giảm giá",stroke:"#f97316",strokeWidth:2,dot:{fill:"#f97316",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#f97316",strokeWidth:2}})]})})})]})}),e.jsx(p,{className:"mb-6",children:e.jsxs(N,{className:"p-6",children:[e.jsxs("div",{className:"flex justify-center gap-6 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Phát hành"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-orange-500 rounded"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Sử dụng"})]})]}),e.jsx("div",{className:"h-120",children:e.jsx(V,{width:"100%",height:"100%",children:e.jsxs(K,{data:oe,margin:{top:20,right:30,left:20,bottom:20},children:[e.jsx(z,{dataKey:"name",hide:!0}),e.jsx($,{domain:[0,1],ticks:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1],tickFormatter:s=>s===0?"0":s.toFixed(1),axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},width:40}),[.1,.2,.3,.4,.5,.6,.7,.8,.9,1].map(s=>e.jsx(A,{y:s,stroke:"#e5e7eb",strokeDasharray:"none"},s)),e.jsx(T,{type:"monotone",dataKey:"Phát hành",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2}}),e.jsx(T,{type:"monotone",dataKey:"Sử dụng",stroke:"#f97316",strokeWidth:2,dot:{fill:"#f97316",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#f97316",strokeWidth:2}})]})})})]})}),e.jsxs(p,{children:[e.jsx(F,{children:e.jsx(Y,{className:"text-base font-medium text-gray-700 text-center",children:"SỬ DỤNG VOUCHER THEO CỬA HÀNG"})}),e.jsxs(N,{children:[e.jsx("div",{className:"mb-4 flex justify-end",children:e.jsx(U,{placeholder:"Tìm kiếm nhanh",value:w,onChange:s=>h(s.target.value),className:"w-64 text-xs"})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b bg-gray-50",children:[e.jsx("th",{className:"text-left p-3 font-medium text-gray-700",children:"Cửa hàng"}),e.jsx("th",{className:"text-center p-3 font-medium text-gray-700",children:"Voucher sử dụng"}),e.jsx("th",{className:"text-center p-3 font-medium text-gray-700",children:"Doanh thu trước giảm giá"}),e.jsx("th",{className:"text-center p-3 font-medium text-gray-700",children:"Doanh thu sau giảm giá"}),e.jsx("th",{className:"text-center p-3 font-medium text-gray-700",children:"Chi phí giảm giá"})]})}),e.jsx("tbody",{children:C.report_by_pos.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:5,className:"text-center p-8 text-gray-500",children:"No data available in table"})}):C.report_by_pos.filter(s=>{var n;return(n=s.pos_name)==null?void 0:n.toLowerCase().includes(w.toLowerCase())}).map((s,n)=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsx("td",{className:"p-3 text-gray-900",children:s.pos_name||"N/A"}),e.jsx("td",{className:"p-3 text-center text-gray-900",children:s.number_of_voucher_used||0}),e.jsxs("td",{className:"p-3 text-center text-gray-900",children:[(s.amount_before_discount||0).toLocaleString()," VND"]}),e.jsxs("td",{className:"p-3 text-center text-gray-900",children:[((s.amount_before_discount||0)-(s.amount_discount||0)).toLocaleString()," VND"]}),e.jsxs("td",{className:"p-3 text-center text-gray-900",children:[(s.amount_discount||0).toLocaleString()," VND"]})]},n))})]})})]})]})]})}const De=ce;export{De as component};
