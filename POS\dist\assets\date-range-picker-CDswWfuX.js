import{r as a,j as s,B as u,c as b,a4 as y}from"./index-CVQ6JZo2.js";import{C as k}from"./calendar-BszTCdZH.js";import{P as N,a as M,b as w}from"./popover-DnoSPJNX.js";import{C as P}from"./calendar-CYB-o1z2.js";import{v as c}from"./date-range-picker-CXbMaowj.js";import{f}from"./isSameMonth-C8JQo-AN.js";function C({startDate:r,endDate:i,onStartDateChange:o,onEndDateChange:e,placeholder:p="Chọn khoảng thời gian",disabled:x=!1,className:h}){const[j,n]=a.useState(!1),[l,m]=a.useState("start"),v=()=>r&&i?`${f(r,"dd/MM/yyyy",{locale:c})} - ${f(i,"dd/MM/yyyy",{locale:c})}`:r?`${f(r,"dd/MM/yyyy",{locale:c})} - ...`:p,d=t=>{if(t)if(l==="start")i&&t>i&&(e==null||e(void 0),y.warning("Ngày kết thúc đã được reset vì nhỏ hơn ngày bắt đầu mới")),o==null||o(t),m("end");else{if(r&&t<r){y.error("Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu");return}e==null||e(t),setTimeout(()=>n(!1),100)}},g=t=>{n(t),t&&m("start")};return s.jsxs(N,{open:j,onOpenChange:g,children:[s.jsx(M,{asChild:!0,children:s.jsxs(u,{variant:"outline",className:b("w-full justify-start text-left font-normal",!r&&!i&&"text-muted-foreground",h),disabled:x,children:[s.jsx(P,{className:"mr-2 h-4 w-4"}),v()]})}),s.jsx(w,{className:"w-auto p-0",align:"start",children:s.jsxs("div",{className:"p-3",children:[s.jsx("div",{className:"mb-3 text-center text-sm font-medium",children:l==="start"?"Chọn ngày bắt đầu":"Chọn ngày kết thúc"}),s.jsx(k,{mode:"single",selected:l==="start"?r:i,onSelect:d,locale:c,autoFocus:!0}),s.jsxs("div",{className:"mt-3 flex justify-between",children:[s.jsx(u,{variant:"outline",size:"sm",onClick:()=>m(l==="start"?"end":"start"),children:l==="start"?"Chọn ngày kết thúc":"Chọn ngày bắt đầu"}),s.jsx(u,{variant:"outline",size:"sm",onClick:()=>n(!1),children:"Đóng"})]})]})})]})}export{C as D};
