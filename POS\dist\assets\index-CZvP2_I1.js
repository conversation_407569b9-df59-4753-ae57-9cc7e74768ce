import{j as e,B as T,h as k,r as x,a4 as h}from"./index-CfbMU4Ye.js";import{g as C}from"./error-utils-BYcz3jZ5.js";import"./pos-api-BBB_ZiZD.js";import"./vietqr-api-BHQxfNzq.js";import{a as K,b as L,u as $}from"./use-item-classes-DJrzbexi.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import{H as A}from"./header-CiiJInbE.js";import{M as H}from"./main-B69tr6A0.js";import{C as R}from"./index-TKFSyVOw.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{P as B}from"./profile-dropdown-HjZ6UGjk.js";import{S as O,T as z}from"./search-Bbt2JnTN.js";import{I as Q}from"./input-D8TU6hMD.js";import{B as V}from"./badge-DNJz5hg4.js";import{I as F}from"./IconTrash-NV_v0NzY.js";import{u as U,e as X,f as I}from"./index-DrO-sOnq.js";import{T as q,a as G,b as f,c as J,d as W,e as b}from"./table-C3v-r6-e.js";import{I as Y}from"./IconPlus-CiQ0nQi0.js";import"./useQuery-BvDWg4vp.js";import"./utils-km2FGkQ4.js";import"./useMutation-C9PewMvL.js";import"./query-keys-3lmd-xp6.js";import"./separator-DVvwOaSX.js";import"./dialog-FztlF_ds.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./select-_nXsh5SU.js";import"./index-D41EikqA.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./avatar-CE3yFgmj.js";import"./dropdown-menu-8bnotEGr.js";import"./index-4DjKSQeL.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./scroll-area-Bx6sgJqp.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";const Z=({title:s,searchValue:r,searchPlaceholder:l,onSearchChange:a,onSearchKeyDown:i,actionButton:t})=>e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-2xl font-semibold",children:s}),e.jsx(Q,{placeholder:l,className:"w-64",value:r,onChange:n=>a(n.target.value),onKeyDown:i})]}),t&&e.jsxs(T,{size:"sm",onClick:t.onClick,children:[t.icon&&e.jsx("span",{className:"mr-2",children:t.icon}),t.label]})]}),ee=[{accessorKey:"id",header:"#",cell:({row:s})=>{const r=s.index+1;return e.jsx("div",{className:"w-[50px] font-medium",children:r})},enableSorting:!1},{accessorKey:"item_class_id",header:"Mã loại món",cell:({row:s})=>{const r=s.original;return e.jsx("span",{className:"font-medium",children:r.item_class_id})}},{accessorKey:"item_class_name",header:"Tên loại món",cell:({row:s})=>{const r=s.original;return e.jsx("span",{className:"font-medium",children:r.item_class_name})}},{id:"actions",header:"Thao tác",cell:({row:s,table:r})=>{const l=s.original,a=l.active===1,i=r.options.meta;return e.jsx(V,{variant:a?"default":"destructive",className:`cursor-pointer ${a?"bg-green-500 text-white hover:bg-green-600":"bg-red-500 text-white hover:bg-red-600"}`,onClick:t=>{var n;t.stopPropagation(),(n=i==null?void 0:i.onToggleItemClassStatus)==null||n.call(i,l)},children:a?"Active":"Deactive"})}},{id:"delete",header:"",cell:({row:s,table:r})=>{const l=s.original,a=r.options.meta;return e.jsxs(T,{variant:"ghost",size:"sm",onClick:i=>{var t;i.stopPropagation(),(t=a==null?void 0:a.onDeleteItemClass)==null||t.call(a,l)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(F,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa loại món ",l.item_class_name]})]})}}];function se({columns:s,data:r,onEditItemClass:l,onDeleteItemClass:a,onToggleItemClassStatus:i,onRowClick:t}){var d;const n=U({data:r,columns:s,getCoreRowModel:X(),meta:{onEditItemClass:l,onDeleteItemClass:a,onToggleItemClassStatus:i}});return e.jsx("div",{className:"rounded-md border",children:e.jsxs(q,{children:[e.jsx(G,{children:n.getHeaderGroups().map(m=>e.jsx(f,{children:m.headers.map(c=>e.jsx(J,{children:c.isPlaceholder?null:I(c.column.columnDef.header,c.getContext())},c.id))},m.id))}),e.jsx(W,{children:(d=n.getRowModel().rows)!=null&&d.length?n.getRowModel().rows.map(m=>e.jsx(f,{"data-state":m.getIsSelected()&&"selected",className:t?"hover:bg-muted/50 cursor-pointer":"",onClick:()=>t==null?void 0:t(m.original),children:m.getVisibleCells().map(c=>e.jsx(b,{children:I(c.column.columnDef.cell,c.getContext())},c.id))},m.id)):e.jsx(f,{children:e.jsx(b,{colSpan:s.length,className:"h-24 text-center",children:"Không có dữ liệu loại món."})})})]})})}function te(){const s=k(),[r,l]=x.useState(""),[a,i]=x.useState(""),[t,n]=x.useState(!1),[d,m]=x.useState(null),c=K(),j=L(),{data:N,isLoading:y,error:S}=$({searchTerm:r||void 0}),v=y,u=S,_=o=>{h.info(`Chỉnh sửa loại món: ${o.item_class_name}`)},w=async o=>{try{const p={...o,active:o.active===1?0:1};await c.mutateAsync(p);const g=p.active===1?"kích hoạt":"vô hiệu hóa";h.success(`Đã ${g} loại món "${o.item_class_name}"`)}catch(p){const g=C(p);h.error(g)}},D=o=>{m(o),n(!0)},M=async()=>{if(d)try{await j.mutateAsync(d.id),h.success(`Loại món "${d.item_class_name}" đã được xóa thành công!`),n(!1),m(null)}catch(o){const p=C(o);h.error(p)}},P=()=>{s({to:"/menu/item-class/detail"})},E=o=>{s({to:"/menu/item-class/detail/$id",params:{id:o.id}})};return e.jsxs(e.Fragment,{children:[e.jsx(A,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(O,{}),e.jsx(z,{}),e.jsx(B,{})]})}),e.jsx(H,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Z,{title:"Danh sách loại món",searchValue:a,searchPlaceholder:"Tìm kiếm loại món...",onSearchChange:i,onSearchKeyDown:o=>{o.key==="Enter"&&(o.preventDefault(),l(a))},actionButton:{label:"Tạo loại món",icon:e.jsx(Y,{className:"h-4 w-4"}),onClick:P}}),u&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:C(u)})}),!u&&v&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải dữ liệu loại món..."})}),!u&&!v&&e.jsx(se,{columns:ee,data:N||[],onEditItemClass:_,onDeleteItemClass:D,onToggleItemClassStatus:w,onRowClick:E}),e.jsx(R,{open:t,onOpenChange:n,content:"Bạn có muốn xoá ?",onConfirm:M,isLoading:j.isPending})]})})]})}const Je=te;export{Je as component};
