import{j as o}from"./index-UcdZ5AHH.js";import{C as t}from"./index-BEu7L_fZ.js";import"./error-utils-DVDyynMb.js";import"./pos-api-j20LMGrC.js";import"./use-stores-ypC_BTQC.js";import"./useQuery-B4yhTgGk.js";import"./utils-km2FGkQ4.js";import"./useMutation-q12VR5WX.js";import"./vietqr-api-9FERZtmQ.js";import"./stores-api-CmxnE7jq.js";import"./query-keys-3lmd-xp6.js";import"./use-item-types-Ba660Fo2.js";import"./use-items-D1TaTLXR.js";import"./item-api-BSj8C3Ww.js";import"./use-removed-items-B2DPNXQs.js";import"./use-item-categories-Dw7rjin6.js";import"./xlsx-DkH2s96g.js";import"./use-printer-positions-data-B_9EXgyP.js";import"./printer-position-api-CvF0O4CM.js";import"./user-9ajIul7r.js";import"./crm-api-APQEjHWd.js";import"./modal-DNIlBRJT.js";import"./dialog-DmI079wB.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./date-range-picker-DxA68ufO.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./select-DOexGcsG.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";import"./form-D_U5B5Go.js";import"./input-CBpgGfUv.js";import"./checkbox-CDB9_T0n.js";import"./collapsible-Dz-Iaa-P.js";import"./use-printer-positions-DRWJg_8F.js";const O=function(){return o.jsx(t,{})};export{O as component};
