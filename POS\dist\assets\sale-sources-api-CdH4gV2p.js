import{b as u}from"./pos-api-BBB_ZiZD.js";const d=5*60*1e3,D=new Map,l=new Map,y={getSourcesSummary:async a=>{var s;const r=`${a.companyUid}-${a.brandUid}-${a.startDate}-${a.endDate}-${((s=a.storeUids)==null?void 0:s.join(","))||"all"}-${a.byDays||1}`,t=D.get(r);if(t&&Date.now()-t.timestamp<d)return t.data;if(l.has(r))return l.get(r);const e=(async()=>{try{const i=new URLSearchParams({company_uid:a.companyUid,brand_uid:a.brandUid,start_date:a.startDate.toString(),end_date:a.endDate.toString(),limit:(a.limit||100).toString(),by_days:(a.byDays||1).toString(),store_open_at:"0"});a.storeUids&&a.storeUids.length>0&&i.set("list_store_uid",a.storeUids.join(","));const g=await u.get(`/v1/reports/sale-summary/sources?${i.toString()}`);return D.set(r,{data:g.data,timestamp:Date.now()}),g.data}finally{l.delete(r)}})();return l.set(r,e),e},processSourcesSummary:a=>{const r=a.reduce((n,o)=>n+o.revenue_gross,0),t=a.reduce((n,o)=>n+o.total_bill,0),e=a.length,s=new Map;a.forEach(n=>{n.list_data.forEach(o=>{const c=s.get(o.date);c?(c.revenue+=o.revenue_gross,c.bills+=o.total_bill):s.set(o.date,{revenue:o.revenue_gross,bills:o.total_bill})})});const i=Array.from(s.entries()).map(([n,o])=>({date:n,revenue:o.revenue,bills:o.bills})).sort((n,o)=>n.date.localeCompare(o.date)),g=a.map(n=>({sourceId:n.source_id,sourceName:n.source_name,revenue:n.revenue_gross,bills:n.total_bill,percentage:r>0?n.revenue_gross/r*100:0}));return{totalRevenue:r,totalBills:t,sourceCount:e,dailyData:i,sourceData:g}},formatCurrency:a=>new Intl.NumberFormat("vi-VN").format(a),getDateRange:a=>{const r=new Date,t=new Date(r.getFullYear(),r.getMonth(),r.getDate());switch(a){case"today":return{startDate:t.getTime(),endDate:t.getTime()+24*60*60*1e3-1};case"yesterday":{const e=new Date(t);return e.setDate(e.getDate()-1),{startDate:e.getTime(),endDate:e.getTime()+24*60*60*1e3-1}}case"this-week":{const e=new Date(t);return e.setDate(t.getDate()-t.getDay()),{startDate:e.getTime(),endDate:r.getTime()}}case"last-week":{const e=new Date(t);e.setDate(t.getDate()-t.getDay());const s=new Date(e);s.setDate(e.getDate()-7);const i=new Date(e);return i.setTime(i.getTime()-1),{startDate:s.getTime(),endDate:i.getTime()}}case"this-month":return{startDate:new Date(t.getFullYear(),t.getMonth(),1).getTime(),endDate:r.getTime()};case"last-month":{const e=new Date(t.getFullYear(),t.getMonth()-1,1),s=new Date(t.getFullYear(),t.getMonth(),0,23,59,59,999);return{startDate:e.getTime(),endDate:s.getTime()}}case"this-quarter":{const e=Math.floor(t.getMonth()/3);return{startDate:new Date(t.getFullYear(),e*3,1).getTime(),endDate:r.getTime()}}case"last-quarter":{const e=Math.floor(t.getMonth()/3),s=e===0?3:e-1,i=e===0?t.getFullYear()-1:t.getFullYear(),g=new Date(i,s*3,1),n=new Date(i,(s+1)*3,0,23,59,59,999);return{startDate:g.getTime(),endDate:n.getTime()}}case"this-year":return{startDate:new Date(t.getFullYear(),0,1).getTime(),endDate:r.getTime()};case"last-year":{const e=new Date(t.getFullYear()-1,0,1),s=new Date(t.getFullYear()-1,11,31,23,59,59,999);return{startDate:e.getTime(),endDate:s.getTime()}}case"last-7-days":{const e=new Date(t);return e.setDate(t.getDate()-7),{startDate:e.getTime(),endDate:r.getTime()}}case"last-30-days":{const e=new Date(t);return e.setDate(t.getDate()-30),{startDate:e.getTime(),endDate:r.getTime()}}case"last-90-days":{const e=new Date(t);return e.setDate(t.getDate()-90),{startDate:e.getTime(),endDate:r.getTime()}}default:return{startDate:new Date(t.getFullYear(),t.getMonth(),1).getTime(),endDate:r.getTime()}}}};export{y as s};
