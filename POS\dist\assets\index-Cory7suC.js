import{j as e,B as S,r as u,a as R,a4 as f,ay as ee,h as te}from"./index-UcdZ5AHH.js";import"./pos-api-j20LMGrC.js";import"./vietqr-api-9FERZtmQ.js";import{d as se,e as ae,f as oe,h as B,u as ne}from"./use-customizations-DjbYm2qv.js";import"./user-9ajIul7r.js";import"./crm-api-APQEjHWd.js";import{H as ie}from"./header-CE1GZ327.js";import{M as le}from"./main-C1Ukb9JX.js";import{P as re}from"./profile-dropdown-CFMwD8wA.js";import{S as ce,T as de}from"./search-CgdyS-kQ.js";import"./date-range-picker-DxA68ufO.js";import{L as me}from"./form-D_U5B5Go.js";import{I as A}from"./input-CBpgGfUv.js";import{S as H,a as K,b as U,c as V,d as X}from"./select-DOexGcsG.js";import{D as he,a as pe,b as ue,c as k}from"./dropdown-menu-D3XvynCv.js";import{I as xe}from"./IconCopy-DqHGIjT3.js";import{I as fe}from"./IconTrash-BBImt48g.js";import{u as je,b as ge,e as Ce,f as D}from"./index-Bfo4u1qA.js";import{T as P,a as L,b as w,c as _,d as O,e as T}from"./table-DHWQVnPn.js";import{C as ye}from"./index-Bz1ZLo3n.js";import{P as E}from"./modal-DNIlBRJT.js";import{I as $}from"./IconDownload-Dqi9CiWq.js";import{I as q}from"./IconUpload-J9uTMZoC.js";import"./useQuery-B4yhTgGk.js";import"./utils-km2FGkQ4.js";import"./useMutation-q12VR5WX.js";import"./query-keys-3lmd-xp6.js";import"./separator-C5UQ7YqK.js";import"./avatar-BOI9P1fI.js";import"./search-context-DK2BgvuK.js";import"./command-DJT46NtT.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DmI079wB.js";import"./search-B6f_4BGP.js";import"./createReactComponent-C1S2Ujit.js";import"./scroll-area-DQUG4R9C.js";import"./index-MuNXZ_zP.js";import"./IconChevronRight-CnyriCST.js";import"./IconSearch-Ca5dfxyj.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";import"./index-iiVug-md.js";const Ne=({onCopyCustomization:i,onDeleteCustomization:s})=>[{id:"index",header:"#",cell:({row:t})=>e.jsx("div",{className:"text-center",children:t.index+1}),size:60},{accessorKey:"name",header:"Tên customization",cell:({row:t})=>{const a=t.original;return e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"font-medium",children:a.name})})},size:250},{accessorKey:"cityName",header:"Thành phố",cell:({row:t})=>{const a=t.original;return e.jsx("div",{className:"text-sm",children:a.cityName||"Không xác định"})},size:120},{accessorKey:"storeName",header:"Cửa hàng",cell:({row:t})=>{const a=t.original;return e.jsx("div",{className:"text-sm",children:a.storeName||"Cửa hàng hiện tại"})},size:150},{id:"copy",header:()=>e.jsx("div",{className:"text-center",children:"Sao chép"}),cell:({row:t})=>{const a=t.original;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(S,{variant:"ghost",size:"sm",onClick:l=>{l.stopPropagation(),i(a)},className:"h-8 w-8 p-0",children:e.jsx(xe,{className:"h-4 w-4"})})})},size:100},{id:"actions",header:()=>e.jsx("div",{className:"text-center"}),cell:({row:t})=>{const a=t.original;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(S,{variant:"ghost",size:"sm",onClick:l=>{l.stopPropagation(),s(a)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:e.jsx(fe,{className:"h-4 w-4"})})})},size:80}];function ze({columns:i,data:s,isLoading:t=!1,onRowClick:a}){var m;const[l,n]=u.useState([]),o=je({data:s,columns:i,getCoreRowModel:Ce(),getSortedRowModel:ge(),onSortingChange:n,state:{sorting:l}});return t?e.jsx("div",{className:"rounded-md border",children:e.jsxs(P,{children:[e.jsx(L,{children:o.getHeaderGroups().map(c=>e.jsx(w,{children:c.headers.map(r=>e.jsx(_,{children:r.isPlaceholder?null:D(r.column.columnDef.header,r.getContext())},r.id))},c.id))}),e.jsx(O,{children:Array.from({length:5}).map((c,r)=>e.jsx(w,{children:i.map((x,h)=>e.jsx(T,{children:e.jsx("div",{className:"h-4 w-full animate-pulse rounded bg-gray-200"})},h))},r))})]})}):e.jsx("div",{className:"rounded-md border",children:e.jsxs(P,{children:[e.jsx(L,{children:o.getHeaderGroups().map(c=>e.jsx(w,{children:c.headers.map(r=>e.jsx(_,{children:r.isPlaceholder?null:D(r.column.columnDef.header,r.getContext())},r.id))},c.id))}),e.jsx(O,{children:(m=o.getRowModel().rows)!=null&&m.length?o.getRowModel().rows.map(c=>e.jsx(w,{"data-state":c.getIsSelected()&&"selected",className:a?"cursor-pointer hover:bg-gray-50":"",onClick:()=>a==null?void 0:a(c.original),children:c.getVisibleCells().map(r=>e.jsx(T,{children:D(r.column.columnDef.cell,r.getContext())},r.id))},c.id)):e.jsx(w,{children:e.jsx(T,{colSpan:i.length,className:"h-24 text-center",children:"Không có customization nào."})})})]})})}function Se({open:i,onOpenChange:s,selectedCustomization:t,onCancel:a,onConfirm:l,isLoading:n}){return e.jsx(ye,{open:i,onOpenChange:s,title:"Xác nhận xóa customization",content:`Bạn có chắc muốn xóa customization "${t==null?void 0:t.name}"?`,onCancel:a,onConfirm:l,confirmText:"Xóa",cancelText:"Hủy",isLoading:n})}function be({open:i,onOpenChange:s,copyName:t,onCopyNameChange:a,onCancel:l,onConfirm:n,isLoading:o}){return e.jsx(E,{title:"Sao chép customization",open:i,onOpenChange:s,onCancel:l,onConfirm:n,confirmText:"Sao chép",cancelText:"Hủy",isLoading:o,children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(me,{htmlFor:"copy-name",className:"min-w-[120px] text-sm font-medium",children:["Tên customization ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(A,{id:"copy-name",value:t,onChange:m=>a(m.target.value),placeholder:"Nhập tên customization",className:"flex-1"})]})})})}function Ie({open:i,onOpenChange:s,showParsedData:t,exportStoreId:a,onExportStoreIdChange:l,stores:n,selectedFile:o,parsedData:m,isExporting:c,isSaving:r,onCancel:x,onConfirm:h,onDownloadExportFile:d,onUploadFile:y}){return e.jsx(E,{title:"Xuất, sửa customization",open:i,onOpenChange:s,onCancel:x,onConfirm:h,confirmText:t?"Lưu":"Tiếp tục",cancelText:"Hủy",centerTitle:!0,maxWidth:t?"sm:max-w-6xl":"sm:max-w-[400px]",isLoading:r,children:e.jsxs("div",{className:"space-y-4",children:[!t&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs(H,{value:a,onValueChange:l,children:[e.jsx(K,{className:"w-full",children:e.jsx(U,{placeholder:"Chọn cửa hàng"})}),e.jsx(V,{children:n.map(j=>e.jsx(X,{value:j.id,children:j.store_name},j.id))})]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Tải xuống"})}),e.jsx(S,{size:"sm",variant:"outline",onClick:d,disabled:c,className:"h-8 w-8 p-0",children:e.jsx($,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"rounded-md bg-yellow-50 p-3 text-sm text-yellow-800",children:[e.jsx("strong",{children:"Không sửa cột:"})," Cửa hàng."]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-600",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),o&&e.jsxs("span",{className:"mt-1 text-xs text-gray-500",children:["File đã chọn: ",o.name]})]}),e.jsx(S,{size:"sm",variant:"default",onClick:y,className:"h-8 w-8 p-0",children:e.jsx(q,{className:"h-4 w-4"})})]})]})]}),t&&e.jsx("div",{className:"max-h-60 overflow-y-auto rounded-md border",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Tên"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Cửa hàng"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Mã món áp dụng"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Tên nhóm"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Yêu cầu chọn"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Giới hạn chọn"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Mã món theo nhóm"})]})}),e.jsx("tbody",{children:m.map((j,g)=>e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-3 py-2",children:j.name}),e.jsx("td",{className:"px-3 py-2",children:j.storeName||"Cửa hàng hiện tại"}),e.jsx("td",{className:"px-3 py-2",children:j.appliedItemCodes}),e.jsx("td",{className:"px-3 py-2",children:j.groupName}),e.jsx("td",{className:"px-3 py-2",children:j.minRequired}),e.jsx("td",{className:"px-3 py-2",children:j.maxAllowed}),e.jsx("td",{className:"px-3 py-2",children:j.groupItemCodes})]},g))})]})})]})})}function ve({open:i,onOpenChange:s,showImportParsedData:t,importSelectedFile:a,importParsedData:l,isLoading:n,storeName:o,onCancel:m,onConfirm:c,onDownloadTemplate:r,onImportFileUpload:x}){return e.jsx(E,{title:"Thêm customization từ file",open:i,onOpenChange:s,onCancel:m,onConfirm:c,confirmText:t?"Lưu":"Tiếp tục",cancelText:"Hủy",centerTitle:!0,maxWidth:t?"sm:max-w-6xl":"sm:max-w-[400px]",isLoading:n,children:e.jsxs("div",{className:"space-y-4",children:[!t&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 1. Tải file mẫu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Tải xuống file mẫu để tạo customization"})}),e.jsx(S,{size:"sm",variant:"outline",onClick:r,className:"h-8 w-8 p-0",children:e.jsx($,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"rounded-md bg-blue-50 p-3 text-sm text-blue-800",children:[e.jsx("strong",{children:"Các customize được thêm vào chỉ được áp dụng tại:"})," ",o||"Cửa hàng hiện tại"]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"}),a&&e.jsxs("span",{className:"mt-1 text-xs text-gray-500",children:["File đã chọn: ",a.name]})]}),e.jsx(S,{size:"sm",variant:"default",onClick:x,className:"h-8 w-8 p-0",children:e.jsx(q,{className:"h-4 w-4"})})]})]})]}),t&&e.jsx("div",{className:"max-h-60 overflow-y-auto rounded-md border",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Tên"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Cửa hàng"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Mã món áp dụng"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Tên nhóm"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Yêu cầu chọn"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Giới hạn chọn"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Mã món theo nhóm"})]})}),e.jsx("tbody",{children:l.map((h,d)=>e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-3 py-2",children:h.name}),e.jsx("td",{className:"px-3 py-2",children:h.storeName||o||"Cửa hàng hiện tại"}),e.jsx("td",{className:"px-3 py-2",children:h.appliedItemCodes}),e.jsx("td",{className:"px-3 py-2",children:h.groupName}),e.jsx("td",{className:"px-3 py-2",children:h.minRequired}),e.jsx("td",{className:"px-3 py-2",children:h.maxAllowed}),e.jsx("td",{className:"px-3 py-2",children:h.groupItemCodes})]},d))})]})})]})})}function we(){const[i,s]=u.useState(""),[t,a]=u.useState(""),[l,n]=u.useState(""),{currentBrandStores:o}=R();u.useEffect(()=>{!l&&o.length>0&&n(o[0].id)},[o,l]);const m=l?[l]:[],c=()=>{s(t)};return{searchTerm:i,searchQuery:t,setSearchQuery:a,selectedStoreId:l,setSelectedStoreId:n,listStoreUid:m,stores:o,handleSearchSubmit:c,handleSearchKeyDown:x=>{x.key==="Enter"&&(x.preventDefault(),c())}}}function Me(){const[i,s]=u.useState(null),[t,a]=u.useState(!1),[l,n]=u.useState(!1),[o,m]=u.useState(!1),[c,r]=u.useState(!1);return{selectedCustomization:i,isCopyModalOpen:t,isDeleteModalOpen:l,isExportModalOpen:o,isImportModalOpen:c,openCopyModal:C=>{s(C),a(!0)},closeCopyModal:()=>{a(!1),s(null)},openDeleteModal:C=>{s(C),n(!0)},closeDeleteModal:()=>{n(!1),s(null)},openExportModal:()=>{m(!0)},closeExportModal:()=>{m(!1)},openImportModal:()=>{r(!0)},closeImportModal:()=>{r(!1)}}}function De(){const[i,s]=u.useState(""),t=se(),a=o=>{s(`${o.name} - Copy`)},l=()=>{s("")};return{copyName:i,setCopyName:s,initializeCopyName:a,resetCopyName:l,handleCopyCustomization:async(o,m)=>{if(!i.trim())return f.error("Vui lòng nhập tên customization"),!1;try{return await t.mutateAsync({customizationId:o.id,newName:i.trim(),targetStoreUid:m}),f.success("Sao chép customization thành công"),l(),!0}catch(c){return console.error("Copy customization error:",c),f.error("Có lỗi xảy ra khi sao chép customization"),!1}},isLoading:t.isPending}}function Te(){const i=ae();return{handleDeleteCustomization:async t=>{try{return await i.mutateAsync(t.id),f.success("Xóa customization thành công"),!0}catch(a){return console.error("Delete customization error:",a),f.error("Có lỗi xảy ra khi xóa customization"),!1}},isLoading:i.isPending}}function Q(){return{parseExcelFile:async(s,t=[],a=!1)=>{try{const l=await ee(()=>import("./xlsx-DkH2s96g.js"),[]),n=await s.arrayBuffer(),o=l.read(n,{type:"array"}),m=o.SheetNames[0],c=o.Sheets[m],r=l.utils.sheet_to_json(c,{header:1}),x=[];for(let h=1;h<r.length;h++){const d=r[h];if(a){if(d&&d.length>=6){const y={id:"",name:String(d[0]||"").trim(),cityName:"",storeName:"",appliedItemCodes:String(d[1]||"").trim(),groupName:String(d[2]||"").trim(),minRequired:Number(d[3])||0,maxAllowed:Number(d[4])||0,groupItemCodes:String(d[5]||"").trim()};x.push(y)}}else d&&d.length>=6&&x.push({id:"",name:String(d[0]||"").trim(),cityName:"",storeName:"",appliedItemCodes:String(d[1]||"").trim(),groupName:String(d[2]||"").trim(),minRequired:Number(d[3])||0,maxAllowed:Number(d[4])||0,groupItemCodes:String(d[5]||"").trim()})}return x}catch(l){throw f.error("Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file."),l}}}}function Ee(){const{currentBrandStores:i}=R(),[s,t]=u.useState(i.length>0?i[0].id:"all"),[a,l]=u.useState(null),[n,o]=u.useState([]),[m,c]=u.useState(!1),r=u.useRef(null),{parseExcelFile:x}=Q(),h=oe(),d=B();u.useEffect(()=>{i.length>0&&s==="all"&&t(i[0].id)},[i,s]);const y=()=>{c(!1),o([]),l(null)};return{exportStoreId:s,setExportStoreId:t,selectedFile:a,parsedData:n,showParsedData:m,fileInputRef:r,stores:i,resetExportState:y,handleDownloadExportFile:async()=>{try{const C=s==="all"?i.map(z=>z.id):[s],N=i.find(z=>z.id===s),v=(N==null?void 0:N.store_name)||"N/A";await h.mutateAsync({list_city_uid:C,storeName:v}),f.success("File đã được tải xuống thành công!")}catch{f.error("Có lỗi xảy ra khi tải file xuống")}},handleUploadFile:()=>{var C;(C=r.current)==null||C.click()},handleFileChange:async C=>{var v;const N=(v=C.target.files)==null?void 0:v[0];if(N){l(N);try{const z=await x(N,[],!1);o(z),c(!0),f.success(`Đã phân tích ${z.length} customization từ file!`)}catch{}}},handleSaveImportedData:async()=>{if(n.length===0)return f.error("Không có dữ liệu để lưu"),!1;try{return await d.mutateAsync({parsedData:n}),f.success(`Đã tạo thành công ${n.length} customization!`),y(),!0}catch{return f.error("Lỗi khi tạo customization. Vui lòng thử lại."),!1}},isExporting:h.isPending,isSaving:d.isPending}}function Fe(i){const[s,t]=u.useState(null),[a,l]=u.useState([]),[n,o]=u.useState(!1),m=u.useRef(null),{parseExcelFile:c}=Q(),r=B(),x=()=>{o(!1),l([]),t(null)};return{importSelectedFile:s,importParsedData:a,showImportParsedData:n,importFileInputRef:m,resetImportState:x,handleDownloadTemplate:()=>{const g=document.createElement("a");g.href="/files/customization/customization-in-store/create-customize-store.xlsx",g.download="create-customize-store.xlsx",document.body.appendChild(g),g.click(),document.body.removeChild(g),f.success("Đã tải file mẫu thành công!")},handleImportFileUpload:()=>{var g;(g=m.current)==null||g.click()},handleImportFileChange:async g=>{var I;const b=(I=g.target.files)==null?void 0:I[0];if(b){t(b);try{const C=await c(b,[],!0);l(C),o(!0),f.success(`Đã phân tích ${C.length} customization từ file!`)}catch{}}},handleSaveImportedCustomizations:async()=>{if(a.length===0)return f.error("Không có dữ liệu để lưu"),!1;try{return await r.mutateAsync({parsedData:a,storeUid:i}),f.success(`Đã tạo thành công ${a.length} customization!`),x(),!0}catch{return f.error("Lỗi khi tạo customization. Vui lòng thử lại."),!1}},isLoading:r.isPending}}function ke(){var F;const i=te(),s=we(),t=Me(),a=De(),l=Te(),n=Ee(),o=Fe(s.selectedStoreId),{data:m,isLoading:c,error:r}=ne({searchTerm:s.searchTerm||void 0,store_uid:s.selectedStoreId}),x=(m==null?void 0:m.map(p=>{const M=s.stores.find(Z=>Z.id===s.selectedStoreId),J=(M==null?void 0:M.store_name)||"N/A";return{...p,storeName:J}}))||[],h=p=>{a.initializeCopyName(p),t.openCopyModal(p)},d=p=>{t.openDeleteModal(p)},y=async()=>{if(!t.selectedCustomization)return;const p=s.selectedStoreId;await a.handleCopyCustomization(t.selectedCustomization,p)&&t.closeCopyModal()},j=async()=>{if(!t.selectedCustomization)return;await l.handleDeleteCustomization(t.selectedCustomization)&&t.closeDeleteModal()},g=()=>{a.resetCopyName(),t.closeCopyModal()},b=()=>{t.openExportModal()},I=()=>{o.resetImportState(),t.openImportModal()},C=()=>{n.resetExportState(),t.closeExportModal()},N=async()=>{await n.handleSaveImportedData()&&t.closeExportModal()},v=()=>{o.resetImportState(),t.closeImportModal()},z=async()=>{await o.handleSaveImportedCustomizations()&&t.closeImportModal()},W=()=>{i({to:"/menu/customization/customization-in-store/detail"})},G=p=>{i({to:"/menu/customization/customization-in-store/detail/$customizationId",params:{customizationId:p.id}})},Y=Ne({onCopyCustomization:h,onDeleteCustomization:d});return e.jsxs(e.Fragment,{children:[e.jsx(ie,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ce,{}),e.jsx(de,{}),e.jsx(re,{})]})}),e.jsx(le,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Customization"}),e.jsx(A,{placeholder:"Tìm kiếm customization",className:"w-64",value:s.searchQuery,onChange:p=>s.setSearchQuery(p.target.value),onKeyDown:s.handleSearchKeyDown}),e.jsxs(H,{value:s.selectedStoreId,onValueChange:s.setSelectedStoreId,children:[e.jsx(K,{className:"w-48",children:e.jsx(U,{placeholder:"Chọn cửa hàng"})}),e.jsx(V,{children:s.stores.map(p=>e.jsx(X,{value:p.id,children:p.store_name},p.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(he,{children:[e.jsx(pe,{asChild:!0,children:e.jsx(S,{variant:"outline",children:"Tiện ích"})}),e.jsxs(ue,{children:[e.jsx(k,{onClick:b,children:"Xuất, sửa customization"}),e.jsx(k,{onClick:I,children:"Thêm customization từ file"})]})]}),e.jsx(S,{onClick:W,children:"Tạo customization"})]})]}),r&&e.jsxs("div",{className:"mb-4 rounded-md bg-red-50 p-4 text-red-700",children:["Có lỗi xảy ra khi tải dữ liệu: ",r.message]}),e.jsx(ze,{columns:Y,data:x,isLoading:c,onRowClick:G}),e.jsx(be,{open:t.isCopyModalOpen,onOpenChange:t.closeCopyModal,selectedCustomization:t.selectedCustomization,copyName:a.copyName,onCopyNameChange:a.setCopyName,onCancel:g,onConfirm:y,isLoading:a.isLoading}),e.jsx(Se,{open:t.isDeleteModalOpen,onOpenChange:t.closeDeleteModal,selectedCustomization:t.selectedCustomization,onCancel:t.closeDeleteModal,onConfirm:j,isLoading:l.isLoading}),e.jsx(Ie,{open:t.isExportModalOpen,onOpenChange:t.closeExportModal,showParsedData:n.showParsedData,exportStoreId:n.exportStoreId,onExportStoreIdChange:n.setExportStoreId,stores:n.stores,selectedFile:n.selectedFile,parsedData:n.parsedData,isExporting:n.isExporting,isSaving:n.isSaving,onCancel:C,onConfirm:n.showParsedData?N:C,onDownloadExportFile:n.handleDownloadExportFile,onUploadFile:n.handleUploadFile}),e.jsx(ve,{open:t.isImportModalOpen,onOpenChange:t.closeImportModal,showImportParsedData:o.showImportParsedData,importSelectedFile:o.importSelectedFile,importParsedData:o.importParsedData,isLoading:o.isLoading,storeName:(F=s.stores.find(p=>p.id===s.selectedStoreId))==null?void 0:F.store_name,onCancel:v,onConfirm:z,onDownloadTemplate:o.handleDownloadTemplate,onImportFileUpload:o.handleImportFileUpload}),e.jsx("input",{ref:o.importFileInputRef,type:"file",accept:".xlsx,.xls",onChange:o.handleImportFileChange,className:"hidden"}),e.jsx("input",{ref:n.fileInputRef,type:"file",accept:".xlsx,.xls",onChange:n.handleFileChange,className:"hidden"})]})})]})}const Mt=ke;export{Mt as component};
