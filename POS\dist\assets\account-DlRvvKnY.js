import{j as t,B as m,r as c,L as p}from"./index-CfbMU4Ye.js";import{H as l}from"./header-CiiJInbE.js";import{P as d}from"./profile-dropdown-HjZ6UGjk.js";import{S as h,T as x}from"./search-Bbt2JnTN.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{D as g}from"./data-table-CKwgOqix.js";import{B as u}from"./badge-DNJz5hg4.js";import{S as j}from"./settings-DfmLInNB.js";import{u as f,n as v,A as y}from"./navigation-CkEtSHI4.js";import"./separator-DVvwOaSX.js";import"./avatar-CE3yFgmj.js";import"./dropdown-menu-8bnotEGr.js";import"./index-D41EikqA.js";import"./index-CBP3KeI0.js";import"./index-4DjKSQeL.js";import"./check-C1W3FWto.js";import"./createLucideIcon-BH-J_-vM.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./calendar-DmzcYdpW.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-FztlF_ds.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./pos-api-BBB_ZiZD.js";import"./scroll-area-Bx6sgJqp.js";import"./select-_nXsh5SU.js";import"./index-Cqf6DKEV.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./table-pagination-Dl_vQX9-.js";import"./pagination-DqTRfWAm.js";import"./table-C3v-r6-e.js";function T({users:i,isLoading:s,onEditUser:o,onToggleStatus:a}){const n=[{key:"username",header:"Tên người dùng",width:"200px"},{key:"email",header:"Email",width:"250px"},{key:"status",header:"Trạng thái",width:"120px",render:r=>t.jsx(u,{variant:r==="active"?"default":"secondary",children:r==="active"?"Hoạt động":"Không hoạt động"})},{key:"actions",header:"",width:"100px",render:(r,e)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>o(e),className:"h-8 w-8",children:t.jsx(j,{className:"h-4 w-4"})}),t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>a(e.id),className:"h-8 w-8",children:e.status==="active"?"Hủy":"Kích hoạt"})]})}];return s?t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{children:"Đang tải dữ liệu..."})}):t.jsx(g,{data:i,columns:n,isLoading:s,pageSize:20,emptyMessage:"Không có tài khoản nào",loadingMessage:"Đang tải..."})}const N=({error:i})=>t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-red-600",children:i})})}),k=()=>t.jsx("div",{className:"mb-6",children:t.jsx(p,{to:y.CREATE,children:t.jsx(m,{children:"Tạo tài khoản"})})});function S(){const{users:i,isLoading:s,error:o,toggleUserStatus:a}=f(),n=c.useCallback(e=>{v(e.id)},[]),r=c.useCallback(async e=>{await a(e)},[a]);return o?t.jsx(N,{error:o}):t.jsxs(t.Fragment,{children:[t.jsxs(l,{children:[t.jsx(h,{}),t.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[t.jsx(x,{}),t.jsx(d,{})]})]}),t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx(k,{}),t.jsx(T,{users:i,isLoading:s,onEditUser:n,onToggleStatus:r})]})]})}const mt=S;export{mt as component};
