import{u as p}from"./useQuery-HgcIHxlE.js";import{a as m}from"./pos-api-mRg02iop.js";import{Q as _}from"./query-keys-3lmd-xp6.js";const g=async e=>(await m.get("/v1/reports/sale-summary/payment-methods",{params:e})).data,R=async e=>(await m.get("/v2/reports/sale-summary/payment-method-details",{params:e})).data,M=({startDate:e,endDate:t,selectedStoreIds:r,companyUid:n,brandUid:o,limit:s,enabled:y=!0})=>{var u,i;const d=[_.REPORTS_PAYMENT_METHOD_REVENUE,n,o,e,t,r.sort().join(","),s],a=p({queryKey:d,queryFn:async()=>{if(!r.length)return{data:[],message:"No stores selected",track_id:""};const c={brand_uid:o,company_uid:n,start_date:e,end_date:t,list_store_uid:r.join(","),store_open_at:0,by_days:1,...s&&{limit:s}};return g(c)},enabled:y&&r.length>0,staleTime:5*60*1e3,gcTime:10*60*1e3,retry:2,refetchOnWindowFocus:!1});return{data:((u=a.data)==null?void 0:u.data)||[],isLoading:a.isLoading,isError:a.isError,error:a.error,refetch:a.refetch,hasData:(((i=a.data)==null?void 0:i.data)||[]).length>0}};export{g as a,R as g,M as u};
