import{j as t,B as m,r as c,L as p}from"./index-UcdZ5AHH.js";import{H as l}from"./header-CE1GZ327.js";import{P as d}from"./profile-dropdown-CFMwD8wA.js";import{S as h,T as x}from"./search-CgdyS-kQ.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{D as g}from"./data-table-C_fR-seh.js";import{B as u}from"./badge-BlAal7b-.js";import{S as j}from"./settings-DBM6wTh3.js";import{u as f,n as v,A as y}from"./navigation-DG7LeI6C.js";import"./separator-C5UQ7YqK.js";import"./avatar-BOI9P1fI.js";import"./dropdown-menu-D3XvynCv.js";import"./index-MuNXZ_zP.js";import"./index-DPUGtNbb.js";import"./index-iiVug-md.js";import"./check-vnaEv-AC.js";import"./createLucideIcon-D7O7McKr.js";import"./search-context-DK2BgvuK.js";import"./command-DJT46NtT.js";import"./calendar-BZ1UqQsL.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DmI079wB.js";import"./search-B6f_4BGP.js";import"./createReactComponent-C1S2Ujit.js";import"./pos-api-j20LMGrC.js";import"./scroll-area-DQUG4R9C.js";import"./select-DOexGcsG.js";import"./index-BKS-UfoD.js";import"./IconChevronRight-CnyriCST.js";import"./IconSearch-Ca5dfxyj.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./table-pagination-CokcdUZG.js";import"./pagination-CTPzQV-r.js";import"./table-DHWQVnPn.js";function T({users:i,isLoading:s,onEditUser:o,onToggleStatus:a}){const n=[{key:"username",header:"Tên người dùng",width:"200px"},{key:"email",header:"Email",width:"250px"},{key:"status",header:"Trạng thái",width:"120px",render:r=>t.jsx(u,{variant:r==="active"?"default":"secondary",children:r==="active"?"Hoạt động":"Không hoạt động"})},{key:"actions",header:"",width:"100px",render:(r,e)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>o(e),className:"h-8 w-8",children:t.jsx(j,{className:"h-4 w-4"})}),t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>a(e.id),className:"h-8 w-8",children:e.status==="active"?"Hủy":"Kích hoạt"})]})}];return s?t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{children:"Đang tải dữ liệu..."})}):t.jsx(g,{data:i,columns:n,isLoading:s,pageSize:20,emptyMessage:"Không có tài khoản nào",loadingMessage:"Đang tải..."})}const N=({error:i})=>t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-red-600",children:i})})}),k=()=>t.jsx("div",{className:"mb-6",children:t.jsx(p,{to:y.CREATE,children:t.jsx(m,{children:"Tạo tài khoản"})})});function S(){const{users:i,isLoading:s,error:o,toggleUserStatus:a}=f(),n=c.useCallback(e=>{v(e.id)},[]),r=c.useCallback(async e=>{await a(e)},[a]);return o?t.jsx(N,{error:o}):t.jsxs(t.Fragment,{children:[t.jsxs(l,{children:[t.jsx(h,{}),t.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[t.jsx(x,{}),t.jsx(d,{})]})]}),t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx(k,{}),t.jsx(T,{users:i,isLoading:s,onEditUser:n,onToggleStatus:r})]})]})}const mt=S;export{mt as component};
