import{j as e,r as m,R as P,B as _,h as H,i as $,a4 as N}from"./index-CVQ6JZo2.js";import{P as U}from"./modal-DEESjk2b.js";import"./pos-api-mRg02iop.js";import"./date-range-picker-CXbMaowj.js";import{L as q}from"./form-CzmGigtT.js";import{I as z}from"./input-Al6WtUZF.js";import{T as W,a as K,b as A,c as F,d as Q,e as V}from"./table-BOc3nItc.js";import{C as T}from"./checkbox-BfLSzhzg.js";import{C as k,a as G,b as R}from"./collapsible-CRaCKnru.js";import{C as L}from"./select-BFhNE0YE.js";import{C as O}from"./chevron-right-CxgpqvrH.js";import{D as Y,a as X,e as J}from"./dialog-DDrduXt3.js";import{u as Z}from"./useCanGoBack-9-TGp53U.js";import"./vietqr-api-_ZZrmuU0.js";import{a as ee}from"./use-customizations-CIOuk7WT.js";import{u as se}from"./use-update-customization-YDjNTEEJ.js";import"./user-BJzEhOTa.js";import"./crm-api-CDzLLTww.js";function Ne({open:c,onOpenChange:a,onCancel:o,onConfirm:d,onAddMenuItem:x,isEditing:h,customizationName:p,groupName:S,setGroupName:v,minRequired:g,setMinRequired:w,maxAllowed:i,setMaxAllowed:j,menuItems:C}){return e.jsx(U,{title:`${h?"Sửa":"Tạo"} nhóm cho customization ${p||""}`,open:c,onOpenChange:a,onCancel:o,onConfirm:d,confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(z,{placeholder:"Tên nhóm",value:S,onChange:t=>v(t.target.value)})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{htmlFor:"min-required",className:"min-w-[80px] text-sm font-medium",children:"Yêu cầu"}),e.jsx(z,{id:"min-required",type:"number",value:g,onChange:t=>{let u=Number(t.target.value||0);u>1e3&&(u=1e3),u<0&&(u=0);const n=Number(g||0),l=u-n;w(String(u));{let s=Number(i||0)+l;s<0&&(s=0),s>1e3&&(s=1e3),j(String(s))}},min:"0",max:"1000",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(q,{htmlFor:"max-allowed",className:"min-w-[80px] text-sm font-medium",children:"Tối đa"}),e.jsx(z,{id:"max-allowed",type:"number",value:i,onChange:t=>{let u=Number(t.target.value||0);u<0&&(u=0),u>1e3&&(u=1e3),j(String(u))},min:"0",max:"1000",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-sm font-medium",children:"Danh sách món"}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(W,{children:[e.jsx(K,{children:e.jsxs(A,{children:[e.jsx(F,{children:"Tên"}),e.jsx(F,{children:"Giá"})]})}),e.jsxs(Q,{children:[C.map(t=>e.jsxs(A,{children:[e.jsx(V,{children:t.name}),e.jsxs(V,{children:[t.price.toLocaleString("vi-VN")," đ"]})]},t.id)),e.jsx(A,{className:"cursor-pointer hover:bg-gray-50",onClick:x,children:e.jsx(V,{colSpan:2,className:"py-4 text-center",children:e.jsx("span",{className:"font-medium text-blue-600",children:"Thêm món"})})})]})]})})]})]})})}function Se({open:c,onOpenChange:a,onCancel:o,onConfirm:d,menuItemSearchTerm:x,setMenuItemSearchTerm:h,selectedMenuSectionOpen:p,setSelectedMenuSectionOpen:S,remainingMenuSectionOpen:v,setRemainingMenuSectionOpen:g,selectedMenuItems:w,handleMenuItemToggle:i,selectedMenuItemsList:j,remainingMenuItemsList:C}){return e.jsx(U,{title:"Chọn món để thêm vào nhóm",open:c,onOpenChange:a,onCancel:o,onConfirm:d,confirmText:"Xác nhận",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(z,{placeholder:"Tìm kiếm món",value:x,onChange:t=>h(t.target.value),className:"w-full"}),e.jsxs(k,{open:p,onOpenChange:S,children:[e.jsxs(G,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Đã chọn (",j.length,")"]}),p?e.jsx(L,{className:"h-4 w-4"}):e.jsx(O,{className:"h-4 w-4"})]}),e.jsx(R,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:j.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Chưa có món nào được chọn"}):j.map(t=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(T,{checked:w.has(t.id),onCheckedChange:()=>i(t.id)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:t.item_name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[t.ots_price.toLocaleString("vi-VN")," đ"]})]})]},t.id))})})]}),e.jsxs(k,{open:v,onOpenChange:g,children:[e.jsxs(G,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Còn lại (",C.length,")"]}),v?e.jsx(L,{className:"h-4 w-4"}):e.jsx(O,{className:"h-4 w-4"})]}),e.jsx(R,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:C.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Không có món nào"}):C.map(t=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(T,{checked:w.has(t.id),onCheckedChange:()=>i(t.id)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:t.item_name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[t.ots_price.toLocaleString("vi-VN")," đ"]})]})]},t.id))})})]})]})})}function ve({open:c,onOpenChange:a,items:o,selectedItems:d,onItemsSelected:x}){const[h,p]=m.useState(""),[S,v]=m.useState(!1),[g,w]=m.useState(!1),[i,j]=m.useState(d);P.useEffect(()=>{j(d)},[d]);const C=m.useMemo(()=>{if(!h.trim())return o;const s=h.toLowerCase();return o.filter(f=>f.item_name.toLowerCase().includes(s)||f.item_id.toLowerCase().includes(s))},[o,h]),t=C.filter(s=>i.includes(s.item_id)),u=C.filter(s=>!i.includes(s.item_id)),n=s=>{const f=i.includes(s)?i.filter(y=>y!==s):[...i,s];j(f)},l=()=>{x(i),a(!1)},r=()=>{j(d),a(!1)};return e.jsx(Y,{open:c,onOpenChange:a,children:e.jsxs(X,{className:"max-w-2xl",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold",children:"Chọn món áp dụng"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Chọn các món mà customization này sẽ áp dụng"})]}),e.jsx("div",{children:e.jsx(z,{placeholder:"Tìm kiếm món...",value:h,onChange:s=>p(s.target.value),className:"w-full"})}),e.jsxs(k,{open:!S,onOpenChange:s=>v(!s),children:[e.jsx(G,{asChild:!0,children:e.jsxs(_,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",t.length,")"]}),S?e.jsx(O,{className:"h-4 w-4"}):e.jsx(L,{className:"h-4 w-4"})]})}),e.jsx(R,{className:"mt-2 overflow-hidden data-[state=closed]:hidden",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-2",children:t.length===0?e.jsx("p",{className:"p-2 text-sm text-gray-500",children:"Chưa có món nào được chọn"}):t.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(T,{checked:!0,onCheckedChange:()=>n(s.item_id)}),e.jsx("span",{className:"text-sm",children:s.item_name})]},s.id))})})]}),e.jsxs(k,{open:!g,onOpenChange:s=>w(!s),children:[e.jsx(G,{asChild:!0,children:e.jsxs(_,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",u.length,")"]}),g?e.jsx(O,{className:"h-4 w-4"}):e.jsx(L,{className:"h-4 w-4"})]})}),e.jsx(R,{className:"mt-2 overflow-hidden data-[state=closed]:hidden",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-2",children:u.length===0?e.jsx("p",{className:"p-2 text-sm text-gray-500",children:"Không có món nào"}):u.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(T,{checked:!1,onCheckedChange:()=>n(s.item_id)}),e.jsx("span",{className:"text-sm",children:s.item_name})]},s.id))})})]})]}),e.jsxs(J,{children:[e.jsx(_,{variant:"outline",onClick:r,children:"Hủy"}),e.jsx(_,{onClick:l,children:"Lưu"})]})]})})}function we(c={}){const a=H(),o=$(),d=Z(),[x,h]=m.useState(""),[p,S]=m.useState(""),[v,g]=m.useState(!1),[w,i]=m.useState(null),j=ee(),C=se();return{customizationName:x,selectedStoreId:p,isSubmitting:v,setCustomizationName:h,setSelectedStoreId:S,setExistingCustomization:i,handleBack:()=>{d?o.history.back():a({to:"/menu/customization/customization-in-store"})},handleSave:async(n,l,r)=>{var s;if(!x.trim()){N.error("Vui lòng nhập tên customization");return}if(!p){N.error("Vui lòng chọn cửa hàng");return}if(n.length===0){N.error("Vui lòng tạo ít nhất một nhóm");return}if(l.size===0){N.error("Vui lòng chọn ít nhất một món áp dụng");return}g(!0);try{const f=n.map(M=>({LstItem_Id:M.items.map(b=>b.code||b.id),Min_Permitted:M.minRequired,Max_Permitted:M.maxAllowed,Name:M.name,id:M.id.startsWith("CUS_GROUP_")?M.id:`CUS_GROUP_${Math.random().toString(36).substring(2,7).toUpperCase()}`})),y=n.flatMap(M=>M.items.map(b=>b.code||b.id)),I=Array.from(l).map(M=>{const b=r.find(B=>B.id===M);return(b==null?void 0:b.item_id)||M}),D=[...new Set([...y,...I])],E={name:x.trim(),storeUid:p,data:{LstItem_Options:f},listItem:D,sort:1e3,isUpdateSameCustomization:!1};c.isEdit&&c.customizationId?(await C.mutateAsync({customizationId:c.customizationId,existingCustomization:w||void 0,...E}),N.success("Đã cập nhật customization thành công!")):(await j.mutateAsync(E),N.success("Đã tạo customization thành công!")),(s=c.onSuccess)==null||s.call(c),d?o.history.back():a({to:"/menu/customization/customization-in-store"})}catch{const f=c.isEdit?"cập nhật":"tạo";N.error(`Lỗi khi ${f} customization. Vui lòng thử lại.`)}finally{g(!1)}},isFormValid:x.trim()&&p}}function ye(){const[c,a]=m.useState([]),[o,d]=m.useState(null),[x,h]=m.useState(""),[p,S]=m.useState("0"),[v,g]=m.useState("0"),[w,i]=m.useState([]),j=()=>{d(null),n()},C=l=>{const r=c.find(s=>s.id===l);r&&(d(l),h(r.name),S(r.minRequired.toString()),g(r.maxAllowed.toString()),i(r.items))},t=l=>{a(r=>r.filter(s=>s.id!==l)),N.success("Đã xóa nhóm thành công!")},u=l=>{if(!x.trim())return N.error("Vui lòng nhập tên nhóm"),!1;const r=parseInt(p),s=parseInt(v);if(isNaN(r)||r<0)return N.error("Yêu cầu phải là số hợp lệ"),!1;if(isNaN(s)||s<0)return N.error("Tối đa phải là số hợp lệ"),!1;if(w.length===0)return N.error("Vui lòng thêm ít nhất một món"),!1;const f={id:o||Date.now().toString(),name:x.trim(),minRequired:r,maxAllowed:s,items:w.map(y=>{const I=l.find(D=>D.id===y.id);return{...y,code:(I==null?void 0:I.item_id)||y.code||y.id,size:"M"}})};return o?(a(y=>y.map(I=>I.id===o?f:I)),N.success("Đã cập nhật nhóm thành công!")):(a(y=>[...y,f]),N.success("Đã tạo nhóm thành công!")),n(),!0},n=()=>{h(""),S("0"),g("0"),i([]),d(null)};return{customizationGroups:c,editingGroupId:o,groupName:x,minRequired:p,maxAllowed:v,menuItems:w,setGroupName:h,setMinRequired:S,setMaxAllowed:g,setMenuItems:i,setCustomizationGroups:a,handleCreateGroup:j,handleEditGroup:C,handleDeleteGroup:t,handleSaveGroup:u,resetGroupForm:n,isEditing:!!o}}function Me(c={}){const[a,o]=m.useState(new Set),[d,x]=m.useState(""),[h,p]=m.useState(!0),[S,v]=m.useState(!0),g=n=>{const l=new Set(a);l.has(n)?l.delete(n):l.add(n),o(l)},w=n=>{var s;const r=n.filter(f=>a.has(f.id)).map(f=>({id:f.id,name:f.item_name,price:f.ots_price,code:f.item_id}));return(s=c.onConfirm)==null||s.call(c,r),i(),N.success("Cập nhật món thành công"),r},i=()=>{o(new Set),x(""),p(!0),v(!0)},j=n=>{const l=new Set(n.map(r=>r.id));o(l)},C=n=>n.filter(l=>l.item_name.toLowerCase().includes(d.toLowerCase()));return{selectedMenuItems:a,menuItemSearchTerm:d,selectedMenuSectionOpen:h,remainingMenuSectionOpen:S,setMenuItemSearchTerm:x,setSelectedMenuSectionOpen:p,setRemainingMenuSectionOpen:v,handleMenuItemToggle:g,handleConfirmMenuItems:w,resetSelection:i,setSelectedMenuItemsFromGroup:j,getFilteredMenuItems:C,getSelectedMenuItemsList:n=>C(n).filter(s=>a.has(s.id)),getRemainingMenuItemsList:n=>C(n).filter(s=>!a.has(s.id)),hasSelectedItems:a.size>0}}function be(){const[c,a]=m.useState(!1),[o,d]=m.useState(!1),[x,h]=m.useState(!1);return{createGroupModalOpen:c,addItemModalOpen:o,dishModalOpen:x,setCreateGroupModalOpen:a,setAddItemModalOpen:d,setDishModalOpen:h,handleCloseModal:()=>{a(!1)},handleCloseAddItemModal:()=>{d(!1)},handleCloseDishModal:()=>{h(!1)},handleAddMenuItem:i=>i?(d(!0),!0):(N.error("Vui lòng chọn cửa hàng trước"),!1),handleOpenDishModal:i=>i?!1:(h(!0),!0)}}export{Se as A,Ne as C,ve as I,ye as a,Me as b,be as c,we as u};
