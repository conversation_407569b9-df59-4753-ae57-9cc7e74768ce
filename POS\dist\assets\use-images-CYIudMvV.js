import{a4 as n,a3 as o}from"./index-UcdZ5AHH.js";import{u as t}from"./useMutation-q12VR5WX.js";import"./pos-api-j20LMGrC.js";import{i}from"./images-api-BMYin8XI.js";import{Q as r}from"./query-keys-3lmd-xp6.js";const h=()=>t({mutationFn:i.uploadImage,onError:()=>{n.error("Tải ảnh lên thất bại. Vui lòng thử lại.")}}),l=()=>{const e=o();return t({mutationFn:i.syncStoreBackground,onSuccess:()=>{e.invalidateQueries({queryKey:[r.STORES]}),e.invalidateQueries({queryKey:[r.STORES_LIST]}),e.invalidateQueries({queryKey:[r.STORES_DETAIL]}),e.invalidateQueries({queryKey:[r.STORES_STATS]}),n.success("Đồng bộ màn hình phụ thành công!")},onError:()=>{n.error("Đồng bộ màn hình phụ thất bại. Vui lòng thử lại.")}})};export{l as a,h as u};
