import{u as S}from"./useQuery-HgcIHxlE.js";import{u as h,l as b,a3 as g,a4 as _}from"./index-CVQ6JZo2.js";import{u as y}from"./useMutation-ZsyDznMu.js";import{a as m}from"./pos-api-mRg02iop.js";import{Q as p}from"./query-keys-3lmd-xp6.js";const A=async t=>{const e=new URLSearchParams({company_uid:t.company_uid,brand_uid:t.brand_uid});if(t.page!==void 0&&e.append("page",String(t.page)),t.aggregate!==void 0&&e.append("aggregate",t.aggregate.toString()),t.store_uid&&e.append("store_uid",t.store_uid),t.search&&e.append("search",t.search),t.status&&e.append("status",t.status),t.type&&e.append("type",t.type),t.skip_limit!==void 0&&e.append("skip_limit",t.skip_limit?"true":"false"),t.list_store_uid){const o=Array.isArray(t.list_store_uid)?t.list_store_uid.join(","):t.list_store_uid;e.append("list_store_uid",o)}t.partner_auto_gen!==void 0&&e.append("partner_auto_gen",String(t.partner_auto_gen)),t.active!==void 0&&e.append("active",String(t.active));const s=`/mdata/v1/promotions?${e.toString()}`,a=await m.get(s);let r=[];return Array.isArray(a.data)?r=a.data:a.data&&a.data.data&&Array.isArray(a.data.data)?r=a.data.data:a.data&&typeof a.data=="object"&&(r=[]),{data:r,track_id:a.data.track_id||"generated-track-id"}},k=async t=>(await m.post("/mdata/v1/promotion",t)).data,w=async t=>(await m.put("/mdata/v1/promotion",t)).data,T=async(t,e,s)=>{const a=new URLSearchParams({company_uid:e,brand_uid:s,list_promotion_uid:t});await m.delete(`/mdata/v1/promotion?${a.toString()}`)},P=t=>{if(!t.promotion_id||!t.promotion_name)throw new Error("Invalid promotion data: missing promotion_id or promotion_name");const e=t.list_data||t.promotions||[];if(!Array.isArray(e))throw new Error("Invalid promotion data: promotion details must be an array");const s=e.map(n=>n.store_uid),a=s.length>0?`${s.length} cửa hàng`:"Không có cửa hàng",r=e.filter(n=>n.active===1),o=e.map(n=>n.created_at).filter(Boolean),i=o.length>0?Math.max(...o):Date.now()/1e3;return{code:t.promotion_id,name:t.promotion_name,store:a||"N/A",totalStores:e.length,isActive:r.length>0,createdAt:new Date(i*1e3),originalData:{promotion_id:t.promotion_id,promotion_name:t.promotion_name,partner_auto_gen:t.partner_auto_gen||0,array_agg:t.array_agg||[],ids_same_promotion:t.ids_same_promotion||[],promotions:e.map(n=>({active:n.active,created_at:n.created_at,store_name:n.store_name||"",store_uid:n.store_uid,promotion_uid:n.id||""}))}}},U=(t={})=>{var l,f;const{params:e={},enabled:s=!0,searchTerm:a,storeUid:r}=t,{company:o}=h(d=>d.auth),{selectedBrand:i}=b(),n={company_uid:(o==null?void 0:o.id)||"595e8cb4-674c-49f7-adec-826b211a7ce3",brand_uid:(i==null?void 0:i.id)||"d43a01ec-2f38-4430-a7ca-9b3324f7d39e",skip_limit:!0,aggregate:void 0,...e};r&&r!=="all"&&(n.store_uid=r),a&&(n.search=a);const c=S({queryKey:[p.PROMOTIONS_LIST,n],queryFn:async()=>{const d=await A(n);let u=[];Array.isArray(d.data)?u=d.data:d.data&&typeof d.data=="object"&&"data"in d.data&&Array.isArray(d.data.data)?u=d.data.data:d.data?u=[d.data]:u=[];const v=u.map(P);return{data:u,tableData:v,track_id:d.track_id||"generated-track-id"}},enabled:s&&!!(o!=null&&o.id||e.company_uid)&&!!(i!=null&&i.id||e.brand_uid),staleTime:5*60*1e3,gcTime:10*60*1e3});return{promotions:((l=c.data)==null?void 0:l.tableData)||[],apiPromotions:((f=c.data)==null?void 0:f.data)||[],isLoading:c.isLoading,error:c.error,refetch:c.refetch,isError:c.isError,isSuccess:c.isSuccess}},E=()=>{const t=g(),{company:e,brands:s}=h(i=>i.auth),a=s==null?void 0:s[0],{mutate:r,isPending:o}=y({mutationFn:async i=>{const n=(e==null?void 0:e.id)||"595e8cb4-674c-49f7-adec-826b211a7ce3",c=(a==null?void 0:a.id)||"d43a01ec-2f38-4430-a7ca-9b3324f7d39e";await T(i,n,c)},onSuccess:()=>{t.invalidateQueries({queryKey:[p.PROMOTIONS_LIST]})}});return{deletePromotion:r,isDeleting:o}},L=()=>{const t=g(),{mutate:e,isPending:s}=y({mutationFn:async a=>await k(a),onSuccess:a=>{_.success("Tạo khuyến mãi thành công!"),t.invalidateQueries({queryKey:[p.PROMOTIONS_LIST]})},onError:a=>{var o,i;const r=((i=(o=a==null?void 0:a.response)==null?void 0:o.data)==null?void 0:i.message)||(a==null?void 0:a.message)||"Có lỗi xảy ra khi tạo khuyến mãi";_.error(r)}});return{createPromotion:e,isCreating:s}},M=()=>{const t=g(),{mutate:e,isPending:s}=y({mutationFn:async a=>await w(a),onSuccess:a=>{_.success("Cập nhật khuyến mãi thành công!"),t.invalidateQueries({queryKey:[p.PROMOTIONS_LIST]})},onError:a=>{var o,i;const r=((i=(o=a==null?void 0:a.response)==null?void 0:o.data)==null?void 0:i.message)||(a==null?void 0:a.message)||"Có lỗi xảy ra khi cập nhật khuyến mãi";_.error(r)}});return{updatePromotion:e,isUpdating:s}};export{L as a,M as b,E as c,U as u};
