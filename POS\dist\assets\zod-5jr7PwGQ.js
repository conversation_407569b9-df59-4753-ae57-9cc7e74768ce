import{h as l,s as u,i as y}from"./form-D_U5B5Go.js";const d=(o,n,e)=>{if(o&&"reportValidity"in o){const r=l(e,n);o.setCustomValidity(r&&r.message||""),o.reportValidity()}},m=(o,n)=>{for(const e in n.fields){const r=n.fields[e];r&&r.ref&&"reportValidity"in r.ref?d(r.ref,e,o):r&&r.refs&&r.refs.forEach(i=>d(i,e,o))}},g=(o,n)=>{n.shouldUseNativeValidation&&m(o,n);const e={};for(const r in o){const i=l(n.fields,r),t=Object.assign(o[r]||{},{ref:i&&i.ref});if(E(n.names||Object.keys(o),r)){const s=Object.assign({},l(e,r));u(s,"root",t),u(e,r,s)}else u(e,r,t)}return e},E=(o,n)=>{const e=h(n);return o.some(r=>h(r).match(`^${e}\\.\\d+`))};function h(o){return o.replace(/\]|\[/g,"")}function V(o,n){for(var e={};o.length;){var r=o[0],i=r.code,t=r.message,s=r.path.join(".");if(!e[s])if("unionErrors"in r){var a=r.unionErrors[0].errors[0];e[s]={message:a.message,type:a.code}}else e[s]={message:t,type:i};if("unionErrors"in r&&r.unionErrors.forEach(function(p){return p.errors.forEach(function(v){return o.push(v)})}),n){var c=e[s].types,f=c&&c[r.code];e[s]=y(s,n,e,i,f?[].concat(f,r.message):r.message)}o.shift()}return e}function b(o,n,e){return e===void 0&&(e={}),function(r,i,t){try{return Promise.resolve(function(s,a){try{var c=Promise.resolve(o[e.mode==="sync"?"parse":"parseAsync"](r,n)).then(function(f){return t.shouldUseNativeValidation&&m({},t),{errors:{},values:e.raw?Object.assign({},r):f}})}catch(f){return a(f)}return c&&c.then?c.then(void 0,a):c}(0,function(s){if(function(a){return Array.isArray(a==null?void 0:a.errors)}(s))return{values:{},errors:g(V(s.errors,!t.shouldUseNativeValidation&&t.criteriaMode==="all"),t)};throw s}))}catch(s){return Promise.reject(s)}}}export{b as s};
