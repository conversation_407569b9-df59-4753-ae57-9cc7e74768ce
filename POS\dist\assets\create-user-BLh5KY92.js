import{r as m,j as e,h as g,B as C}from"./index-CVQ6JZo2.js";import"./date-range-picker-CXbMaowj.js";import{u as b,c as j,F as N,a as P,b as F,d as S,e as f}from"./form-CzmGigtT.js";import{I as k}from"./input-Al6WtUZF.js";import{c as v,u as y,A as V}from"./navigation-Pr2_Qafa.js";import{s as w}from"./zod-ByV4TDQ9.js";import{C as A}from"./checkbox-BfLSzhzg.js";import"./calendar-BszTCdZH.js";import"./createLucideIcon-DKVxsQv7.js";import"./index-CtK-wKtB.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-CxgpqvrH.js";import"./react-icons.esm-DMMA_g0o.js";import"./popover-DnoSPJNX.js";import"./select-BFhNE0YE.js";import"./index-LVHINuqD.js";import"./index-nc1u7392.js";import"./check-BE_j5GZD.js";function E(){const s=b({resolver:w(v),defaultValues:{username:"",email:"",password:"",permissions:[]}});return{form:s,resetForm:()=>{s.reset({username:"",email:"",password:"",permissions:[]})},isValid:s.formState.isValid,errors:s.formState.errors}}const I=s=>{const r=m.useCallback((a,n)=>{const c=s.getValues("permissions")||[],l=n?[...c,a]:c.filter(d=>d!==a);s.setValue("permissions",l)},[s]),i=m.useCallback(a=>(s.getValues("permissions")||[]).includes(a),[s]),t=m.useCallback(()=>{s.setValue("permissions",[])},[s]),o=m.useCallback(a=>{s.setValue("permissions",a)},[s]);return{togglePermission:r,isPermissionSelected:i,clearAllPermissions:t,selectAllPermissions:o}},M=({permission:s,isSelected:r,onChange:i})=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:s.id,checked:r,onCheckedChange:i}),e.jsx("label",{htmlFor:s.id,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:s.name})]}),T=({category:s,isPermissionSelected:r,onPermissionChange:i})=>e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-sm font-medium",children:s.name}),e.jsx("div",{className:"grid grid-cols-1 gap-2 md:grid-cols-2",children:s.permissions.map(t=>e.jsx(M,{permission:t,isSelected:r(t.id),onChange:o=>i(t.id,o)},t.id))})]}),U=({categories:s,isPermissionSelected:r,onPermissionChange:i,hasError:t,errorMessage:o})=>e.jsxs("div",{children:[e.jsxs(j,{className:"text-base font-medium",children:["Cài đặt quyền cho tài khoản ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"mt-4 space-y-6",children:s.map(a=>e.jsx(T,{category:a,isPermissionSelected:r,onPermissionChange:i},a.name))}),t&&o&&e.jsx("p",{className:"mt-2 text-sm text-destructive",children:o})]}),x=({name:s,label:r,placeholder:i,type:t="text",required:o=!1,form:a})=>e.jsx(P,{control:a.control,name:s,render:({field:n})=>e.jsxs(F,{children:[e.jsxs(j,{children:[r," ",o&&e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(S,{children:e.jsx(k,{placeholder:i,type:t,...n})}),e.jsx(f,{})]})});function q(){var h;const s=g(),{form:r,resetForm:i}=E(),{createUser:t,permissionCategories:o,isLoading:a}=y(),{togglePermission:n,isPermissionSelected:c}=I(r),l=m.useCallback(async p=>{try{await t(p),i(),s({to:V.LIST})}catch(u){console.error("Error creating account:",u)}},[t,i,s]),d=m.useCallback((p,u)=>{n(p,u)},[n]);return e.jsx("div",{className:"mx-auto px-4 py-8",children:e.jsx("div",{className:"max-w-4xl",children:e.jsx(N,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(l),className:"space-y-6",children:[e.jsx(x,{name:"username",label:"Tên người dùng",placeholder:"Nhập tên người dùng",required:!0,form:r}),e.jsx(x,{name:"email",label:"Email",placeholder:"Nhập email",type:"email",form:r}),e.jsx(x,{name:"password",label:"Mật khẩu",placeholder:"Nhập mật khẩu",type:"password",required:!0,form:r}),e.jsx(U,{categories:o,isPermissionSelected:c,onPermissionChange:d,hasError:!!r.formState.errors.permissions,errorMessage:(h=r.formState.errors.permissions)==null?void 0:h.message}),e.jsx(C,{type:"submit",disabled:a,className:"mt-3",children:a?"Đang tạo...":"Xác nhận"})]})})})})}const se=q;export{se as component};
