import{j as r,B as n,c as i}from"./index-UcdZ5AHH.js";import{C as m}from"./calendar-BZ1UqQsL.js";import{P as c,a as f,b as u}from"./popover-BeZit_vZ.js";import{C as p}from"./calendar-DZLqW2ag.js";import{f as x}from"./isSameMonth-C8JQo-AN.js";import{v as s}from"./date-range-picker-DxA68ufO.js";function y({date:o,onDateChange:e,placeholder:t="Chọn ngày",disabled:a=!1,className:l}){return r.jsxs(c,{children:[r.jsx(f,{asChild:!0,children:r.jsxs(n,{variant:"outline",className:i("w-full justify-start text-left font-normal",!o&&"text-muted-foreground",l),disabled:a,children:[r.jsx(p,{className:"mr-2 h-4 w-4"}),o?x(o,"dd/MM/yyyy",{locale:s}):t]})}),r.jsx(u,{className:"w-auto p-0",align:"start",children:r.jsx(m,{mode:"single",selected:o,onSelect:e,initialFocus:!0,locale:s})})]})}export{y as D};
