import{u as y}from"./useQuery-HgcIHxlE.js";import{u as _,l as g,a3 as p,a4 as m}from"./index-CVQ6JZo2.js";import{u as v}from"./useMutation-ZsyDznMu.js";import{c as T}from"./vietqr-api-_ZZrmuU0.js";import{a as h}from"./pos-api-mRg02iop.js";import{c as l,g as f}from"./stores-api-BwFq9epY.js";import{Q as u}from"./query-keys-3lmd-xp6.js";const L=(i={})=>{const{params:r={},enabled:a=!0}=i,{company:e}=_(d=>d.auth),{selectedBrand:s}=g(),t={...{company_uid:(e==null?void 0:e.id)||"",brand_uid:(s==null?void 0:s.id)||"",page:1,limit:50},...r},c=!!(e!=null&&e.id&&(s!=null&&s.id));return y({queryKey:[u.STORES_LIST,t],queryFn:async()=>{var S;return((S=(await f(t)).data)==null?void 0:S.map(n=>({...T(n),fb_store_id:n.fb_store_id,city_name:n.city_name,expiry_date:n.expiry_date,active:n.active,latitude:n.latitude,longitude:n.longitude,address:n.address})))||[]},enabled:a&&c,staleTime:10*60*1e3,refetchInterval:5*60*1e3})},F=(i,r=!0)=>{const{company:a}=_(s=>s.auth),{selectedBrand:e}=g();return y({queryKey:[u.STORES_DETAIL,i,a==null?void 0:a.id,e==null?void 0:e.id],queryFn:async()=>{var c,d;if(!(a!=null&&a.id)||!(e!=null&&e.id)||!i)return;const s=new URLSearchParams({company_uid:a.id,brand_uid:e.id,id:i}),o=await h.get(`/mdata/v1/store?${s.toString()}`);if(!((c=o.data)!=null&&c.data))return;const t=o.data.data;return{id:t.id,name:t.store_name,code:t.store_id,status:t.active===1?"active":"inactive",address:t.address,phone:t.phone,email:t.email||"",companyId:t.company_uid,brandId:t.brand_uid,cityId:t.city_uid,cityName:((d=t.city)==null?void 0:d.city_name)||"",isActive:t.active===1,isFabi:t.is_fabi===1,isAhamoveActive:t.is_ahamove_active===1,latitude:t.latitude||0,longitude:t.longitude||0,deliveryServices:t.delivery_services||"",createdAt:new Date(t.created_at*1e3),updatedAt:new Date(t.updated_at*1e3),expiryDate:new Date(t.expiry_date*1e3)}},enabled:r&&!!i&&!!(a!=null&&a.id)&&!!(e!=null&&e.id),staleTime:15*60*1e3,gcTime:30*60*1e3})},I=()=>{const{company:i,brands:r}=_(e=>e.auth),a=r==null?void 0:r[0];return y({queryKey:[u.STORES_LIST,"sort"],queryFn:async()=>{const e=(i==null?void 0:i.id)||"",s=(a==null?void 0:a.id)||"";if(!e||!s)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");return(await h.get("/mdata/v1/stores",{params:{company_uid:e,brand_uid:s,skip_limit:!0}})).data},enabled:!!(i!=null&&i.id&&(a!=null&&a.id)),staleTime:5*60*1e3})},K=()=>{const i=p(),{mutate:r,isPending:a}=v({mutationFn:async e=>await h.put("/mdata/v1/sort-store",e),onSuccess:()=>{i.invalidateQueries({queryKey:[u.STORES_LIST]}),m.success("Cập nhật thứ tự cửa hàng thành công")},onError:e=>{var o,t;const s=((t=(o=e==null?void 0:e.response)==null?void 0:o.data)==null?void 0:t.message)||"Có lỗi xảy ra khi cập nhật thứ tự cửa hàng";m.error(s)}});return{updateSort:r,isUpdating:a}},P=()=>{const i=p(),{mutate:r,isPending:a}=v({mutationFn:async e=>await l(e),onSuccess:()=>{i.invalidateQueries({queryKey:[u.STORES_LIST],refetchType:"none"}),setTimeout(()=>{i.refetchQueries({queryKey:[u.STORES_LIST]})},100),m.success("Tạo cửa hàng thành công!")},onError:e=>{m.error(`Lỗi khi tạo cửa hàng: ${e.message}`)}});return{createStore:r,isCreating:a}};export{I as a,K as b,F as c,P as d,L as u};
