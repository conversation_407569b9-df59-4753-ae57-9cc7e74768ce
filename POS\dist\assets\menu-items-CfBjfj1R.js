import{r as y,j as e,B as x,h as X,e as E}from"./index-CfbMU4Ye.js";import{u as q}from"./useLocation-qT9H2kdU.js";import{T as Q,a as P,c as C,b as D}from"./tabs-BqFFB_d7.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import"./address-map-display--KjctECt.js";import{u as _,g as J,a as W,b as Y,c as Z,d as ee,e as ae,f as A}from"./index-DrO-sOnq.js";import{T as se,a as te,b as H,c as ne,d as le,e as G}from"./table-C3v-r6-e.js";import{D as ce}from"./data-table-pagination-Kq742vwq.js";import{D as c}from"./data-table-column-header-DxTOtvid.js";import{B as re}from"./badge-DNJz5hg4.js";import{I as F}from"./image-DIo0afo3.js";import{C as T}from"./circle-check-big-CnA50nSH.js";import{C as I}from"./circle-x-BmLiWp0R.js";import{c as ie}from"./createLucideIcon-BH-J_-vM.js";import{T as oe}from"./trash-2-BgCVQZay.js";import{I as B}from"./input-D8TU6hMD.js";import{S as j,a as p,b as v,c as b,d as i}from"./select-_nXsh5SU.js";import{E as U}from"./eye-B1Vc13Bm.js";import{P as $}from"./plus-CerxpTbe.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const de=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],M=ie("rotate-ccw",de),me=[{id:"ITEM-KDXI",code:"ITEM-KDXI",name:"sdfsd",group:"Uncategory",type:"Món thường",status:"available",canTakeaway:!0,canDelivery:!0,order:1e3,lastUpdated:"23-07-2025 16:19:12"},{id:"ITEM-FF6U",code:"ITEM-FF6U",name:"okok",group:"Uncategory",type:"Món thường",status:"available",canTakeaway:!0,canDelivery:!0,order:1337,lastUpdated:"29-07-2025 10:50:03"}],he=[{id:"1",code:"1234",name:"manhmanh",status:"available",order:1e3,maxItems:0,requiredItems:0,lastUpdated:"30-07-2025 00:00:00"},{id:"2",code:"CHADA",name:"chà da da",status:"available",order:1,maxItems:0,requiredItems:0,lastUpdated:"08-08-2025 00:00:00"},{id:"3",code:"EATCHICK",name:"123anga",status:"available",order:2,maxItems:0,requiredItems:0,lastUpdated:"06-08-2025 00:00:00"},{id:"4",code:"1",name:"131231234",status:"unavailable",order:1,maxItems:0,requiredItems:0,lastUpdated:"28-07-2025 00:00:00"}],xe=[{id:"1",code:"COMBO-SPXU",name:"ok",image:"https://via.placeholder.com/48x48/4A5568/FFFFFF?text=IMG",type:"combo",items:[],price:5e4,status:"available",canTakeaway:!0,canDelivery:!0,lastUpdated:"12-08-2025 13:35:27"},{id:"2",code:"BUN",name:"bún bò",type:"combo",items:[],price:35e3,status:"available",canTakeaway:!0,canDelivery:!0,lastUpdated:"12-08-2025 08:57:02"},{id:"3",code:"COMBO-SK26",name:"Combo test 1",type:"combo",items:[],price:75e3,status:"available",canTakeaway:!0,canDelivery:!0,lastUpdated:"12-08-2025 08:47:23"},{id:"4",code:"COMBO-INQV",name:"Matcha",type:"custom-combo",items:[],price:45e3,status:"available",canTakeaway:!0,canDelivery:!0,lastUpdated:"12-08-2025 11:02:21"}];function ue(t){const[a,s]=y.useState([]),[l,o]=y.useState([]),[m,d]=y.useState([]),[u,g]=y.useState(!0),[N,n]=y.useState({search:"",group:"",type:"",status:"",canTakeaway:"",canDelivery:"",order:""});return y.useEffect(()=>{(async()=>{g(!0);try{await new Promise(h=>setTimeout(h,1e3)),s(me),o(he),d(xe)}catch(h){console.error("Error fetching menu items:",h)}finally{g(!1)}})()},[t]),{items:a,groups:l,combos:m,filters:N,updateFilters:w=>{n(h=>({...h,...w}))},isLoading:u}}const ge=()=>[...[{id:"index",header:()=>e.jsx("div",{className:"text-left",children:"#"}),cell:({row:a,table:s})=>{const l=s.getState().pagination.pageIndex,o=s.getState().pagination.pageSize;return e.jsx("div",{className:"w-[50px] text-left font-medium",children:l*o+a.index+1})},enableSorting:!1,enableHiding:!1,size:60}],{accessorKey:"code",header:({column:a})=>e.jsx(c,{column:a,title:"Mã nhóm"}),cell:({row:a})=>e.jsx("div",{className:"text-left font-medium",children:a.getValue("code")}),enableSorting:!0},{accessorKey:"name",header:({column:a})=>e.jsx(c,{column:a,title:"Tên nhóm"}),cell:({row:a})=>e.jsx("div",{className:"text-left",children:a.getValue("name")}),enableSorting:!0},{accessorKey:"status",header:({column:a})=>e.jsx(c,{column:a,title:"Trạng thái"}),cell:({row:a})=>{const s=a.getValue("status");return e.jsx("div",{className:"text-center",children:e.jsx("span",{className:`text-sm font-medium ${s==="available"?"text-green-600":"text-red-600"}`,children:s==="available"?"Có bán":"Không bán"})})},enableSorting:!0},{accessorKey:"order",header:({column:a})=>e.jsx(c,{column:a,title:"Thứ tự"}),cell:({row:a})=>e.jsx("div",{className:"text-center",children:a.getValue("order")}),enableSorting:!0},{accessorKey:"maxItems",header:({column:a})=>e.jsx(c,{column:a,title:"Số món tối đa"}),cell:({row:a})=>e.jsx("div",{className:"text-center",children:a.getValue("maxItems")||0}),enableSorting:!0},{accessorKey:"requiredItems",header:({column:a})=>e.jsx(c,{column:a,title:"Số món yêu cầu"}),cell:({row:a})=>e.jsx("div",{className:"text-center",children:a.getValue("requiredItems")||0}),enableSorting:!0},{accessorKey:"lastUpdated",header:({column:a})=>e.jsx(c,{column:a,title:"Thời gian cập nhật"}),cell:({row:a})=>e.jsx("div",{className:"text-left text-sm text-muted-foreground",children:a.getValue("lastUpdated")}),enableSorting:!0}],L=()=>[...[{id:"index",header:()=>e.jsx("div",{className:"text-left",children:"#"}),cell:({row:a,table:s})=>{const l=s.getState().pagination.pageIndex,o=s.getState().pagination.pageSize;return e.jsx("div",{className:"w-[50px] text-left font-medium",children:l*o+a.index+1})},enableSorting:!1,enableHiding:!1,size:60}],{accessorKey:"code",header:({column:a})=>e.jsx(c,{column:a,title:"Mã món"}),cell:({row:a})=>e.jsx("div",{className:"text-left font-medium",children:a.getValue("code")}),enableSorting:!0},{accessorKey:"name",header:({column:a})=>e.jsx(c,{column:a,title:"Tên món"}),cell:({row:a})=>e.jsx("div",{className:"text-left",children:a.getValue("name")}),enableSorting:!0},{id:"image",header:()=>e.jsx("div",{children:"Ảnh"}),cell:({row:a})=>{const s=a.original;return e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-md",children:s.image?e.jsx("img",{src:s.image,alt:s.name,className:"w-12 h-12 object-cover rounded-md"}):e.jsx(F,{className:"w-6 h-6 text-gray-400"})})})},enableSorting:!1},{accessorKey:"group",header:({column:a})=>e.jsx(c,{column:a,title:"Nhóm món"}),cell:({row:a})=>e.jsx("div",{className:"text-left",children:a.getValue("group")}),enableSorting:!0},{accessorKey:"type",header:({column:a})=>e.jsx(c,{column:a,title:"Loại món"}),cell:({row:a})=>e.jsx("div",{className:"text-left",children:a.getValue("type")}),enableSorting:!0},{accessorKey:"status",header:({column:a})=>e.jsx(c,{column:a,title:"Trạng thái"}),cell:({row:a})=>{const s=a.getValue("status");return e.jsx("div",{className:"flex justify-center",children:e.jsx(re,{variant:s==="available"?"default":"secondary",children:s==="available"?"Có bán":"Hết hàng"})})},enableSorting:!0},{accessorKey:"canTakeaway",header:({column:a})=>e.jsx(c,{column:a,title:"Bán tại chỗ"}),cell:({row:a})=>{const s=a.getValue("canTakeaway");return e.jsx("div",{className:"flex justify-center",children:s?e.jsx(T,{className:"h-5 w-5 text-green-500"}):e.jsx(I,{className:"h-5 w-5 text-red-500"})})},enableSorting:!0},{accessorKey:"canDelivery",header:({column:a})=>e.jsx(c,{column:a,title:"Bán mang đi"}),cell:({row:a})=>{const s=a.getValue("canDelivery");return e.jsx("div",{className:"flex justify-center",children:s?e.jsx(T,{className:"h-5 w-5 text-green-500"}):e.jsx(I,{className:"h-5 w-5 text-red-500"})})},enableSorting:!0},{accessorKey:"order",header:({column:a})=>e.jsx(c,{column:a,title:"Thứ tự"}),cell:({row:a})=>e.jsx("div",{className:"text-center",children:a.getValue("order")}),enableSorting:!0},{accessorKey:"lastUpdated",header:({column:a})=>e.jsx(c,{column:a,title:"Thời gian cập nhật"}),cell:({row:a})=>e.jsx("div",{className:"text-left text-sm text-muted-foreground",children:a.getValue("lastUpdated")}),enableSorting:!0}],je=()=>[...[{id:"index",header:()=>e.jsx("div",{className:"text-left",children:"#"}),cell:({row:a,table:s})=>{const l=s.getState().pagination.pageIndex,o=s.getState().pagination.pageSize;return e.jsx("div",{className:"w-[50px] text-left font-medium",children:l*o+a.index+1})},enableSorting:!1,enableHiding:!1,size:60}],{accessorKey:"code",header:({column:a})=>e.jsx(c,{column:a,title:"Mã combo"}),cell:({row:a})=>e.jsx("div",{className:"text-left font-medium",children:a.getValue("code")}),enableSorting:!0},{accessorKey:"name",header:({column:a})=>e.jsx(c,{column:a,title:"Tên combo"}),cell:({row:a})=>e.jsx("div",{className:"text-left",children:a.getValue("name")}),enableSorting:!0},{id:"image",header:()=>e.jsx("div",{children:"Ảnh"}),cell:({row:a})=>{const s=a.original;return e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-md",children:s.image?e.jsx("img",{src:s.image,alt:s.name,className:"w-12 h-12 object-cover rounded-md"}):e.jsx(F,{className:"w-6 h-6 text-gray-400"})})})},enableSorting:!1},{accessorKey:"status",header:({column:a})=>e.jsx(c,{column:a,title:"Trạng thái"}),cell:({row:a})=>{const s=a.getValue("status");return e.jsx("div",{className:"text-center",children:e.jsx("span",{className:`text-sm font-medium ${s==="available"?"text-green-600":"text-red-600"}`,children:s==="available"?"Có bán":"Hết hàng"})})},enableSorting:!0},{accessorKey:"canTakeaway",header:({column:a})=>e.jsx(c,{column:a,title:"Bán ăn tại chỗ"}),cell:({row:a})=>{const s=a.getValue("canTakeaway");return e.jsx("div",{className:"flex justify-center",children:s?e.jsx(T,{className:"h-5 w-5 text-green-500"}):e.jsx(I,{className:"h-5 w-5 text-red-500"})})},enableSorting:!0},{accessorKey:"canDelivery",header:({column:a})=>e.jsx(c,{column:a,title:"Bán mang đi"}),cell:({row:a})=>{const s=a.getValue("canDelivery");return e.jsx("div",{className:"flex justify-center",children:s?e.jsx(T,{className:"h-5 w-5 text-green-500"}):e.jsx(I,{className:"h-5 w-5 text-red-500"})})},enableSorting:!0},{accessorKey:"lastUpdated",header:({column:a})=>e.jsx(c,{column:a,title:"Thời gian cập nhật"}),cell:({row:a})=>e.jsx("div",{className:"text-left text-sm text-muted-foreground",children:a.getValue("lastUpdated")}),enableSorting:!0}],pe=()=>[...[{id:"index",header:()=>e.jsx("div",{className:"text-left",children:"#"}),cell:({row:a,table:s})=>{const l=s.getState().pagination.pageIndex,o=s.getState().pagination.pageSize;return e.jsx("div",{className:"w-[50px] text-left font-medium",children:l*o+a.index+1})},enableSorting:!1,enableHiding:!1,size:60}],{accessorKey:"code",header:({column:a})=>e.jsx(c,{column:a,title:"Mã combo"}),cell:({row:a})=>e.jsx("div",{className:"text-left font-medium",children:a.getValue("code")}),enableSorting:!0},{accessorKey:"name",header:({column:a})=>e.jsx(c,{column:a,title:"Tên combo"}),cell:({row:a})=>e.jsx("div",{className:"text-left",children:a.getValue("name")}),enableSorting:!0},{id:"image",header:()=>e.jsx("div",{children:"Ảnh"}),cell:({row:a})=>{const s=a.original;return e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-md",children:s.image?e.jsx("img",{src:s.image,alt:s.name,className:"w-12 h-12 object-cover rounded-md"}):e.jsx(F,{className:"w-6 h-6 text-gray-400"})})})},enableSorting:!1},{accessorKey:"status",header:({column:a})=>e.jsx(c,{column:a,title:"Trạng thái"}),cell:({row:a})=>{const s=a.getValue("status");return e.jsx("div",{className:"text-center",children:e.jsx("span",{className:`text-sm font-medium ${s==="available"?"text-green-600":"text-red-600"}`,children:s==="available"?"Có bán":"Hết hàng"})})},enableSorting:!0},{accessorKey:"canTakeaway",header:({column:a})=>e.jsx(c,{column:a,title:"Bán ăn tại chỗ"}),cell:({row:a})=>{const s=a.getValue("canTakeaway");return e.jsx("div",{className:"flex justify-center",children:s?e.jsx(T,{className:"h-5 w-5 text-green-500"}):e.jsx(I,{className:"h-5 w-5 text-red-500"})})},enableSorting:!0},{accessorKey:"canDelivery",header:({column:a})=>e.jsx(c,{column:a,title:"Bán mang đi"}),cell:({row:a})=>{const s=a.getValue("canDelivery");return e.jsx("div",{className:"flex justify-center",children:s?e.jsx(T,{className:"h-5 w-5 text-green-500"}):e.jsx(I,{className:"h-5 w-5 text-red-500"})})},enableSorting:!0},{accessorKey:"lastUpdated",header:({column:a})=>e.jsx(c,{column:a,title:"Thời gian cập nhật"}),cell:({row:a})=>e.jsx("div",{className:"text-left text-sm text-muted-foreground",children:a.getValue("lastUpdated")}),enableSorting:!0}],ve=t=>{switch(t){case"item-type":return ge();case"items":return L();case"combo":return je();case"combo-special":return pe();default:return L()}};function K({data:t,type:a}){var w;const[s,l]=y.useState([]),[o,m]=y.useState([]),[d,u]=y.useState({}),[g,N]=y.useState({}),n=ve(a),f=_({data:t,columns:n,state:{sorting:s,columnVisibility:d,columnFilters:o,rowSelection:g},onSortingChange:l,onColumnFiltersChange:m,onColumnVisibilityChange:u,onRowSelectionChange:N,getCoreRowModel:ae(),getFilteredRowModel:ee(),getPaginationRowModel:Z(),getSortedRowModel:Y(),getFacetedRowModel:W(),getFacetedUniqueValues:J()});return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(se,{children:[e.jsx(te,{children:f.getHeaderGroups().map(h=>e.jsx(H,{children:h.headers.map(r=>e.jsx(ne,{className:"text-center",children:r.isPlaceholder?null:A(r.column.columnDef.header,r.getContext())},r.id))},h.id))}),e.jsx(le,{children:(w=f.getRowModel().rows)!=null&&w.length?f.getRowModel().rows.map(h=>e.jsx(H,{"data-state":h.getIsSelected()&&"selected",className:"hover:bg-muted/50",children:h.getVisibleCells().map(r=>e.jsx(G,{className:"text-center",children:A(r.column.columnDef.cell,r.getContext())},r.id))},h.id)):e.jsx(H,{children:e.jsx(G,{colSpan:n.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]})}),e.jsx(ce,{table:f})]})}function O({onSyncMenu:t,onDeleteMenu:a,variant:s="desktop",className:l=""}){const o=s==="desktop",m=o?`flex gap-2 ${l}`:`flex flex-col gap-2 w-full md:w-auto md:flex-shrink-0 ${l}`,d=o?"":"text-sm";return e.jsxs("div",{className:m,children:[e.jsxs(x,{variant:"outline",onClick:t,className:`text-orange-600 border-orange-600 hover:bg-orange-50 ${d}`,children:[e.jsx(M,{className:"h-4 w-4 mr-2"}),"Đồng bộ thực đơn từ POS"]}),e.jsxs(x,{variant:"outline",onClick:a,className:`text-red-600 border-red-600 hover:bg-red-50 ${d}`,children:[e.jsx(oe,{className:"h-4 w-4 mr-2"}),"Xóa thực đơn"]})]})}function be({onSyncItems:t,onViewPreview:a,onCreateItem:s,className:l=""}){return e.jsxs("div",{className:`flex flex-wrap items-center gap-2 ${l}`,children:[e.jsxs(x,{variant:"outline",onClick:t,className:"text-orange-600 border-orange-600 hover:bg-orange-50",children:[e.jsx(M,{className:"h-4 w-4 mr-2"}),"Đồng bộ nhóm món"]}),e.jsxs(x,{variant:"outline",onClick:a,children:[e.jsx(U,{className:"h-4 w-4 mr-2"}),"Xem trước"]}),e.jsxs(x,{onClick:s,children:[e.jsx($,{className:"h-4 w-4 mr-2"}),"Tạo nhóm"]})]})}function Ne({filters:t,onFiltersChange:a}){const s=n=>{a({search:n})},l=n=>{a({status:n})},o=n=>{a({order:n})},m=()=>{console.log("Applying filters:",t)},d=()=>{console.log("Xem trước")},u=()=>{console.log("Tạo nhóm món")},g=()=>{console.log("Đồng bộ nhóm món")},N=()=>1;return e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-[220px]",children:e.jsx(B,{placeholder:"TÊN/MÃ NHÓM",value:t.search,onChange:n=>s(n.target.value),className:"h-10"})}),e.jsx("div",{className:"w-[160px]",children:e.jsxs(j,{value:t.status,onValueChange:l,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"TRẠNG THÁI"})}),e.jsxs(b,{children:[e.jsx(i,{value:"available",children:"Có bán"}),e.jsx(i,{value:"unavailable",children:"Hết hàng"})]})]})}),e.jsx("div",{className:"w-[140px]",children:e.jsxs(j,{value:t.order,onValueChange:o,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"THỨ TỰ"})}),e.jsxs(b,{children:[e.jsx(i,{value:"asc",children:"Tăng dần"}),e.jsx(i,{value:"desc",children:"Giảm dần"})]})]})}),e.jsx("div",{className:"ml-2",children:e.jsx(x,{onClick:m,className:"h-10 px-6 bg-blue-600 text-white hover:bg-blue-700",children:"Lọc"})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm font-medium",children:["SỐ LƯỢNG: ",N()]}),e.jsx("button",{className:"text-blue-600 text-sm hover:underline",children:"Sắp xếp thứ tự hiển thị"})]})}),e.jsx(be,{onSyncItems:g,onViewPreview:d,onCreateItem:u})]})]})}function fe({onSyncItems:t,onViewPreview:a,onCreateItem:s,className:l=""}){return e.jsxs("div",{className:`flex flex-wrap items-center gap-2 ${l}`,children:[e.jsxs(x,{variant:"outline",onClick:t,className:"text-orange-600 border-orange-600 hover:bg-orange-50",children:[e.jsx(M,{className:"h-4 w-4 mr-2"}),"Đồng bộ món"]}),e.jsxs(x,{variant:"outline",onClick:a,children:[e.jsx(U,{className:"h-4 w-4 mr-2"}),"Xem trước"]}),e.jsxs(x,{onClick:s,children:[e.jsx($,{className:"h-4 w-4 mr-2"}),"Tạo món cha"]})]})}function ye({filters:t,onFiltersChange:a}){const s=r=>{a({search:r})},l=r=>{a({group:r})},o=r=>{a({type:r})},m=r=>{a({status:r})},d=r=>{a({canTakeaway:r})},u=r=>{a({canDelivery:r})},g=r=>{a({order:r})},N=()=>{console.log("Applying filters:",t)},n=()=>{console.log("Xem trước")},f=()=>{console.log("Tạo món cha")},w=()=>{console.log("Đồng bộ món")},h=()=>39;return e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2 flex-wrap",children:[e.jsx("div",{className:"w-[180px]",children:e.jsx(B,{placeholder:"TÊN/MÃ MÓN",value:t.search,onChange:r=>s(r.target.value),className:"h-10"})}),e.jsx("div",{className:"w-[140px]",children:e.jsxs(j,{value:t.group,onValueChange:l,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"NHÓM MÓN"})}),e.jsx(b,{children:e.jsx(i,{value:"uncategory",children:"Uncategory"})})]})}),e.jsx("div",{className:"w-[130px]",children:e.jsxs(j,{value:t.type,onValueChange:o,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"LOẠI MÓN"})}),e.jsx(b,{children:e.jsx(i,{value:"normal",children:"Món thường"})})]})}),e.jsx("div",{className:"w-[130px]",children:e.jsxs(j,{value:t.status,onValueChange:m,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"TRẠNG THÁI"})}),e.jsxs(b,{children:[e.jsx(i,{value:"available",children:"Có bán"}),e.jsx(i,{value:"unavailable",children:"Hết hàng"})]})]})}),e.jsx("div",{className:"w-[130px]",children:e.jsxs(j,{value:t.canTakeaway,onValueChange:d,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"BÁN TẠI CHỖ"})}),e.jsxs(b,{children:[e.jsx(i,{value:"true",children:"Có"}),e.jsx(i,{value:"false",children:"Không"})]})]})}),e.jsx("div",{className:"w-[130px]",children:e.jsxs(j,{value:t.canDelivery,onValueChange:u,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"BÁN MANG ĐI"})}),e.jsxs(b,{children:[e.jsx(i,{value:"true",children:"Có"}),e.jsx(i,{value:"false",children:"Không"})]})]})}),e.jsx("div",{className:"w-[110px]",children:e.jsxs(j,{value:t.order,onValueChange:g,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"THỨ TỰ"})}),e.jsxs(b,{children:[e.jsx(i,{value:"asc",children:"Tăng dần"}),e.jsx(i,{value:"desc",children:"Giảm dần"})]})]})}),e.jsx("div",{className:"ml-2",children:e.jsx(x,{onClick:N,className:"h-10 px-6 bg-blue-600 text-white hover:bg-blue-700",children:"Lọc"})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm font-medium",children:["SỐ LƯỢNG: ",h()]}),e.jsx("button",{className:"text-blue-600 text-sm hover:underline",children:"Sắp xếp thứ tự hiển thị"})]})}),e.jsx(fe,{onSyncItems:w,onViewPreview:n,onCreateItem:f})]})]})}function we({onSyncItems:t,onViewPreview:a,className:s=""}){return e.jsxs("div",{className:`flex flex-wrap items-center gap-2 ${s}`,children:[e.jsxs(x,{variant:"outline",onClick:t,className:"text-orange-600 border-orange-600 hover:bg-orange-50",children:[e.jsx(M,{className:"h-4 w-4 mr-2"}),"Đồng bộ combo"]}),e.jsxs(x,{variant:"outline",onClick:a,children:[e.jsx(U,{className:"h-4 w-4 mr-2"}),"Xem trước"]})]})}function Ce({filters:t,onFiltersChange:a}){const s=n=>{a({search:n})},l=n=>{a({status:n})},o=n=>{a({canTakeaway:n})},m=n=>{a({canDelivery:n})},d=()=>{console.log("Applying filters:",t)},u=()=>{},g=()=>{},N=()=>0;return e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-[220px]",children:e.jsx(B,{placeholder:"TÊN/MÃ COMBO",value:t.search,onChange:n=>s(n.target.value),className:"h-10"})}),e.jsx("div",{className:"w-[160px]",children:e.jsxs(j,{value:t.status,onValueChange:l,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"TRẠNG THÁI"})}),e.jsxs(b,{children:[e.jsx(i,{value:"available",children:"Có bán"}),e.jsx(i,{value:"unavailable",children:"Hết hàng"})]})]})}),e.jsx("div",{className:"w-[160px]",children:e.jsxs(j,{value:t.canTakeaway,onValueChange:o,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"BÁN TẠI CHỖ"})}),e.jsxs(b,{children:[e.jsx(i,{value:"true",children:"Có"}),e.jsx(i,{value:"false",children:"Không"})]})]})}),e.jsx("div",{className:"w-[160px]",children:e.jsxs(j,{value:t.canDelivery,onValueChange:m,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"BÁN MANG ĐI"})}),e.jsxs(b,{children:[e.jsx(i,{value:"true",children:"Có"}),e.jsx(i,{value:"false",children:"Không"})]})]})}),e.jsx("div",{className:"ml-2",children:e.jsx(x,{onClick:d,className:"h-10 px-6 bg-blue-600 text-white hover:bg-blue-700",children:"Lọc"})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm font-medium",children:["SỐ LƯỢNG: ",N()]}),e.jsx("button",{className:"text-blue-600 text-sm hover:underline",children:"Sắp xếp thứ tự hiển thị"})]})}),e.jsx(we,{onSyncItems:g,onViewPreview:u})]})]})}function Se({onSyncItems:t,onViewPreview:a,className:s=""}){return e.jsxs("div",{className:`flex flex-wrap items-center gap-2 ${s}`,children:[e.jsxs(x,{variant:"outline",onClick:t,className:"text-orange-600 border-orange-600 hover:bg-orange-50",children:[e.jsx(M,{className:"h-4 w-4 mr-2"}),"Đồng bộ combo tùy chỉnh"]}),e.jsxs(x,{variant:"outline",onClick:a,children:[e.jsx(U,{className:"h-4 w-4 mr-2"}),"Xem trước"]})]})}function Te({filters:t,onFiltersChange:a}){const s=n=>{a({search:n})},l=n=>{a({status:n})},o=n=>{a({canTakeaway:n})},m=n=>{a({canDelivery:n})},d=()=>{console.log("Applying filters:",t)},u=()=>{console.log("Xem trước")},g=()=>{console.log("Đồng bộ combo tùy chỉnh")},N=()=>0;return e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-[220px]",children:e.jsx(B,{placeholder:"TÊN/MÃ COMBO",value:t.search,onChange:n=>s(n.target.value),className:"h-10"})}),e.jsx("div",{className:"w-[160px]",children:e.jsxs(j,{value:t.status,onValueChange:l,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"TRẠNG THÁI"})}),e.jsxs(b,{children:[e.jsx(i,{value:"available",children:"Có bán"}),e.jsx(i,{value:"unavailable",children:"Hết hàng"})]})]})}),e.jsx("div",{className:"w-[160px]",children:e.jsxs(j,{value:t.canTakeaway,onValueChange:o,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"BÁN TẠI CHỖ"})}),e.jsxs(b,{children:[e.jsx(i,{value:"true",children:"Có"}),e.jsx(i,{value:"false",children:"Không"})]})]})}),e.jsx("div",{className:"w-[160px]",children:e.jsxs(j,{value:t.canDelivery,onValueChange:m,children:[e.jsx(p,{className:"h-10",children:e.jsx(v,{placeholder:"BÁN MANG ĐI"})}),e.jsxs(b,{children:[e.jsx(i,{value:"true",children:"Có"}),e.jsx(i,{value:"false",children:"Không"})]})]})}),e.jsx("div",{className:"ml-2",children:e.jsx(x,{onClick:d,className:"h-10 px-6 bg-blue-600 text-white hover:bg-blue-700",children:"Lọc"})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm font-medium",children:["SỐ LƯỢNG: ",N()]}),e.jsx("button",{className:"text-blue-600 text-sm hover:underline",children:"Sắp xếp thứ tự hiển thị"})]})}),e.jsx(Se,{onSyncItems:g,onViewPreview:u})]})]})}const qe=({className:t})=>{const a=X(),s=q(),l=E({strict:!1}),m=(()=>{const S=s.pathname;return S.includes("/item-type")?"item-type":S.includes("/combo-special")?"combo-special":S.includes("/combo")?"combo":(S.includes("/items"),"items")})(),d=l.pos,{items:u,groups:g,combos:N,filters:n,updateFilters:f,isLoading:w}=ue(d),h=S=>{const z=S,k=d?{pos:String(d)}:{};switch(z){case"item-type":a({to:"/crm/general-setups/item-type",search:k});break;case"items":a({to:"/crm/general-setups/items",search:k});break;case"combo":a({to:"/crm/general-setups/combo",search:k});break;case"combo-special":a({to:"/crm/general-setups/combo-special",search:k});break}},r=()=>{},R=()=>{},V=()=>{switch(m){case"item-type":return g;case"items":return u;case"combo":case"combo-special":return N;default:return u}};return w?e.jsx("div",{className:`container mx-auto p-6 ${t}`,children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):e.jsxs("div",{className:`container mx-auto p-6 ${t}`,children:[e.jsx("div",{className:"mb-6",children:e.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Quản lý thực đơn"})}),e.jsx("div",{className:"mb-6",children:e.jsxs(Q,{value:m,onValueChange:h,className:"w-full",children:[e.jsxs("div",{className:"hidden lg:flex items-start justify-between gap-4",children:[e.jsxs(P,{className:"flex w-auto",children:[e.jsx(C,{value:"item-type",className:"whitespace-nowrap px-4 py-2 text-sm flex-1 text-center",children:"Danh sách nhóm"}),e.jsx(C,{value:"items",className:"whitespace-nowrap px-4 py-2 text-sm flex-1 text-center",children:"Danh sách món"}),e.jsx(C,{value:"combo",className:"whitespace-nowrap px-4 py-2 text-sm flex-1 text-center",children:"Danh sách combo"}),e.jsx(C,{value:"combo-special",className:"whitespace-nowrap px-4 py-2 text-sm flex-1 text-center",children:"Danh sách combo tùy chỉnh"})]}),e.jsx(O,{onSyncMenu:r,onDeleteMenu:R,variant:"desktop"})]}),e.jsx("div",{className:"lg:hidden space-y-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row md:items-start md:justify-between gap-4",children:[e.jsxs(P,{className:"flex flex-wrap md:flex-nowrap w-full md:w-auto",children:[e.jsx(C,{value:"item-type",className:"text-xs sm:text-sm px-2 py-2 flex-1 text-center min-w-0",children:"Danh sách nhóm"}),e.jsx(C,{value:"items",className:"text-xs sm:text-sm px-2 py-2 flex-1 text-center min-w-0",children:"Danh sách món"}),e.jsx(C,{value:"combo",className:"text-xs sm:text-sm px-2 py-2 flex-1 text-center min-w-0",children:"Danh sách combo"}),e.jsx(C,{value:"combo-special",className:"text-xs sm:text-sm px-2 py-2 flex-1 text-center min-w-0",children:"Combo tùy chỉnh"})]}),e.jsx(O,{onSyncMenu:r,onDeleteMenu:R,variant:"mobile"})]})}),e.jsxs(D,{value:"item-type",className:"mt-6",children:[e.jsx(Ne,{filters:n,onFiltersChange:f}),e.jsx(K,{data:V(),type:"item-type"})]}),e.jsxs(D,{value:"items",className:"mt-6",children:[e.jsx(ye,{filters:n,onFiltersChange:f}),e.jsx(K,{data:V(),type:"items"})]}),e.jsxs(D,{value:"combo",className:"mt-6",children:[e.jsx(Ce,{filters:n,onFiltersChange:f}),e.jsx(K,{data:V(),type:"combo"})]}),e.jsxs(D,{value:"combo-special",className:"mt-6",children:[e.jsx(Te,{filters:n,onFiltersChange:f}),e.jsx(K,{data:V(),type:"combo-special"})]})]})})]})};export{qe as M};
