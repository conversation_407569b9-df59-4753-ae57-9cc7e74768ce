import{a3 as ae,r as u,j as e,B as D,T as ye,o as U,p as J,q as Z}from"./index-CfbMU4Ye.js";import{M as fe}from"./main-B69tr6A0.js";import{C as ge,a as ve,d as _e}from"./card-Dq-aHO9v.js";import"./date-range-picker-FRR8J6T3.js";import{L as f}from"./form-DPp_Bp7A.js";import{c as P}from"./crm-api-8UaIokQG.js";import"./pos-api-BBB_ZiZD.js";import{u as je}from"./useQuery-BvDWg4vp.js";import{C as V}from"./query-keys-DQo7uRnN.js";import{u as se}from"./use-pos-company-data-DHh5_mMz.js";import{c as q,u as ne}from"./use-membership-type-D9suemET.js";import"./user-3BSjwAvJ.js";import{u as re}from"./useMutation-C9PewMvL.js";import{I as Ne}from"./IconEdit-DQ9kYlHR.js";import{I as k}from"./input-D8TU6hMD.js";import{S as be,a as we,b as Se,c as Te,d as H}from"./select-_nXsh5SU.js";import{C as Ee}from"./checkbox-CSFn543p.js";import{R as Ce,a as K}from"./radio-group-KASmsiJo.js";import{S as De}from"./switch-DgMQ7bQC.js";import{C as ee}from"./circle-help-a_rpWlnp.js";import{I as Ie}from"./IconX-D1iK2feM.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./utils-km2FGkQ4.js";import"./createReactComponent-CVG1We1Z.js";import"./index-D41EikqA.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./index-4DjKSQeL.js";const Q={getList:async i=>(await P.get("/loyalty/get-list-extra-point",{params:i})).data,create:async(i,r)=>(await P.post("/loyalty/create-extra-point",i,{params:r})).data,update:async(i,r)=>(await P.post("/loyalty/update-extra-point",i,{params:r})).data},ke=()=>{var d;const i=se(),r=q(),a=i&&r?{company_id:i.company_id,pos_parent:((d=r[0])==null?void 0:d.brand_id)||""}:{company_id:"",pos_parent:""};return je({queryKey:["crm",V.EXTRA_POINT,a],queryFn:()=>Q.getList(a),enabled:!!a.company_id&&!!a.pos_parent})},ie=()=>{const i=ae(),r=q();return re({mutationFn:async a=>{var p;const d={pos_parent:((p=r==null?void 0:r[0])==null?void 0:p.brand_id)||""};return Q.update(a,d)},onSuccess:()=>{i.invalidateQueries({queryKey:["crm",V.EXTRA_POINT]})},onError:a=>{console.error("Error updating extra point:",a)}})};function Me(){const i=ae();return re({mutationFn:async({data:r,params:a})=>await Q.create(r,a),onSuccess:()=>{i.invalidateQueries({queryKey:["crm",V.EXTRA_POINT]})}})}const le=u.createContext(void 0);function Ae({children:i}){var l;const r=ke(),a=ne(),d=ie(),[p,j]=u.useState(!1),[N,S]=u.useState(!1),[c,h]=u.useState(null),x=u.useMemo(()=>{var g,b;const s=((g=r.data)==null?void 0:g.list_membership_type_extra_rate)||[],m=((b=a.data)==null?void 0:b.list_membership_type)||[];return s.map(w=>{const _=m.find(C=>C.type_id===w.type_id),M=((_==null?void 0:_.point_rate)||0)*w.extra_rate*100;return{...w,calculatedRate:M,membershipTypeName:(_==null?void 0:_.type_name)||w.type_name||"N/A"}})},[r.data,a.data]),y=(s,m)=>{var _;const b=(((_=r.data)==null?void 0:_.list_membership_type_extra_rate)||[]).find(E=>E.id===s);if(!b){console.error("Extra point item not found:",s);return}const w={...b,active:m===1?0:1,updated_at:new Date().toISOString().replace("T"," ").slice(0,19),class:"membership-feature",title:m===1?"Đã hủy":"Đang hoạt động",action:m===1?"Kích hoạt":"Hủy"};d.mutate(w)},t={data:x,membershipTypes:((l=a.data)==null?void 0:l.list_membership_type)||[],isLoading:r.isLoading||a.isLoading,error:r.error||a.error,refetch:()=>{r.refetch(),a.refetch()},toggleActivation:y,createDialogOpen:p,setCreateDialogOpen:j,editDialogOpen:N,setEditDialogOpen:S,selectedExtraPoint:c,setSelectedExtraPoint:h};return e.jsx(le.Provider,{value:t,children:i})}function ce(){const i=u.useContext(le);if(i===void 0)throw new Error("useExtraPointContext must be used within an ExtraPointProvider");return i}function Re(){const{data:i,isLoading:r,toggleActivation:a,setEditDialogOpen:d,setSelectedExtraPoint:p}=ce(),j=t=>[{value:2,name:"Chủ nhật"},{value:4,name:"Thứ 2"},{value:8,name:"Thứ 3"},{value:16,name:"Thứ 4"},{value:32,name:"Thứ 5"},{value:64,name:"Thứ 6"},{value:128,name:"Thứ 7"}].filter(s=>(t&s.value)===s.value).map(s=>s.name),N=t=>{const l=[];for(let s=0;s<24;s++)if((t&Math.pow(2,s))===Math.pow(2,s)){const m=s+1;l.push(m===24?`${s}h-24h`:`${s}-${m}h`)}return l},S=t=>{if(t.extra_rate_type==="SPECIAL_TIME"){if(t.time_hour_day===16777215&&t.time_date_week===254)return"Tất cả khung giờ";const l=j(t.time_date_week),s=N(t.time_hour_day);if(l.length===0&&s.length===0)return"Chưa chọn khung thời gian";const m=l.length>0?`Ngày trong tuần: ${l.join(", ")}`:"",g=s.length>0?`Khung giờ: ${s.join(", ")}`:"";return[m,g].filter(Boolean).join(`
`)}if(t.extra_rate_type==="BIRTHDAY"){const l=t.day_before_birthday||0,s=t.day_after_birthday||0;return l===0&&s===0?"Sinh nhật: đúng ngày sinh nhật":`Sinh nhật: từ trước sinh nhật ${l} ngày, đến sau sinh nhật ${s} ngày`}return""},c=(t,l)=>{const s=new Date(t).toLocaleDateString("vi-VN"),m=new Date(l).toLocaleDateString("vi-VN");return`${s} - ${m}`},h=t=>`${t.toLocaleString("vi-VN",{minimumFractionDigits:3,maximumFractionDigits:3})}%`,x=(t,l,s)=>{if(s===0)return{text:"Đã hủy",className:"bg-red-100 text-red-800"};const m=new Date,g=new Date(t),b=new Date(l);return m<g?{text:"Sắp đến",className:"bg-yellow-100 text-yellow-800"}:m>=g&&m<=b?{text:"Đang hoạt động",className:"bg-green-100 text-green-800"}:{text:"Hết hạn",className:"bg-gray-100 text-gray-800"}},y=t=>{p(t),d(!0)};return r?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-muted-foreground",children:"Đang tải..."})}):e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs("table",{className:"w-full border-collapse",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Khoảng thời gian"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Hạng thành viên"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Thay đổi tỷ lệ tích điểm"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Trạng thái"}),e.jsx("th",{className:"w-16"})]})}),e.jsx("tbody",{children:i.map(t=>e.jsxs("tr",{className:"hover:bg-muted/50 border-b",children:[e.jsx("td",{className:"px-4 py-3",children:c(t.start_date,t.end_date)}),e.jsx("td",{className:"px-4 py-3",children:t.membershipTypeName}),e.jsx("td",{className:"px-4 py-3",children:e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"font-medium",children:h(t.calculatedRate)}),e.jsx("div",{className:"text-xs whitespace-pre-line text-gray-600",children:S(t)})]})}),e.jsx("td",{className:"px-4 py-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[(()=>{const l=x(t.start_date,t.end_date,t.active);return e.jsx("span",{className:`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${l.className}`,children:l.text})})(),(()=>{const s=x(t.start_date,t.end_date,t.active).text==="Hết hạn";return e.jsx("button",{onClick:()=>{s?y(t):a(t.id,t.active)},className:"cursor-pointer text-xs text-blue-600 hover:text-blue-800 hover:underline",children:s?"Gia hạn":t.active===1?"Hủy":"Kích hoạt"})})()]})}),e.jsx("td",{className:"px-4 py-3",children:e.jsx(D,{variant:"ghost",size:"sm",onClick:()=>y(t),className:"h-8 w-8 p-0",children:e.jsx(Ne,{className:"h-4 w-4"})})})]},t.id))})]}),i.length===0&&e.jsx("div",{className:"text-muted-foreground py-8 text-center",children:"Không có dữ liệu"})]})}const Fe=[{label:"CN",value:"6"},{label:"T2",value:"0"},{label:"T3",value:"1"},{label:"T4",value:"2"},{label:"T5",value:"3"},{label:"T6",value:"4"},{label:"T7",value:"5"}],Le=[{value:"0"},{value:"1"},{value:"2"},{value:"3"},{value:"4"},{value:"5"},{value:"6"},{value:"7"},{value:"8"},{value:"9"},{value:"10"},{value:"11"},{value:"12"},{value:"13"},{value:"14"},{value:"15"},{value:"16"},{value:"17"},{value:"18"},{value:"19"},{value:"20"},{value:"21"},{value:"22"},{value:"23"}];function Oe({marketingDays:i=[],marketingHours:r=[],onDaysChange:a,onHoursChange:d,onTimeValuesChange:p}){const j=(c,h)=>{const x={6:2,0:4,1:8,2:16,3:32,4:64,5:128},y=c.reduce((l,s)=>l+(x[s]||0),0),t=h.reduce((l,s)=>{const m=parseInt(s);return l+Math.pow(2,m)},0);return{time_date_week:y,time_hour_day:t}},N=c=>{const h=i.includes(c)?i.filter(y=>y!==c):[...i,c];a==null||a(h);const x=j(h,r);p==null||p(x)},S=c=>{const h=r.includes(c)?r.filter(y=>y!==c):[...r,c];d==null||d(h);const x=j(i,h);p==null||p(x)};return e.jsx(ye,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(f,{className:"text-sm font-medium text-gray-500",children:"Chọn ngày"}),e.jsxs(U,{children:[e.jsx(J,{asChild:!0,children:e.jsx(ee,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(Z,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần"})})]})]}),e.jsx("div",{className:"flex gap-2",children:Fe.map(c=>e.jsx(D,{type:"button",variant:i.includes(c.value)?"default":"outline",size:"sm",onClick:()=>N(c.value),className:"flex-1",children:c.label},c.value))})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(f,{className:"text-sm font-medium text-gray-500",children:"Chọn giờ"}),e.jsxs(U,{children:[e.jsx(J,{asChild:!0,children:e.jsx(ee,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(Z,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày"})})]})]}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:Le.map(c=>{const h=parseInt(c.value),x=h+1,y=x===24?`${h}h-24h`:`${h}-${x}h`;return e.jsx(D,{type:"button",variant:r.includes(c.value)?"default":"outline",size:"sm",onClick:()=>S(c.value),className:"text-xs",children:y},c.value)})})]})]})})}function te({open:i,onOpenChange:r,extraPoint:a}){var W;const d=!!a,[p,j]=u.useState(!1),[N,S]=u.useState(""),[c,h]=u.useState(()=>new Date().toISOString().split("T")[0]),[x,y]=u.useState(""),[t,l]=u.useState("all"),[s,m]=u.useState(!0),[g,b]=u.useState(""),[w,_]=u.useState(""),[E,M]=u.useState(""),[C,F]=u.useState(!1),[Y,L]=u.useState([]),[z,O]=u.useState([]),[A,$]=u.useState({time_date_week:0,time_hour_day:0}),X=ne(),B=((W=X.data)==null?void 0:W.list_membership_type)||[],oe=Me(),de=ie(),I=se(),R=q(),T=B.find(n=>n.type_id===N),G=(T==null?void 0:T.point_rate)||0,me=()=>{const n=parseFloat(g)||0;return(G*n*100).toFixed(3)},ue=n=>[{value:2,dayValue:"6"},{value:4,dayValue:"0"},{value:8,dayValue:"1"},{value:16,dayValue:"2"},{value:32,dayValue:"3"},{value:64,dayValue:"4"},{value:128,dayValue:"5"}].filter(v=>(n&v.value)===v.value).map(v=>v.dayValue),he=n=>{const o=[];for(let v=0;v<24;v++)(n&Math.pow(2,v))===Math.pow(2,v)&&o.push(v.toString());return o};u.useEffect(()=>{if(d&&a)if(S(a.type_id),h(a.start_date.split(" ")[0]),y(a.end_date.split(" ")[0]),b(a.extra_rate.toString()),m(a.active===1),a.extra_rate_type==="SPECIAL_TIME")if(a.time_hour_day===16777215&&a.time_date_week===254)l("all");else{l("timeframe");const n=ue(a.time_date_week),o=he(a.time_hour_day);L(n),O(o),$({time_date_week:a.time_date_week,time_hour_day:a.time_hour_day})}else if(a.extra_rate_type==="BIRTHDAY"){l("birthday");const n=a.day_before_birthday||0,o=a.day_after_birthday||0;_(n.toString()),M(o.toString()),F(n===0&&o===0)}else l("all");else S(""),h(new Date().toISOString().split("T")[0]),y(""),l("all"),m(!0),b("")},[d,a,i]),u.useEffect(()=>{if(t==="timeframe"&&Y.length===0&&z.length===0){const n=["6","0","1","2","3","4","5"],o=Array.from({length:24},(v,xe)=>xe.toString());L(n),O(o),$({time_date_week:254,time_hour_day:16777215})}},[t]);const pe=async()=>{var n;if(!(I!=null&&I.company_id)||!N||!c||!x||!g||!((n=R==null?void 0:R[0])!=null&&n.brand_id)){console.error("Missing required fields");return}j(!0);try{if(d&&a){const o={id:a.id,company_id:I.company_id,type_id:N,type_name:(T==null?void 0:T.type_name)||"",start_date:`${c} 00:00:00`,end_date:`${x} 23:59:59`,extra_rate:parseFloat(g),active:s?1:0,extra_rate_type:t==="all"||t==="timeframe"?"SPECIAL_TIME":"BIRTHDAY",created_at:a.created_at,...t==="all"||t==="timeframe"?{time_date_week:t==="all"?254:A.time_date_week,time_hour_day:t==="all"?16777215:A.time_hour_day}:{},...t==="birthday"&&!C&&{day_before_birthday:parseInt(w)||0,day_after_birthday:parseInt(E)||0}};await de.mutateAsync(o)}else{const o={company_id:I.company_id,type_id:N,type_name:(T==null?void 0:T.type_name)||"",start_date:`${c} 00:00:00`,end_date:`${x} 23:59:59`,extra_rate:parseFloat(g),active:s?1:0,extra_rate_type:t==="all"||t==="timeframe"?"SPECIAL_TIME":"BIRTHDAY",time_date_week:t==="all"?254:t==="timeframe"?A.time_date_week:0,time_hour_day:t==="all"?16777215:t==="timeframe"?A.time_hour_day:0,...t==="birthday"&&!C&&{day_before_birthday:parseInt(w)||0,day_after_birthday:parseInt(E)||0}},v={pos_parent:R[0].brand_id};await oe.mutateAsync({data:o,params:v})}r(!1)}catch(o){console.error("Error submitting extra point:",o)}finally{j(!1)}};return i?e.jsxs("div",{className:"fixed inset-0 z-50 flex",children:[e.jsx("div",{className:"fixed inset-0 bg-black/50 transition-opacity duration-300",onClick:()=>r(!1)}),e.jsxs("div",{className:`fixed top-0 right-0 h-full w-full max-w-2xl transform bg-white shadow-xl transition-transform duration-300 ease-in-out ${i?"translate-x-0":"translate-x-full"} `,children:[e.jsxs("div",{className:"flex items-center justify-between border-b px-6 py-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-xl font-semibold",children:d?"Chỉnh sửa hệ số tích điểm":"Tạo hệ số tích điểm"})}),e.jsx(D,{variant:"ghost",size:"sm",onClick:()=>r(!1),className:"h-8 w-8 p-0",children:e.jsx(Ie,{className:"h-4 w-4"})})]}),e.jsx("div",{className:"border-b bg-blue-50 px-6 py-4",children:e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("strong",{children:"Lưu ý:"})," Nếu bạn tạo nhiều hệ số gia tăng tỷ lệ tích điểm khác nhau cùng áp dụng trong một thời điểm, hệ thống sẽ ưu tiên chính sách áp dụng cho sinh nhật thành viên, sau đó đến ngày trong tuần và giờ trong ngày; và ưu tiên hệ số tạo gần nhất."]})}),e.jsx("div",{className:"flex-1 overflow-y-auto px-6 py-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(f,{htmlFor:"membership-type",className:"text-sm font-medium",children:["Hạng thành viên ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(be,{value:N,onValueChange:S,children:[e.jsx(we,{className:"w-full",children:e.jsx(Se,{placeholder:"Chọn hạng thành viên"})}),e.jsx(Te,{children:X.isLoading?e.jsx(H,{value:"loading",disabled:!0,children:"Đang tải..."}):B.length>0?B.map(n=>e.jsx(H,{value:n.type_id||"",children:n.type_name},n.id)):e.jsx(H,{value:"no-data",disabled:!0,children:"Không có dữ liệu"})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(f,{className:"text-sm font-medium",children:["Khoảng thời gian áp dụng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(f,{htmlFor:"start-date",className:"min-w-[40px] text-sm font-medium",children:"Từ"}),e.jsx(k,{id:"start-date",type:"date",value:c,onChange:n=>h(n.target.value),min:d?void 0:new Date().toISOString().split("T")[0],className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(f,{htmlFor:"end-date",className:"min-w-[40px] text-sm font-medium",children:"Đến"}),e.jsx(k,{id:"end-date",type:"date",value:x,onChange:n=>y(n.target.value),min:c||(d?void 0:new Date().toISOString().split("T")[0]),className:"flex-1"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(f,{className:"text-sm font-medium",children:"Thay đổi thời gian tích điểm"}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsxs("span",{children:[(G*100).toFixed(1),"%"]}),e.jsx("span",{children:"x"}),e.jsx(k,{type:"number",value:g,onChange:n=>{const o=n.target.value;(o===""||/^\d*\.?\d*$/.test(o))&&b(o)},onKeyDown:n=>{!/[\d.]/.test(n.key)&&!["Backspace","Delete","Tab","Enter","ArrowLeft","ArrowRight"].includes(n.key)&&n.preventDefault()},placeholder:"0",className:"w-20 text-center",min:"0",step:"0.1",pattern:"[0-9]*\\.?[0-9]*"}),e.jsx("span",{children:"="}),e.jsxs("span",{className:"font-medium text-green-600",children:[me(),"%"]})]})]}),e.jsx("div",{className:"space-y-2",children:e.jsxs(Ce,{value:t,onValueChange:l,className:"flex flex-row space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(K,{value:"all",id:"all"}),e.jsx(f,{htmlFor:"all",className:"cursor-pointer text-sm font-medium",children:"Tất cả khung giờ"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(K,{value:"timeframe",id:"timeframe"}),e.jsx(f,{htmlFor:"timeframe",className:"cursor-pointer text-sm font-medium",children:"Khung thời gian"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(K,{value:"birthday",id:"birthday"}),e.jsx(f,{htmlFor:"birthday",className:"cursor-pointer text-sm font-medium",children:"Ngày sinh nhật"})]})]})}),t==="timeframe"&&e.jsx(Oe,{marketingDays:Y,marketingHours:z,onDaysChange:L,onHoursChange:O,onTimeValuesChange:$}),t==="birthday"&&e.jsxs("div",{className:"space-y-3",children:[e.jsx(f,{className:"text-sm font-medium",children:"Áp dụng cho sinh nhật"}),!C&&e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(f,{className:"text-sm font-medium whitespace-nowrap",children:"Từ trước sinh nhật"}),e.jsx(k,{type:"number",value:w,onChange:n=>{const o=n.target.value;(o===""||/^\d+$/.test(o))&&_(o)},placeholder:"0",className:"w-16 text-center",min:"0"}),e.jsx("span",{className:"text-sm",children:"ngày"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(f,{className:"text-sm font-medium whitespace-nowrap",children:"Đến sau sinh nhật"}),e.jsx(k,{type:"number",value:E,onChange:n=>{const o=n.target.value;(o===""||/^\d+$/.test(o))&&M(o)},placeholder:"0",className:"w-16 text-center",min:"0"}),e.jsx("span",{className:"text-sm",children:"ngày"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Ee,{id:"only-birthday-day",checked:C,onCheckedChange:n=>F(n===!0)}),e.jsx(f,{htmlFor:"only-birthday-day",className:"cursor-pointer text-sm font-medium",children:"Chỉ áp dụng trong ngày sinh nhật"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(f,{htmlFor:"status",className:"text-sm font-medium",children:"Trạng thái"}),e.jsx(De,{id:"status",checked:s,onCheckedChange:m})]})]})}),e.jsx("div",{className:"border-t px-6 py-4",children:e.jsx("div",{className:"flex justify-end",children:e.jsx(D,{onClick:pe,disabled:p,children:p?d?"Đang cập nhật...":"Đang tạo...":d?"Cập nhật":"Tạo mới"})})})]})]}):null}function $e(){const{setCreateDialogOpen:i,createDialogOpen:r,editDialogOpen:a,setEditDialogOpen:d,selectedExtraPoint:p}=ce(),j=()=>{i(!0)};return e.jsx(fe,{children:e.jsxs("div",{className:"container mx-auto space-y-6 py-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Hệ số tích điểm"})}),e.jsxs(ge,{children:[e.jsx(ve,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(D,{onClick:j,children:"Tạo mới"})}),e.jsx(_e,{children:e.jsx(Re,{})})]}),e.jsx(te,{open:r,onOpenChange:i}),e.jsx(te,{open:a,onOpenChange:d,extraPoint:p||void 0})]})})}function Be(){return e.jsx(Ae,{children:e.jsx($e,{})})}const _t=Be;export{_t as component};
