import{h as I,r as g,j as e,B as d,aX as b}from"./index-CVQ6JZo2.js";import"./pos-api-mRg02iop.js";import"./vietqr-api-_ZZrmuU0.js";import"./user-BJzEhOTa.js";import"./crm-api-CDzLLTww.js";import"./header-DiKooCuw.js";import"./main-BD6dUgw2.js";import"./search-context-CkCLuJFL.js";import"./date-range-picker-CXbMaowj.js";import{L as r}from"./form-CzmGigtT.js";import{c as R,d as U}from"./use-payment-methods-DXMDdGIc.js";import{C as G}from"./checkbox-BfLSzhzg.js";import{I as h}from"./input-Al6WtUZF.js";import{S as E,a as V,b as z,c as A,d as w}from"./select-BFhNE0YE.js";import{S as X}from"./store-selection-modal-Bc3wwIPT.js";import{X as H}from"./calendar-BszTCdZH.js";import"./separator-BcoNozmD.js";import"./command-Nb4B17YQ.js";import"./dialog-DDrduXt3.js";import"./search-B4Rlb4i6.js";import"./createLucideIcon-DKVxsQv7.js";import"./createReactComponent-DSXPaZ4c.js";import"./scroll-area-CGsZUbT-.js";import"./index-LVHINuqD.js";import"./IconChevronRight-LXWXuzjR.js";import"./chevron-right-CxgpqvrH.js";import"./react-icons.esm-DMMA_g0o.js";import"./popover-DnoSPJNX.js";import"./index-CtK-wKtB.js";import"./isSameMonth-C8JQo-AN.js";import"./useQuery-HgcIHxlE.js";import"./utils-km2FGkQ4.js";import"./useMutation-ZsyDznMu.js";import"./payment-methods-api-CVNkcw7x.js";import"./query-keys-3lmd-xp6.js";import"./index-nc1u7392.js";import"./check-BE_j5GZD.js";import"./collapsible-CRaCKnru.js";function J({paymentMethodId:p,companyUid:x,brandUid:u,paymentMethodCode:i}){var y;const l=I(),{data:c,isLoading:j,error:C}=R(i),{updatePaymentMethod:_,isUpdating:f}=U();console.log("PaymentMethodDetailForm props:",{paymentMethodId:p,companyUid:x,brandUid:u,paymentMethodCode:i}),console.log("API Response:",{paymentMethodDetail:c,isLoading:j,error:C});const[t,o]=g.useState({name:"",code:"",autoGenerateCode:!0,cardProcessingFee:"0",feeBearer:"customer",requireTransactionCode:!1,selectedStores:[],logoFile:null,logoPreview:""}),[P,v]=g.useState(!1);g.useEffect(()=>{var s,n;if(c){const a=c;o({name:a.payment_method_name||"",code:a.payment_method_id||"",autoGenerateCode:!1,cardProcessingFee:((a.payment_fee_extra||0)*100).toString(),feeBearer:a.payment_fee_type===0?"customer":"restaurant",requireTransactionCode:((s=a.extra_data)==null?void 0:s.require_traceno)===1,selectedStores:((n=a.stores)==null?void 0:n.map(m=>m.id))||[],logoFile:null,logoPreview:a.image_path||""})}},[c]);const F=()=>{l({to:"/setting/payment-method"})},N=t.name.trim()!==""&&t.selectedStores.length>0,k=async()=>{if(!N)return;const s={id:p,payment_method_name:t.name,payment_method_id:t.code,payment_fee_extra:parseFloat(t.cardProcessingFee)/100,payment_fee_type:t.feeBearer==="customer"?0:1,stores:t.selectedStores,extra_data:t.requireTransactionCode?{require_traceno:1}:{require_traceno:0},logoFile:t.logoFile};_(s,{onSuccess:()=>{l({to:"/setting/payment-method"})}})},M=()=>{v(!0)},L=s=>{o({...t,selectedStores:s})},T=s=>{var a;const n=(a=s.target.files)==null?void 0:a[0];if(n){const m=new FileReader;m.onload=q=>{var S;const D=(S=q.target)==null?void 0:S.result;o({...t,logoFile:n,logoPreview:D})},m.readAsDataURL(n)}},B=()=>{o({...t,logoFile:null,logoPreview:""})};return j?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex h-64 items-center justify-center",children:e.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[e.jsx(d,{variant:"ghost",size:"sm",onClick:F,className:"flex items-center gap-2 text-gray-600 hover:text-gray-900",children:e.jsx(H,{className:"h-4 w-4"})}),e.jsx("h1",{className:"text-2xl font-bold",children:"Chi tiết phương thức thanh toán"}),e.jsx(d,{type:"button",disabled:f||!N,className:"min-w-[100px]",onClick:k,children:f?"Đang cập nhật...":"Lưu"})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"rounded-lg border bg-white p-6 shadow-sm",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin chi tiết"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(r,{htmlFor:"payment-method-name",className:"min-w-[200px] text-sm font-medium",children:"Tên phương thức *"}),e.jsx(h,{id:"payment-method-name",value:t.name,onChange:s=>o({...t,name:s.target.value}),placeholder:"Nhập tên phương thức thanh toán",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(r,{className:"min-w-[200px] text-sm font-medium",children:"Cửa hàng áp dụng *"}),e.jsx(d,{type:"button",variant:"outline",onClick:M,className:"flex-1 justify-start",children:t.selectedStores.length>0?`${t.selectedStores.length} điểm`:"0 điểm"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(r,{htmlFor:"payment-method-code",className:"min-w-[200px] text-sm font-medium",children:"Mã PTTT"}),e.jsx(h,{id:"payment-method-code",value:t.code,onChange:s=>o({...t,code:s.target.value}),placeholder:"Mã phương thức thanh toán",disabled:!0,className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(r,{htmlFor:"card-processing-fee",className:"min-w-[200px] text-sm font-medium",children:"Phí cà thẻ"}),e.jsxs("div",{className:"relative flex-1",children:[e.jsx(h,{id:"card-processing-fee",type:"number",min:"0",max:"100",step:"0.1",value:t.cardProcessingFee,onChange:s=>o({...t,cardProcessingFee:s.target.value}),className:"pr-8"}),e.jsx("span",{className:"absolute top-1/2 right-3 -translate-y-1/2 text-sm text-gray-500",children:"%"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(r,{className:"min-w-[200px] text-sm font-medium",children:"Bên chịu phí cà thẻ"}),e.jsxs(E,{value:t.feeBearer,onValueChange:s=>o({...t,feeBearer:s}),children:[e.jsx(V,{className:"flex-1",children:e.jsx(z,{})}),e.jsxs(A,{children:[e.jsx(w,{value:"customer",children:"Khách hàng"}),e.jsx(w,{value:"restaurant",children:"Nhà hàng"})]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(r,{className:"min-w-[200px] text-sm font-medium",children:"Yêu cầu nhập mã giao dịch khi thanh toán"}),e.jsx("div",{className:"flex-1",children:e.jsx(G,{id:"require-transaction-code",checked:t.requireTransactionCode,onCheckedChange:s=>o({...t,requireTransactionCode:s})})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Logo"}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(r,{className:"min-w-[200px] pt-2 text-sm font-medium",children:"Logo tải lên"}),e.jsx("div",{className:"flex-1",children:t.logoPreview?e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"relative inline-block",children:[e.jsx("img",{src:t.logoPreview,alt:"Logo preview",className:"h-24 w-24 rounded-md border object-cover"}),e.jsx(d,{type:"button",variant:"destructive",size:"sm",className:"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0",onClick:B,children:"×"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",children:(y=t.logoFile)==null?void 0:y.name})]}):e.jsx("div",{className:"rounded-md border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"mx-auto h-12 w-12 text-gray-400",children:e.jsx("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),e.jsx("div",{children:e.jsxs("label",{htmlFor:"payment-method-logo",className:"cursor-pointer",children:[e.jsx("span",{className:"text-sm font-medium text-blue-600 hover:text-blue-500",children:"Tải ảnh lên"}),e.jsx(h,{id:"payment-method-logo",type:"file",accept:"image/*",onChange:T,className:"hidden"})]})}),e.jsx("p",{className:"text-xs text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})})})]})]})]})]})})}),e.jsx(X,{open:P,onOpenChange:v,selectedStoreIds:t.selectedStores,onStoreSelectionChange:L})]})}const Le=function(){const{paymentMethodId:x}=b.useParams(),{company_uid:u,brand_uid:i,payment_method_id:l}=b.useSearch();return e.jsx(J,{paymentMethodId:x,companyUid:u,brandUid:i,paymentMethodCode:l})};export{Le as component};
