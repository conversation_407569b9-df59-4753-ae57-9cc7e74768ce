import{a as n}from"./pos-api-mRg02iop.js";const s={getPaymentMethods:async t=>{const a=new URLSearchParams({company_uid:t.company_uid,brand_uid:t.brand_uid});return t.page!==void 0&&a.set("page",t.page.toString()),t.limit!==void 0&&a.set("limit",t.limit.toString()),t.store_uid&&t.store_uid!=="all"&&a.set("store_uid",t.store_uid),t.skip_limit!==void 0&&a.set("skip_limit",t.skip_limit.toString()),t.is_fb!==void 0&&a.set("is_fb",t.is_fb.toString()),(await n.get(`/mdata/v1/payment-methods?${a.toString()}`)).data},uploadImage:async t=>{console.log("Uploading file:",t.name,t.size,t.type);const a=new FormData;a.append("file",t),console.log("FormData entries:",Array.from(a.entries()));const e=await n.post("/v3/pos-cms/image/upload",a,{headers:{"Content-Type":void 0}});return console.log("Upload response:",e.data),e.data},createPaymentMethod:async t=>{const a=await n.post("/mdata/v1/payment-method",t);return a.data.data||a.data},updatePaymentMethod:async t=>{const a=await n.put("/mdata/v1/payment-method",t);return a.data.data||a.data},deletePaymentMethod:async t=>{const a=new URLSearchParams({company_uid:t.company_uid,brand_uid:t.brand_uid,id:t.id});await n.delete(`/mdata/v1/payment-method?${a.toString()}`)},getPaymentMethodById:async(t,a,e)=>{const d=new URLSearchParams({company_uid:a,brand_uid:e}),o=await n.get(`/mdata/v1/payment-method/${t}?${d.toString()}`);return o.data.data||o.data},getPaymentMethodWithStores:async t=>{const a=new URLSearchParams({list_payment_method_uid:t.join(",")}),e=await n.get(`/mdata/v1/payment-method?${a.toString()}`);return e.data.data||e.data},getPaymentMethodDetail:async(t,a,e)=>{const d=new URLSearchParams({company_uid:a,brand_uid:e,payment_method_id:t}),o=await n.get(`/mdata/v1/payment-method?${d.toString()}`);return o.data.data||o.data}};export{s as p};
