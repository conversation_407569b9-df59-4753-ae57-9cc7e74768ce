import{u as i}from"./useQuery-BvDWg4vp.js";import{a3 as c,a4 as p}from"./index-CfbMU4Ye.js";import{u as m}from"./useMutation-C9PewMvL.js";import{b as a}from"./pos-api-BBB_ZiZD.js";import{g}from"./error-utils-BYcz3jZ5.js";import{Q as u}from"./query-keys-3lmd-xp6.js";function q({companyUid:t,brandUid:r,storeUid:e,enabled:s=!0,includePromotionPartnerAutoGen:o=!0}){return i({queryKey:[u.DISCOUNTS,"programs",t,r,e,o],queryFn:async()=>{if(!t||!r||!e)throw new Error("Missing required parameters");const n=new URLSearchParams({skip_limit:"true",company_uid:t,brand_uid:r,store_uid:e});return o&&n.append("promotion_partner_auto_gen","1"),(await a.get(`/mdata/v1/discounts?${n.toString()}`)).data},enabled:s&&!!t&&!!r&&!!e})}function w(){const t=c();return m({mutationFn:async r=>{const e={company_uid:r.companyUid,brand_uid:r.brandUid,list_discount_uid:r.listDiscountUid,source_store:r.sourceStore,target_store:r.targetStore};return(await a.post("/mdata/v1/clone_discount",e)).data},onSuccess:()=>{t.invalidateQueries({queryKey:[u.DISCOUNTS]})},onError:r=>{const e=g(r,"Lỗi khi sao chép chương trình giảm giá");p.error(e)}})}export{q as a,w as u};
