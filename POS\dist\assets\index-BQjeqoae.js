import{j as e,r as g,u as U,a4 as D,B as v,T as ee,o as te,p as se,q as ne,h as X,R as ae,l as re}from"./index-CfbMU4Ye.js";import{g as oe}from"./error-utils-BYcz3jZ5.js";import{H as E}from"./header-CiiJInbE.js";import{M as P}from"./main-B69tr6A0.js";import{P as O}from"./profile-dropdown-HjZ6UGjk.js";import{S as A,T as R}from"./search-Bbt2JnTN.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{S as w,a as H}from"./revenue-lock-section-DPzrOMY8.js";import{S as ie}from"./status-badge-BBBMKdZV.js";import"./pos-api-BBB_ZiZD.js";import{u as ce}from"./useMutation-C9PewMvL.js";import{u as le,g as $}from"./stores-api-3ul-JRE8.js";import{C as de}from"./index-TKFSyVOw.js";import{c as I}from"./createLucideIcon-BH-J_-vM.js";import{S as me}from"./square-pen-CK-Wj9tj.js";import{u as he,e as ue,f as B}from"./index-DrO-sOnq.js";import{T as xe,a as pe,b as K,c as ge,d as fe,e as V}from"./table-C3v-r6-e.js";import{a as Se,C as ye}from"./chevron-right-BwGWQXH2.js";import{I as je}from"./input-D8TU6hMD.js";import{F as Ce}from"./filter-dropdown-CFMAnMA-.js";import{D as Ne,a as Te,b as ve,c as W}from"./dropdown-menu-8bnotEGr.js";import{a as we,b as be,u as De}from"./use-stores-Cb_kvevV.js";import{S as _e}from"./skeleton-HVXRv3hO.js";import{P as J}from"./modal-D_ZqQrH_.js";import{A as Ie}from"./arrow-up-down-DG_CxWIL.js";import{I as Me}from"./IconPlus-CiQ0nQi0.js";import{c as Le}from"./vietqr-api-BHQxfNzq.js";import{u as ke,a as Ee}from"./use-images-D7y6sUV3.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import{M as Pe}from"./multi-select-combobox-CU7aPzsC.js";import{U as z}from"./upload-DzCrKix_.js";import{I as Oe}from"./image-DIo0afo3.js";import{u as Ae}from"./use-removed-items-DWRDzX0n.js";import{u as G}from"./useQuery-BvDWg4vp.js";import{Q}from"./query-keys-3lmd-xp6.js";import"./separator-DVvwOaSX.js";import"./avatar-CE3yFgmj.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./calendar-DmzcYdpW.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-FztlF_ds.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./scroll-area-Bx6sgJqp.js";import"./index-D41EikqA.js";import"./select-_nXsh5SU.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./TileLayer-CGDVvl29.js";import"./date-picker-00goVjYH.js";import"./calendar-BqbXMtoi.js";import"./combobox-B1W092_-.js";import"./chevrons-up-down-BYVIbgdq.js";import"./badge-DNJz5hg4.js";import"./utils-km2FGkQ4.js";import"./index-4DjKSQeL.js";import"./images-api-RS3EYfrE.js";import"./checkbox-CSFn543p.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Re=[["path",{d:"m11 17 2 2a1 1 0 1 0 3-3",key:"efffak"}],["path",{d:"m14 14 2.5 2.5a1 1 0 1 0 3-3l-3.88-3.88a3 3 0 0 0-4.24 0l-.88.88a1 1 0 1 1-3-3l2.81-2.81a5.79 5.79 0 0 1 7.06-.87l.47.28a2 2 0 0 0 1.42.25L21 4",key:"9pr0kb"}],["path",{d:"m21 3 1 11h-2",key:"1tisrp"}],["path",{d:"M3 3 2 14l6.5 6.5a1 1 0 1 0 3-3",key:"1uvwmv"}],["path",{d:"M3 4h8",key:"1ep09j"}]],Ke=I("handshake",Re);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fe=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Ue=I("house",Fe);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qe=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],He=I("info",qe);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $e=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],Be=I("monitor",$e);function Ve({expiryTimestamp:t}){if(!t)return e.jsx("span",{className:"text-muted-foreground",children:"-"});const s=new Date(t*1e3);return s<new Date?e.jsx("span",{className:"rounded bg-red-100 px-2 py-1 text-xs text-red-600",children:w.EXPIRED_LICENSE_BADGE}):e.jsx("span",{className:"font-medium",children:s.toLocaleDateString("vi-VN")})}function We({store:t}){const s=t.fb_store_id||t.code;return e.jsx("span",{className:"font-medium",children:s})}function ze({store:t}){const s=t.active===H.ACTIVE||t.status===H.ACTIVE_TEXT;return e.jsx(ie,{isActive:s,activeText:w.BUTTON_ACTIVE,inactiveText:w.BUTTON_DEACTIVE})}const Ge=({store:t})=>{var d;const[s,n]=g.useState(!1),{auth:a}=U(),r=((d=t.extraData)==null?void 0:d.is_franchise)===1,o=ce({mutationFn:le,onSuccess:()=>{D.success(`Đã cập nhật loại hình nhà hàng thành ${r?"thuộc chuỗi thương hiệu":"nhượng quyền"}`),n(!1),window.location.reload()},onError:m=>{D.error(m.message||"Có lỗi xảy ra khi cập nhật loại hình nhà hàng")}}),c=()=>{var x;const m=a.company,h=(x=a.brands)==null?void 0:x[0];if(!(m!=null&&m.id)||!(h!=null&&h.id)){D.error("Không tìm thấy thông tin công ty hoặc thương hiệu");return}o.mutate({company_uid:m.id,brand_uid:h.id,id:t.id,is_franchise:r?0:1})},l=r?{title:`Bạn xác nhận thay đổi ${t.name}`,content:"Từ điểm nhượng quyền thành điểm thuộc chuỗi thương hiệu",confirmText:"Xác nhận",cancelText:"Hủy"}:{title:`Bạn xác nhận thay đổi ${t.name}`,content:"Từ điểm thuộc chuỗi thương hiệu thành điểm nhượng quyền",confirmText:"Xác nhận",cancelText:"Hủy"};return e.jsxs("div",{onClick:m=>m.stopPropagation(),children:[e.jsxs(v,{variant:"ghost",size:"sm",className:`flex w-fit items-center justify-center gap-2 rounded px-2 py-1 text-white ${r?"bg-orange-500 hover:bg-orange-600":"bg-blue-500 hover:bg-blue-600"}`,onClick:()=>n(!0),disabled:o.isPending,children:[r?e.jsx(Ke,{className:"h-4 w-4"}):e.jsx(Ue,{className:"h-4 w-4"}),e.jsx(me,{className:"h-4 w-4"})]}),e.jsx(de,{open:s,onOpenChange:n,title:l.title,content:l.content,confirmText:l.confirmText,cancelText:l.cancelText,onConfirm:c,isLoading:o.isPending})]})},Qe=[{accessorKey:"id",header:"#",cell:({row:t})=>{const s=t.index+1;return e.jsx("div",{className:"w-8 text-xs font-medium sm:w-[50px] sm:text-sm",children:s})},enableSorting:!1},{accessorKey:"fb_store_id",header:"Pos ID",cell:({row:t})=>e.jsx(We,{store:t.original})},{accessorKey:"name",header:"Tên",cell:({row:t})=>e.jsx("span",{className:"font-medium",children:t.original.name})},{accessorKey:"city_name",header:"Địa điểm",cell:({row:t})=>{const s=t.original,n=s.city_name||s.cityName,a=s.address,r=s.latitude,o=s.longitude;return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:n}),a&&e.jsx(ee,{children:e.jsxs(te,{children:[e.jsx(se,{asChild:!0,children:e.jsx(He,{className:"text-muted-foreground hover:text-foreground h-4 w-4 cursor-help transition-colors"})}),e.jsx(ne,{className:"max-w-xs",children:e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:"text-sm",children:a}),r&&o&&e.jsxs("p",{className:"text-xs",children:["lat: ",r,", long: ",o]})]})})]})})]})}},{accessorKey:"phone",header:"Số điện thoại",cell:({row:t})=>{const s=t.original;return s.phone?e.jsx("span",{className:"font-medium",children:s.phone}):e.jsx("span",{className:"text-muted-foreground",children:"-"})}},{accessorKey:"email",header:"Email",cell:({row:t})=>{const s=t.original;return s.email?e.jsx("span",{className:"font-medium",children:s.email}):e.jsx("span",{className:"text-muted-foreground",children:"-"})}},{accessorKey:"expiry_date",header:"Thời hạn bán quyền",cell:({row:t})=>{const s=t.original,n=s.expiry_date||s.expiryDate;return e.jsx(Ve,{expiryTimestamp:n})}},{accessorKey:"store_type",header:"Loại hình nhà hàng",cell:({row:t})=>e.jsx(Ge,{store:t.original})},{accessorKey:"active",header:"",cell:({row:t})=>e.jsx(ze,{store:t.original})}];function Ye({currentPage:t,onPageChange:s,hasNextPage:n,total:a,pageSize:r=50}){const o=()=>{t>1&&s(t-1)},c=()=>{n&&s(t+1)},i=(t-1)*r+1,l=Math.min(t*r,a||0),d=a?`Hiển thị ${i}-${l} trong tổng số ${a} cửa hàng`:`Trang ${t}`;return e.jsxs("div",{className:"flex items-center justify-between py-4",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:d}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{variant:"outline",size:"sm",onClick:o,disabled:t===1,className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),"Trước"]}),e.jsxs("span",{className:"text-sm font-medium",children:["Trang ",t]}),e.jsxs(v,{variant:"outline",size:"sm",onClick:c,disabled:!n,className:"flex items-center gap-2",children:["Sau",e.jsx(ye,{className:"h-4 w-4"})]})]})]})}function Xe({columns:t,data:s,currentPage:n,onPageChange:a,hasNextPage:r,total:o,pageSize:c}){var m;const i=X(),l=he({data:s,columns:t,getCoreRowModel:ue()}),d=h=>{i({to:`/setting/store/detail/${h.id}`})};return e.jsxs("div",{className:"w-full overflow-auto",children:[e.jsx("div",{className:"min-w-full rounded-md border",children:e.jsxs(xe,{className:"w-full table-auto",children:[e.jsx(pe,{children:l.getHeaderGroups().map(h=>e.jsx(K,{className:"h-6 sm:h-7",children:h.headers.map(x=>e.jsx(ge,{className:"px-1 py-0.5 text-xs font-medium whitespace-nowrap sm:px-2 sm:py-1",children:x.isPlaceholder?null:B(x.column.columnDef.header,x.getContext())},x.id))},h.id))}),e.jsx(fe,{children:(m=l.getRowModel().rows)!=null&&m.length?l.getRowModel().rows.map(h=>e.jsx(K,{"data-state":h.getIsSelected()&&"selected",className:"hover:bg-muted/50 h-8 cursor-pointer sm:h-10",onClick:()=>d(h.original),children:h.getVisibleCells().map(x=>e.jsx(V,{className:"px-1 py-0.5 text-xs sm:px-2 sm:py-1 sm:text-sm",children:B(x.column.columnDef.cell,x.getContext())},x.id))},h.id)):e.jsx(K,{children:e.jsx(V,{colSpan:t.length,className:"h-12 text-center text-xs sm:h-16 sm:text-sm",children:"Không có dữ liệu cửa hàng."})})})]})}),n&&a&&e.jsx(Ye,{currentPage:n,onPageChange:a,hasNextPage:r||!1,total:o,pageSize:c})]})}function Je({value:t,onValueChange:s,cities:n,isLoading:a,placeholder:r="Tất cả thành phố",className:o="w-48"}){const c=(n==null?void 0:n.map(i=>({value:i.id,label:i.name})))||[];return e.jsx(Ce,{value:t,onValueChange:s,options:c,isLoading:a,placeholder:r,className:o,allOptionLabel:"Tất cả thành phố",loadingText:"Đang tải thành phố...",emptyText:"Không có thành phố"})}const Ze=8,et=6,Y="text/plain",_={title:"Sắp xếp cửa hàng",confirmText:"Lưu",maxWidth:"sm:max-w-4xl",description:"Thứ tự hiển thị các cửa hàng sẽ được áp dụng tại hệ thống"},Z={loadError:"Có lỗi xảy ra khi tải danh sách cửa hàng",noStores:"Không có cửa hàng nào để sắp xếp"},T={description:"text-muted-foreground text-sm",errorText:"text-sm text-red-600",emptyState:"py-8 text-center",grid:`grid grid-cols-${et} gap-2`,draggableItem:"flex aspect-square cursor-move items-center justify-center bg-slate-300 p-1 transition-colors select-none hover:bg-slate-400 rounded-sm text-xs",itemText:"text-center text-xs font-medium leading-tight",skeletonContainer:"aspect-square bg-slate-300 p-1 rounded-sm"};function tt(){return e.jsx("div",{className:T.grid,children:Array.from({length:Ze}).map((t,s)=>e.jsx("div",{className:T.skeletonContainer,children:e.jsx(_e,{className:"h-4 w-full"})},s))})}function st(){return e.jsx("div",{className:T.emptyState,children:e.jsx("p",{className:T.errorText,children:Z.loadError})})}function nt(){return e.jsx("div",{className:T.emptyState,children:e.jsx("p",{className:T.description,children:Z.noStores})})}function at({store:t,index:s,onDragStart:n,onDragOver:a,onDrop:r}){return e.jsx("div",{draggable:!0,onDragStart:o=>n(o,s),onDragOver:a,onDrop:o=>r(o,s),className:T.draggableItem,children:e.jsx("div",{className:T.itemText,children:t.store_name})},t.id)}function rt(t){return g.useMemo(()=>{if(!(t!=null&&t.data))return[];const s=n=>n.active===1;return t.data.filter(s).sort((n,a)=>n.sort-a.sort)},[t==null?void 0:t.data])}function ot(t){const[s,n]=g.useState([]);return g.useEffect(()=>{n(t)},[t]),{sortedStores:s,handleDragStart:(c,i)=>{c.dataTransfer.setData(Y,i.toString())},handleDragOver:c=>{c.preventDefault()},handleDrop:(c,i)=>{c.preventDefault();const l=parseInt(c.dataTransfer.getData(Y));if(l===i)return;const d=[...s],m=d[l];d.splice(l,1),d.splice(i,0,m),n(d)}}}function it({open:t,onOpenChange:s}){const{data:n,isLoading:a,error:r}=we(),{updateSort:o,isUpdating:c}=be(),i=rt(n),{sortedStores:l,handleDragStart:d,handleDragOver:m,handleDrop:h}=ot(i),x=a||c,f=l.length===0,S=()=>{const y=l.map((j,u)=>({...j,sort:u}));o(y,{onSuccess:()=>s(!1)})},C=()=>{s(!1)},N=()=>a?e.jsx(tt,{}):r?e.jsx(st,{}):f?e.jsx(nt,{}):e.jsx("div",{className:T.grid,children:l.map((y,j)=>e.jsx(at,{store:y,index:j,onDragStart:d,onDragOver:m,onDrop:h},y.id))});return e.jsxs(J,{open:t,onOpenChange:s,title:_.title,onCancel:C,onConfirm:S,confirmText:_.confirmText,hideButtons:!1,confirmDisabled:x,isLoading:c,disableCancelButton:!0,maxWidth:_.maxWidth,children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:T.description,children:_.description})}),e.jsx("div",{children:N()})]})}function ct({onSyncSecondaryScreen:t}){const[s,n]=ae.useState(!1),a=()=>{n(!0)};return e.jsxs(e.Fragment,{children:[e.jsxs(Ne,{children:[e.jsx(Te,{asChild:!0,children:e.jsx(v,{variant:"outline",size:"sm",children:"Tiện ích"})}),e.jsxs(ve,{align:"end",children:[e.jsxs(W,{onClick:a,children:[e.jsx(Ie,{className:"mr-2 h-4 w-4"}),"Sắp xếp cửa hàng"]}),e.jsxs(W,{onClick:t,children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),"Đồng bộ màn hình phụ"]})]})]}),s&&e.jsx(it,{open:s,onOpenChange:n})]})}function F({searchTerm:t,onSearchChange:s,selectedCity:n,onCityChange:a,cities:r,citiesLoading:o,onCreateStore:c,onSyncSecondaryScreen:i}){return e.jsx("div",{className:"mb-4 space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex min-w-0 flex-1 items-center gap-3",children:[e.jsx("h2",{className:"text-xl font-semibold whitespace-nowrap",children:"Danh sách nhà hàng"}),e.jsx(je,{placeholder:"Tìm kiếm nhà hàng",value:t,onChange:l=>s(l.target.value),className:"w-64"}),e.jsx(Je,{value:n,onValueChange:a,cities:r,isLoading:o})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ct,{onSyncSecondaryScreen:i}),e.jsxs(v,{onClick:c,children:[e.jsx(Me,{className:"mr-2 h-4 w-4"}),"Tạo nhà hàng mới"]})]})]})})}function lt({open:t,onOpenChange:s}){const{data:n=[]}=De(),{company:a,brands:r}=U(u=>u.auth),o=r==null?void 0:r[0],[c,i]=g.useState([]),[l,d]=g.useState(null),[m,h]=g.useState(null),x=g.useRef(null),f=ke(),S=Ee(),C=()=>{i([]),d(null),h(null),s(!1)},N=async()=>{if(c.length===0){D.error("Vui lòng chọn ít nhất một cửa hàng");return}if(!(a!=null&&a.id)||!(o!=null&&o.id)){D.error("Thiếu thông tin công ty hoặc thương hiệu");return}try{let u="";l&&(u=(await f.mutateAsync(l)).data.image_url),await S.mutateAsync({company_uid:a.id,brand_uid:o.id,list_store_uid:c,background:u}),i([]),d(null),h(null),s(!1)}catch(u){console.error("Sync failed:",u)}},y=u=>{var k;const p=(k=u.target.files)==null?void 0:k[0];if(!p)return;const M=2.5*1024*1024;if(p.size>M){alert("Kích thước ảnh phải nhỏ hơn 2.5MB");return}if(!p.type.startsWith("image/")){alert("Vui lòng chọn file ảnh");return}d(p);const L=new FileReader;L.onload=b=>{var q;h((q=b.target)==null?void 0:q.result)},L.readAsDataURL(p)},j=()=>{var u;(u=x.current)==null||u.click()};return e.jsx(J,{open:t,onOpenChange:s,title:"Đồng bộ màn hình phụ",onCancel:C,onConfirm:N,confirmText:"Đồng bộ",confirmDisabled:c.length===0||f.isPending||S.isPending,maxWidth:"sm:max-w-[600px] sm:min-h-[500px]",disableCancelButton:!0,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chọn cửa hàng"}),e.jsx(Pe,{options:n.map(u=>({value:u.id,label:u.name})),value:c,onValueChange:i,placeholder:"Chọn cửa hàng để đồng bộ",searchPlaceholder:"Tìm kiếm cửa hàng...",emptyText:"Không tìm thấy cửa hàng.",selectAllLabel:"Chọn tất cả",allSelectedText:"Tất cả cửa hàng",selectedCountText:u=>`${u} cửa hàng đã chọn`,className:"w-full justify-between"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Hình nền trên thiết bị bán hàng"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Hình ảnh này xuất hiện trên màn hình 2 của các thiết bị pos 2 màn hình. Kích thước đề xuất 1920x1080 px"}),e.jsx("input",{ref:x,type:"file",accept:"image/*",onChange:y,className:"hidden"}),e.jsxs("div",{className:"min-h-[200px] rounded-lg border-2 border-dashed border-gray-300 p-6",children:[m&&e.jsxs("div",{className:"space-y-3",children:[e.jsx("img",{src:m,alt:"Preview",className:"h-48 w-full rounded-lg object-cover"}),e.jsxs(v,{type:"button",variant:"outline",size:"sm",onClick:j,className:"w-full",children:[e.jsx(z,{className:"mr-2 h-4 w-4"}),"Thay đổi ảnh"]})]}),!m&&e.jsxs("div",{className:"space-y-3 text-center",children:[e.jsx(Oe,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Tải lên một hình ảnh hồ sơ"}),e.jsxs(v,{type:"button",variant:"outline",size:"sm",onClick:j,children:[e.jsx(z,{className:"mr-2 h-4 w-4"}),"Chọn ảnh"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Kích thước ảnh nhỏ hơn 2.5MB"})]})]})]})]})})}function dt(){const[t,s]=g.useState(""),[n,a]=g.useState(w.CITY_ALL);return{searchTerm:t,setSearchTerm:s,selectedCity:n,setSelectedCity:a}}function mt(t=[]){return{cities:t.map(n=>({id:n.city_id||n.id,name:n.city_name}))}}function ht({stores:t,searchTerm:s,selectedCity:n}){return{filteredStores:t.filter(r=>{const o=r.name.toLowerCase().includes(s.toLowerCase())||r.code.toLowerCase().includes(s.toLowerCase())||r.address.toLowerCase().includes(s.toLowerCase())||r.phone.toLowerCase().includes(s.toLowerCase())||r.email.toLowerCase().includes(s.toLowerCase()),c=r.cityId;return o&&(n==="all"||c===n)})}}const ut=(t={})=>{var f,S,C,N,y,j,u;const{params:s={},enabled:n=!0}=t,{company:a}=U(p=>p.auth),{selectedBrand:r}=re(),o={company_uid:(a==null?void 0:a.id)||"",brand_uid:(r==null?void 0:r.id)||"",page:1,limit:50,...s},c=!!(a!=null&&a.id&&(r!=null&&r.id)),i=G({queryKey:[Q.STORES_LIST,"paginated",JSON.stringify(o)],queryFn:async()=>await $(o),enabled:n&&c,staleTime:5*60*1e3,refetchInterval:10*60*1e3}),l={...o,page:(o.page||1)+1},d=G({queryKey:[Q.STORES_LIST,"paginated","next",JSON.stringify(l)],queryFn:async()=>await $(l),enabled:n&&c&&((f=i.data)!=null&&f.data?i.data.data.length>0:!1),staleTime:2*60*1e3,gcTime:5*60*1e3}),m=o.limit||50,h=((S=d.data)!=null&&S.data?d.data.data.length>0:!1)||((C=i.data)!=null&&C.data?i.data.data.length===m:!1);return{data:((y=(N=i.data)==null?void 0:N.data)==null?void 0:y.map(p=>({...Le(p),fb_store_id:p.fb_store_id,city_name:p.city_name,expiry_date:p.expiry_date,active:p.active,latitude:p.latitude,longitude:p.longitude,address:p.address})))||[],isLoading:i.isLoading,error:i.error,refetch:i.refetch,isFetching:i.isFetching,nextPageData:((j=d.data)==null?void 0:j.data)||[],hasNextPage:h,total:(u=i.data)==null?void 0:u.total,currentPage:o.page||1}};function xt(){const t=X(),[s,n]=g.useState(!1),[a,r]=g.useState(1),{searchTerm:o,setSearchTerm:c,selectedCity:i,setSelectedCity:l}=dt(),d=()=>r(1),m=g.useMemo(()=>({page:a,limit:50}),[a]),{data:h=[],isLoading:x,error:f,hasNextPage:S,total:C}=ut({params:m}),{data:N=[],isLoading:y}=Ae(),{cities:j}=mt(N),{filteredStores:u}=ht({stores:h,searchTerm:o,selectedCity:i});return{stores:h,filteredStores:u,cities:j,isLoading:x,citiesLoading:y,error:f,currentPage:a,setCurrentPage:r,hasNextPage:S,total:C,searchTerm:o,setSearchTerm:b=>{c(b),d()},selectedCity:i,setSelectedCity:b=>{l(b),d()},handleCreateStore:()=>{t({to:w.ROUTE_STORE_DETAIL})},handleSyncSecondaryScreen:()=>{n(!0)},syncModalOpen:s,setSyncModalOpen:n}}function pt(){const{filteredStores:t,cities:s,isLoading:n,citiesLoading:a,error:r,searchTerm:o,setSearchTerm:c,selectedCity:i,setSelectedCity:l,handleCreateStore:d,handleSyncSecondaryScreen:m,syncModalOpen:h,setSyncModalOpen:x,currentPage:f,setCurrentPage:S,hasNextPage:C,total:N}=xt();return r?e.jsxs(e.Fragment,{children:[e.jsx(E,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(A,{}),e.jsx(R,{}),e.jsx(O,{})]})}),e.jsx(P,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(F,{searchTerm:o,onSearchChange:c,selectedCity:i,onCityChange:l,cities:s,citiesLoading:a,onCreateStore:d,onSyncSecondaryScreen:m}),e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:oe(r)})})]})})]}):n?e.jsxs(e.Fragment,{children:[e.jsx(E,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(A,{}),e.jsx(R,{}),e.jsx(O,{})]})}),e.jsx(P,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(F,{searchTerm:o,onSearchChange:c,selectedCity:i,onCityChange:l,cities:s,citiesLoading:a,onCreateStore:d,onSyncSecondaryScreen:m}),e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:w.LOADING_STORES})})]})})]}):e.jsxs(e.Fragment,{children:[e.jsx(E,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(A,{}),e.jsx(R,{}),e.jsx(O,{})]})}),e.jsx(P,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(F,{searchTerm:o,onSearchChange:c,selectedCity:i,onCityChange:l,cities:s,citiesLoading:a,onCreateStore:d,onSyncSecondaryScreen:m}),e.jsx(Xe,{columns:Qe,data:t,currentPage:f,onPageChange:S,hasNextPage:C,total:N,pageSize:50}),e.jsx(lt,{open:h,onOpenChange:x})]})})]})}const _s=function(){return e.jsx(pt,{})};export{_s as component};
