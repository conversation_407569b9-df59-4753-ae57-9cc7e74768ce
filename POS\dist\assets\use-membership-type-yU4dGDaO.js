import{u as m}from"./useQuery-B4yhTgGk.js";import{r as y,aQ as d,a3 as c}from"./index-UcdZ5AHH.js";import{u}from"./useMutation-q12VR5WX.js";import{c as o}from"./crm-api-APQEjHWd.js";import"./pos-api-j20LMGrC.js";import{C as n}from"./query-keys-DQo7uRnN.js";import{u as l}from"./use-pos-company-data-C9bJLMU4.js";import"./user-9ajIul7r.js";const p={getList:async e=>(await o.get("/loyalty/get-list-membership-type",{params:e})).data,create:async(e,r)=>(await o.post("/loyalty/create-membership-type",e,{params:r})).data,update:async(e,r)=>(await o.post("/loyalty/update-membership-type",e,{params:r})).data},i=()=>y.useMemo(()=>{try{const e=localStorage.getItem(d);return e?JSON.parse(e):null}catch(e){return console.error("Error parsing pos_brands_data from localStorage:",e),null}},[]),C=()=>{var s;const e=l(),r=i(),t=e&&r?{company_id:e.company_id,pos_parent:((s=r[0])==null?void 0:s.brand_id)||""}:{company_id:"",pos_parent:""};return m({queryKey:["crm",n.MEMBERSHIP_TYPE,t],queryFn:()=>p.getList(t),enabled:!!t.company_id&&!!t.pos_parent})},P=()=>{const e=c(),r=i();return u({mutationFn:async t=>{var a;const s=((a=r==null?void 0:r[0])==null?void 0:a.brand_id)||"";return p.update(t,{pos_parent:s})},onSuccess:()=>{e.invalidateQueries({queryKey:["crm",n.MEMBERSHIP_TYPE]})},onError:t=>{console.error("Error updating membership type:",t)}})},T=()=>{const e=c(),r=i();return u({mutationFn:async t=>{var a;const s=((a=r==null?void 0:r[0])==null?void 0:a.brand_id)||"";return p.create(t,{pos_parent:s})},onSuccess:()=>{e.invalidateQueries({queryKey:["crm",n.MEMBERSHIP_TYPE]})},onError:t=>{console.error("Error creating membership type:",t)}})};export{P as a,T as b,i as c,C as u};
