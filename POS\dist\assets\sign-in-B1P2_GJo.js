import{j as r}from"./index-CVQ6JZo2.js";import{C as e,a as t,b as o,c as i,d as s,f as a}from"./card-DtE5r_AG.js";import{A as n}from"./auth-layout-ByQ9c3UY.js";import{U as m}from"./user-auth-form-BibmeLJ4.js";import"./form-CzmGigtT.js";import"./zod-ByV4TDQ9.js";import"./use-auth-DUSq4Cn-.js";import"./useMutation-ZsyDznMu.js";import"./utils-km2FGkQ4.js";import"./pos-api-mRg02iop.js";import"./input-Al6WtUZF.js";import"./password-input-COPnI1k2.js";import"./createReactComponent-DSXPaZ4c.js";import"./IconBrandGithub-CIy8-OIO.js";function c(){return r.jsx(n,{children:r.jsxs(e,{className:"gap-4",children:[r.jsxs(t,{children:[r.jsx(o,{className:"text-lg tracking-tight",children:"Login"}),r.jsxs(i,{children:["Enter your email and password below to ",r.jsx("br",{}),"log into your account"]})]}),r.jsx(s,{children:r.jsx(m,{})}),r.jsx(a,{children:r.jsxs("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["By clicking login, you agree to our"," ",r.jsx("a",{href:"/terms",className:"hover:text-primary underline underline-offset-4",children:"Terms of Service"})," ","and"," ",r.jsx("a",{href:"/privacy",className:"hover:text-primary underline underline-offset-4",children:"Privacy Policy"}),"."]})})]})})}const A=c;export{A as component};
