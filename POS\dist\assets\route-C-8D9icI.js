import{j as r,O as t}from"./index-CVQ6JZo2.js";import{C as i}from"./index-Dcrro_32.js";import"./date-range-picker-CXbMaowj.js";import"./search-context-CkCLuJFL.js";import"./pos-api-mRg02iop.js";import"./form-CzmGigtT.js";import"./main-BD6dUgw2.js";import"./index-C0S7iRhJ.js";import"./calendar-BszTCdZH.js";import"./createLucideIcon-DKVxsQv7.js";import"./index-CtK-wKtB.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-CxgpqvrH.js";import"./react-icons.esm-DMMA_g0o.js";import"./popover-DnoSPJNX.js";import"./select-BFhNE0YE.js";import"./index-LVHINuqD.js";import"./index-nc1u7392.js";import"./check-BE_j5GZD.js";import"./command-Nb4B17YQ.js";import"./dialog-DDrduXt3.js";import"./search-B4Rlb4i6.js";import"./createReactComponent-DSXPaZ4c.js";import"./scroll-area-CGsZUbT-.js";import"./IconChevronRight-LXWXuzjR.js";const o="...",K=function(){return r.jsx(i,{publishableKey:o,afterSignOutUrl:"/clerk/sign-in",signInUrl:"/clerk/sign-in",signUpUrl:"/clerk/sign-up",signInFallbackRedirectUrl:"/clerk/user-management",signUpFallbackRedirectUrl:"/clerk/user-management",children:r.jsx(t,{})})};export{K as component};
