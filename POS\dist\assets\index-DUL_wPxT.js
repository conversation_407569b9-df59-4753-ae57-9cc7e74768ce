import{r as g,j as e,R as I,z as n,B as N,k as X,a4 as $,aw as q}from"./index-CfbMU4Ye.js";import"./pos-api-BBB_ZiZD.js";import{u as O}from"./use-stores-Cb_kvevV.js";import"./vietqr-api-BHQxfNzq.js";import{a as W,b as G,c as J,u as Q}from"./use-promotions-DBZTsfbS.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import{H as Y}from"./header-CiiJInbE.js";import{M as Z}from"./main-B69tr6A0.js";import{P as ee}from"./profile-dropdown-HjZ6UGjk.js";import{S as te,T as se}from"./search-Bbt2JnTN.js";import{D as k}from"./data-table-column-header-DxTOtvid.js";import{u as oe}from"./use-dialog-state-CkZCkUo8.js";import{I as ae}from"./IconTrash-NV_v0NzY.js";import{I as ne}from"./IconPlus-CiQ0nQi0.js";import{S as x}from"./skeleton-HVXRv3hO.js";import{T as U,a as E,b as _,c as b,d as K,e as j}from"./table-C3v-r6-e.js";import{C as ie}from"./confirm-dialog-dvjeaHx8.js";import{I as L}from"./input-D8TU6hMD.js";import{L as R}from"./form-DPp_Bp7A.js";import{M as re}from"./multi-select-DH0FdNoK.js";import{X as le}from"./calendar-DmzcYdpW.js";import{u as me,g as ce,a as de,b as xe,c as ue,d as he,e as pe,f as F}from"./index-DrO-sOnq.js";import{D as ge}from"./data-table-pagination-Kq742vwq.js";import{g as je}from"./react-icons.esm-DefBGHOQ.js";import{S as fe,a as be,b as _e,c as Ne,d as V}from"./select-_nXsh5SU.js";import{D as we}from"./data-table-view-options-DLFflQzW.js";import"./useQuery-BvDWg4vp.js";import"./utils-km2FGkQ4.js";import"./useMutation-C9PewMvL.js";import"./stores-api-3ul-JRE8.js";import"./query-keys-3lmd-xp6.js";import"./separator-DVvwOaSX.js";import"./date-range-picker-FRR8J6T3.js";import"./createLucideIcon-BH-J_-vM.js";import"./chevron-right-BwGWQXH2.js";import"./popover-C4SSkcaE.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./avatar-CE3yFgmj.js";import"./dropdown-menu-8bnotEGr.js";import"./index-D41EikqA.js";import"./index-4DjKSQeL.js";import"./check-C1W3FWto.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./dialog-FztlF_ds.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./scroll-area-Bx6sgJqp.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";import"./alert-dialog-Bac0_q2y.js";import"./badge-DNJz5hg4.js";import"./circle-x-BmLiWp0R.js";import"./index-Cqf6DKEV.js";const z=I.createContext(null);function Ce({children:t}){const[a,s]=oe(null),[l,m]=g.useState(null);return e.jsx(z,{value:{open:a,setOpen:s,currentRow:l,setCurrentRow:m},children:t})}const w=()=>{const t=I.useContext(z);if(!t)throw new Error("usePromotions has to be used within <PromotionsContext>");return t},ve=n.object({code:n.string(),name:n.string(),store:n.string(),totalStores:n.number(),isActive:n.boolean(),createdAt:n.date(),originalData:n.object({promotion_id:n.string(),promotion_name:n.string(),partner_auto_gen:n.number(),array_agg:n.array(n.number().nullable()),ids_same_promotion:n.array(n.string()),promotions:n.array(n.object({promotion_uid:n.string(),active:n.number(),created_at:n.number().nullable(),store_uid:n.string(),store_name:n.string()}))})});function Se({row:t}){const a=ve.parse(t.original),{setOpen:s,setCurrentRow:l}=w();return e.jsxs(N,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50",onClick:()=>{l(a),s("delete")},children:[e.jsx(ae,{size:16}),e.jsx("span",{className:"sr-only",children:"Xóa"})]})}const ye=[{id:"index",header:"#",cell:({row:t})=>e.jsx("div",{className:"w-[50px]",children:t.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:t})=>e.jsx(k,{column:t,title:"Mã CTKM"}),cell:({row:t})=>e.jsx("div",{className:"text-sm font-medium",children:t.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:t})=>e.jsx(k,{column:t,title:"Tên CTKM"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"store",header:({column:t})=>e.jsx(k,{column:t,title:"Cửa hàng"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("store")}),enableSorting:!1,enableHiding:!0},{id:"actions",cell:({row:t})=>e.jsx(Se,{row:t})}];function Te(){const{setOpen:t}=w();return e.jsx("div",{className:"flex gap-2",children:e.jsxs(N,{className:"space-x-1",onClick:()=>t("create"),children:[e.jsx("span",{children:"Tạo chương trình"})," ",e.jsx(ne,{size:18})]})})}function Pe(){return e.jsxs("div",{className:"w-full space-y-4",children:[e.jsx("div",{className:"space-y-2",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(x,{className:"h-6 w-48"}),e.jsx(x,{className:"h-5 w-20"}),e.jsx(x,{className:"h-5 w-24"})]})})}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto rounded-md border bg-white",children:e.jsxs(U,{style:{minWidth:"800px"},children:[e.jsx(E,{children:e.jsxs(_,{children:[e.jsx(b,{className:"w-[50px]",children:e.jsx(x,{className:"h-4 w-6"})}),e.jsx(b,{children:e.jsx(x,{className:"h-4 w-20"})}),e.jsx(b,{children:e.jsx(x,{className:"h-4 w-32"})}),e.jsx(b,{children:e.jsx(x,{className:"h-4 w-24"})})]})}),e.jsx(K,{children:Array.from({length:10}).map((t,a)=>e.jsxs(_,{children:[e.jsx(j,{children:e.jsx(x,{className:"h-4 w-6"})}),e.jsx(j,{children:e.jsx(x,{className:"h-4 w-16"})}),e.jsx(j,{children:e.jsx(x,{className:"h-4 w-40"})}),e.jsx(j,{children:e.jsx(x,{className:"h-4 w-28"})})]},a))})]})})})]})}const De=()=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let a="";for(let s=0;s<4;s++)a+=t.charAt(Math.floor(Math.random()*t.length));return`PROMOTION-${a}`};function H({open:t,onOpenChange:a,currentRow:s}){const{data:l=[],isLoading:m}=O(),{company:c,selectedBrand:r}=X(),{createPromotion:u,isCreating:f}=W(),{updatePromotion:y,isUpdating:T}=G(),C=!!s,[d,h]=g.useState({promotion_name:"",selectedStores:[]});g.useEffect(()=>{t&&s?h({promotion_name:s.name||"",selectedStores:s.originalData.promotions.map(o=>o.store_uid)||[]}):t&&!s&&h({promotion_name:"",selectedStores:[]})},[s,t]);const P=()=>{a(!1),h({promotion_name:"",selectedStores:[]})},p=d.promotion_name.trim()!=="",D=async()=>{if(!p)return;if(!(c!=null&&c.id)||!(r!=null&&r.id)){$.error("Thiếu thông tin công ty hoặc thương hiệu");return}const o=d.selectedStores.length>0?d.selectedStores:[];if(C&&s){const i=s.originalData,S=Math.floor(Date.now()/1e3),M=q();y({brand_uid:r.id,company_uid:c.id,sort:1e3,is_fabi:1,partner_auto_gen:0,active:1,extra_data:{},source_uid:"b78fe607-2ab5-4f08-baac-e9dfbade7a87",promotion_id:i.promotion_id,promotion_name:d.promotion_name,description:null,deleted:!1,created_by:M,updated_by:M,deleted_by:null,created_at:S,updated_at:S,deleted_at:null,promotions:o.map((A,B)=>({id:i.ids_same_promotion[B]||i.ids_same_promotion[0]||"",company_uid:c.id,brand_uid:r.id,sort:1e3,is_fabi:1,partner_auto_gen:0,active:1,extra_data:{},source_uid:"b78fe607-2ab5-4f08-baac-e9dfbade7a87",promotion_id:i.promotion_id,promotion_name:d.promotion_name,description:null,store_uid:A})),promotion_uids_same_promotion_id:i.ids_same_promotion.join(","),stores:o,list_store_uid:o,list_promotion_uid:i.ids_same_promotion})}else u({promotion_name:d.promotion_name,company_uid:c.id,brand_uid:r.id,promotion_id:De(),list_store_uid:o});a(!1),h({promotion_name:"",selectedStores:[]})},v=o=>{h({...d,selectedStores:o})};return t?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(N,{variant:"ghost",size:"sm",onClick:P,className:"flex items-center",children:e.jsx(le,{className:"h-4 w-4"})}),e.jsx(N,{type:"button",disabled:f||T||!p,onClick:D,children:"Lưu"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:C?"Chỉnh sửa khuyến mãi":"Tạo khuyến mãi mới"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"rounded-lg border bg-white p-6 shadow-sm",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin chi tiết"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(R,{htmlFor:"promotion-name",className:"min-w-[200px] text-sm font-medium",children:["Tên khuyến mãi ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(L,{id:"promotion-name",value:d.promotion_name,onChange:o=>h({...d,promotion_name:o.target.value}),placeholder:"Nhập tên khuyến mãi...",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(R,{className:"min-w-[200px] text-sm font-medium",children:"Cửa hàng áp dụng"}),e.jsx("div",{className:"flex-1",children:e.jsx(re,{options:l.map(o=>({label:o.name,value:o.id})),onValueChange:v,defaultValue:d.selectedStores,placeholder:m?"Đang tải...":"Chọn cửa hàng",variant:"default",animation:.2,maxCount:3,disabled:m},`multiselect-${d.selectedStores.join("-")}-${t}`)})]})]})]})})})]}):null}function ke(){const{open:t,setOpen:a,currentRow:s,setCurrentRow:l}=w(),{deletePromotion:m}=J();return e.jsxs(e.Fragment,{children:[e.jsx(H,{open:t==="create",onOpenChange:()=>a("create")},"tasks-create"),s&&e.jsxs(e.Fragment,{children:[e.jsx(H,{open:t==="update",onOpenChange:()=>{a("update"),setTimeout(()=>{l(null)},500)},currentRow:s},`tasks-update-${s.code}`),e.jsx(ie,{destructive:!0,open:t==="delete",onOpenChange:()=>{a("delete"),setTimeout(()=>{l(null)},500)},handleConfirm:async()=>{a(null),setTimeout(()=>{l(null)},500),await m(s.originalData.ids_same_promotion.join(","))},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"task-delete")]})]})}function Me({table:t,storesData:a=[],selectedStoreUid:s="all",onStoreChange:l}){var c;const m=t.getState().columnFilters.length>0;return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2",children:[e.jsx(L,{placeholder:"Tìm kiếm...",value:((c=t.getColumn("name"))==null?void 0:c.getFilterValue())??"",onChange:r=>{var u;return(u=t.getColumn("name"))==null?void 0:u.setFilterValue(r.target.value)},className:"h-8 w-[150px] lg:w-[250px]"}),e.jsx("div",{className:"flex gap-x-2",children:e.jsxs(fe,{value:s,onValueChange:l,children:[e.jsx(be,{className:"h-8 w-[180px]",children:e.jsx(_e,{placeholder:"Chọn cửa hàng"})}),e.jsxs(Ne,{children:[e.jsx(V,{value:"all",children:"Tất cả cửa hàng"}),a.map(r=>e.jsx(V,{value:r.id,children:r.name},r.id))]})]})}),m&&e.jsxs(N,{variant:"ghost",onClick:()=>t.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:["Reset",e.jsx(je,{className:"ml-2 h-4 w-4"})]})]}),e.jsx(we,{table:t})]})}function Re({columns:t,data:a,storesData:s=[],selectedStoreUid:l="all",onStoreChange:m}){var v;const[c,r]=g.useState({}),[u,f]=g.useState({}),[y,T]=g.useState([]),[C,d]=g.useState([]),{setOpen:h,setCurrentRow:P}=w(),p=me({data:a,columns:t,state:{sorting:C,columnVisibility:u,rowSelection:c,columnFilters:y},enableRowSelection:!0,onRowSelectionChange:r,onSortingChange:d,onColumnFiltersChange:T,onColumnVisibilityChange:f,getCoreRowModel:pe(),getFilteredRowModel:he(),getPaginationRowModel:ue(),getSortedRowModel:xe(),getFacetedRowModel:de(),getFacetedUniqueValues:ce()}),D=o=>{const i=o.original;P(i),h("update")};return e.jsxs("div",{className:"space-y-4",children:[e.jsx(Me,{table:p,storesData:s,selectedStoreUid:l,onStoreChange:m}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(U,{children:[e.jsx(E,{children:p.getHeaderGroups().map(o=>e.jsx(_,{children:o.headers.map(i=>e.jsx(b,{colSpan:i.colSpan,children:i.isPlaceholder?null:F(i.column.columnDef.header,i.getContext())},i.id))},o.id))}),e.jsx(K,{children:(v=p.getRowModel().rows)!=null&&v.length?p.getRowModel().rows.map(o=>e.jsx(_,{"data-state":o.getIsSelected()&&"selected",className:"cursor-pointer hover:bg-muted/50",onClick:()=>D(o),children:o.getVisibleCells().map(i=>e.jsx(j,{onClick:S=>{i.column.id==="actions"&&S.stopPropagation()},children:F(i.column.columnDef.cell,i.getContext())},i.id))},o.id)):e.jsx(_,{children:e.jsx(j,{colSpan:t.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]})}),e.jsx(ge,{table:p})]})}function Fe(){const[t,a]=g.useState("all"),{data:s}=O(),{promotions:l,isLoading:m,error:c,refetch:r}=Q({storeUid:t==="all"?void 0:t}),{open:u}=w(),f=!u||u!=="create"&&u!=="update";return c?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu khuyến mãi"}),e.jsx("button",{onClick:()=>r(),className:"text-primary text-sm hover:underline",children:"Thử lại"})]})}):e.jsxs(e.Fragment,{children:[f&&e.jsxs(e.Fragment,{children:[e.jsx(Y,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(te,{}),e.jsx(se,{}),e.jsx(ee,{})]})}),e.jsxs(Z,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Chương trình khuyến mãi"})}),e.jsx(Te,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[m&&e.jsx(Pe,{}),!m&&e.jsx(Re,{columns:ye,data:l,storesData:s,selectedStoreUid:t,onStoreChange:a})]})]})]}),e.jsx(ke,{})]})}function Ve(){return e.jsx(Ce,{children:e.jsx(Fe,{})})}const Ot=Ve;export{Ot as component};
