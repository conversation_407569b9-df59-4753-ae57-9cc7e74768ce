import{a3 as V,a4 as M,j as e,B as F,h as R,r as j,a as Y,l as G,b as X}from"./index-CfbMU4Ye.js";import"./pos-api-BBB_ZiZD.js";import"./vietqr-api-BHQxfNzq.js";import{u as ae}from"./use-combos-D8CSrAWY.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import{u as W}from"./useQuery-BvDWg4vp.js";import{u as B}from"./useMutation-C9PewMvL.js";import{d as $}from"./discount-form-context-Rw1r9z2U.js";import{Q as I}from"./query-keys-3lmd-xp6.js";import{H as re}from"./header-CiiJInbE.js";import{M as ce}from"./main-B69tr6A0.js";import{C as de}from"./index-TKFSyVOw.js";import{P as le}from"./profile-dropdown-HjZ6UGjk.js";import{S as he,T as me}from"./search-Bbt2JnTN.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{S as T,a as w,b as k,c as E,d as g,C as ue}from"./select-_nXsh5SU.js";import{D as xe,a as pe,b as ge,c as je}from"./dropdown-menu-8bnotEGr.js";import{B as Q}from"./badge-DNJz5hg4.js";import{T as _e,a as fe,b as z,c as b,d as ve,e as S}from"./table-C3v-r6-e.js";import{T as Ce}from"./trash-2-BgCVQZay.js";import{P as Se}from"./modal-D_ZqQrH_.js";import{C as U}from"./checkbox-CSFn543p.js";import"./utils-km2FGkQ4.js";import"./separator-DVvwOaSX.js";import"./dialog-FztlF_ds.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./avatar-CE3yFgmj.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./scroll-area-Bx6sgJqp.js";import"./index-D41EikqA.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./index-4DjKSQeL.js";const Ne=(o={})=>{const{params:i={}}=o;return W({queryKey:[I.DISCOUNT_PAYMENT,i],queryFn:async()=>{if(!i.company_uid||!i.brand_uid)throw new Error("Company UID and Brand UID are required");return(await $.getDiscountPayments(i)).data||[]}})},ye=()=>{const o=V();return B({mutationFn:i=>$.updateDiscountPayment(i),onSuccess:()=>{o.invalidateQueries({queryKey:[I.DISCOUNT_PAYMENT]}),M.success("Đã cập nhật trạng thái chiết khấu thành công!")},onError:i=>{console.error("Error updating discount payment:",i),M.error(i.message||"Lỗi khi cập nhật chiết khấu thanh toán")}})},De=()=>{const o=V();return B({mutationFn:i=>$.deleteDiscountPayment(i),onSuccess:()=>{o.invalidateQueries({queryKey:[I.DISCOUNT_PAYMENT]}),M.success("Đã xóa chiết khấu thanh toán thành công!")},onError:i=>{console.error("Error deleting discount payment:",i),M.error(i.message||"Lỗi khi xóa chiết khấu thanh toán")}})},Pe=o=>W({queryKey:[I.DISCOUNT_PAYMENT,"programs",o.company_uid,o.brand_uid,o.store_uid],queryFn:async()=>{if(!o.company_uid||!o.brand_uid||!o.store_uid)throw new Error("Missing required parameters");return $.getDiscountPaymentPrograms({company_uid:o.company_uid,brand_uid:o.brand_uid,store_uid:o.store_uid})},staleTime:2*60*1e3,gcTime:5*60*1e3}),be=()=>{const o=V();return B({mutationFn:$.cloneDiscountPayments,onSuccess:()=>{o.invalidateQueries({queryKey:[I.DISCOUNT_PAYMENT]}),M.success("Đã sao chép chiết khấu thanh toán thành công!")},onError:i=>{console.error("Error cloning discount payments:",i),M.error(i.message||"Lỗi khi sao chép chiết khấu thanh toán")}})};function Te({selectedStoreId:o,setSelectedStoreId:i,selectedPromotion:h,setSelectedPromotion:p,selectedStatus:d,setSelectedStatus:u,selectedExpiry:x,setSelectedExpiry:_,currentBrandStores:N,promotions:m,onCreateNew:l,onCopy:n}){return e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold whitespace-nowrap",children:"Chiết khấu thanh toán"}),e.jsx("div",{className:"min-w-[160px]",children:e.jsxs(T,{value:o,onValueChange:i,children:[e.jsx(w,{children:e.jsx(k,{placeholder:"Tất cả các điểm"})}),e.jsxs(E,{children:[e.jsx(g,{value:"all",children:"Tất cả các điểm"}),N.map(r=>e.jsx(g,{value:r.id,children:r.store_name},r.id))]})]})}),e.jsx("div",{className:"min-w-[160px]",children:e.jsxs(T,{value:h,onValueChange:p,children:[e.jsx(w,{children:e.jsx(k,{placeholder:"Tất cả các CKTM"})}),e.jsxs(E,{children:[e.jsx(g,{value:"all",children:"Tất cả các CKTM"}),m.map(r=>e.jsx(g,{value:r.promotion_id,children:r.promotion_name},r.promotion_id))]})]})}),e.jsx("div",{className:"min-w-[130px]",children:e.jsxs(T,{value:d,onValueChange:u,children:[e.jsx(w,{children:e.jsx(k,{placeholder:"Tất cả trạng thái"})}),e.jsxs(E,{children:[e.jsx(g,{value:"all",children:"Tất cả trạng thái"}),e.jsx(g,{value:"1",children:"Active"}),e.jsx(g,{value:"0",children:"Deactive"})]})]})}),e.jsx("div",{className:"min-w-[150px]",children:e.jsxs(T,{value:x,onValueChange:_,children:[e.jsx(w,{children:e.jsx(k,{placeholder:"Tất cả ngày áp dụng"})}),e.jsxs(E,{children:[e.jsx(g,{value:"all",children:"Tất cả ngày áp dụng"}),e.jsx(g,{value:"expired",children:"Hết hạn"}),e.jsx(g,{value:"unexpired",children:"Chưa hết hạn"})]})]})}),e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsxs(xe,{children:[e.jsx(pe,{asChild:!0,children:e.jsxs(F,{variant:"outline",size:"sm",children:["Tiện ích ",e.jsx(ue,{className:"ml-2 h-4 w-4"})]})}),e.jsx(ge,{children:e.jsx(je,{onClick:n,children:"Sao chép chiết khấu thanh toán"})})]}),e.jsx(F,{size:"sm",onClick:l,children:"Tạo chiết khấu"})]})]})}function we({discountPayments:o,isLoading:i,currentBrandStores:h,onToggleActive:p,onDelete:d,isDeleting:u}){const x=R(),_=n=>{const r=h.find(f=>f.id===n);return(r==null?void 0:r.store_name)||"Không xác định"},N=n=>{x({to:"/sale/discount-payment/detail/$id",params:{id:n}})},m=(n,r)=>{const f=new Date(n).toLocaleDateString("vi-VN"),c=new Date(r).toLocaleDateString("vi-VN");return`${f} - ${c}`},l=n=>n.type==="PERCENT"?`${n.value*100}%`:`${n.value.toLocaleString("vi-VN")} ₫`;return e.jsx("div",{className:"rounded-md border",children:e.jsxs(_e,{children:[e.jsx(fe,{children:e.jsxs(z,{children:[e.jsx(b,{className:"w-[50px]",children:"#"}),e.jsx(b,{children:"Tên"}),e.jsx(b,{children:"Cửa hàng"}),e.jsx(b,{children:"Thời gian áp dụng"}),e.jsx(b,{children:"Số tiền"}),e.jsx(b,{children:"Thao tác"}),e.jsx(b,{className:"w-[50px]"})]})}),e.jsx(ve,{children:i?e.jsx(z,{children:e.jsx(S,{colSpan:7,className:"h-24 text-center",children:"Đang tải dữ liệu..."})}):o.length===0?e.jsx(z,{children:e.jsx(S,{colSpan:7,className:"text-muted-foreground h-24 text-center",children:"Không có dữ liệu"})}):o.map((n,r)=>{var f;return e.jsxs(z,{className:"hover:bg-muted/50 cursor-pointer",onClick:()=>N(n.id),children:[e.jsx(S,{className:"font-medium",children:r+1}),e.jsx(S,{children:e.jsx("div",{className:"font-medium",children:((f=n.promotion)==null?void 0:f.promotion_name)||"N/A"})}),e.jsx(S,{children:_(n.store_uid)}),e.jsx(S,{children:e.jsx("div",{className:"space-y-1",children:n.to_date<Date.now()?e.jsx("div",{className:"font-medium text-red-600",children:"Hết hạn"}):e.jsx("div",{className:"text-sm",children:m(n.from_date,n.to_date)})})}),e.jsx(S,{className:"font-medium",children:l(n)}),e.jsx(S,{children:n.active===1?e.jsx(Q,{variant:"default",className:"cursor-pointer bg-green-100 text-green-800 hover:bg-green-200",onClick:c=>{c.stopPropagation(),p(n)},children:"Active"}):e.jsx(Q,{variant:"destructive",className:"cursor-pointer bg-red-100 text-red-800 hover:bg-red-200",onClick:c=>{c.stopPropagation(),p(n)},children:"Deactive"})}),e.jsx(S,{children:e.jsx(F,{variant:"ghost",size:"sm",onClick:c=>{c.stopPropagation(),d(n)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",disabled:u,children:e.jsx(Ce,{className:"h-4 w-4"})})})]},n.id)})})]})})}function ke({isOpen:o,onClose:i}){const[h,p]=j.useState(""),[d,u]=j.useState(new Set),[x,_]=j.useState(new Set),{currentBrandStores:N}=Y(),{selectedBrand:m}=G(),{company:l}=X(),n=be(),{data:r=[],isLoading:f}=Pe({company_uid:l==null?void 0:l.id,brand_uid:m==null?void 0:m.id,store_uid:h}),c=N.filter(t=>t.id!==h),A=t=>{const a=new Set(x);a.has(t)?a.delete(t):a.add(t),_(a)},v=t=>{const a=new Set(d);a.has(t)?a.delete(t):a.add(t),u(a)},C=()=>{d.size===c.length?u(new Set):u(new Set(c.map(t=>t.id)))},K=async()=>{if(!(!(l!=null&&l.id)||!(m!=null&&m.id)||!h||d.size===0||x.size===0))try{const t=Array.from(x),a=Array.from(d);console.log("🎯 Starting copy operation:",{programs:t.length,targetStores:a.length,sourceStore:h});for(const D of a){console.log(`📤 Copying ${t.length} programs to store: ${D}`);try{const P=await n.mutateAsync({company_uid:l.id,brand_uid:m.id,list_discount_payment_uid:t,store_uid_root:h,store_uid_target:D});console.log(`✅ Successfully copied to store: ${D}`,P)}catch(P){console.error(`❌ Failed to copy to store: ${D}`,P),console.warn("⚠️ Continuing with remaining stores...")}}y()}catch(t){console.error("Error copying discount payment programs:",t)}},y=()=>{p(""),u(new Set),_(new Set),i()},L=t=>t.type==="PERCENT"?`${t.value*100}%`:`${t.value.toLocaleString("vi-VN")} ₫`,q=r.filter(t=>x.has(t.id));return e.jsx(Se,{title:"Sao chép chiết khấu thanh toán",open:o,onOpenChange:y,onCancel:y,onConfirm:K,confirmText:"Sao chép",confirmDisabled:!h||d.size===0||x.size===0||n.isPending,maxWidth:"sm:max-w-6xl",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng nguồn"}),e.jsxs(T,{value:h,onValueChange:p,children:[e.jsx(w,{className:"mt-1",children:e.jsx(k,{placeholder:"Chọn cửa hàng nguồn"})}),e.jsx(E,{children:N.map(t=>e.jsx(g,{value:t.id,children:t.store_name},t.id))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chiết khấu thanh toán"}),e.jsx("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:f?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Đang tải..."}):r.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:h?"Không có chiết khấu thanh toán":"Chọn cửa hàng nguồn"}):e.jsx("div",{className:"space-y-3",children:r.map(t=>{var a;return e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(U,{id:t.id,checked:x.has(t.id),onCheckedChange:()=>A(t.id)}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-sm font-medium",children:((a=t.promotion)==null?void 0:a.promotion_name)||"Chiết khấu"}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Giảm ",L(t)]})]})]},t.id)})})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng đích"}),e.jsxs(T,{children:[e.jsx(w,{className:"mt-1",children:e.jsx(k,{placeholder:d.size===0?"Chọn cửa hàng đích":`Đã chọn ${d.size} cửa hàng`})}),e.jsx(E,{children:c.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chọn cửa hàng nguồn trước"}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-3 border-b px-2 py-1.5",children:[e.jsx(U,{id:"select-all-targets",checked:d.size===c.length&&c.length>0,onCheckedChange:C,onClick:t=>t.stopPropagation()}),e.jsx("label",{htmlFor:"select-all-targets",className:"flex-1 cursor-pointer text-sm font-medium",onClick:t=>{t.preventDefault(),C()},children:"Chọn tất cả"})]}),c.map(t=>e.jsxs("div",{className:"flex items-center space-x-3 px-2 py-1.5",children:[e.jsx(U,{id:`target-${t.id}`,checked:d.has(t.id),onCheckedChange:()=>v(t.id),onClick:a=>a.stopPropagation()}),e.jsx("label",{htmlFor:`target-${t.id}`,className:"flex-1 cursor-pointer text-sm font-medium",onClick:a=>{a.preventDefault(),v(t.id)},children:t.store_name})]},t.id))]})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chiết khấu thanh toán được chọn"}),e.jsx("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:q.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chưa chọn chiết khấu thanh toán nào"}):e.jsx("div",{className:"space-y-2",children:q.map(t=>{var a;return e.jsxs("div",{className:"bg-muted rounded-md p-2",children:[e.jsx("div",{className:"text-sm font-medium",children:((a=t.promotion)==null?void 0:a.promotion_name)||"Chiết khấu"}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Giảm ",L(t)]})]},t.id)})})})]})]})]})})}function Ee(){var H;const o=R(),[i,h]=j.useState("all"),[p,d]=j.useState("all"),[u,x]=j.useState("all"),[_,N]=j.useState("unexpired"),[m,l]=j.useState(!1),[n,r]=j.useState(null),[f,c]=j.useState(!1),{currentBrandStores:A}=Y(),{selectedBrand:v}=G(),{company:C}=X(),K=ye(),y=De(),L=i==="all"?A.map(s=>s.id):[i],{data:q=[]}=ae({storeUid:i==="all"?void 0:i}),{data:t=[],isLoading:a}=Ne({params:{company_uid:(C==null?void 0:C.id)||"",brand_uid:(v==null?void 0:v.id)||"",page:1,list_store_uid:L.join(",")}}),D=p==="all"?t:t.filter(s=>{var O;return((O=s.promotion)==null?void 0:O.promotion_id)===p}),P=u==="all"?D:D.filter(s=>s.active===parseInt(u)),J=_==="all"?P:_==="expired"?P.filter(s=>s.to_date<Date.now()):P.filter(s=>s.to_date>=Date.now()),Z=a;j.useEffect(()=>{d("all")},[i]);const ee=s=>{const O={id:s.id,value:s.value,type:s.type,from_amount:s.from_amount,to_amount:s.to_amount,from_date:s.from_date,to_date:s.to_date,time_sale_hour_day:s.time_sale_hour_day,time_sale_date_week:s.time_sale_date_week,is_membership:s.is_membership,is_coupon:s.is_coupon,description:s.description,extra_data:s.extra_data,active:s.active===1?0:1,revision:s.revision,sort:s.sort,membership_type_uid:s.membership_type_uid,promotion_uid:s.promotion_uid,brand_uid:s.brand_uid,company_uid:s.company_uid,discount_payment_clone_id:s.discount_payment_clone_id,deleted:s.deleted,created_by:s.created_by,updated_by:s.updated_by,deleted_by:s.deleted_by,created_at:s.created_at,updated_at:s.updated_at,deleted_at:s.deleted_at,store_uid:s.store_uid,promotion:s.promotion};K.mutate(O)},te=s=>{r(s),l(!0)},se=()=>{n&&y.mutate({id:n.id,company_uid:(C==null?void 0:C.id)||"",brand_uid:(v==null?void 0:v.id)||""}),l(!1),r(null)},ne=()=>{l(!1),r(null)},ie=()=>{o({to:"/sale/discount-payment/detail"})},oe=()=>{c(!0)};return e.jsxs(e.Fragment,{children:[e.jsx(re,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(he,{}),e.jsx(me,{}),e.jsx(le,{})]})}),e.jsx(ce,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(Te,{selectedStoreId:i,setSelectedStoreId:h,selectedPromotion:p,setSelectedPromotion:d,selectedStatus:u,setSelectedStatus:x,selectedExpiry:_,setSelectedExpiry:N,currentBrandStores:A,promotions:q,onCreateNew:ie,onCopy:oe}),e.jsx(we,{discountPayments:J,isLoading:Z,currentBrandStores:A,onToggleActive:ee,onDelete:te,isDeleting:y.isPending})]}),e.jsx(de,{open:m,onOpenChange:l,title:"Xác nhận xóa",content:`Bạn có chắc chắn muốn xóa chiết khấu "${(H=n==null?void 0:n.promotion)==null?void 0:H.promotion_name}"?`,onConfirm:se,onCancel:ne,confirmText:"Xóa",cancelText:"Hủy",isLoading:y.isPending}),e.jsx(ke,{isOpen:f,onClose:()=>c(!1)})]})})]})}const yt=Ee;export{yt as component};
