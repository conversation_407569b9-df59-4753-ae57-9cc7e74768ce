import{r as s,I as A,j as f,C as se,P as _,E as O,F as C,_ as P,Y as ce,c as U}from"./index-CVQ6JZo2.js";import{u as ae}from"./index-LVHINuqD.js";import{e as ie}from"./select-BFhNE0YE.js";function de(e,o){return s.useReducer((r,l)=>o[r][l]??r,e)}var V="ScrollArea",[$,_e]=se(V),[ue,p]=$(V),q=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,type:l="hover",dir:t,scrollHideDelay:n=600,...c}=e,[a,i]=s.useState(null),[h,d]=s.useState(null),[b,u]=s.useState(null),[S,m]=s.useState(null),[T,X]=s.useState(null),[x,L]=s.useState(0),[M,D]=s.useState(0),[j,y]=s.useState(!1),[N,W]=s.useState(!1),v=A(o,E=>i(E)),w=ae(t);return f.jsx(ue,{scope:r,type:l,dir:w,scrollHideDelay:n,scrollArea:a,viewport:h,onViewportChange:d,content:b,onContentChange:u,scrollbarX:S,onScrollbarXChange:m,scrollbarXEnabled:j,onScrollbarXEnabledChange:y,scrollbarY:T,onScrollbarYChange:X,scrollbarYEnabled:N,onScrollbarYEnabledChange:W,onCornerWidthChange:L,onCornerHeightChange:D,children:f.jsx(_.div,{dir:w,...c,ref:v,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":M+"px",...e.style}})})});q.displayName=V;var G="ScrollAreaViewport",J=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,children:l,nonce:t,...n}=e,c=p(G,r),a=s.useRef(null),i=A(o,a,c.onViewportChange);return f.jsxs(f.Fragment,{children:[f.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:t}),f.jsx(_.div,{"data-radix-scroll-area-viewport":"",...n,ref:i,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:f.jsx("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});J.displayName=G;var g="ScrollAreaScrollbar",K=s.forwardRef((e,o)=>{const{forceMount:r,...l}=e,t=p(g,e.__scopeScrollArea),{onScrollbarXEnabledChange:n,onScrollbarYEnabledChange:c}=t,a=e.orientation==="horizontal";return s.useEffect(()=>(a?n(!0):c(!0),()=>{a?n(!1):c(!1)}),[a,n,c]),t.type==="hover"?f.jsx(he,{...l,ref:o,forceMount:r}):t.type==="scroll"?f.jsx(fe,{...l,ref:o,forceMount:r}):t.type==="auto"?f.jsx(Q,{...l,ref:o,forceMount:r}):t.type==="always"?f.jsx(B,{...l,ref:o}):null});K.displayName=g;var he=s.forwardRef((e,o)=>{const{forceMount:r,...l}=e,t=p(g,e.__scopeScrollArea),[n,c]=s.useState(!1);return s.useEffect(()=>{const a=t.scrollArea;let i=0;if(a){const h=()=>{window.clearTimeout(i),c(!0)},d=()=>{i=window.setTimeout(()=>c(!1),t.scrollHideDelay)};return a.addEventListener("pointerenter",h),a.addEventListener("pointerleave",d),()=>{window.clearTimeout(i),a.removeEventListener("pointerenter",h),a.removeEventListener("pointerleave",d)}}},[t.scrollArea,t.scrollHideDelay]),f.jsx(O,{present:r||n,children:f.jsx(Q,{"data-state":n?"visible":"hidden",...l,ref:o})})}),fe=s.forwardRef((e,o)=>{const{forceMount:r,...l}=e,t=p(g,e.__scopeScrollArea),n=e.orientation==="horizontal",c=Y(()=>i("SCROLL_END"),100),[a,i]=de("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return s.useEffect(()=>{if(a==="idle"){const h=window.setTimeout(()=>i("HIDE"),t.scrollHideDelay);return()=>window.clearTimeout(h)}},[a,t.scrollHideDelay,i]),s.useEffect(()=>{const h=t.viewport,d=n?"scrollLeft":"scrollTop";if(h){let b=h[d];const u=()=>{const S=h[d];b!==S&&(i("SCROLL"),c()),b=S};return h.addEventListener("scroll",u),()=>h.removeEventListener("scroll",u)}},[t.viewport,n,i,c]),f.jsx(O,{present:r||a!=="hidden",children:f.jsx(B,{"data-state":a==="hidden"?"hidden":"visible",...l,ref:o,onPointerEnter:C(e.onPointerEnter,()=>i("POINTER_ENTER")),onPointerLeave:C(e.onPointerLeave,()=>i("POINTER_LEAVE"))})})}),Q=s.forwardRef((e,o)=>{const r=p(g,e.__scopeScrollArea),{forceMount:l,...t}=e,[n,c]=s.useState(!1),a=e.orientation==="horizontal",i=Y(()=>{if(r.viewport){const h=r.viewport.offsetWidth<r.viewport.scrollWidth,d=r.viewport.offsetHeight<r.viewport.scrollHeight;c(a?h:d)}},10);return R(r.viewport,i),R(r.content,i),f.jsx(O,{present:l||n,children:f.jsx(B,{"data-state":n?"visible":"hidden",...t,ref:o})})}),B=s.forwardRef((e,o)=>{const{orientation:r="vertical",...l}=e,t=p(g,e.__scopeScrollArea),n=s.useRef(null),c=s.useRef(0),[a,i]=s.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),h=te(a.viewport,a.content),d={...l,sizes:a,onSizesChange:i,hasThumb:h>0&&h<1,onThumbChange:u=>n.current=u,onThumbPointerUp:()=>c.current=0,onThumbPointerDown:u=>c.current=u};function b(u,S){return we(u,c.current,a,S)}return r==="horizontal"?f.jsx(be,{...d,ref:o,onThumbPositionChange:()=>{if(t.viewport&&n.current){const u=t.viewport.scrollLeft,S=F(u,a,t.dir);n.current.style.transform=`translate3d(${S}px, 0, 0)`}},onWheelScroll:u=>{t.viewport&&(t.viewport.scrollLeft=u)},onDragScroll:u=>{t.viewport&&(t.viewport.scrollLeft=b(u,t.dir))}}):r==="vertical"?f.jsx(Se,{...d,ref:o,onThumbPositionChange:()=>{if(t.viewport&&n.current){const u=t.viewport.scrollTop,S=F(u,a);n.current.style.transform=`translate3d(0, ${S}px, 0)`}},onWheelScroll:u=>{t.viewport&&(t.viewport.scrollTop=u)},onDragScroll:u=>{t.viewport&&(t.viewport.scrollTop=b(u))}}):null}),be=s.forwardRef((e,o)=>{const{sizes:r,onSizesChange:l,...t}=e,n=p(g,e.__scopeScrollArea),[c,a]=s.useState(),i=s.useRef(null),h=A(o,i,n.onScrollbarXChange);return s.useEffect(()=>{i.current&&a(getComputedStyle(i.current))},[i]),f.jsx(ee,{"data-orientation":"horizontal",...t,ref:h,sizes:r,style:{bottom:0,left:n.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:n.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":I(r)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.x),onDragScroll:d=>e.onDragScroll(d.x),onWheelScroll:(d,b)=>{if(n.viewport){const u=n.viewport.scrollLeft+d.deltaX;e.onWheelScroll(u),le(u,b)&&d.preventDefault()}},onResize:()=>{i.current&&n.viewport&&c&&l({content:n.viewport.scrollWidth,viewport:n.viewport.offsetWidth,scrollbar:{size:i.current.clientWidth,paddingStart:H(c.paddingLeft),paddingEnd:H(c.paddingRight)}})}})}),Se=s.forwardRef((e,o)=>{const{sizes:r,onSizesChange:l,...t}=e,n=p(g,e.__scopeScrollArea),[c,a]=s.useState(),i=s.useRef(null),h=A(o,i,n.onScrollbarYChange);return s.useEffect(()=>{i.current&&a(getComputedStyle(i.current))},[i]),f.jsx(ee,{"data-orientation":"vertical",...t,ref:h,sizes:r,style:{top:0,right:n.dir==="ltr"?0:void 0,left:n.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":I(r)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.y),onDragScroll:d=>e.onDragScroll(d.y),onWheelScroll:(d,b)=>{if(n.viewport){const u=n.viewport.scrollTop+d.deltaY;e.onWheelScroll(u),le(u,b)&&d.preventDefault()}},onResize:()=>{i.current&&n.viewport&&c&&l({content:n.viewport.scrollHeight,viewport:n.viewport.offsetHeight,scrollbar:{size:i.current.clientHeight,paddingStart:H(c.paddingTop),paddingEnd:H(c.paddingBottom)}})}})}),[ve,Z]=$(g),ee=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,sizes:l,hasThumb:t,onThumbChange:n,onThumbPointerUp:c,onThumbPointerDown:a,onThumbPositionChange:i,onDragScroll:h,onWheelScroll:d,onResize:b,...u}=e,S=p(g,r),[m,T]=s.useState(null),X=A(o,v=>T(v)),x=s.useRef(null),L=s.useRef(""),M=S.viewport,D=l.content-l.viewport,j=P(d),y=P(i),N=Y(b,10);function W(v){if(x.current){const w=v.clientX-x.current.left,E=v.clientY-x.current.top;h({x:w,y:E})}}return s.useEffect(()=>{const v=w=>{const E=w.target;(m==null?void 0:m.contains(E))&&j(w,D)};return document.addEventListener("wheel",v,{passive:!1}),()=>document.removeEventListener("wheel",v,{passive:!1})},[M,m,D,j]),s.useEffect(y,[l,y]),R(m,N),R(S.content,N),f.jsx(ve,{scope:r,scrollbar:m,hasThumb:t,onThumbChange:P(n),onThumbPointerUp:P(c),onThumbPositionChange:y,onThumbPointerDown:P(a),children:f.jsx(_.div,{...u,ref:X,style:{position:"absolute",...u.style},onPointerDown:C(e.onPointerDown,v=>{v.button===0&&(v.target.setPointerCapture(v.pointerId),x.current=m.getBoundingClientRect(),L.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",S.viewport&&(S.viewport.style.scrollBehavior="auto"),W(v))}),onPointerMove:C(e.onPointerMove,W),onPointerUp:C(e.onPointerUp,v=>{const w=v.target;w.hasPointerCapture(v.pointerId)&&w.releasePointerCapture(v.pointerId),document.body.style.webkitUserSelect=L.current,S.viewport&&(S.viewport.style.scrollBehavior=""),x.current=null})})})}),z="ScrollAreaThumb",re=s.forwardRef((e,o)=>{const{forceMount:r,...l}=e,t=Z(z,e.__scopeScrollArea);return f.jsx(O,{present:r||t.hasThumb,children:f.jsx(me,{ref:o,...l})})}),me=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,style:l,...t}=e,n=p(z,r),c=Z(z,r),{onThumbPositionChange:a}=c,i=A(o,b=>c.onThumbChange(b)),h=s.useRef(void 0),d=Y(()=>{h.current&&(h.current(),h.current=void 0)},100);return s.useEffect(()=>{const b=n.viewport;if(b){const u=()=>{if(d(),!h.current){const S=ge(b,a);h.current=S,a()}};return a(),b.addEventListener("scroll",u),()=>b.removeEventListener("scroll",u)}},[n.viewport,d,a]),f.jsx(_.div,{"data-state":c.hasThumb?"visible":"hidden",...t,ref:i,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:C(e.onPointerDownCapture,b=>{const S=b.target.getBoundingClientRect(),m=b.clientX-S.left,T=b.clientY-S.top;c.onThumbPointerDown({x:m,y:T})}),onPointerUp:C(e.onPointerUp,c.onThumbPointerUp)})});re.displayName=z;var k="ScrollAreaCorner",oe=s.forwardRef((e,o)=>{const r=p(k,e.__scopeScrollArea),l=!!(r.scrollbarX&&r.scrollbarY);return r.type!=="scroll"&&l?f.jsx(pe,{...e,ref:o}):null});oe.displayName=k;var pe=s.forwardRef((e,o)=>{const{__scopeScrollArea:r,...l}=e,t=p(k,r),[n,c]=s.useState(0),[a,i]=s.useState(0),h=!!(n&&a);return R(t.scrollbarX,()=>{var b;const d=((b=t.scrollbarX)==null?void 0:b.offsetHeight)||0;t.onCornerHeightChange(d),i(d)}),R(t.scrollbarY,()=>{var b;const d=((b=t.scrollbarY)==null?void 0:b.offsetWidth)||0;t.onCornerWidthChange(d),c(d)}),h?f.jsx(_.div,{...l,ref:o,style:{width:n,height:a,position:"absolute",right:t.dir==="ltr"?0:void 0,left:t.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function H(e){return e?parseInt(e,10):0}function te(e,o){const r=e/o;return isNaN(r)?0:r}function I(e){const o=te(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,l=(e.scrollbar.size-r)*o;return Math.max(l,18)}function we(e,o,r,l="ltr"){const t=I(r),n=t/2,c=o||n,a=t-c,i=r.scrollbar.paddingStart+c,h=r.scrollbar.size-r.scrollbar.paddingEnd-a,d=r.content-r.viewport,b=l==="ltr"?[0,d]:[d*-1,0];return ne([i,h],b)(e)}function F(e,o,r="ltr"){const l=I(o),t=o.scrollbar.paddingStart+o.scrollbar.paddingEnd,n=o.scrollbar.size-t,c=o.content-o.viewport,a=n-l,i=r==="ltr"?[0,c]:[c*-1,0],h=ie(e,i);return ne([0,c],[0,a])(h)}function ne(e,o){return r=>{if(e[0]===e[1]||o[0]===o[1])return o[0];const l=(o[1]-o[0])/(e[1]-e[0]);return o[0]+l*(r-e[0])}}function le(e,o){return e>0&&e<o}var ge=(e,o=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},l=0;return function t(){const n={left:e.scrollLeft,top:e.scrollTop},c=r.left!==n.left,a=r.top!==n.top;(c||a)&&o(),r=n,l=window.requestAnimationFrame(t)}(),()=>window.cancelAnimationFrame(l)};function Y(e,o){const r=P(e),l=s.useRef(0);return s.useEffect(()=>()=>window.clearTimeout(l.current),[]),s.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(r,o)},[r,o])}function R(e,o){const r=P(o);ce(()=>{let l=0;if(e){const t=new ResizeObserver(()=>{cancelAnimationFrame(l),l=window.requestAnimationFrame(r)});return t.observe(e),()=>{window.cancelAnimationFrame(l),t.unobserve(e)}}},[e,r])}var xe=q,Pe=J,Ce=oe;function Le({className:e,children:o,orientation:r="vertical",...l}){return f.jsxs(xe,{"data-slot":"scroll-area",className:U("relative",e),...l,children:[f.jsx(Pe,{"data-slot":"scroll-area-viewport",className:U("focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",r==="horizontal"&&"overflow-x-auto!"),children:o}),f.jsx(Ee,{orientation:r}),f.jsx(Ce,{})]})}function Ee({className:e,orientation:o="vertical",...r}){return f.jsx(K,{"data-slot":"scroll-area-scrollbar",orientation:o,className:U("flex touch-none p-px transition-colors select-none",o==="vertical"&&"h-full w-2.5 border-l border-l-transparent",o==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent",e),...r,children:f.jsx(re,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}export{Le as S,Ee as a};
