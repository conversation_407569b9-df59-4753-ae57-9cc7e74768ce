import{j as s}from"./index-UcdZ5AHH.js";import{S as e}from"./skeleton-B5wLl279.js";function r({title:c="Lỗi: Không tìm thấy ID customization",description:a="URL không hợp lệ"}){return s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsx("div",{className:"flex h-64 items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"mb-4 text-lg text-red-600",children:c}),s.jsx("div",{className:"text-sm text-gray-600",children:a})]})})})}function m({title:c="Lỗi tải dữ liệu",description:a="Không thể tải thông tin customization"}){return s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsx("div",{className:"flex h-64 items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"mb-4 text-lg text-red-600",children:c}),s.jsx("div",{className:"text-sm text-gray-600",children:a})]})})})}function n(){return s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"mb-8",children:[s.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[s.jsx(e,{className:"h-8 w-8"}),s.jsx(e,{className:"h-10 w-24"})]}),s.jsx("div",{className:"text-center",children:s.jsx(e,{className:"mx-auto h-8 w-48"})})]}),s.jsx("div",{className:"mx-auto max-w-4xl",children:s.jsx("div",{className:"p-6",children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx(e,{className:"h-4 w-48"}),s.jsx(e,{className:"h-10 flex-1"})]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx(e,{className:"h-4 w-48"}),s.jsx(e,{className:"h-10 flex-1"})]}),s.jsxs("div",{className:"space-y-4 pt-6",children:[s.jsx(e,{className:"h-6 w-64"}),s.jsx(e,{className:"h-16 w-full"})]}),s.jsx("div",{className:"flex justify-center pt-4",children:s.jsx(e,{className:"h-10 w-24"})}),s.jsxs("div",{className:"space-y-6 pt-6",children:[s.jsx(e,{className:"h-6 w-48"}),Array.from({length:2}).map((c,a)=>s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(e,{className:"h-5 w-64"}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx(e,{className:"h-8 w-12"}),s.jsx(e,{className:"h-8 w-12"})]})]}),s.jsx("div",{className:"grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4",children:Array.from({length:4}).map((i,l)=>s.jsx(e,{className:"h-20 w-full"},l))})]},a))]})]})})})]})})}export{n as L,r as M,m as a};
