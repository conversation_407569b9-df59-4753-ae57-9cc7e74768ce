import{j as e,f as A,B as H,r as g,u as se,a4 as d}from"./index-CfbMU4Ye.js";import"./pos-api-BBB_ZiZD.js";import{g as F}from"./stores-api-3ul-JRE8.js";import{a as oe,b as ne,c as re,d as ae,e as ce,r as U}from"./use-removed-items-DWRDzX0n.js";import{g as N}from"./error-utils-BYcz3jZ5.js";import{utils as R,writeFile as V}from"./xlsx-DkH2s96g.js";import{u as ie}from"./use-stores-Cb_kvevV.js";import"./vietqr-api-BHQxfNzq.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import{H as le}from"./header-CiiJInbE.js";import{M as me}from"./main-B69tr6A0.js";import{C as de}from"./index-TKFSyVOw.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{P as he}from"./profile-dropdown-HjZ6UGjk.js";import{S as ue,T as pe}from"./search-Bbt2JnTN.js";import{C as X}from"./checkbox-CSFn543p.js";import{I as ge}from"./IconRestore-C9584EVd.js";import{f as v}from"./isSameMonth-C8JQo-AN.js";import{u as xe,e as fe,f as z}from"./index-DrO-sOnq.js";import{T as Se,a as we,b as E,c as je,d as ye,e as G}from"./table-C3v-r6-e.js";import{D as be,a as Re,b as Ie,c as O}from"./dropdown-menu-8bnotEGr.js";import{S as Me,a as _e,b as Ce,c as Ne,d as ve}from"./select-_nXsh5SU.js";import{I as De}from"./input-D8TU6hMD.js";import{I as P}from"./IconDownload-Be7ISuXh.js";import{I as ke}from"./IconChevronDown-COoXBaJ3.js";import"./useQuery-BvDWg4vp.js";import"./utils-km2FGkQ4.js";import"./useMutation-C9PewMvL.js";import"./query-keys-3lmd-xp6.js";import"./separator-DVvwOaSX.js";import"./dialog-FztlF_ds.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./avatar-CE3yFgmj.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./search-N2Gpb9W7.js";import"./createReactComponent-CVG1We1Z.js";import"./scroll-area-Bx6sgJqp.js";import"./index-D41EikqA.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./index-4DjKSQeL.js";const Te=[{id:"select",header:({table:t})=>e.jsx(X,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:s=>t.toggleAllPageRowsSelected(!!s),"aria-label":"Select all"}),cell:({row:t})=>e.jsx(X,{checked:t.getIsSelected(),onCheckedChange:s=>t.toggleSelected(!!s),"aria-label":"Select row"}),enableSorting:!1,enableHiding:!1},{accessorKey:"id",header:"#",cell:({row:t})=>{const s=t.index+1;return e.jsx("div",{className:"w-[50px] font-medium",children:s})},enableSorting:!1},{accessorKey:"item_id",header:"Mã món",cell:({row:t})=>{const s=t.original;return e.jsx("span",{className:"font-mono text-sm",children:s.item_id})}},{accessorKey:"item_name",header:"Tên món",cell:({row:t})=>{const s=t.original;return e.jsx("span",{className:"font-medium",children:s.item_name})}},{accessorKey:"price",header:"Giá",cell:({row:t})=>{const s=t.original;return e.jsx("span",{children:A(s.ots_price)})}},{accessorKey:"deleted_by",header:"Người xoá",cell:({row:t})=>{const s=t.original;return e.jsx("span",{children:s.deleted_by})}},{accessorKey:"deleted_at",header:"Thời gian xoá",cell:({row:t})=>{const s=t.original;return e.jsx("span",{children:v(new Date(s.deleted_at*1e3),"dd/MM/yyyy HH:mm")})}},{id:"restore",header:"",cell:({row:t,table:s})=>{const h=t.original,r=s.options.meta;return e.jsxs(H,{variant:"ghost",size:"sm",onClick:()=>{var o;return(o=r==null?void 0:r.onRestoreItem)==null?void 0:o.call(r,h)},className:"h-8 w-8 p-0 text-blue-600 hover:text-blue-700",title:"Khôi phục món",children:[e.jsx(ge,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Khôi phục món ",h.item_name]})]})}}];function Ke({columns:t,data:s,onRestoreItem:h,onBulkRestore:r,clearSelection:o}){var u;const[x,c]=g.useState({});g.useEffect(()=>{o&&c({})},[o]);const i=xe({data:s,columns:t,getCoreRowModel:fe(),enableRowSelection:!0,onRowSelectionChange:c,state:{rowSelection:x},meta:{onRestoreItem:h}}),n=i.getFilteredSelectedRowModel().rows.map(m=>m.original);return e.jsxs("div",{className:"space-y-4",children:[n.length>0&&e.jsxs("div",{className:"flex items-center justify-between rounded-md border p-4",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("span",{className:"text-muted-foreground text-sm",children:["Đã chọn ",n.length," món"]})}),e.jsx(H,{size:"sm",onClick:()=>r==null?void 0:r(n),className:"bg-blue-600 hover:bg-blue-700",children:"Khôi phục các món đã chọn"})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(Se,{children:[e.jsx(we,{children:i.getHeaderGroups().map(m=>e.jsx(E,{children:m.headers.map(p=>e.jsx(je,{children:p.isPlaceholder?null:z(p.column.columnDef.header,p.getContext())},p.id))},m.id))}),e.jsx(ye,{children:(u=i.getRowModel().rows)!=null&&u.length?i.getRowModel().rows.map(m=>e.jsx(E,{"data-state":m.getIsSelected()&&"selected",children:m.getVisibleCells().map(p=>e.jsx(G,{children:z(p.column.columnDef.cell,p.getContext())},p.id))},m.id)):e.jsx(E,{children:e.jsx(G,{colSpan:t.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]})})]})}function Ee({searchQuery:t,onSearchQueryChange:s,onSearchSubmit:h,selectedStoreId:r,onStoreChange:o,stores:x,isExporting:c,onExportBySelectedStore:i,onExportAllStores:f}){return e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Món đã xóa tại cửa hàng"}),e.jsx(De,{placeholder:"Tìm kiếm món đã xóa...",className:"w-64",value:t,onChange:n=>s(n.target.value),onKeyDown:n=>{n.key==="Enter"&&(n.preventDefault(),h(t))}}),e.jsxs(Me,{value:r,onValueChange:o,children:[e.jsx(_e,{className:"w-48",children:e.jsx(Ce,{placeholder:"Chọn cửa hàng"})}),e.jsx(Ne,{children:x.map(n=>e.jsx(ve,{value:n.id,children:n.name},n.id))})]})]}),e.jsxs(be,{children:[e.jsx(Re,{asChild:!0,children:e.jsxs(H,{size:"sm",disabled:c,children:[e.jsx(P,{className:"mr-2 h-4 w-4"}),c?"Đang xuất...":"Xuất báo cáo",e.jsx(ke,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(Ie,{align:"end",children:[e.jsxs(O,{onClick:i,children:[e.jsx(P,{className:"mr-2 h-4 w-4"}),"Xuất báo cáo theo cửa hàng đã chọn"]}),e.jsxs(O,{onClick:f,children:[e.jsx(P,{className:"mr-2 h-4 w-4"}),"Xuất báo cáo tất cả cửa hàng"]})]})]})]})}function Pe(t,s){const h=R.book_new(),r=[];r.push([`Món đã xóa tại cửa hàng ${s}`]),r.push(["Mã món","Tên món","Giá","Người xóa","Thời gian xóa"]),t.forEach(c=>{const i=c.item_id||"",f=c.item_name||"",n=A(c.ots_price),u=c.deleted_by||"",m=v(new Date(c.deleted_at*1e3),"dd/MM/yyyy HH:mm");r.push([i,f,n,u,m])});const o=R.aoa_to_sheet(r);o["!cols"]=[{wch:15},{wch:30},{wch:15},{wch:25},{wch:20}],o["!merges"]=[{s:{r:0,c:0},e:{r:0,c:4}}],R.book_append_sheet(h,o,"Món đã xóa"),V(h,"item-removed-in-store.xlsx")}function Ae(t){const s=R.book_new();t.forEach(({store:r,data:o})=>{const x=`${r.fb_store_id}-${r.store_name}`,c=[];c.push([`Món đã xóa tại cửa hàng ${x}`]),c.push(["Mã món","Tên món","Giá","Người xóa","Thời gian xóa"]),o.forEach(n=>{const u=n.item_id||"",m=n.item_name||"",p=A(n.ots_price),I=n.deleted_by||"",D=v(new Date(n.deleted_at*1e3),"dd/MM/yyyy HH:mm");c.push([u,m,p,I,D])});const i=R.aoa_to_sheet(c);i["!cols"]=[{wch:15},{wch:30},{wch:15},{wch:25},{wch:20}],i["!merges"]=[{s:{r:0,c:0},e:{r:0,c:4}}];const f=x.replace(/[\\/?*[\]]/g,"").substring(0,31);R.book_append_sheet(s,i,f)});const h=`mon-da-xoa-tat-ca-cua-hang-${v(new Date,"dd-MM-yyyy")}.xlsx`;V(s,h)}function He(){const[t,s]=g.useState(""),[h,r]=g.useState(""),[o,x]=g.useState(""),[c,i]=g.useState(!1),[f,n]=g.useState(null),[u,m]=g.useState([]),[p,I]=g.useState(!1),D=oe(),k=ne(),L=re(),Q=ae(),{data:j=[]}=ie(),{company:S,brands:y}=se(l=>l.auth);g.useMemo(()=>{j.length>0&&!o&&x(j[0].id)},[j,o]);const W=g.useMemo(()=>o?[o]:[],[o]),{data:q,isLoading:$,error:M}=ce({searchTerm:t||void 0,listStoreUid:W}),J=l=>{n(l),m([]),i(!0)},Y=async()=>{try{if(u.length>0){const l=u.map(a=>a.id);await k.mutateAsync(l),d.success(`${u.length} món đã được khôi phục thành công!`),m([]),I(!0),setTimeout(()=>I(!1),100)}else f&&(await k.mutateAsync([f.id]),d.success(`Món "${f.item_name}" đã được khôi phục thành công!`),n(null));i(!1)}catch(l){const a=N(l);d.error(a)}},Z=l=>{m(l),n(null),i(!0)},ee=async()=>{if(!o){d.error("Không có cửa hàng nào được chọn");return}if(!j.find(a=>a.id===o)){d.error("Không tìm thấy thông tin cửa hàng");return}try{const a=y==null?void 0:y[0];if(!(S!=null&&S.id)||!(a!=null&&a.id)){d.error("Không tìm thấy thông tin công ty hoặc thương hiệu");return}const C=(await F({company_uid:S.id,brand_uid:a.id})).data.find(K=>K.id===o);if(!C){d.error("Không tìm thấy thông tin chi tiết cửa hàng");return}const w=await U.getRemovedItems({listStoreUid:[o]});if(!w||w.length===0){d.error("Không có dữ liệu món đã xóa để xuất báo cáo");return}const T=`${C.fb_store_id}-${C.store_name}`;Pe(w,T),d.success("Báo cáo theo cửa hàng đã được xuất thành công!")}catch(a){const _=N(a);d.error(_)}},te=async()=>{if(j.length===0){d.error("Không có cửa hàng nào để xuất báo cáo");return}try{const l=y==null?void 0:y[0];if(!(S!=null&&S.id)||!(l!=null&&l.id)){d.error("Không tìm thấy thông tin công ty hoặc thương hiệu");return}const a=await F({company_uid:S.id,brand_uid:l.id});if(!a.data||a.data.length===0){d.error("Không tìm thấy danh sách cửa hàng");return}const _=a.data.map(async b=>{try{const B=await U.getRemovedItems({listStoreUid:[b.id]});return{store:b,data:B||[],success:!0}}catch{return{store:b,data:[],success:!1}}}),w=(await Promise.all(_)).filter(b=>b.success&&b.data.length>0);if(w.length===0){d.error("Không có dữ liệu món đã xóa từ bất kỳ cửa hàng nào");return}Ae(w);const T=a.data.length,K=w.length;d.success(`Báo cáo tất cả cửa hàng đã được xuất thành công! (${K}/${T} cửa hàng có dữ liệu)`)}catch(l){const a=N(l);d.error(a)}};return e.jsxs(e.Fragment,{children:[e.jsx(le,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ue,{}),e.jsx(pe,{}),e.jsx(he,{})]})}),e.jsx(me,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Ee,{searchQuery:h,onSearchQueryChange:r,onSearchSubmit:s,selectedStoreId:o,onStoreChange:x,stores:j,isExporting:L.isPending||Q.isPending,onExportBySelectedStore:ee,onExportAllStores:te}),M&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:N(M)})}),!M&&$&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải dữ liệu món đã xóa..."})}),!M&&!$&&e.jsx(Ke,{columns:Te,data:q||[],onRestoreItem:J,onBulkRestore:Z,clearSelection:p}),e.jsx(de,{open:c,onOpenChange:i,content:u.length>0?`Bạn có muốn khôi phục ${u.length} món đã chọn?`:"Bạn có muốn khôi phục?",confirmText:"Xác nhận",onConfirm:Y,isLoading:D.isPending||k.isPending})]})})]})}const Pt=He;export{Pt as component};
