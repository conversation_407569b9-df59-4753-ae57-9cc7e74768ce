import{u as y}from"./useQuery-B4yhTgGk.js";import{a3 as _}from"./index-UcdZ5AHH.js";import{u as m}from"./useMutation-q12VR5WX.js";import{b as p}from"./vietqr-api-9FERZtmQ.js";import{a as c}from"./pos-api-j20LMGrC.js";import{Q as u}from"./query-keys-3lmd-xp6.js";const S=async(a={})=>{var i;const t=localStorage.getItem("pos_user_data"),e=localStorage.getItem("pos_brands_data");let r="",s="";if(t)try{r=JSON.parse(t).company_uid||""}catch{}if(e)try{const n=JSON.parse(e);Array.isArray(n)&&n.length>0&&(s=n[0].id||"")}catch{}if(!r||!s)throw new Error("Company or brand UID not found in localStorage");const d=new URLSearchParams({company_uid:r,brand_uid:s});a.skip_limit?d.append("skip_limit","true"):d.append("page",(a.page||1).toString()),a.results_per_page&&d.append("results_per_page",a.results_per_page.toString()),a.searchTerm&&d.append("search",a.searchTerm);const o=await c.get(`/mdata/v1/item-classes?${d.toString()}`);return(i=o.data)!=null&&i.data?o.data.data.map(n=>p(n)):[]},g=async a=>{var e;const t=await c.get(`/mdata/v1/item-class?id=${a}`);if((e=t.data)!=null&&e.data)return p(t.data.data);throw new Error("Item class not found")},f=async a=>{var i;const t=localStorage.getItem("pos_user_data"),e=localStorage.getItem("pos_brands_data");let r="",s="";if(t)try{r=JSON.parse(t).company_uid||""}catch{}if(e)try{const n=JSON.parse(e);Array.isArray(n)&&n.length>0&&(s=n[0].id||"")}catch{}if(!r||!s)throw new Error("Company or brand UID not found in localStorage");const d={item_class_name:a.item_class_name,item_class_id:a.item_class_id||`ITEM_CLASS-${Math.random().toString(36).substring(2,6).toUpperCase()}`,company_uid:r,brand_uid:s,list_item:a.list_item||[],sort:1e3},o=await c.post("/mdata/v1/item-classes",d);if((i=o.data)!=null&&i.data)return p(o.data.data);throw new Error("Failed to create item class")},I=async a=>{var r;const t={id:a.id,item_class_id:a.item_class_id,item_class_name:a.item_class_name,description:a.description,sort:a.sort,extra_data:a.extra_data,active:a.active,revision:a.revision,brand_uid:a.brand_uid,company_uid:a.company_uid,created_by:a.created_by,updated_by:a.updated_by,deleted_by:a.deleted_by,created_at:a.created_at,updated_at:a.updated_at,deleted_at:a.deleted_at,deleted:a.deleted,list_item:a.list_item||[]},e=await c.put("/mdata/v1/item-class",t);if((r=e.data)!=null&&r.data)return p(e.data.data);throw new Error("Failed to update item class")},b=async a=>{const t=localStorage.getItem("pos_user_data"),e=localStorage.getItem("pos_brands_data");let r="",s="";if(t)try{r=JSON.parse(t).company_uid||""}catch{}if(e)try{const o=JSON.parse(e);Array.isArray(o)&&o.length>0&&(s=o[0].id||"")}catch{}if(!r||!s)throw new Error("Company or brand UID not found in localStorage");const d=new URLSearchParams({company_uid:r,brand_uid:s,id:a});await c.delete(`/mdata/v1/item-class?${d.toString()}`)},l={getItemClasses:S,getItemClass:g,createItemClass:f,updateItemClass:I,deleteItemClass:b};function A(a={}){const{enabled:t=!0,...e}=a;return y({queryKey:[u.ITEM_CLASSES,e],queryFn:()=>l.getItemClasses(e),enabled:t,staleTime:5*60*1e3,gcTime:10*60*1e3})}function v(a,t=!0){return y({queryKey:[u.ITEM_CLASSES,"detail",a],queryFn:()=>l.getItemClass(a),enabled:t&&!!a,staleTime:5*60*1e3,gcTime:10*60*1e3})}function T(){const a=_();return m({mutationFn:t=>l.createItemClass(t),onSuccess:()=>{a.invalidateQueries({queryKey:[u.ITEM_CLASSES]})}})}function q(){const a=_();return m({mutationFn:t=>l.updateItemClass(t),onSuccess:()=>{a.invalidateQueries({queryKey:[u.ITEM_CLASSES]})}})}function L(){const a=_();return m({mutationFn:t=>l.deleteItemClass(t),onSuccess:()=>{a.invalidateQueries({queryKey:[u.ITEM_CLASSES]})}})}export{q as a,L as b,T as c,v as d,A as u};
