import{j as e,B as F,r as f,a4 as g,u as Y,h as q}from"./index-CfbMU4Ye.js";import"./pos-api-BBB_ZiZD.js";import{u as G}from"./use-stores-Cb_kvevV.js";import{a as J,i as Z,b as ee,c as te}from"./use-item-types-mN8TSC7t.js";import"./vietqr-api-BHQxfNzq.js";import{u as ae,a as se}from"./use-item-categories-CKFGimTQ.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import{H as ne}from"./header-CiiJInbE.js";import{M as re}from"./main-B69tr6A0.js";import{C as oe}from"./index-TKFSyVOw.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{g as ie,I as le,S as ce}from"./id-generators-qrU0VDV5.js";import{P as me}from"./profile-dropdown-HjZ6UGjk.js";import{S as de,T as he}from"./search-Bbt2JnTN.js";import{S as pe}from"./status-badge-BBBMKdZV.js";import{I as ue}from"./IconTrash-NV_v0NzY.js";import{u as xe,e as ge,f as $}from"./index-DrO-sOnq.js";import{S as fe,a as ye}from"./scroll-area-Bx6sgJqp.js";import{T as X,a as z,b as D,c as U,d as W,e as E}from"./table-C3v-r6-e.js";import{D as je,a as Ce,b as Se,c as K}from"./dropdown-menu-8bnotEGr.js";import{I as ve}from"./input-D8TU6hMD.js";import{S as B,a as H,b as V,c as Q,d as A}from"./select-_nXsh5SU.js";import{read as Ie,utils as Te}from"./xlsx-DkH2s96g.js";import{P as we}from"./modal-D_ZqQrH_.js";import{I as Ne}from"./IconUpload-WlthH4Pc.js";import{I as be}from"./IconChevronDown-COoXBaJ3.js";import{I as _e}from"./IconDownload-Be7ISuXh.js";import{I as De}from"./IconPlus-CiQ0nQi0.js";import{u as Me}from"./useQuery-BvDWg4vp.js";import{Q as ke}from"./query-keys-3lmd-xp6.js";import"./useMutation-C9PewMvL.js";import"./utils-km2FGkQ4.js";import"./stores-api-3ul-JRE8.js";import"./separator-DVvwOaSX.js";import"./dialog-FztlF_ds.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./popover-C4SSkcaE.js";import"./createReactComponent-CVG1We1Z.js";import"./skeleton-HVXRv3hO.js";import"./avatar-CE3yFgmj.js";import"./search-context-DXPkaUlN.js";import"./command-Jt-qPT7s.js";import"./search-N2Gpb9W7.js";import"./IconChevronRight-1SGwHwL2.js";import"./IconSearch-cKq6-nw5.js";import"./badge-DNJz5hg4.js";import"./index-D41EikqA.js";import"./index-4DjKSQeL.js";import"./check-C1W3FWto.js";import"./index-Cqf6DKEV.js";const Fe=[{accessorKey:"id",header:"#",cell:({row:t})=>{const i=t.index+1;return e.jsx("div",{className:"w-[50px] font-medium",children:i})},enableSorting:!1},{accessorKey:"item_type_id",header:"Mã nhóm",cell:({row:t})=>{const i=t.original;return e.jsx("span",{className:"font-mono text-sm",children:i.item_type_id})}},{accessorKey:"item_type_name",header:"Tên nhóm",cell:({row:t})=>{const i=t.original;return e.jsx("span",{className:"font-medium",children:i.item_type_name})}},{accessorKey:"active",header:"Thao tác",cell:({row:t,table:i})=>{const s=t.original,r=i.options.meta;return e.jsx("div",{onClick:o=>{var a;o.stopPropagation(),(a=r==null?void 0:r.onToggleStatus)==null||a.call(r,s)},className:"cursor-pointer",children:e.jsx(pe,{isActive:s.active===1,activeText:"Active",inactiveText:"Deactive"})})}},{id:"delete",header:"",cell:({row:t,table:i})=>{const s=t.original,r=i.options.meta;return e.jsxs(F,{variant:"ghost",size:"sm",onClick:o=>{var a;o.stopPropagation(),(a=r==null?void 0:r.onDeleteCategory)==null||a.call(r,s)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",title:"Xóa nhóm",children:[e.jsx(ue,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa nhóm ",s.item_type_name]})]})}}];function Ee({columns:t,data:i,onEditCategory:s,onDeleteCategory:r,onToggleStatus:o,onRowClick:a}){var d;const l=xe({data:i,columns:t,getCoreRowModel:ge(),meta:{onEditCategory:s,onDeleteCategory:r,onToggleStatus:o}});return e.jsxs(fe,{className:"h-[calc(100vh-300px)] rounded-md border",children:[e.jsxs(X,{className:"relative",children:[e.jsx(z,{children:l.getHeaderGroups().map(c=>e.jsx(D,{children:c.headers.map(h=>e.jsx(U,{children:h.isPlaceholder?null:$(h.column.columnDef.header,h.getContext())},h.id))},c.id))}),e.jsx(W,{children:(d=l.getRowModel().rows)!=null&&d.length?l.getRowModel().rows.map(c=>e.jsx(D,{className:a?"cursor-pointer hover:bg-muted/50":"",onClick:()=>a==null?void 0:a(c.original),children:c.getVisibleCells().map(h=>e.jsx(E,{children:$(h.column.columnDef.cell,h.getContext())},h.id))},c.id)):e.jsx(D,{children:e.jsx(E,{colSpan:t.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(ye,{orientation:"horizontal"})]})}function Pe(t){const[i,s]=f.useState(!1),[r,o]=f.useState(null),[a,l]=f.useState([]),[d,c]=f.useState(!1),h=f.useRef(null),y=J(t),n=()=>{s(!1),c(!1),l([]),o(null)},w=()=>{const m=document.createElement("a");m.href="/files/categories/categories-in-store/import_item_type_template.xlsx",m.download="import_item_type_template.xlsx",document.body.appendChild(m),m.click(),document.body.removeChild(m)},I=()=>{s(!0)},N=()=>{var m;(m=h.current)==null||m.click()},b=async m=>new Promise((p,j)=>{const C=new FileReader;C.onload=P=>{var M;try{const _=new Uint8Array((M=P.target)==null?void 0:M.result),k=Ie(_,{type:"array"}),O=k.SheetNames[0],R=k.Sheets[O],S=Te.sheet_to_json(R,{header:["name","id"],range:1}).filter(v=>v.name).map(v=>({name:String(v.name).trim(),id:v.id?String(v.id).trim():ie()}));if(S.length===0){g.error("File không có dữ liệu hợp lệ"),j(new Error("No valid data"));return}p(S)}catch(_){g.error("Lỗi khi đọc file Excel"),j(_)}},C.onerror=()=>{g.error("Lỗi khi đọc file"),j(new Error("File read error"))},C.readAsArrayBuffer(m)});return{importModalOpen:i,setImportModalOpen:s,importSelectedFile:r,importParsedData:a,showImportParsedData:d,importFileInputRef:h,resetImportState:n,handleDownloadTemplate:w,handleOpenImportModal:I,handleImportFileUpload:N,handleImportFileChange:async m=>{var j;const p=(j=m.target.files)==null?void 0:j[0];if(p){if(!p.name.endsWith(".xlsx")&&!p.name.endsWith(".xls")){g.error("Vui lòng chọn file Excel (.xlsx hoặc .xls)");return}o(p);try{const C=await b(p);l(C),c(!0),g.success(`Đã phân tích ${C.length} nhóm món tại cửa hàng từ file!`)}catch{o(null)}}},handleSaveImportedCategories:async()=>{if(a.length===0)return g.error("Không có dữ liệu để lưu"),!1;try{const m=a.map((p,j)=>({item_type_name:p.name,item_type_id:p.id,sort:j+1}));return await y.mutateAsync(m),g.success(`Đã tạo thành công ${a.length} nhóm món tại cửa hàng!`),n(),!0}catch{return g.error("Lỗi khi tạo nhóm món tại cửa hàng. Vui lòng thử lại."),!1}},isLoading:y.isPending}}function Oe({open:t,onOpenChange:i,showImportParsedData:s,importSelectedFile:r,importParsedData:o,isLoading:a,onCancel:l,onConfirm:d,onDownloadTemplate:c,onImportFileUpload:h}){return e.jsx(we,{title:"Thêm nhóm tại cửa hàng",open:t,onOpenChange:i,onCancel:l,onConfirm:d||(()=>{}),confirmText:s?"Lưu":void 0,cancelText:s?"Hủy":void 0,hideButtons:!s,centerTitle:!0,maxWidth:s?"sm:max-w-4xl":"sm:max-w-[500px]",isLoading:a,children:e.jsx("div",{className:"space-y-4",children:s?e.jsx(e.Fragment,{children:e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"max-h-96 overflow-auto rounded-md border",children:e.jsxs(X,{children:[e.jsx(z,{children:e.jsxs(D,{children:[e.jsx(U,{children:"ID"}),e.jsx(U,{children:"Tên nhóm"})]})}),e.jsx(W,{children:o.map((y,n)=>e.jsxs(D,{children:[e.jsx(E,{children:y.id}),e.jsx(E,{children:y.name})]},n))})]})})})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"File tải lên có cấu trúc như sau:"}),e.jsx("div",{className:"flex justify-center",children:e.jsx("img",{src:"/images/categories/categories-in-store/template-tao-nhom-mon.png",alt:"Template structure",className:"h-auto max-w-full rounded-md border"})}),e.jsx("div",{className:"text-left",children:e.jsxs("span",{className:"text-sm text-gray-600",children:["Hoặc xem trong"," ",e.jsx("button",{onClick:c,className:"text-blue-600 underline hover:text-blue-800",children:"file mẫu"})]})}),e.jsx("div",{className:"flex justify-center pt-4",children:e.jsxs(F,{onClick:h,className:"flex items-center gap-2",children:[e.jsx(Ne,{className:"h-4 w-4"}),"Tải file excel lên"]})})]})})})})}function Re(t){return t instanceof Error?t.message:typeof t=="string"?t:t&&typeof t=="object"&&"message"in t?String(t.message):"Đã xảy ra lỗi không xác định"}function Ae({searchQuery:t,onSearchQueryChange:i,onSearchSubmit:s,onCreateCategory:r,isCreating:o,selectedStoreUid:a,onStoreChange:l,storeOptions:d,viewType:c,onViewTypeChange:h}){const y=ae(),n=Pe(a),w=async()=>{try{const x=c==="store"&&a?{store_uid:a,apply_with_store:!0}:void 0,T=await y.mutateAsync(x),m=URL.createObjectURL(T),p=document.createElement("a");p.href=m,p.download=`nhom-mon-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(p),p.click(),document.body.removeChild(p),URL.revokeObjectURL(m),g.success("Xuất nhóm món tại cửa hàng thành công!")}catch(x){const T=Re(x);g.error(T)}},I=()=>{n.handleOpenImportModal()},N=()=>{n.resetImportState()},b=async()=>{await n.handleSaveImportedCategories()};return e.jsxs("div",{className:"mb-6 flex items-center gap-4",children:[e.jsx("h2",{className:"text-2xl font-semibold",children:"Nhóm món tại cửa hàng"}),e.jsxs(B,{value:a,onValueChange:l,children:[e.jsx(H,{className:"w-[200px]",children:e.jsx(V,{placeholder:"Chọn cửa hàng"})}),e.jsx(Q,{children:d.map(x=>e.jsx(A,{value:x.value,children:x.label},x.value))})]}),e.jsx(ve,{placeholder:"Tìm kiếm nhóm món...",className:"w-64",value:t,onChange:x=>i(x.target.value),onKeyDown:x=>{x.key==="Enter"&&(x.preventDefault(),s())}}),e.jsxs(B,{value:c,onValueChange:h,children:[e.jsx(H,{className:"w-[180px]",children:e.jsx(V,{})}),e.jsxs(Q,{children:[e.jsx(A,{value:"store-brand",children:"Cửa hàng và thương hiệu"}),e.jsx(A,{value:"store",children:"Cửa hàng"})]})]}),e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsxs(je,{children:[e.jsx(Ce,{asChild:!0,children:e.jsxs(F,{size:"sm",children:["Tiện ích",e.jsx(be,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(Se,{align:"end",children:[e.jsxs(K,{onClick:I,className:"flex items-center gap-2",children:[e.jsx(le,{className:"h-4 w-4"}),"Thêm nhóm từ file"]}),e.jsxs(K,{onClick:w,className:"flex items-center gap-2",disabled:y.isPending,children:[e.jsx(_e,{className:"h-4 w-4"}),y.isPending?"Đang xuất...":"Xuất nhóm món"]})]})]}),e.jsxs(F,{onClick:r,className:"flex items-center gap-2",disabled:o,children:[e.jsx(De,{className:"h-4 w-4"}),o?"Đang tạo...":"Tạo nhóm"]})]}),e.jsx(Oe,{open:n.importModalOpen,onOpenChange:n.setImportModalOpen,showImportParsedData:n.showImportParsedData,importSelectedFile:n.importSelectedFile,importParsedData:n.importParsedData,isLoading:n.isLoading,onCancel:N,onConfirm:b,onDownloadTemplate:n.handleDownloadTemplate,onImportFileUpload:n.handleImportFileUpload}),e.jsx("input",{ref:n.importFileInputRef,type:"file",accept:".xlsx,.xls",onChange:n.handleImportFileChange,className:"hidden"})]})}function Le(t={}){const{enabled:i=!0,store_uid:s,...r}=t,{company:o,brands:a}=Y(c=>c.auth),l=a==null?void 0:a[0],d={company_uid:(o==null?void 0:o.id)||"",brand_uid:(l==null?void 0:l.id)||"",skip_limit:!0,active:1,...s&&{store_uid:s,apply_with_store:1},...r};return Me({queryKey:[ke.ITEM_TYPES,"store",d],queryFn:async()=>(await Z.getItemTypes(d)).data||[],enabled:i&&!!(o!=null&&o.id&&(l!=null&&l.id)),staleTime:5*60*1e3,gcTime:10*60*1e3})}function L(t){return t instanceof Error?t.message:typeof t=="string"?t:t&&typeof t=="object"&&"message"in t?String(t.message):"Đã xảy ra lỗi không xác định"}function Ue(){const t=q(),[i,s]=f.useState(""),[r,o]=f.useState(""),[a,l]=f.useState(!1),[d,c]=f.useState(null),[h,y]=f.useState("store"),[n,w]=f.useState(""),{data:I=[]}=G();f.useEffect(()=>{var u;I.length>0&&!n&&w(((u=I[0])==null?void 0:u.id)||"")},[I,n]);const N=h==="store"&&n?n:void 0,{data:b,isLoading:x,error:T}=Le({search:i||void 0,store_uid:N,enabled:!0}),m=se(),p=ee(),j=te(),C=()=>{t({to:"/menu/category-in-store/detail"})},P=async u=>{try{const S={...u,active:u.active===1?0:1};await p.mutateAsync(S);const v=S.active===1?"kích hoạt":"vô hiệu hóa";g.success(`${v} nhóm "${u.item_type_name}" thành công!`)}catch(S){const v=L(S);g.error(v)}},M=u=>{t({to:"/menu/category-in-store/detail/$id",params:{id:u.id}})},_=u=>{t({to:"/menu/category-in-store/detail/$id",params:{id:u.id}})},k=u=>{c(u),l(!0)},O=async()=>{if(d)try{await j.mutateAsync(d.id),g.success(`Xóa nhóm "${d.item_type_name}" thành công!`),l(!1),c(null)}catch(u){const S=L(u);g.error(S)}},R=I.map(u=>({value:u.id,label:u.name}));return e.jsxs(e.Fragment,{children:[e.jsx(ne,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(de,{}),e.jsx(he,{}),e.jsx(me,{})]})}),e.jsx(re,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Ae,{searchQuery:r,onSearchQueryChange:o,onSearchSubmit:()=>s(r),onCreateCategory:C,isCreating:m.isPending,selectedStoreUid:n,onStoreChange:w,storeOptions:R,viewType:h,onViewTypeChange:y}),T&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:L(T)})}),x&&e.jsx(ce,{rows:8,columns:5}),!T&&!x&&e.jsx(Ee,{columns:Fe,data:b||[],onEditCategory:M,onDeleteCategory:k,onToggleStatus:P,onRowClick:_}),e.jsx(oe,{open:a,onOpenChange:l,content:d?`Bạn có muốn xóa nhóm "${d.item_type_name}"?`:"Bạn có muốn xóa nhóm này?",confirmText:"Xác nhận",onConfirm:O,isLoading:j.isPending})]})})]})}const Ht=Ue;export{Ht as component};
