import{r as i,j as e,B as v}from"./index-CVQ6JZo2.js";import{C as m,d as h,a as E,b as I}from"./card-DtE5r_AG.js";import{I as ne}from"./input-Al6WtUZF.js";import{D as ie}from"./date-range-picker-o-N_0Uo2.js";import{c as M}from"./crm-api-CDzLLTww.js";import"./pos-api-mRg02iop.js";import{u as H}from"./useQuery-HgcIHxlE.js";import{C as P}from"./query-keys-DQo7uRnN.js";import"./user-BJzEhOTa.js";import{R as K,X as A,Y as B,a0 as _}from"./generateCategoricalChart-CbMKN4d7.js";import{L as $}from"./LineChart-hfNGuKgm.js";import{L}from"./Line-CE8nwEPj.js";import{f as w,s as S}from"./isSameMonth-C8JQo-AN.js";import{a as V,b as C}from"./subMonths-BRhS7Uii.js";import"./popover-DnoSPJNX.js";import"./index-CtK-wKtB.js";import"./calendar-CYB-o1z2.js";import"./createLucideIcon-DKVxsQv7.js";import"./chevron-right-CxgpqvrH.js";import"./utils-km2FGkQ4.js";import"./index-Chjiymov.js";const F={getCustomerRegistrationReport:async a=>{try{const r=new URLSearchParams({start_date:a.start_date,end_date:a.end_date,pos_parent:a.pos_parent||"BRAND-953H"}),n=await M.get(`/general/report-customer?${r.toString()}`);if(!n.data||typeof n.data!="object")throw new Error("Invalid response format from customer registration report API");return n.data}catch(r){throw console.error("Error fetching customer registration report:",r),new Error("Failed to fetch customer registration report")}},getMembershipTypeChangeStats:async a=>{try{const r=new URLSearchParams({start_date:a.start_date,end_date:a.end_date,pos_parent:a.pos_parent||"BRAND-953H"}),n=await M.get(`/general/membership-type-change-stat?${r.toString()}`);if(!n.data||typeof n.data!="object")throw new Error("Invalid response format from membership type change stats API");return n.data}catch(r){throw console.error("Error fetching membership type change stats:",r),new Error("Failed to fetch membership type change statistics")}},getOrderByMembershipType:async a=>{try{const r=new URLSearchParams({start_date:a.start_date,end_date:a.end_date,pos_parent:a.pos_parent||"BRAND-953H"}),n=await M.get(`/general/order-by-membership-type?${r.toString()}`);if(!n.data||typeof n.data!="object")throw new Error("Invalid response format from order by membership type API");return n.data}catch(r){throw console.error("Error fetching order by membership type:",r),new Error("Failed to fetch order by membership type data")}}},ce=a=>{var d,T,k;const[r,n]=i.useState({start_date:new Date(Date.now()-2592e6).toISOString().split("T")[0],end_date:new Date().toISOString().split("T")[0],pos_parent:"BRAND-953H",...a}),u=!!(r.start_date&&r.end_date),j=H({queryKey:[P.CUSTOMER_REGISTRATION_REPORT,r],queryFn:()=>F.getCustomerRegistrationReport(r),enabled:u,staleTime:5*60*1e3,retry:2}),x=H({queryKey:[P.MEMBERSHIP_TYPE_CHANGE_STATS,r],queryFn:()=>F.getMembershipTypeChangeStats(r),enabled:u,staleTime:5*60*1e3,retry:2}),g=H({queryKey:[P.ORDER_BY_MEMBERSHIP_TYPE,r],queryFn:()=>F.getOrderByMembershipType(r),enabled:u,staleTime:5*60*1e3,retry:2}),R=j.isLoading||x.isLoading||g.isLoading,f=((d=j.error)==null?void 0:d.message)||((T=x.error)==null?void 0:T.message)||((k=g.error)==null?void 0:k.message)||null,N=()=>{j.refetch(),x.refetch(),g.refetch()},y=i.useCallback(l=>{n(c=>({...c,...l}))},[]);return{data:{registrationReport:j.data||null,membershipTypeStats:x.data||null,orderByMembershipType:g.data||null},isLoading:R,error:f,refetch:N,params:r,updateParams:y,queries:{registration:j,membershipStats:x,orderByMembership:g}}},de={member_register_count:0,total_eat_count:0,first_time_count:0,second_times_count:0,three_and_above_times_count:0,report_by_pos:[],report_by_date:[]};function oe(){const[a,r]=i.useState(null),[n,u]=i.useState(null),[j,x]=i.useState(""),[g,R]=i.useState(""),[f,N]=i.useState("7days"),y=i.useMemo(()=>({start_date:a?w(a,"yyyy-M-d"):"",end_date:n?w(n,"yyyy-M-d"):"",pos_parent:"BRAND-953H"}),[a,n]),{data:O,isLoading:d,error:T,updateParams:k}=ce();i.useEffect(()=>{y.start_date&&y.end_date&&k(y)},[y.start_date,y.end_date,y.pos_parent,k]);const l=T,c=O.registrationReport||de,q=t=>{if(!t||t.length!==8)return t;const s=t.substring(0,4),o=t.substring(4,6),p=t.substring(6,8);return`${s}/${o}/${p}`},z=i.useMemo(()=>{var t;return(t=c.report_by_date)!=null&&t.length?c.report_by_date.map(s=>({name:q(s.date_hash),value:s.number_member_register||0,date_hash:s.date_hash})):[]},[c.report_by_date]),U=i.useMemo(()=>{var t;return(t=c.report_by_date)!=null&&t.length?c.report_by_date.map(s=>({name:q(s.date_hash),"Lượt khách hàng chi tiêu lần đầu":s.number_eat_first_time||0,"Lượt khách hàng chi tiêu lần 2":s.number_eat_2nd_time||0,"Lượt khách hàng chi tiêu từ 3 lần":s.number_eat_3_or_more_time||0,date_hash:s.date_hash})):[]},[c.report_by_date]),W=i.useMemo(()=>{var t;return(t=c.report_by_pos)!=null&&t.length?c.report_by_pos.map(s=>({store_name:"Tutimi-Bình Lợi",first_time_count:s.number_eat_first_time||0,second_times_count:s.number_eat_2nd_time||0,three_and_above_times_count:s.number_eat_3_or_more_time||0})):[]},[c.report_by_pos]),G=i.useMemo(()=>[],[]),D=i.useCallback((t,s)=>`${w(t,"dd/MM/yyyy")} - ${w(s,"dd/MM/yyyy")}`,[]),b=i.useCallback(t=>{const s=new Date;let o,p=V(s);switch(t){case"today":o=S(s);break;case"yesterday":o=S(C(s,1)),p=V(C(s,1));break;case"7days":o=S(C(s,6));break;case"15days":o=S(C(s,14));break;case"30days":o=S(C(s,29));break;default:return}r(o),u(p),x(D(o,p)),N(t)},[D]);i.useEffect(()=>{b("7days")},[b]);const X=i.useCallback(t=>{x(t),N("");const s=/^(\d{2}\/\d{2}\/\d{4})\s*-\s*(\d{2}\/\d{2}\/\d{4})$/,o=t.match(s);if(o)try{const[,p,J]=o,[Z,ee,te]=p.split("/").map(Number),[se,re,ae]=J.split("/").map(Number),Y=new Date(te,ee-1,Z),Q=new Date(ae,re-1,se);!isNaN(Y.getTime())&&!isNaN(Q.getTime())&&(r(Y),u(Q))}catch{}},[]);return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Biến động khách hàng"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Theo dõi và phân tích sự thay đổi trong cơ sở khách hàng"})]}),e.jsxs("div",{className:"flex justify-end items-center gap-2 mb-6",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{variant:"outline",size:"sm",className:`text-xs ${f==="today"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>b("today"),children:"Hôm nay"}),e.jsx(v,{variant:"outline",size:"sm",className:`text-xs ${f==="yesterday"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>b("yesterday"),children:"Hôm qua"}),e.jsx(v,{variant:"outline",size:"sm",className:`text-xs ${f==="7days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>b("7days"),children:"7 ngày trước"}),e.jsx(v,{variant:"outline",size:"sm",className:`text-xs ${f==="15days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>b("15days"),children:"15 ngày trước"}),e.jsx(v,{variant:"outline",size:"sm",className:`text-xs ${f==="30days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>b("30days"),children:"30 ngày trước"})]}),e.jsx(ie,{startDate:a,endDate:n,onDateChange:(t,s)=>{r(t),u(s),t&&s&&(x(D(t,s)),N(""))},dateRange:j,onDateRangeChange:X})]}),e.jsxs("div",{className:"grid grid-cols-5 gap-4 mb-6",children:[e.jsx(m,{children:e.jsxs(h,{className:"p-4 text-center",children:[d?e.jsx("div",{className:"text-2xl font-bold text-gray-400",children:"..."}):l?e.jsx("div",{className:"text-2xl font-bold text-red-500",children:"--"}):e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:c.member_register_count.toLocaleString()}),e.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"KHÁCH ĐĂNG KÝ"})]})}),e.jsx(m,{children:e.jsxs(h,{className:"p-4 text-center",children:[d?e.jsx("div",{className:"text-2xl font-bold text-gray-400",children:"..."}):l?e.jsx("div",{className:"text-2xl font-bold text-red-500",children:"--"}):e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:c.total_eat_count.toLocaleString()}),e.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"LƯỢT CHI TIÊU"})]})}),e.jsx(m,{children:e.jsxs(h,{className:"p-4 text-center",children:[d?e.jsx("div",{className:"text-2xl font-bold text-gray-400",children:"..."}):l?e.jsx("div",{className:"text-2xl font-bold text-red-500",children:"--"}):e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:c.first_time_count.toLocaleString()}),e.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"LƯỢT CHI TIÊU LẦN ĐẦU"})]})}),e.jsx(m,{children:e.jsxs(h,{className:"p-4 text-center",children:[d?e.jsx("div",{className:"text-2xl font-bold text-gray-400",children:"..."}):l?e.jsx("div",{className:"text-2xl font-bold text-red-500",children:"--"}):e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:c.second_times_count.toLocaleString()}),e.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"LƯỢT CHI TIÊU LẦN 2"})]})}),e.jsx(m,{children:e.jsxs(h,{className:"p-4 text-center",children:[d?e.jsx("div",{className:"text-2xl font-bold text-gray-400",children:"..."}):l?e.jsx("div",{className:"text-2xl font-bold text-red-500",children:"--"}):e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:c.three_and_above_times_count.toLocaleString()}),e.jsx("div",{className:"text-sm text-gray-600 mt-1",children:"LƯỢT CHI TIÊU TỪ 3 LẦN"})]})})]}),e.jsx(m,{className:"mb-6",children:e.jsxs(h,{className:"p-6",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Số lượng khách đăng ký"})]})}),e.jsx("div",{className:"h-[500px]",children:d?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"text-gray-500",children:"Đang tải dữ liệu..."})}):l?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"text-red-500",children:"Không thể tải dữ liệu"})}):e.jsx(K,{width:"100%",height:"100%",children:e.jsxs($,{data:z,margin:{top:20,right:30,left:50,bottom:50},children:[e.jsx(A,{dataKey:"name",axisLine:!0,tickLine:!0,tick:{fontSize:12,fill:"#6b7280"},stroke:"#d1d5db"}),e.jsx(B,{domain:[0,1],ticks:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1],tickFormatter:t=>t===0?"0":t.toFixed(1),axisLine:!0,tickLine:!0,tick:{fontSize:12,fill:"#6b7280"},width:50,stroke:"#d1d5db"}),[.1,.2,.3,.4,.5,.6,.7,.8,.9,1].map(t=>e.jsx(_,{y:t,stroke:"#e5e7eb",strokeDasharray:"none"},t)),z.map((t,s)=>e.jsx(_,{x:t.name,stroke:"#d1d5db",strokeWidth:1},`vertical-${s}`)),e.jsx(L,{type:"monotone",dataKey:"value",stroke:"#22c55e",strokeWidth:2,dot:{fill:"#22c55e",strokeWidth:2,r:4}})]})})})]})}),e.jsx(m,{className:"mb-6",children:e.jsxs(h,{className:"p-6",children:[e.jsxs("div",{className:"flex justify-center gap-6 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Lượt khách hàng chi tiêu lần đầu"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Lượt khách hàng chi tiêu lần 2"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-orange-500 rounded"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Lượt khách hàng chi tiêu từ 3 lần"})]})]}),e.jsx("div",{className:"h-[500px]",children:d?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"text-gray-500",children:"Đang tải dữ liệu..."})}):l?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"text-red-500",children:"Không thể tải dữ liệu"})}):e.jsx(K,{width:"100%",height:"100%",children:e.jsxs($,{data:U,margin:{top:20,right:30,left:50,bottom:50},children:[e.jsx(A,{dataKey:"name",axisLine:!0,tickLine:!0,tick:{fontSize:12,fill:"#6b7280"},stroke:"#d1d5db"}),e.jsx(B,{domain:[0,1],ticks:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1],tickFormatter:t=>t===0?"0":t.toFixed(1),axisLine:!0,tickLine:!0,tick:{fontSize:12,fill:"#6b7280"},width:50,stroke:"#d1d5db"}),[.1,.2,.3,.4,.5,.6,.7,.8,.9,1].map(t=>e.jsx(_,{y:t,stroke:"#e5e7eb",strokeDasharray:"none"},t)),U.map((t,s)=>e.jsx(_,{x:t.name,stroke:"#d1d5db",strokeWidth:1},`vertical-${s}`)),e.jsx(L,{type:"monotone",dataKey:"Lượt khách hàng chi tiêu lần đầu",stroke:"#22c55e",strokeWidth:2}),e.jsx(L,{type:"monotone",dataKey:"Lượt khách hàng chi tiêu lần 2",stroke:"#3b82f6",strokeWidth:2}),e.jsx(L,{type:"monotone",dataKey:"Lượt khách hàng chi tiêu từ 3 lần",stroke:"#f97316",strokeWidth:2})]})})})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs(m,{children:[e.jsx(E,{children:e.jsx(I,{className:"text-base font-medium text-gray-700 text-center",children:"GIAO DỊCH THEO HẠNG THÀNH VIÊN"})}),e.jsx(h,{children:e.jsx("div",{className:"h-[500px]",children:d?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"text-gray-500",children:"Đang tải dữ liệu..."})}):l?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx("div",{className:"text-red-500",children:"Không thể tải dữ liệu"})}):e.jsx(K,{width:"100%",height:"100%",children:e.jsxs($,{data:G,margin:{top:20,right:30,left:50,bottom:50},children:[e.jsx(A,{dataKey:"name",axisLine:!0,tickLine:!0,tick:{fontSize:12,fill:"#6b7280"},stroke:"#d1d5db"}),e.jsx(B,{domain:[0,1],ticks:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1],tickFormatter:t=>t===0?"0":t.toFixed(1),axisLine:!0,tickLine:!0,tick:{fontSize:12,fill:"#6b7280"},width:50,stroke:"#d1d5db"}),[.1,.2,.3,.4,.5,.6,.7,.8,.9,1].map(t=>e.jsx(_,{y:t,stroke:"#e5e7eb",strokeDasharray:"none"},t)),G.map((t,s)=>e.jsx(_,{x:t.name,stroke:"#d1d5db",strokeWidth:1},`vertical-${s}`)),e.jsx(L,{type:"monotone",dataKey:"value",stroke:"#22c55e",strokeWidth:2,dot:{fill:"#22c55e",strokeWidth:2,r:4}})]})})})})]}),e.jsxs(m,{children:[e.jsx(E,{children:e.jsx(I,{className:"text-base font-medium text-gray-700",children:"BIẾN ĐỘNG HẠNG THÀNH VIÊN"})}),e.jsx(h,{children:e.jsx("div",{className:"overflow-hidden",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b bg-gray-50",children:[e.jsx("th",{className:"text-left p-3 text-sm font-medium text-gray-600",children:"Từ"}),e.jsx("th",{className:"text-left p-3 text-sm font-medium text-gray-600",children:"Lên"}),e.jsx("th",{className:"text-left p-3 text-sm font-medium text-gray-600",children:"Số khách hàng"})]})}),e.jsx("tbody",{children:e.jsx("tr",{children:e.jsx("td",{colSpan:3,className:"text-center p-8 text-gray-500",children:"Chưa có dữ liệu"})})})]})})})]})]}),e.jsxs(m,{children:[e.jsxs(E,{className:"flex flex-row items-center justify-between",children:[e.jsx(I,{className:"text-base font-medium text-gray-700",children:"THỐNG KÊ LƯỢT CHI TIÊU THEO CỬA HÀNG"}),e.jsx(ne,{placeholder:"Tìm kiếm nhanh",value:g,onChange:t=>R(t.target.value),className:"w-64 text-xs"})]}),e.jsx(h,{children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b bg-gray-50",children:[e.jsx("th",{className:"text-left p-3 font-medium text-gray-700",children:"#"}),e.jsx("th",{className:"text-left p-3 font-medium text-gray-700",children:"Cửa hàng"}),e.jsx("th",{className:"text-center p-3 font-medium text-gray-700",children:"Lượt khách hàng chi tiêu lần đầu"}),e.jsx("th",{className:"text-center p-3 font-medium text-gray-700",children:"Lượt khách hàng chi tiêu lần 2"}),e.jsx("th",{className:"text-center p-3 font-medium text-gray-700",children:"Lượt khách hàng chi tiêu từ 3 lần"})]})}),e.jsx("tbody",{children:d?e.jsx("tr",{children:e.jsx("td",{colSpan:5,className:"text-center p-8 text-gray-500",children:"Đang tải dữ liệu..."})}):l?e.jsx("tr",{children:e.jsx("td",{colSpan:5,className:"text-center p-8 text-red-500",children:"Không thể tải dữ liệu"})}):W.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:5,className:"text-center p-8 text-gray-500",children:"Chưa có dữ liệu"})}):W.filter(t=>{var s;return(s=t.store_name)==null?void 0:s.toLowerCase().includes(g.toLowerCase())}).map((t,s)=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsx("td",{className:"p-3 text-gray-900",children:s+1}),e.jsx("td",{className:"p-3 text-gray-900",children:t.store_name}),e.jsx("td",{className:"p-3 text-center text-gray-900",children:t.first_time_count.toLocaleString()}),e.jsx("td",{className:"p-3 text-center text-gray-900",children:t.second_times_count.toLocaleString()}),e.jsx("td",{className:"p-3 text-center text-gray-900",children:t.three_and_above_times_count.toLocaleString()})]},s))})]})})})]})]})}const De=oe;export{De as component};
