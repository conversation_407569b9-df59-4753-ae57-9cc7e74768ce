import{j as e,B as i}from"./index-CfbMU4Ye.js";import{D as o,h as r,b as t,d as l,e as d,i as p}from"./dropdown-menu-8bnotEGr.js";import{M as c}from"./react-icons.esm-DefBGHOQ.js";function u({table:a}){return e.jsxs(o,{modal:!1,children:[e.jsx(r,{asChild:!0,children:e.jsxs(i,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[e.jsx(c,{className:"mr-2 h-4 w-4"}),"View"]})}),e.jsxs(t,{align:"end",className:"w-[150px]",children:[e.jsx(l,{children:"Toggle columns"}),e.jsx(d,{}),a.getAllColumns().filter(s=>typeof s.accessorFn<"u"&&s.getCanHide()).map(s=>e.jsx(p,{className:"capitalize",checked:s.getIsVisible(),onCheckedChange:n=>s.toggleVisibility(!!n),children:s.id},s.id))]})]})}export{u as D};
