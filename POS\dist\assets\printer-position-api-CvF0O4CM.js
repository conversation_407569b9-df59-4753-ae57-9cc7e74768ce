import{b as o}from"./pos-api-j20LMGrC.js";const a={createPrinterPosition:async t=>{const i=await o.post("/v3/pos-cms/printer-position",t);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from printer position API");return i.data},updatePrinterPosition:async t=>{const i=await o.post("/v3/pos-cms/printer-position",t);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from printer position API");return i.data},getPrinterPositions:async t=>{const i=new URLSearchParams;i.append("company_uid",t.company_uid),i.append("brand_uid",t.brand_uid),t.store_uid&&i.append("store_uid",t.store_uid),t.page&&i.append("page",t.page.toString()),t.limit&&i.append("limit",t.limit.toString()),t.search&&i.append("search",t.search),t.active!==void 0&&i.append("active",t.active.toString()),t.apply_with_store!==void 0&&i.append("apply_with_store",t.apply_with_store.toString());const e=`/v3/pos-cms/printer-position?${i.toString()}`,n=await o.get(e);if(!n.data||typeof n.data!="object")throw new Error("Invalid response format from printer position API");return n.data},getPrinterPositionDetail:async t=>{const i=new URLSearchParams;i.append("company_uid",t.company_uid),i.append("brand_uid",t.brand_uid),i.append("id",t.id);const e=await o.get(`/pos/v1/printer-position/detail?${i.toString()}`);if(!e.data||typeof e.data!="object")throw new Error("Invalid response format from printer position detail API");return e.data},deletePrinterPosition:async t=>{const i=await o.delete("/pos/v1/printer-position",{headers:{Accept:"application/json, text/plain, */*","Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},data:{company_uid:t.company_uid,brand_uid:t.brand_uid,id:t.id}});if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from printer position delete API");return i.data}};export{a as p};
