import{e as a,r as p,j as t}from"./index-CVQ6JZo2.js";import{I as e}from"./item-detail-form-CflK8ErY.js";import"./form-CzmGigtT.js";import"./exceljs.min-CsFn5HPa.js";import"./pos-api-mRg02iop.js";import{j as c}from"./customization-dialog-qTQRDzC6.js";import"./user-BJzEhOTa.js";import"./vietqr-api-_ZZrmuU0.js";import"./crm-api-CDzLLTww.js";import"./header-DiKooCuw.js";import"./main-BD6dUgw2.js";import"./search-context-CkCLuJFL.js";import"./date-range-picker-CXbMaowj.js";import"./multi-select-Cw4ybFq5.js";import"./zod-ByV4TDQ9.js";import"./use-upload-image-CEKqOCdc.js";import"./images-api-DZkYJB2_.js";import"./use-item-types-DnnQzY0g.js";import"./useQuery-HgcIHxlE.js";import"./utils-km2FGkQ4.js";import"./useMutation-ZsyDznMu.js";import"./query-keys-3lmd-xp6.js";import"./use-item-classes-uo7m9d9N.js";import"./use-units-DPUE3WPd.js";import"./use-items-266COz_R.js";import"./item-api-RI9c_YlZ.js";import"./use-removed-items-Yn7fGIc0.js";import"./use-customizations-CIOuk7WT.js";import"./use-customization-by-id-CKIdZ_Dk.js";import"./use-sources-cLXJkEjy.js";import"./sources-api-BbeVL2d3.js";import"./sources-CfiQ7039.js";import"./calendar-BszTCdZH.js";import"./createLucideIcon-DKVxsQv7.js";import"./index-CtK-wKtB.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-BfLSzhzg.js";import"./index-nc1u7392.js";import"./check-BE_j5GZD.js";import"./input-Al6WtUZF.js";import"./textarea-DQwjUKcg.js";import"./combobox-Kh9YbXHg.js";import"./command-Nb4B17YQ.js";import"./dialog-DDrduXt3.js";import"./search-B4Rlb4i6.js";import"./popover-DnoSPJNX.js";import"./chevrons-up-down-BUvu5Qi-.js";import"./upload-Df52ST6B.js";import"./collapsible-CRaCKnru.js";import"./confirm-dialog-VdWaMiM8.js";import"./alert-dialog-DEHyMeO0.js";import"./date-picker-rKqp65a-.js";import"./calendar-CYB-o1z2.js";import"./circle-help-CHqJBYbr.js";import"./select-BFhNE0YE.js";import"./index-LVHINuqD.js";import"./chevron-right-CxgpqvrH.js";import"./use-dialog-state-Duzyky_e.js";import"./modal-DEESjk2b.js";import"./separator-BcoNozmD.js";import"./createReactComponent-DSXPaZ4c.js";import"./scroll-area-CGsZUbT-.js";import"./IconChevronRight-LXWXuzjR.js";import"./react-icons.esm-DMMA_g0o.js";import"./badge-DIVCRE0k.js";import"./circle-x-DJOknGq-.js";const vt=function(){const i=a({from:"/_authenticated/menu/items/items-in-city/detail"}),[r,s]=p.useState(null),o=i==null?void 0:i.id,{data:m,isLoading:n}=c(o,!!o);return o?(p.useEffect(()=>{m&&s(m.data)},[m]),n||!r?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):t.jsx(e,{currentRow:{...r,item_id:"",item_id_barcode:""},isCopyMode:!!r})):t.jsx(e,{})};export{vt as component};
