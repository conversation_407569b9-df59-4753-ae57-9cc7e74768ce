import{r as u,j as c}from"./index-CfbMU4Ye.js";import{u as I,P as C}from"./pagination-DqTRfWAm.js";function T({data:i,pageSize:P=10,className:m,showPageSizeSelector:p=!0,showItemCount:f=!0,showFirstLastButtons:l=!0,pageSizeOptions:x=[10,20,50,100],disabled:h=!1,children:z}){const{currentPage:r,pageSize:S,setPage:o,setPageSize:M}=I(),t=S||P,a=i.length,n=Math.ceil(a/t),e=Math.min(Math.max(1,r),n||1),s=(e-1)*t,g=Math.min(s+t,a),d=u.useMemo(()=>i.slice(s,g),[i,s,g]),j={currentPage:e,totalPages:n,pageSize:t,totalItems:a,startIndex:s,endIndex:g};return u.useEffect(()=>{r!==e&&n>0&&o(e)},[r,e,n,o]),c.jsxs(c.Fragment,{children:[z(d,j),a>0&&c.jsx(C,{currentPage:e,totalPages:n,pageSize:t,totalItems:a,onPageChange:o,onPageSizeChange:M,showPageSizeSelector:p,showItemCount:f,showFirstLastButtons:l,pageSizeOptions:x,className:m,disabled:h})]})}export{T};
