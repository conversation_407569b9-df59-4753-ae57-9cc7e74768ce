import{aU as t,j as m}from"./index-CVQ6JZo2.js";import"./pos-api-mRg02iop.js";import"./vietqr-api-_ZZrmuU0.js";import"./user-BJzEhOTa.js";import"./crm-api-CDzLLTww.js";import"./header-DiKooCuw.js";import"./main-BD6dUgw2.js";import"./search-context-CkCLuJFL.js";import"./date-range-picker-CXbMaowj.js";import"./form-CzmGigtT.js";import{C as i}from"./create-table-form-DUXT9p6Y.js";import"./separator-BcoNozmD.js";import"./command-Nb4B17YQ.js";import"./calendar-BszTCdZH.js";import"./createLucideIcon-DKVxsQv7.js";import"./index-CtK-wKtB.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DDrduXt3.js";import"./search-B4Rlb4i6.js";import"./createReactComponent-DSXPaZ4c.js";import"./scroll-area-CGsZUbT-.js";import"./index-LVHINuqD.js";import"./select-BFhNE0YE.js";import"./index-nc1u7392.js";import"./check-BE_j5GZD.js";import"./IconChevronRight-LXWXuzjR.js";import"./chevron-right-CxgpqvrH.js";import"./react-icons.esm-DMMA_g0o.js";import"./popover-DnoSPJNX.js";import"./use-areas-BEwI37-s.js";import"./useQuery-HgcIHxlE.js";import"./utils-km2FGkQ4.js";import"./useMutation-ZsyDznMu.js";import"./images-api-DZkYJB2_.js";import"./query-keys-3lmd-xp6.js";import"./use-sales-channels-CDxRsdJ_.js";import"./use-tables-C5Z7koKH.js";import"./input-Al6WtUZF.js";import"./checkbox-BfLSzhzg.js";import"./collapsible-CRaCKnru.js";import"./use-items-in-store-data-B90Qq0xn.js";import"./use-item-types-DnnQzY0g.js";import"./use-item-classes-uo7m9d9N.js";import"./use-units-DPUE3WPd.js";import"./use-removed-items-Yn7fGIc0.js";import"./items-in-store-api-C7Two_iQ.js";import"./xlsx-DkH2s96g.js";import"./copy-BPVrVEjy.js";import"./plus-D3Ku3i4v.js";import"./minus-DqBpQdBc.js";const rt=function(){const{tableId:o}=t.useParams(),{store_uid:r}=t.useSearch();return m.jsx(i,{areaId:o,storeUid:r,fromTableLayout:!0})};export{rt as component};
