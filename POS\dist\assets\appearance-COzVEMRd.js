import{t as b,v,z as t,j as e,w as p,c as N,n as w,B as y}from"./index-CVQ6JZo2.js";import{C as F}from"./content-section-B490l29y.js";import{u as S,F as C,a as i,b as r,c as d,d as o,g as m,e as h}from"./form-CzmGigtT.js";import{h as k}from"./react-icons.esm-DMMA_g0o.js";import{s as A}from"./zod-ByV4TDQ9.js";import{R,a as x}from"./radio-group-CqrMW8s9.js";import"./separator-BcoNozmD.js";import"./index-C34iUvGy.js";import"./index-LVHINuqD.js";import"./index-nc1u7392.js";import"./createLucideIcon-DKVxsQv7.js";const V=t.object({theme:t.enum(["light","dark"],{required_error:"Please select a theme."}),font:t.enum(p,{invalid_type_error:"Select a font",required_error:"Please select a font."})});function _(){const{font:c,setFont:u}=b(),{theme:l,setTheme:j}=v(),f={theme:l,font:c},a=S({resolver:A(V),defaultValues:f});function g(s){s.font!=c&&u(s.font),s.theme!=l&&j(s.theme)}return e.jsx(C,{...a,children:e.jsxs("form",{onSubmit:a.handleSubmit(g),className:"space-y-8",children:[e.jsx(i,{control:a.control,name:"font",render:({field:s})=>e.jsxs(r,{children:[e.jsx(d,{children:"Font"}),e.jsxs("div",{className:"relative w-max",children:[e.jsx(o,{children:e.jsx("select",{className:N(w({variant:"outline"}),"w-[200px] appearance-none font-normal capitalize"),...s,children:p.map(n=>e.jsx("option",{value:n,children:n},n))})}),e.jsx(k,{className:"absolute top-2.5 right-3 h-4 w-4 opacity-50"})]}),e.jsx(m,{className:"font-manrope",children:"Set the font you want to use in the dashboard."}),e.jsx(h,{})]})}),e.jsx(i,{control:a.control,name:"theme",render:({field:s})=>e.jsxs(r,{className:"space-y-1",children:[e.jsx(d,{children:"Theme"}),e.jsx(m,{children:"Select the theme for the dashboard."}),e.jsx(h,{}),e.jsxs(R,{onValueChange:s.onChange,defaultValue:s.value,className:"grid max-w-md grid-cols-2 gap-8 pt-2",children:[e.jsx(r,{children:e.jsxs(d,{className:"[&:has([data-state=checked])>div]:border-primary",children:[e.jsx(o,{children:e.jsx(x,{value:"light",className:"sr-only"})}),e.jsx("div",{className:"border-muted hover:border-accent items-center rounded-md border-2 p-1",children:e.jsxs("div",{className:"space-y-2 rounded-sm bg-[#ecedef] p-2",children:[e.jsxs("div",{className:"space-y-2 rounded-md bg-white p-2 shadow-xs",children:[e.jsx("div",{className:"h-2 w-[80px] rounded-lg bg-[#ecedef]"}),e.jsx("div",{className:"h-2 w-[100px] rounded-lg bg-[#ecedef]"})]}),e.jsxs("div",{className:"flex items-center space-x-2 rounded-md bg-white p-2 shadow-xs",children:[e.jsx("div",{className:"h-4 w-4 rounded-full bg-[#ecedef]"}),e.jsx("div",{className:"h-2 w-[100px] rounded-lg bg-[#ecedef]"})]}),e.jsxs("div",{className:"flex items-center space-x-2 rounded-md bg-white p-2 shadow-xs",children:[e.jsx("div",{className:"h-4 w-4 rounded-full bg-[#ecedef]"}),e.jsx("div",{className:"h-2 w-[100px] rounded-lg bg-[#ecedef]"})]})]})}),e.jsx("span",{className:"block w-full p-2 text-center font-normal",children:"Light"})]})}),e.jsx(r,{children:e.jsxs(d,{className:"[&:has([data-state=checked])>div]:border-primary",children:[e.jsx(o,{children:e.jsx(x,{value:"dark",className:"sr-only"})}),e.jsx("div",{className:"border-muted bg-popover hover:bg-accent hover:text-accent-foreground items-center rounded-md border-2 p-1",children:e.jsxs("div",{className:"space-y-2 rounded-sm bg-slate-950 p-2",children:[e.jsxs("div",{className:"space-y-2 rounded-md bg-slate-800 p-2 shadow-xs",children:[e.jsx("div",{className:"h-2 w-[80px] rounded-lg bg-slate-400"}),e.jsx("div",{className:"h-2 w-[100px] rounded-lg bg-slate-400"})]}),e.jsxs("div",{className:"flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-xs",children:[e.jsx("div",{className:"h-4 w-4 rounded-full bg-slate-400"}),e.jsx("div",{className:"h-2 w-[100px] rounded-lg bg-slate-400"})]}),e.jsxs("div",{className:"flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-xs",children:[e.jsx("div",{className:"h-4 w-4 rounded-full bg-slate-400"}),e.jsx("div",{className:"h-2 w-[100px] rounded-lg bg-slate-400"})]})]})}),e.jsx("span",{className:"block w-full p-2 text-center font-normal",children:"Dark"})]})})]})]})}),e.jsx(y,{type:"submit",children:"Update preferences"})]})})}function z(){return e.jsx(F,{title:"Appearance",desc:`Customize the appearance of the app. Automatically switch between day\r
          and night themes.`,children:e.jsx(_,{})})}const H=z;export{H as component};
