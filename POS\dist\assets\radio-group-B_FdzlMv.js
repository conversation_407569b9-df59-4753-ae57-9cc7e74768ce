import{r as d,A as L,j as a,C,P as y,I,F as h,E as q,af as K,c as w}from"./index-UcdZ5AHH.js";import{c as E,R as V,I as z}from"./index-iiVug-md.js";import{u as T}from"./index-MuNXZ_zP.js";import{u as U}from"./index-BKS-UfoD.js";import{c as B}from"./createLucideIcon-D7O7McKr.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],$=B("circle",H);var g="Radio",[W,P]=C(g),[X,Y]=W(g),j=d.forwardRef((e,s)=>{const{__scopeRadio:o,name:n,checked:r=!1,required:t,disabled:i,value:p="on",onCheck:u,form:f,...m}=e,[l,v]=d.useState(null),c=I(s,x=>v(x)),R=d.useRef(!1),b=l?f||!!l.closest("form"):!0;return a.jsxs(X,{scope:o,checked:r,disabled:i,children:[a.jsx(y.button,{type:"button",role:"radio","aria-checked":r,"data-state":S(r),"data-disabled":i?"":void 0,disabled:i,value:p,...m,ref:c,onClick:h(e.onClick,x=>{r||u==null||u(),b&&(R.current=x.isPropagationStopped(),R.current||x.stopPropagation())})}),b&&a.jsx(J,{control:l,bubbles:!R.current,name:n,value:p,checked:r,required:t,disabled:i,form:f,style:{transform:"translateX(-100%)"}})]})});j.displayName=g;var G="RadioIndicator",_=d.forwardRef((e,s)=>{const{__scopeRadio:o,forceMount:n,...r}=e,t=Y(G,o);return a.jsx(q,{present:n||t.checked,children:a.jsx(y.span,{"data-state":S(t.checked),"data-disabled":t.disabled?"":void 0,...r,ref:s})})});_.displayName=G;var J=e=>{const{control:s,checked:o,bubbles:n=!0,...r}=e,t=d.useRef(null),i=U(o),p=K(s);return d.useEffect(()=>{const u=t.current,f=window.HTMLInputElement.prototype,l=Object.getOwnPropertyDescriptor(f,"checked").set;if(i!==o&&l){const v=new Event("click",{bubbles:n});l.call(u,o),u.dispatchEvent(v)}},[i,o,n]),a.jsx("input",{type:"radio","aria-hidden":!0,defaultChecked:o,...r,tabIndex:-1,ref:t,style:{...e.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function S(e){return e?"checked":"unchecked"}var Q=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],k="RadioGroup",[Z,le]=C(k,[E,P]),N=E(),A=P(),[ee,oe]=Z(k),D=d.forwardRef((e,s)=>{const{__scopeRadioGroup:o,name:n,defaultValue:r,value:t,required:i=!1,disabled:p=!1,orientation:u,dir:f,loop:m=!0,onValueChange:l,...v}=e,c=N(o),R=T(f),[b,x]=L({prop:t,defaultProp:r,onChange:l});return a.jsx(ee,{scope:o,name:n,required:i,disabled:p,value:b,onValueChange:x,children:a.jsx(V,{asChild:!0,...c,orientation:u,dir:R,loop:m,children:a.jsx(y.div,{role:"radiogroup","aria-required":i,"aria-orientation":u,"data-disabled":p?"":void 0,dir:R,...v,ref:s})})})});D.displayName=k;var F="RadioGroupItem",M=d.forwardRef((e,s)=>{const{__scopeRadioGroup:o,disabled:n,...r}=e,t=oe(F,o),i=t.disabled||n,p=N(o),u=A(o),f=d.useRef(null),m=I(s,f),l=t.value===r.value,v=d.useRef(!1);return d.useEffect(()=>{const c=b=>{Q.includes(b.key)&&(v.current=!0)},R=()=>v.current=!1;return document.addEventListener("keydown",c),document.addEventListener("keyup",R),()=>{document.removeEventListener("keydown",c),document.removeEventListener("keyup",R)}},[]),a.jsx(z,{asChild:!0,...p,focusable:!i,active:l,children:a.jsx(j,{disabled:i,required:t.required,checked:l,...u,...r,name:t.name,ref:m,onCheck:()=>t.onValueChange(r.value),onKeyDown:h(c=>{c.key==="Enter"&&c.preventDefault()}),onFocus:h(r.onFocus,()=>{var c;v.current&&((c=f.current)==null||c.click())})})})});M.displayName=F;var re="RadioGroupIndicator",O=d.forwardRef((e,s)=>{const{__scopeRadioGroup:o,...n}=e,r=A(o);return a.jsx(_,{...r,...n,ref:s})});O.displayName=re;var te=D,ae=M,se=O;function pe({className:e,...s}){return a.jsx(te,{"data-slot":"radio-group",className:w("grid gap-3",e),...s})}function fe({className:e,...s}){return a.jsx(ae,{"data-slot":"radio-group-item",className:w("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:a.jsx(se,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:a.jsx($,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}export{pe as R,fe as a};
