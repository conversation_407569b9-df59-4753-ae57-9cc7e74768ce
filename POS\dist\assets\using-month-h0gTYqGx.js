import{r as _,j as e,B as g,c as k,h as w}from"./index-CfbMU4Ye.js";import{c as v}from"./crm-api-8UaIokQG.js";import"./pos-api-BBB_ZiZD.js";import{u as N}from"./useQuery-BvDWg4vp.js";import{C}from"./query-keys-DQo7uRnN.js";import"./user-3BSjwAvJ.js";import{M as $}from"./main-B69tr6A0.js";import{C as S,a as y,b as T,d as M}from"./card-Dq-aHO9v.js";import{P as z,a as P,b as E}from"./popover-C4SSkcaE.js";import{c as A}from"./createReactComponent-CVG1We1Z.js";import{I as D}from"./IconChevronRight-1SGwHwL2.js";import{I as U}from"./IconSearch-cKq6-nw5.js";import{I as L}from"./IconDownload-Be7ISuXh.js";import"./date-range-picker-FRR8J6T3.js";import"./form-DPp_Bp7A.js";import{T as O,a as R,b,c as h,d as B,e as t}from"./table-C3v-r6-e.js";import"./utils-km2FGkQ4.js";import"./index-CBP3KeI0.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BwGWQXH2.js";import"./react-icons.esm-DefBGHOQ.js";import"./select-_nXsh5SU.js";import"./index-D41EikqA.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Y=A("outline","chevron-left","IconChevronLeft",[["path",{d:"M15 6l-6 6l6 6",key:"svg-0"}]]);const q=async n=>{const r=new URLSearchParams({year:n.year,month:n.month,pos_parent:n.pos_parent});return v.get(`/billing/get-using-month?${r.toString()}`)};function H(n){return N({queryKey:[C.USING_MONTH,n],queryFn:async()=>{if(!n)throw new Error("Params are required");return(await q(n)).data},enabled:!!n})}const F=async n=>{const r=new URLSearchParams({date_start:n.date_start,date_end:n.date_end,number_per_page:n.number_per_page.toString(),pos_parent:n.pos_parent});return v.get(`/billing/get-using-daily?${r.toString()}`)};function Z(n){return N({queryKey:[C.USING_DAILY,n],queryFn:async()=>{if(!n)throw new Error("Params are required");return(await F(n)).data},enabled:!!n})}const K=["Tháng 1","Tháng 2","Tháng 3","Tháng 4","Tháng 5","Tháng 6","Tháng 7","Tháng 8","Tháng 9","Tháng 10","Tháng 11","Tháng 12"];function Q({selectedMonth:n,onMonthChange:r,className:d}){const[p,i]=_.useState(!1),[l,x]=_.useState(()=>{const[o]=n.split("-");return parseInt(o)}),[a,u]=_.useState(()=>{const[,o]=n.split("-");return parseInt(o)-1}),j=o=>{u(o);const c=String(o+1).padStart(2,"0"),f=`${l}-${c}`;r(f),i(!1)},s=o=>{x(o);const c=String(a+1).padStart(2,"0"),f=`${o}-${c}`;r(f)},m=o=>{const[c,f]=o.split("-");return`${f}/${c}`};return e.jsxs(z,{open:p,onOpenChange:i,children:[e.jsx(P,{asChild:!0,children:e.jsx(g,{variant:"outline",className:k("w-[200px] justify-start text-left font-normal",!n&&"text-muted-foreground",d),children:n?m(n):"Chọn tháng"})}),e.jsx(E,{className:"w-auto p-0",align:"start",children:e.jsxs("div",{className:"p-3",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>s(l-1),children:e.jsx(Y,{className:"h-4 w-4"})}),e.jsx("span",{className:"text-sm font-medium",children:l}),e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>s(l+1),children:e.jsx(D,{className:"h-4 w-4"})})]}),e.jsx("div",{className:"grid grid-cols-3 gap-1",children:K.map((o,c)=>e.jsx(g,{variant:a===c?"default":"ghost",size:"sm",onClick:()=>j(c),className:"text-xs",children:o},c))})]})})]})}function V({selectedMonth:n,onMonthChange:r,onSearch:d,onExport:p,isLoading:i}){return e.jsxs(S,{children:[e.jsx(y,{children:e.jsx(T,{children:"Chọn tháng"})}),e.jsx(M,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(Q,{selectedMonth:n,onMonthChange:r})}),e.jsxs(g,{onClick:d,disabled:i,children:[e.jsx(U,{className:"mr-2 h-4 w-4"}),i?"Đang tìm...":"Tìm kiếm"]}),e.jsx("div",{className:"ml-auto",children:e.jsxs(g,{variant:"outline",onClick:p,children:[e.jsx(L,{className:"mr-2 h-4 w-4"}),"Xuất file"]})})]})})]})}function G({data:n,searchParams:r}){var u,j;const d=w(),p=r?{date_start:`${r.year}-${r.month.padStart(2,"0")}-01 00:00:00`,date_end:`${r.year}-${r.month.padStart(2,"0")}-31 23:59:59`,number_per_page:40,pos_parent:r.pos_parent}:null,i=Z(p),l=(s,m,o)=>`${s}/${m}/${o}`,x=s=>({voucher_many_time_used:"MANY_TIME",used_voucher_amount:"PUBLISH",facebook_message:"FACEBOOK",zalo_message:"ZALO",sms_message:"SMS",zns_message:"ZNS",tin_gui_ho:"ZALO_SEND_BY_APP_MERCHANT"})[s]||"",a=(s,m)=>{const o=`${m.year}-${String(m.month).padStart(2,"0")}-${String(m.day).padStart(2,"0")} 00:00:00`,c=x(s);d({to:"/crm/billing-detail",search:{date:o,type:c}})};return e.jsxs(S,{children:[e.jsx(y,{children:e.jsx(T,{children:"Dữ liệu sử dụng"})}),e.jsx(M,{children:e.jsxs(O,{children:[e.jsx(R,{children:e.jsxs(b,{children:[e.jsx(h,{children:"Thời gian"}),e.jsx(h,{children:"Voucher sử dụng nhiều lần"}),e.jsx(h,{children:"Voucher sử dụng 1 lần"}),e.jsx(h,{children:"Tin nhắn Facebook"}),e.jsx(h,{children:"Tin nhắn Zalo"}),e.jsx(h,{children:"Tin nhắn giao dịch zalo"}),e.jsx(h,{children:"Tin nhắn SMS OTP (Brandname iPOS.vn)"}),e.jsx(h,{children:"Tin nhắn ZNS"}),e.jsx(h,{children:"Tin gửi hộ"})]})}),e.jsxs(B,{children:[(j=(u=i.data)==null?void 0:u.list_rp_daily_billing)==null?void 0:j.map(s=>e.jsxs(b,{children:[e.jsx(t,{className:"font-medium",children:l(s.day,s.month,s.year)}),e.jsx(t,{children:e.jsx("span",{className:"cursor-pointer transition-colors hover:text-blue-600 hover:underline",onClick:()=>a("voucher_many_time_used",s),children:s.voucher_many_time_used})}),e.jsx(t,{children:e.jsx("span",{className:"cursor-pointer transition-colors hover:text-blue-600 hover:underline",onClick:()=>a("used_voucher_amount",s),children:s.used_voucher_amount})}),e.jsx(t,{children:e.jsx("span",{className:"cursor-pointer transition-colors hover:text-blue-600 hover:underline",onClick:()=>a("facebook_message",s),children:s.facebook_message})}),e.jsx(t,{children:e.jsx("span",{className:"cursor-pointer transition-colors hover:text-blue-600 hover:underline",onClick:()=>a("zalo_message",s),children:s.zalo_message})}),e.jsx(t,{children:s.zalo_transaction_message}),e.jsx(t,{children:e.jsx("span",{className:"cursor-pointer transition-colors hover:text-blue-600 hover:underline",onClick:()=>a("sms_message",s),children:s.sms_message})}),e.jsx(t,{children:e.jsx("span",{className:"cursor-pointer transition-colors hover:text-blue-600 hover:underline",onClick:()=>a("zns_message",s),children:s.zns_message})}),e.jsx(t,{children:e.jsx("span",{className:"cursor-pointer transition-colors hover:text-blue-600 hover:underline",onClick:()=>a("tin_gui_ho",s),children:s.zns_send_by_app_mc+s.promo_send_by_app_mc+s.trans_send_by_app_mc+s.cs_send_by_app_mc})})]},s.id)),e.jsxs(b,{className:"border-t-2 border-gray-300 bg-gray-50",children:[e.jsx(t,{className:"font-bold",children:"Cả tháng"}),e.jsx(t,{className:"font-semibold",children:n.voucher_many_time_used}),e.jsx(t,{className:"font-semibold",children:n.used_voucher_amount}),e.jsx(t,{className:"font-semibold",children:n.facebook_message}),e.jsx(t,{className:"font-semibold",children:n.zalo_message}),e.jsx(t,{className:"font-semibold",children:n.zalo_transaction_message}),e.jsx(t,{className:"font-semibold",children:n.sms_message}),e.jsx(t,{className:"font-semibold",children:n.zns_message}),e.jsx(t,{className:"font-semibold",children:n.zns_send_by_app_mc+n.promo_send_by_app_mc+n.trans_send_by_app_mc+n.cs_send_by_app_mc})]})]})]})})]})}function X(){const[n,r]=_.useState(()=>{const a=new Date;return`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}`}),[d,p]=_.useState(null),i=H(d),l=()=>{const[a,u]=n.split("-");p({year:a,month:u,pos_parent:"BRAND-953H"})};_.useEffect(()=>{l()},[]);const x=()=>{console.log("Export data")};return e.jsx($,{children:e.jsxs("div",{className:"container mx-auto space-y-6 py-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Lưu lượng sử dụng"})}),e.jsx(V,{selectedMonth:n,onMonthChange:r,onSearch:l,onExport:x,isLoading:i.isLoading}),i.data&&e.jsx(G,{data:i.data,searchParams:d})]})})}const Se=X;export{Se as component};
