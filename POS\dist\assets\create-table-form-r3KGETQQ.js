import{r as d,j as e,B as h,h as be,b as ve,l as ye}from"./index-UcdZ5AHH.js";import{u as Ce}from"./use-areas-DvC-z67k.js";import{u as we}from"./use-sales-channels-BiPb6QJ7.js";import{u as _e,e as Se,f as Ie,g as Te,b as Ue,h as ke}from"./use-tables--5pkDgd-.js";import"./date-range-picker-DxA68ufO.js";import{D as oe,g as me,a as xe,b as ze,c as Le}from"./dialog-DmI079wB.js";import{L as p}from"./form-D_U5B5Go.js";import{I as U}from"./input-CBpgGfUv.js";import{S as H,a as R,b as X,c as P,d as F,C as ae}from"./select-DOexGcsG.js";import{C as A}from"./checkbox-CDB9_T0n.js";import{C as ne,a as ie,b as le}from"./collapsible-Dz-Iaa-P.js";import"./pos-api-j20LMGrC.js";import{u as De}from"./use-items-in-store-data-CPIap05T.js";import"./user-9ajIul7r.js";import"./vietqr-api-9FERZtmQ.js";import"./crm-api-APQEjHWd.js";import{C as Me}from"./copy-CvzdUB3u.js";import{S as he}from"./search-B6f_4BGP.js";import{X as re}from"./calendar-BZ1UqQsL.js";import{P as ce}from"./plus-ioKLfxbk.js";import{C as de}from"./chevron-right-Dup7TmpK.js";import{M as Oe}from"./minus-D0JoVnU5.js";function qe({selectedItems:v,storeUid:y}){const[_,k]=d.useState(!1),[f,z]=d.useState(""),[u,C]=d.useState(new Set),[w,L]=d.useState(new Set),{data:g=[],isLoading:M}=_e({storeUid:y});console.log("CopyItemsToTablesModal - storeUid:",y,"isOpen:",_,"tables:",g);const{mutateAsync:D,isPending:j}=Se(),N=d.useMemo(()=>f?g.filter(l=>{var i,a;return((i=l.table_name)==null?void 0:i.toLowerCase().includes(f.toLowerCase()))||((a=l.id)==null?void 0:a.toLowerCase().includes(f.toLowerCase()))}):g,[g,f]),O=l=>{C(i=>{const a=new Set(i);return a.has(l)?a.delete(l):a.add(l),a})},K=async()=>{if(u.size!==0)try{const i=g.filter(a=>u.has(a.id)).map(a=>({...a,extra_data:{...a.extra_data,order_list:v.length>0?v.map(x=>({item_id:x.item_id,quantity:x.quantity})):[]}}));await D({storeUid:y,tables:i}),L(a=>{const x=new Set(a);return u.forEach(b=>x.add(b)),x}),k(!1),C(new Set),z("")}catch(l){console.error("Error copying items to tables:",l)}},q=()=>{k(!1),C(new Set),z("")};return e.jsxs(oe,{open:_,onOpenChange:k,children:[e.jsx(me,{asChild:!0,children:e.jsxs(h,{type:"button",variant:"outline",size:"sm",children:[e.jsx(Me,{className:"h-4 w-4 mr-2"}),"Sao chép tạo món mới"]})}),e.jsxs(xe,{className:"max-w-2xl max-h-[80vh] overflow-hidden flex flex-col",children:[e.jsx(ze,{children:e.jsx(Le,{children:"Sao chép món đặt trước"})}),e.jsx("div",{className:"text-sm text-gray-600 mb-4",children:"Chọn bàn cùng cửa hàng để đồng bộ món đặt trước"}),v.length===0&&e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4",children:e.jsx("div",{className:"text-sm text-yellow-700",children:"⚠️ Chưa có món nào được chọn. Việc sao chép sẽ xóa tất cả món đặt trước của các bàn được chọn."})}),e.jsxs("div",{className:"relative mb-4",children:[e.jsx(he,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx(U,{placeholder:"Tìm kiếm",value:f,onChange:l=>z(l.target.value),className:"pl-10"})]}),e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{checked:N.length>0&&N.every(l=>u.has(l.id)),onCheckedChange:l=>{C(l?i=>{const a=new Set(i);return N.forEach(x=>a.add(x.id)),a}:i=>{const a=new Set(i);return N.forEach(x=>a.delete(x.id)),a})}}),e.jsx("span",{className:"text-sm font-medium text-blue-600",children:"Chọn tất cả"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[w.size>0&&e.jsxs("span",{className:"text-sm text-green-600 font-medium",children:["✓ Đã sao chép: ",w.size," bàn"]}),u.size>0&&e.jsxs("span",{className:"text-sm text-blue-600 font-medium",children:["Đang chọn: ",u.size," bàn"]})]})]}),e.jsx("div",{className:"flex-1 overflow-y-auto space-y-2 max-h-96 border rounded-lg p-3",children:M?e.jsx("div",{className:"text-center py-8 text-gray-500",children:"Đang tải danh sách bàn..."}):e.jsxs(e.Fragment,{children:[N.map(l=>{const i=u.has(l.id),a=w.has(l.id);return e.jsxs("div",{className:`flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 ${a?"bg-green-50 border-green-200":""}`,children:[e.jsx(A,{checked:i,onCheckedChange:()=>O(l.id)}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"font-medium flex items-center gap-2",children:[l.table_name,a&&e.jsx("span",{className:"text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full",children:"✓ Đã sao chép"})]}),e.jsx("div",{className:"text-sm text-gray-500",children:l.id})]})]},l.id)}),N.length===0&&!M&&e.jsx("div",{className:"text-center py-8 text-gray-500",children:f?"Không tìm thấy bàn nào":"Không có bàn nào"})]})}),e.jsxs("div",{className:"flex justify-between pt-4 border-t",children:[e.jsx(h,{type:"button",variant:"outline",onClick:q,children:"Hủy"}),e.jsx(h,{type:"button",onClick:K,disabled:u.size===0||j,children:j?"Đang xử lý...":v.length===0?"Xóa món đặt trước":"Xác nhận"})]})]})]})}function is({areaId:v,storeUid:y}={}){var se,te;const _=be(),{createTable:k,isCreating:f}=Ie(),{updateTable:z,isUpdating:u}=Te(),{toggleTableStatus:C,isToggling:w}=Ue(),{company:L}=ve(),{selectedBrand:g}=ye(),[M,D]=d.useState(!1),[j,N]=d.useState(""),[O,K]=d.useState(!0),[q,l]=d.useState(!0),i=!!v&&!!y,{data:a,isLoading:x}=ke(v||"",y||""),b=d.useMemo(()=>{try{const s=localStorage.getItem("pos_stores_data");if(s){const n=JSON.parse(s);return Array.isArray(n)?n.filter(r=>r.active===1):[]}return[]}catch{return[]}},[]),[t,o]=d.useState({areaName:"",storeUid:y||((se=b[0])==null?void 0:se.id)||"",areaUid:"",description:"",sort:"",sourceId:"none",color:"",fontSize:"15",active:1,selectedItems:[]}),{data:J=[]}=Ce({storeUid:t.storeUid,page:1,results_per_page:15e3}),{data:ue=[]}=we({skipLimit:!0,page:1,results_per_page:15e3,companyUid:(L==null?void 0:L.id)||"",brandUid:(g==null?void 0:g.id)||"",storeUid:t.storeUid||""}),pe=ue||[],{data:S=[]}=De({params:{store_uid:t.storeUid,skip_limit:!0},enabled:!!t.storeUid});d.useEffect(()=>{var s,n,r,c,m;if(a&&i&&b.length>0){const I=b.find(T=>T.id===a.store_uid),Ne={areaName:a.table_name||"",storeUid:I?I.id:((s=b[0])==null?void 0:s.id)||"",areaUid:a.area_uid||"",description:a.description||"",sort:((n=a.sort)==null?void 0:n.toString())||"",sourceId:a.source_id||"none",color:((r=a.extra_data)==null?void 0:r.color)||"",fontSize:((c=a.extra_data)==null?void 0:c.font_size)||"15",active:a.active??1,selectedItems:(((m=a.extra_data)==null?void 0:m.order_list)||[]).map(T=>({item_id:T.item_id,item_name:T.item_name||T.item_id,quantity:T.quantity||0}))};o(Ne)}},[a,i,b]);const[Q,$]=d.useState(!1);d.useEffect(()=>{var s;if(i&&a&&S.length>0&&!Q){const n=((s=a.extra_data)==null?void 0:s.order_list)||[];if(n.length>0){const r=n.map(c=>{const m=S.find(I=>I.code===c.item_id);return{item_id:c.item_id,item_name:(m==null?void 0:m.name)||c.item_id,quantity:c.quantity||0}});o(c=>({...c,selectedItems:r})),$(!0)}}},[S,i,(te=a==null?void 0:a.extra_data)==null?void 0:te.order_list,Q]),d.useEffect(()=>{$(!1)},[a==null?void 0:a.id]);const ge=()=>{_({to:"/setting/table"})},E=d.useMemo(()=>j?S.filter(s=>{var n,r;return((n=s.name)==null?void 0:n.toLowerCase().includes(j.toLowerCase()))||((r=s.code)==null?void 0:r.toLowerCase().includes(j.toLowerCase()))}):S,[S,j]),V=d.useMemo(()=>E.filter(s=>t.selectedItems.some(n=>n.item_id===s.code)),[E,t.selectedItems]),B=d.useMemo(()=>E.filter(s=>!t.selectedItems.some(n=>n.item_id===s.code)),[E,t.selectedItems]),G=s=>{const n=s.code,r=s.name;if(!n)return;const c=t.selectedItems.some(m=>m.item_id===n);o(c?m=>({...m,selectedItems:m.selectedItems.filter(I=>I.item_id!==n)}):m=>({...m,selectedItems:[...m.selectedItems,{item_id:n,item_name:r,quantity:1}]}))},W=(s,n)=>{o(r=>({...r,selectedItems:r.selectedItems.map(c=>c.item_id===s?{...c,quantity:n}:c)}))},Y=s=>{o(n=>({...n,selectedItems:n.selectedItems.filter(r=>r.item_id!==s)}))},fe=async()=>{var s;if(Z)if(i&&a){const n={...a,table_name:t.areaName,description:t.description||void 0,sort:t.sort?parseInt(t.sort):a.sort,store_uid:t.storeUid,area_uid:t.areaUid,source_id:t.sourceId==="none"?"":t.sourceId,active:t.active,extra_data:{...a.extra_data,color:t.color,font_size:t.fontSize,order_list:t.selectedItems.map(r=>({item_id:r.item_id,quantity:r.quantity}))}};z(n,{onSuccess:()=>{_({to:"/setting/table"})}})}else{const n={table_name:t.areaName,description:t.description||void 0,store_uid:t.storeUid,area_uid:t.areaUid||((s=J[0])==null?void 0:s.id)||"",sort:t.sort?parseInt(t.sort):1,sourceId:t.sourceId==="none"?"":t.sourceId,color:t.color,fontSize:t.fontSize,selectedItems:t.selectedItems};k(n,{onSuccess:()=>{_({to:"/setting/table"})}})}},je=async()=>{if(!i||!a)return;C(a);const s=t.active===1?0:1;o(n=>({...n,active:s}))},Z=t.areaName.trim()!==""&&t.storeUid!==""&&(i||t.areaUid!==""),ee=f||u||x||w;return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(h,{variant:"ghost",size:"sm",onClick:ge,className:"flex items-center",children:e.jsx(re,{className:"h-4 w-4"})}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-medium",children:i?"Chi tiết bàn":"Tạo bàn"})}),e.jsxs("div",{className:"flex gap-2",children:[i&&e.jsx(h,{type:"button",variant:t.active===1?"destructive":"default",disabled:w,className:"min-w-[100px]",onClick:je,children:w?"Đang cập nhật...":t.active===1?"Deactive":"Active"}),e.jsx(h,{type:"button",disabled:ee||!Z,className:"min-w-[100px]",onClick:fe,children:ee?i?"Đang cập nhật...":"Đang tạo...":"Lưu"})]})]})}),e.jsx("div",{className:"mx-auto max-w-4xl",children:i&&x?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Đang tải dữ liệu bàn..."})}):e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{htmlFor:"area-name",className:"min-w-[200px] text-sm font-medium",children:"Tên bàn *"}),e.jsx(U,{id:"area-name",value:t.areaName,onChange:s=>o({...t,areaName:s.target.value}),placeholder:"Nhập tên bàn",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{className:"min-w-[200px] text-sm font-medium",children:"Cửa hàng *"}),e.jsxs(H,{value:t.storeUid,onValueChange:s=>o({...t,storeUid:s}),disabled:i,children:[e.jsx(R,{className:"flex-1",children:e.jsx(X,{placeholder:"Chọn cửa hàng"})}),e.jsx(P,{children:b.map(s=>e.jsx(F,{value:s.id,children:e.jsx("span",{className:"text-blue-500",children:s.store_name})},s.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{className:"min-w-[200px] text-sm font-medium",children:"Khu vực *"}),e.jsxs(H,{value:t.areaUid,onValueChange:s=>o({...t,areaUid:s}),children:[e.jsx(R,{className:"flex-1",children:e.jsx(X,{placeholder:"Chọn khu vực"})}),e.jsx(P,{children:J.map(s=>e.jsx(F,{value:s.id,children:e.jsx("span",{className:"text-blue-500",children:s.area_name})},s.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{className:"min-w-[200px] text-sm font-medium",children:"Nguồn đơn"}),e.jsxs(H,{value:t.sourceId,onValueChange:s=>o({...t,sourceId:s}),children:[e.jsx(R,{className:"flex-1",children:e.jsx(X,{placeholder:"Chọn nguồn đơn"})}),e.jsxs(P,{children:[e.jsx(F,{value:"none",children:e.jsx("span",{className:"text-blue-500",children:"None"})}),pe.map(s=>e.jsx(F,{value:s.sourceId,children:e.jsx("span",{className:"text-blue-500",children:s.sourceName})},s.id))]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{className:"min-w-[200px] text-sm font-medium",children:"Món đặt trước"}),e.jsxs("div",{className:"flex flex-1 gap-2",children:[e.jsxs(oe,{open:M,onOpenChange:D,children:[e.jsx(me,{asChild:!0,children:e.jsxs(h,{type:"button",variant:"outline",size:"sm",children:[e.jsx(ce,{className:"mr-2 h-4 w-4"}),"Thêm món"]})}),e.jsxs(xe,{className:"flex max-h-[80vh] max-w-2xl flex-col overflow-hidden",children:[e.jsxs("div",{className:"relative",children:[e.jsx(he,{className:"absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400"}),e.jsx(U,{placeholder:"Tìm kiếm món ăn...",value:j,onChange:s=>N(s.target.value),className:"pl-10"})]}),t.selectedItems.length>0&&e.jsx("div",{className:"mb-4 rounded-lg border border-green-200 bg-green-50 p-3",children:e.jsxs("div",{className:"text-sm font-medium text-green-700",children:["✓ Đã chọn ",t.selectedItems.length," món"]})}),e.jsxs("div",{className:"max-h-96 flex-1 space-y-4 overflow-y-auto",children:[e.jsxs(ne,{open:O,onOpenChange:K,children:[e.jsxs(ie,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Đã chọn (",V.length,")"]}),O?e.jsx(ae,{className:"h-4 w-4"}):e.jsx(de,{className:"h-4 w-4"})]}),e.jsx(le,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:V.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Chưa có món nào được chọn"}):V.map(s=>{const n=s.code,r=t.selectedItems.some(c=>c.item_id===n);return e.jsxs("div",{className:"flex items-center space-x-3 rounded-lg border p-3 hover:bg-gray-50",children:[e.jsx(A,{checked:r,onCheckedChange:()=>G(s)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium",children:s.name}),e.jsx("div",{className:"text-sm text-gray-500",children:n})]})]},s.id)})})})]}),e.jsxs(ne,{open:q,onOpenChange:l,children:[e.jsxs(ie,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Còn lại (",B.length,")"]}),q?e.jsx(ae,{className:"h-4 w-4"}):e.jsx(de,{className:"h-4 w-4"})]}),e.jsx(le,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:B.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Không có món nào"}):B.map(s=>{const n=s.code,r=t.selectedItems.some(c=>c.item_id===n);return e.jsxs("div",{className:"flex items-center space-x-3 rounded-lg border p-3 hover:bg-gray-50",children:[e.jsx(A,{checked:r,onCheckedChange:()=>G(s)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium",children:s.name}),e.jsx("div",{className:"text-sm text-gray-500",children:n})]})]},s.id)})})})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 border-t pt-4",children:[e.jsx(h,{type:"button",variant:"outline",onClick:()=>D(!1),children:"Hủy"}),e.jsx(h,{type:"button",onClick:()=>D(!1),children:"Xong"})]})]})]}),e.jsx(qe,{selectedItems:t.selectedItems,storeUid:t.storeUid})]})]}),t.selectedItems.length>0&&e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(p,{className:"min-w-[200px] pt-2 text-sm font-medium"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"mb-3 rounded-lg border border-blue-200 bg-blue-50 p-3",children:e.jsxs("div",{className:"text-sm font-medium text-blue-700",children:["Tổng số món: ",t.selectedItems.length]})}),e.jsx("div",{className:"max-h-40 space-y-2 overflow-y-auto rounded-lg border p-3",children:t.selectedItems.map(s=>e.jsxs("div",{className:"flex items-center justify-between gap-2 rounded bg-gray-50 p-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm font-medium",children:s.item_name}),e.jsx("div",{className:"text-xs text-gray-500",children:s.item_id})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(h,{type:"button",variant:"outline",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{s.quantity>1?W(s.item_id,s.quantity-1):Y(s.item_id)},children:e.jsx(Oe,{className:"h-4 w-4"})}),e.jsx("div",{className:"w-12 text-center text-sm font-medium",children:s.quantity}),e.jsx(h,{type:"button",variant:"outline",size:"sm",className:"h-8 w-8 p-0",onClick:()=>W(s.item_id,s.quantity+1),children:e.jsx(ce,{className:"h-4 w-4"})}),e.jsx(h,{type:"button",variant:"ghost",size:"sm",onClick:()=>Y(s.item_id),className:"ml-2 h-8 w-8 p-0 text-red-500 hover:text-red-700",children:e.jsx(re,{className:"h-4 w-4"})})]})]},s.item_id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{htmlFor:"area-description",className:"min-w-[200px] text-sm font-medium",children:"Mô tả"}),e.jsx(U,{id:"area-description",value:t.description,onChange:s=>o({...t,description:s.target.value}),placeholder:"Mô tả",className:"flex-1"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thứ tự hiển thị trong thiết bị bán hàng"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Khu vực có số nhỏ hơn sẽ được sắp xếp lên trên trong thiết bị bán hàng"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{htmlFor:"area-sort",className:"min-w-[200px] text-sm font-medium",children:"Thứ tự hiển thị"}),e.jsx(U,{id:"area-sort",type:"number",value:t.sort,onChange:s=>o({...t,sort:s.target.value}),placeholder:"Nhập số thứ tự hiển thị",className:"flex-1"})]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{className:"min-w-[200px] text-sm font-medium",children:"Màu chữ"}),e.jsx("div",{className:"flex-1",children:e.jsx("input",{type:"color",value:t.color||"#545454",onChange:s=>o({...t,color:s.target.value}),className:"h-8 w-12 cursor-pointer rounded border border-gray-300"})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(p,{htmlFor:"font-size",className:"min-w-[200px] text-sm font-medium",children:"Kích thước chữ (pixels)"}),e.jsx(U,{id:"font-size",type:"number",min:"8",max:"50",value:t.fontSize,onChange:s=>o({...t,fontSize:s.target.value}),placeholder:"15",className:"flex-1"})]})]})})})]})}export{is as C};
