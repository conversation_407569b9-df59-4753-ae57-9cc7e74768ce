import{aq as Fn,g as ue,r as q,d as ne,R as T,ar as Ut}from"./index-UcdZ5AHH.js";import{r as Dx}from"./index-Chjiymov.js";var Ma,Gf;function Ie(){if(Gf)return Ma;Gf=1;var e=Array.isArray;return Ma=e,Ma}var Ca,Kf;function Sm(){if(Kf)return Ca;Kf=1;var e=typeof Fn=="object"&&Fn&&Fn.Object===Object&&Fn;return Ca=e,Ca}var $a,Xf;function nt(){if(Xf)return $a;Xf=1;var e=Sm(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return $a=r,$a}var Ia,Vf;function Mn(){if(Vf)return Ia;Vf=1;var e=nt(),t=e.Symbol;return Ia=t,Ia}var Ra,Yf;function kx(){if(Yf)return Ra;Yf=1;var e=Mn(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,i=e?e.toStringTag:void 0;function a(o){var u=r.call(o,i),c=o[i];try{o[i]=void 0;var s=!0}catch{}var f=n.call(o);return s&&(u?o[i]=c:delete o[i]),f}return Ra=a,Ra}var Da,Zf;function qx(){if(Zf)return Da;Zf=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return Da=r,Da}var ka,Jf;function dt(){if(Jf)return ka;Jf=1;var e=Mn(),t=kx(),r=qx(),n="[object Null]",i="[object Undefined]",a=e?e.toStringTag:void 0;function o(u){return u==null?u===void 0?i:n:a&&a in Object(u)?t(u):r(u)}return ka=o,ka}var qa,Qf;function vt(){if(Qf)return qa;Qf=1;function e(t){return t!=null&&typeof t=="object"}return qa=e,qa}var Na,eh;function Pr(){if(eh)return Na;eh=1;var e=dt(),t=vt(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return Na=n,Na}var Ba,th;function Ul(){if(th)return Ba;th=1;var e=Ie(),t=Pr(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){if(e(a))return!1;var u=typeof a;return u=="number"||u=="symbol"||u=="boolean"||a==null||t(a)?!0:n.test(a)||!r.test(a)||o!=null&&a in Object(o)}return Ba=i,Ba}var La,rh;function _t(){if(rh)return La;rh=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return La=e,La}var Fa,nh;function Wl(){if(nh)return Fa;nh=1;var e=dt(),t=_t(),r="[object AsyncFunction]",n="[object Function]",i="[object GeneratorFunction]",a="[object Proxy]";function o(u){if(!t(u))return!1;var c=e(u);return c==n||c==i||c==r||c==a}return Fa=o,Fa}var Ua,ih;function Nx(){if(ih)return Ua;ih=1;var e=nt(),t=e["__core-js_shared__"];return Ua=t,Ua}var Wa,ah;function Bx(){if(ah)return Wa;ah=1;var e=Nx(),t=function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function r(n){return!!t&&t in n}return Wa=r,Wa}var za,oh;function Am(){if(oh)return za;oh=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch{}try{return n+""}catch{}}return""}return za=r,za}var Ha,uh;function Lx(){if(uh)return Ha;uh=1;var e=Wl(),t=Bx(),r=_t(),n=Am(),i=/[\\^$.*+?()[\]{}|]/g,a=/^\[object .+?Constructor\]$/,o=Function.prototype,u=Object.prototype,c=o.toString,s=u.hasOwnProperty,f=RegExp("^"+c.call(s).replace(i,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function l(h){if(!r(h)||t(h))return!1;var d=e(h)?f:a;return d.test(n(h))}return Ha=l,Ha}var Ga,ch;function Fx(){if(ch)return Ga;ch=1;function e(t,r){return t==null?void 0:t[r]}return Ga=e,Ga}var Ka,sh;function zt(){if(sh)return Ka;sh=1;var e=Lx(),t=Fx();function r(n,i){var a=t(n,i);return e(a)?a:void 0}return Ka=r,Ka}var Xa,lh;function Yi(){if(lh)return Xa;lh=1;var e=zt(),t=e(Object,"create");return Xa=t,Xa}var Va,fh;function Ux(){if(fh)return Va;fh=1;var e=Yi();function t(){this.__data__=e?e(null):{},this.size=0}return Va=t,Va}var Ya,hh;function Wx(){if(hh)return Ya;hh=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return Ya=e,Ya}var Za,ph;function zx(){if(ph)return Za;ph=1;var e=Yi(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function i(a){var o=this.__data__;if(e){var u=o[a];return u===t?void 0:u}return n.call(o,a)?o[a]:void 0}return Za=i,Za}var Ja,dh;function Hx(){if(dh)return Ja;dh=1;var e=Yi(),t=Object.prototype,r=t.hasOwnProperty;function n(i){var a=this.__data__;return e?a[i]!==void 0:r.call(a,i)}return Ja=n,Ja}var Qa,vh;function Gx(){if(vh)return Qa;vh=1;var e=Yi(),t="__lodash_hash_undefined__";function r(n,i){var a=this.__data__;return this.size+=this.has(n)?0:1,a[n]=e&&i===void 0?t:i,this}return Qa=r,Qa}var eo,yh;function Kx(){if(yh)return eo;yh=1;var e=Ux(),t=Wx(),r=zx(),n=Hx(),i=Gx();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,eo=a,eo}var to,gh;function Xx(){if(gh)return to;gh=1;function e(){this.__data__=[],this.size=0}return to=e,to}var ro,mh;function zl(){if(mh)return ro;mh=1;function e(t,r){return t===r||t!==t&&r!==r}return ro=e,ro}var no,bh;function Zi(){if(bh)return no;bh=1;var e=zl();function t(r,n){for(var i=r.length;i--;)if(e(r[i][0],n))return i;return-1}return no=t,no}var io,xh;function Vx(){if(xh)return io;xh=1;var e=Zi(),t=Array.prototype,r=t.splice;function n(i){var a=this.__data__,o=e(a,i);if(o<0)return!1;var u=a.length-1;return o==u?a.pop():r.call(a,o,1),--this.size,!0}return io=n,io}var ao,wh;function Yx(){if(wh)return ao;wh=1;var e=Zi();function t(r){var n=this.__data__,i=e(n,r);return i<0?void 0:n[i][1]}return ao=t,ao}var oo,Oh;function Zx(){if(Oh)return oo;Oh=1;var e=Zi();function t(r){return e(this.__data__,r)>-1}return oo=t,oo}var uo,_h;function Jx(){if(_h)return uo;_h=1;var e=Zi();function t(r,n){var i=this.__data__,a=e(i,r);return a<0?(++this.size,i.push([r,n])):i[a][1]=n,this}return uo=t,uo}var co,Sh;function Ji(){if(Sh)return co;Sh=1;var e=Xx(),t=Vx(),r=Yx(),n=Zx(),i=Jx();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,co=a,co}var so,Ah;function Hl(){if(Ah)return so;Ah=1;var e=zt(),t=nt(),r=e(t,"Map");return so=r,so}var lo,Ph;function Qx(){if(Ph)return lo;Ph=1;var e=Kx(),t=Ji(),r=Hl();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return lo=n,lo}var fo,Th;function ew(){if(Th)return fo;Th=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return fo=e,fo}var ho,Eh;function Qi(){if(Eh)return ho;Eh=1;var e=ew();function t(r,n){var i=r.__data__;return e(n)?i[typeof n=="string"?"string":"hash"]:i.map}return ho=t,ho}var po,jh;function tw(){if(jh)return po;jh=1;var e=Qi();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return po=t,po}var vo,Mh;function rw(){if(Mh)return vo;Mh=1;var e=Qi();function t(r){return e(this,r).get(r)}return vo=t,vo}var yo,Ch;function nw(){if(Ch)return yo;Ch=1;var e=Qi();function t(r){return e(this,r).has(r)}return yo=t,yo}var go,$h;function iw(){if($h)return go;$h=1;var e=Qi();function t(r,n){var i=e(this,r),a=i.size;return i.set(r,n),this.size+=i.size==a?0:1,this}return go=t,go}var mo,Ih;function Gl(){if(Ih)return mo;Ih=1;var e=Qx(),t=tw(),r=rw(),n=nw(),i=iw();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,mo=a,mo}var bo,Rh;function Pm(){if(Rh)return bo;Rh=1;var e=Gl(),t="Expected a function";function r(n,i){if(typeof n!="function"||i!=null&&typeof i!="function")throw new TypeError(t);var a=function(){var o=arguments,u=i?i.apply(this,o):o[0],c=a.cache;if(c.has(u))return c.get(u);var s=n.apply(this,o);return a.cache=c.set(u,s)||c,s};return a.cache=new(r.Cache||e),a}return r.Cache=e,bo=r,bo}var xo,Dh;function aw(){if(Dh)return xo;Dh=1;var e=Pm(),t=500;function r(n){var i=e(n,function(o){return a.size===t&&a.clear(),o}),a=i.cache;return i}return xo=r,xo}var wo,kh;function ow(){if(kh)return wo;kh=1;var e=aw(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(i){var a=[];return i.charCodeAt(0)===46&&a.push(""),i.replace(t,function(o,u,c,s){a.push(c?s.replace(r,"$1"):u||o)}),a});return wo=n,wo}var Oo,qh;function Kl(){if(qh)return Oo;qh=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=Array(i);++n<i;)a[n]=r(t[n],n,t);return a}return Oo=e,Oo}var _o,Nh;function uw(){if(Nh)return _o;Nh=1;var e=Mn(),t=Kl(),r=Ie(),n=Pr(),i=e?e.prototype:void 0,a=i?i.toString:void 0;function o(u){if(typeof u=="string")return u;if(r(u))return t(u,o)+"";if(n(u))return a?a.call(u):"";var c=u+"";return c=="0"&&1/u==-1/0?"-0":c}return _o=o,_o}var So,Bh;function Tm(){if(Bh)return So;Bh=1;var e=uw();function t(r){return r==null?"":e(r)}return So=t,So}var Ao,Lh;function Em(){if(Lh)return Ao;Lh=1;var e=Ie(),t=Ul(),r=ow(),n=Tm();function i(a,o){return e(a)?a:t(a,o)?[a]:r(n(a))}return Ao=i,Ao}var Po,Fh;function ea(){if(Fh)return Po;Fh=1;var e=Pr();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return Po=t,Po}var To,Uh;function Xl(){if(Uh)return To;Uh=1;var e=Em(),t=ea();function r(n,i){i=e(i,n);for(var a=0,o=i.length;n!=null&&a<o;)n=n[t(i[a++])];return a&&a==o?n:void 0}return To=r,To}var Eo,Wh;function jm(){if(Wh)return Eo;Wh=1;var e=Xl();function t(r,n,i){var a=r==null?void 0:e(r,n);return a===void 0?i:a}return Eo=t,Eo}var cw=jm();const Fe=ue(cw);var jo,zh;function sw(){if(zh)return jo;zh=1;function e(t){return t==null}return jo=e,jo}var lw=sw();const Z=ue(lw);var Mo,Hh;function fw(){if(Hh)return Mo;Hh=1;var e=dt(),t=Ie(),r=vt(),n="[object String]";function i(a){return typeof a=="string"||!t(a)&&r(a)&&e(a)==n}return Mo=i,Mo}var hw=fw();const Cn=ue(hw);var pw=Wl();const Y=ue(pw);var dw=_t();const Tr=ue(dw);var Co={exports:{}},ee={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gh;function vw(){if(Gh)return ee;Gh=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),o=Symbol.for("react.context"),u=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),s=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),l=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),d=Symbol.for("react.offscreen"),y;y=Symbol.for("react.module.reference");function v(p){if(typeof p=="object"&&p!==null){var b=p.$$typeof;switch(b){case e:switch(p=p.type,p){case r:case i:case n:case s:case f:return p;default:switch(p=p&&p.$$typeof,p){case u:case o:case c:case h:case l:case a:return p;default:return b}}case t:return b}}}return ee.ContextConsumer=o,ee.ContextProvider=a,ee.Element=e,ee.ForwardRef=c,ee.Fragment=r,ee.Lazy=h,ee.Memo=l,ee.Portal=t,ee.Profiler=i,ee.StrictMode=n,ee.Suspense=s,ee.SuspenseList=f,ee.isAsyncMode=function(){return!1},ee.isConcurrentMode=function(){return!1},ee.isContextConsumer=function(p){return v(p)===o},ee.isContextProvider=function(p){return v(p)===a},ee.isElement=function(p){return typeof p=="object"&&p!==null&&p.$$typeof===e},ee.isForwardRef=function(p){return v(p)===c},ee.isFragment=function(p){return v(p)===r},ee.isLazy=function(p){return v(p)===h},ee.isMemo=function(p){return v(p)===l},ee.isPortal=function(p){return v(p)===t},ee.isProfiler=function(p){return v(p)===i},ee.isStrictMode=function(p){return v(p)===n},ee.isSuspense=function(p){return v(p)===s},ee.isSuspenseList=function(p){return v(p)===f},ee.isValidElementType=function(p){return typeof p=="string"||typeof p=="function"||p===r||p===i||p===n||p===s||p===f||p===d||typeof p=="object"&&p!==null&&(p.$$typeof===h||p.$$typeof===l||p.$$typeof===a||p.$$typeof===o||p.$$typeof===c||p.$$typeof===y||p.getModuleId!==void 0)},ee.typeOf=v,ee}var Kh;function yw(){return Kh||(Kh=1,Co.exports=vw()),Co.exports}var gw=yw(),$o,Xh;function Mm(){if(Xh)return $o;Xh=1;var e=dt(),t=vt(),r="[object Number]";function n(i){return typeof i=="number"||t(i)&&e(i)==r}return $o=n,$o}var Io,Vh;function mw(){if(Vh)return Io;Vh=1;var e=Mm();function t(r){return e(r)&&r!=+r}return Io=t,Io}var bw=mw();const $n=ue(bw);var xw=Mm();const ww=ue(xw);var Xe=function(t){return t===0?0:t>0?1:-1},Rt=function(t){return Cn(t)&&t.indexOf("%")===t.length-1},F=function(t){return ww(t)&&!$n(t)},be=function(t){return F(t)||Cn(t)},Ow=0,ta=function(t){var r=++Ow;return"".concat(t||"").concat(r)},Ve=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!F(t)&&!Cn(t))return n;var a;if(Rt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return $n(a)&&(a=n),i&&a>r&&(a=r),a},bt=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},_w=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},Vt=function(t,r){return F(t)&&F(r)?function(n){return t+n*(r-t)}:function(){return r}};function ti(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Fe(n,t))===r})}var VD=function(t){if(!t||!t.length)return null;for(var r=t.length,n=0,i=0,a=0,o=0,u=1/0,c=-1/0,s=0,f=0,l=0;l<r;l++)s=t[l].cx||0,f=t[l].cy||0,n+=s,i+=f,a+=s*f,o+=s*s,u=Math.min(u,s),c=Math.max(c,s);var h=r*o!==n*n?(r*a-n*i)/(r*o-n*n):0;return{xmin:u,xmax:c,a:h,b:(i-h*n)/r}};function tr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Ts(e){"@babel/helpers - typeof";return Ts=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ts(e)}var Sw=["viewBox","children"],Aw=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Yh=["points","pathLength"],Ro={svg:Sw,polygon:Yh,polyline:Yh},Vl=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],ri=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(q.isValidElement(t)&&(n=t.props),!Tr(n))return null;var i={};return Object.keys(n).forEach(function(a){Vl.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},Pw=function(t,r,n){return function(i){return t(r,n,i),null}},ni=function(t,r,n){if(!Tr(t)||Ts(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];Vl.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=Pw(o,r,n))}),i},Tw=["children"],Ew=["children"];function Zh(e,t){if(e==null)return{};var r=jw(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function jw(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Es(e){"@babel/helpers - typeof";return Es=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Es(e)}var Jh={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},st=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Qh=null,Do=null,Yl=function e(t){if(t===Qh&&Array.isArray(Do))return Do;var r=[];return q.Children.forEach(t,function(n){Z(n)||(gw.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),Do=r,Qh=t,r};function Ye(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return st(i)}):n=[st(t)],Yl(e).forEach(function(i){var a=Fe(i,"type.displayName")||Fe(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function ke(e,t){var r=Ye(e,t);return r&&r[0]}var ep=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!F(n)||n<=0||!F(i)||i<=0)},Mw=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],Cw=function(t){return t&&t.type&&Cn(t.type)&&Mw.indexOf(t.type)>=0},YD=function(t){return t&&Es(t)==="object"&&"clipDot"in t},$w=function(t,r,n,i){var a,o=(a=Ro==null?void 0:Ro[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!Y(t)&&(i&&o.includes(r)||Aw.includes(r))||n&&Vl.includes(r)},re=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(q.isValidElement(t)&&(i=t.props),!Tr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;$w((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},js=function e(t,r){if(t===r)return!0;var n=q.Children.count(t);if(n!==q.Children.count(r))return!1;if(n===0)return!0;if(n===1)return tp(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!tp(a,o))return!1}return!0},tp=function(t,r){if(Z(t)&&Z(r))return!0;if(!Z(t)&&!Z(r)){var n=t.props||{},i=n.children,a=Zh(n,Tw),o=r.props||{},u=o.children,c=Zh(o,Ew);return i&&u?tr(a,c)&&js(i,u):!i&&!u?tr(a,c):!1}return!1},rp=function(t,r){var n=[],i={};return Yl(t).forEach(function(a,o){if(Cw(a))n.push(a);else if(a){var u=st(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},Iw=function(t){var r=t&&t.type;return r&&Jh[r]?Jh[r]:null},Rw=function(t,r){return Yl(r).indexOf(t)},Dw=["children","width","height","viewBox","className","style","title","desc"];function Ms(){return Ms=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ms.apply(this,arguments)}function kw(e,t){if(e==null)return{};var r=qw(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function qw(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Cs(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=kw(e,Dw),f=i||{width:r,height:n,x:0,y:0},l=ne("recharts-surface",a);return T.createElement("svg",Ms({},re(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),T.createElement("title",null,u),T.createElement("desc",null,c),t)}var Nw=["children","className"];function $s(){return $s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$s.apply(this,arguments)}function Bw(e,t){if(e==null)return{};var r=Lw(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Lw(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Oe=T.forwardRef(function(e,t){var r=e.children,n=e.className,i=Bw(e,Nw),a=ne("recharts-layer",n);return T.createElement("g",$s({className:a},re(i,!0),{ref:t}),r)}),Nt=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]},ko,np;function Fw(){if(np)return ko;np=1;function e(t,r,n){var i=-1,a=t.length;r<0&&(r=-r>a?0:a+r),n=n>a?a:n,n<0&&(n+=a),a=r>n?0:n-r>>>0,r>>>=0;for(var o=Array(a);++i<a;)o[i]=t[i+r];return o}return ko=e,ko}var qo,ip;function Uw(){if(ip)return qo;ip=1;var e=Fw();function t(r,n,i){var a=r.length;return i=i===void 0?a:i,!n&&i>=a?r:e(r,n,i)}return qo=t,qo}var No,ap;function Cm(){if(ap)return No;ap=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="\\u200d",u=RegExp("["+o+e+i+a+"]");function c(s){return u.test(s)}return No=c,No}var Bo,op;function Ww(){if(op)return Bo;op=1;function e(t){return t.split("")}return Bo=e,Bo}var Lo,up;function zw(){if(up)return Lo;up=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="["+e+"]",u="["+i+"]",c="\\ud83c[\\udffb-\\udfff]",s="(?:"+u+"|"+c+")",f="[^"+e+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",h="[\\ud800-\\udbff][\\udc00-\\udfff]",d="\\u200d",y=s+"?",v="["+a+"]?",p="(?:"+d+"(?:"+[f,l,h].join("|")+")"+v+y+")*",b=v+y+p,w="(?:"+[f+u+"?",u,l,h,o].join("|")+")",x=RegExp(c+"(?="+c+")|"+w+b,"g");function O(g){return g.match(x)||[]}return Lo=O,Lo}var Fo,cp;function Hw(){if(cp)return Fo;cp=1;var e=Ww(),t=Cm(),r=zw();function n(i){return t(i)?r(i):e(i)}return Fo=n,Fo}var Uo,sp;function Gw(){if(sp)return Uo;sp=1;var e=Uw(),t=Cm(),r=Hw(),n=Tm();function i(a){return function(o){o=n(o);var u=t(o)?r(o):void 0,c=u?u[0]:o.charAt(0),s=u?e(u,1).join(""):o.slice(1);return c[a]()+s}}return Uo=i,Uo}var Wo,lp;function Kw(){if(lp)return Wo;lp=1;var e=Gw(),t=e("toUpperCase");return Wo=t,Wo}var Xw=Kw();const ra=ue(Xw);function oe(e){return function(){return e}}const $m=Math.cos,ii=Math.sin,Je=Math.sqrt,ai=Math.PI,na=2*ai,Is=Math.PI,Rs=2*Is,$t=1e-6,Vw=Rs-$t;function Im(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function Yw(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Im;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class Zw{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Im:Yw(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,h=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(h>$t)if(!(Math.abs(l*c-s*f)>$t)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let d=n-o,y=i-u,v=c*c+s*s,p=d*d+y*y,b=Math.sqrt(v),w=Math.sqrt(h),x=a*Math.tan((Is-Math.acos((v+h-p)/(2*b*w)))/2),O=x/w,g=x/b;Math.abs(O-1)>$t&&this._append`L${t+O*f},${r+O*l}`,this._append`A${a},${a},0,0,${+(l*d>f*y)},${this._x1=t+g*c},${this._y1=r+g*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,h=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>$t||Math.abs(this._y1-f)>$t)&&this._append`L${s},${f}`,n&&(h<0&&(h=h%Rs+Rs),h>Vw?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:h>$t&&this._append`A${n},${n},0,${+(h>=Is)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Zl(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new Zw(t)}function Jl(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Rm(e){this._context=e}Rm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function ia(e){return new Rm(e)}function Dm(e){return e[0]}function km(e){return e[1]}function qm(e,t){var r=oe(!0),n=null,i=ia,a=null,o=Zl(u);e=typeof e=="function"?e:e===void 0?Dm:oe(e),t=typeof t=="function"?t:t===void 0?km:oe(t);function u(c){var s,f=(c=Jl(c)).length,l,h=!1,d;for(n==null&&(a=i(d=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(l,s,c),+t(l,s,c));if(d)return a=null,d+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:oe(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:oe(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:oe(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function Un(e,t,r){var n=null,i=oe(!0),a=null,o=ia,u=null,c=Zl(s);e=typeof e=="function"?e:e===void 0?Dm:oe(+e),t=typeof t=="function"?t:oe(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?km:oe(+r);function s(l){var h,d,y,v=(l=Jl(l)).length,p,b=!1,w,x=new Array(v),O=new Array(v);for(a==null&&(u=o(w=c())),h=0;h<=v;++h){if(!(h<v&&i(p=l[h],h,l))===b)if(b=!b)d=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=h-1;y>=d;--y)u.point(x[y],O[y]);u.lineEnd(),u.areaEnd()}b&&(x[h]=+e(p,h,l),O[h]=+t(p,h,l),u.point(n?+n(p,h,l):x[h],r?+r(p,h,l):O[h]))}if(w)return u=null,w+""||null}function f(){return qm().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:oe(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:oe(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:oe(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class Nm{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function Jw(e){return new Nm(e,!0)}function Qw(e){return new Nm(e,!1)}const Ql={draw(e,t){const r=Je(t/ai);e.moveTo(r,0),e.arc(0,0,r,0,na)}},e1={draw(e,t){const r=Je(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Bm=Je(1/3),t1=Bm*2,r1={draw(e,t){const r=Je(t/t1),n=r*Bm;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},n1={draw(e,t){const r=Je(t),n=-r/2;e.rect(n,n,r,r)}},i1=.8908130915292852,Lm=ii(ai/10)/ii(7*ai/10),a1=ii(na/10)*Lm,o1=-$m(na/10)*Lm,u1={draw(e,t){const r=Je(t*i1),n=a1*r,i=o1*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=na*a/5,u=$m(o),c=ii(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},zo=Je(3),c1={draw(e,t){const r=-Je(t/(zo*3));e.moveTo(0,r*2),e.lineTo(-zo*r,-r),e.lineTo(zo*r,-r),e.closePath()}},Ne=-.5,Be=Je(3)/2,Ds=1/Je(12),s1=(Ds/2+1)*3,l1={draw(e,t){const r=Je(t/s1),n=r/2,i=r*Ds,a=n,o=r*Ds+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(Ne*n-Be*i,Be*n+Ne*i),e.lineTo(Ne*a-Be*o,Be*a+Ne*o),e.lineTo(Ne*u-Be*c,Be*u+Ne*c),e.lineTo(Ne*n+Be*i,Ne*i-Be*n),e.lineTo(Ne*a+Be*o,Ne*o-Be*a),e.lineTo(Ne*u+Be*c,Ne*c-Be*u),e.closePath()}};function f1(e,t){let r=null,n=Zl(i);e=typeof e=="function"?e:oe(e||Ql),t=typeof t=="function"?t:oe(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:oe(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:oe(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function oi(){}function ui(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function Fm(e){this._context=e}Fm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:ui(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:ui(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function h1(e){return new Fm(e)}function Um(e){this._context=e}Um.prototype={areaStart:oi,areaEnd:oi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:ui(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function p1(e){return new Um(e)}function Wm(e){this._context=e}Wm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:ui(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function d1(e){return new Wm(e)}function zm(e){this._context=e}zm.prototype={areaStart:oi,areaEnd:oi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function v1(e){return new zm(e)}function fp(e){return e<0?-1:1}function hp(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(fp(a)+fp(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function pp(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Ho(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function ci(e){this._context=e}ci.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Ho(this,this._t0,pp(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Ho(this,pp(this,r=hp(this,e,t)),r);break;default:Ho(this,this._t0,r=hp(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Hm(e){this._context=new Gm(e)}(Hm.prototype=Object.create(ci.prototype)).point=function(e,t){ci.prototype.point.call(this,t,e)};function Gm(e){this._context=e}Gm.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function y1(e){return new ci(e)}function g1(e){return new Hm(e)}function Km(e){this._context=e}Km.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=dp(e),i=dp(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function dp(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function m1(e){return new Km(e)}function aa(e,t){this._context=e,this._t=t}aa.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function b1(e){return new aa(e,.5)}function x1(e){return new aa(e,0)}function w1(e){return new aa(e,1)}function ar(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function ks(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function O1(e,t){return e[t]}function _1(e){const t=[];return t.key=e,t}function S1(){var e=oe([]),t=ks,r=ar,n=O1;function i(a){var o=Array.from(e.apply(this,arguments),_1),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=Jl(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:oe(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:oe(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?ks:typeof a=="function"?a:oe(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??ar,i):r},i}function A1(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}ar(e,t)}}function P1(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}ar(e,t)}}function T1(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,h=f[n-1][1]||0,d=(l-h)/2,y=0;y<u;++y){var v=e[t[y]],p=v[n][1]||0,b=v[n-1][1]||0;d+=p-b}c+=l,s+=d*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,ar(e,t)}}function Xr(e){"@babel/helpers - typeof";return Xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xr(e)}var E1=["type","size","sizeType"];function qs(){return qs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qs.apply(this,arguments)}function vp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function yp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vp(Object(r),!0).forEach(function(n){j1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function j1(e,t,r){return t=M1(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function M1(e){var t=C1(e,"string");return Xr(t)=="symbol"?t:t+""}function C1(e,t){if(Xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $1(e,t){if(e==null)return{};var r=I1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function I1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Xm={symbolCircle:Ql,symbolCross:e1,symbolDiamond:r1,symbolSquare:n1,symbolStar:u1,symbolTriangle:c1,symbolWye:l1},R1=Math.PI/180,D1=function(t){var r="symbol".concat(ra(t));return Xm[r]||Ql},k1=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*R1;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},q1=function(t,r){Xm["symbol".concat(ra(t))]=r},ef=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=$1(t,E1),s=yp(yp({},c),{},{type:n,size:a,sizeType:u}),f=function(){var p=D1(n),b=f1().type(p).size(k1(a,u,n));return b()},l=s.className,h=s.cx,d=s.cy,y=re(s,!0);return h===+h&&d===+d&&a===+a?T.createElement("path",qs({},y,{className:ne("recharts-symbols",l),transform:"translate(".concat(h,", ").concat(d,")"),d:f()})):null};ef.registerSymbol=q1;function or(e){"@babel/helpers - typeof";return or=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},or(e)}function Ns(){return Ns=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ns.apply(this,arguments)}function gp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function N1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gp(Object(r),!0).forEach(function(n){Vr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function B1(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function L1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ym(n.key),n)}}function F1(e,t,r){return t&&L1(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function U1(e,t,r){return t=si(t),W1(e,Vm()?Reflect.construct(t,r||[],si(e).constructor):t.apply(e,r))}function W1(e,t){if(t&&(or(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return z1(e)}function z1(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vm=function(){return!!e})()}function si(e){return si=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},si(e)}function H1(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Bs(e,t)}function Bs(e,t){return Bs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Bs(e,t)}function Vr(e,t,r){return t=Ym(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ym(e){var t=G1(e,"string");return or(t)=="symbol"?t:t+""}function G1(e,t){if(or(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(or(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ze=32,tf=function(e){function t(){return B1(this,t),U1(this,t,arguments)}return H1(t,e),F1(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=ze/2,o=ze/6,u=ze/3,c=n.inactive?i:n.color;if(n.type==="plainline")return T.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:ze,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return T.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(ze,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return T.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(ze/8,"h").concat(ze,"v").concat(ze*3/4,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(T.isValidElement(n.legendIcon)){var s=N1({},n);return delete s.legendIcon,T.cloneElement(n.legendIcon,s)}return T.createElement(ef,{fill:c,cx:a,cy:a,size:ze,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:ze,height:ze},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(d,y){var v=d.formatter||c,p=ne(Vr(Vr({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",d.inactive));if(d.type==="none")return null;var b=Y(d.value)?null:d.value;Nt(!Y(d.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var w=d.inactive?s:d.color;return T.createElement("li",Ns({className:p,style:l,key:"legend-item-".concat(y)},ni(n.props,d,y)),T.createElement(Cs,{width:o,height:o,viewBox:f,style:h},n.renderIcon(d)),T.createElement("span",{className:"recharts-legend-item-text",style:{color:w}},v?v(b,d,y):b))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return T.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(q.PureComponent);Vr(tf,"displayName","Legend");Vr(tf,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var Go,mp;function K1(){if(mp)return Go;mp=1;var e=Ji();function t(){this.__data__=new e,this.size=0}return Go=t,Go}var Ko,bp;function X1(){if(bp)return Ko;bp=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return Ko=e,Ko}var Xo,xp;function V1(){if(xp)return Xo;xp=1;function e(t){return this.__data__.get(t)}return Xo=e,Xo}var Vo,wp;function Y1(){if(wp)return Vo;wp=1;function e(t){return this.__data__.has(t)}return Vo=e,Vo}var Yo,Op;function Z1(){if(Op)return Yo;Op=1;var e=Ji(),t=Hl(),r=Gl(),n=200;function i(a,o){var u=this.__data__;if(u instanceof e){var c=u.__data__;if(!t||c.length<n-1)return c.push([a,o]),this.size=++u.size,this;u=this.__data__=new r(c)}return u.set(a,o),this.size=u.size,this}return Yo=i,Yo}var Zo,_p;function Zm(){if(_p)return Zo;_p=1;var e=Ji(),t=K1(),r=X1(),n=V1(),i=Y1(),a=Z1();function o(u){var c=this.__data__=new e(u);this.size=c.size}return o.prototype.clear=t,o.prototype.delete=r,o.prototype.get=n,o.prototype.has=i,o.prototype.set=a,Zo=o,Zo}var Jo,Sp;function J1(){if(Sp)return Jo;Sp=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return Jo=t,Jo}var Qo,Ap;function Q1(){if(Ap)return Qo;Ap=1;function e(t){return this.__data__.has(t)}return Qo=e,Qo}var eu,Pp;function Jm(){if(Pp)return eu;Pp=1;var e=Gl(),t=J1(),r=Q1();function n(i){var a=-1,o=i==null?0:i.length;for(this.__data__=new e;++a<o;)this.add(i[a])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,eu=n,eu}var tu,Tp;function Qm(){if(Tp)return tu;Tp=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(t[n],n,t))return!0;return!1}return tu=e,tu}var ru,Ep;function eb(){if(Ep)return ru;Ep=1;function e(t,r){return t.has(r)}return ru=e,ru}var nu,jp;function tb(){if(jp)return nu;jp=1;var e=Jm(),t=Qm(),r=eb(),n=1,i=2;function a(o,u,c,s,f,l){var h=c&n,d=o.length,y=u.length;if(d!=y&&!(h&&y>d))return!1;var v=l.get(o),p=l.get(u);if(v&&p)return v==u&&p==o;var b=-1,w=!0,x=c&i?new e:void 0;for(l.set(o,u),l.set(u,o);++b<d;){var O=o[b],g=u[b];if(s)var m=h?s(g,O,b,u,o,l):s(O,g,b,o,u,l);if(m!==void 0){if(m)continue;w=!1;break}if(x){if(!t(u,function(_,S){if(!r(x,S)&&(O===_||f(O,_,c,s,l)))return x.push(S)})){w=!1;break}}else if(!(O===g||f(O,g,c,s,l))){w=!1;break}}return l.delete(o),l.delete(u),w}return nu=a,nu}var iu,Mp;function eO(){if(Mp)return iu;Mp=1;var e=nt(),t=e.Uint8Array;return iu=t,iu}var au,Cp;function tO(){if(Cp)return au;Cp=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i,a){n[++r]=[a,i]}),n}return au=e,au}var ou,$p;function rf(){if($p)return ou;$p=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i){n[++r]=i}),n}return ou=e,ou}var uu,Ip;function rO(){if(Ip)return uu;Ip=1;var e=Mn(),t=eO(),r=zl(),n=tb(),i=tO(),a=rf(),o=1,u=2,c="[object Boolean]",s="[object Date]",f="[object Error]",l="[object Map]",h="[object Number]",d="[object RegExp]",y="[object Set]",v="[object String]",p="[object Symbol]",b="[object ArrayBuffer]",w="[object DataView]",x=e?e.prototype:void 0,O=x?x.valueOf:void 0;function g(m,_,S,P,M,A,E){switch(S){case w:if(m.byteLength!=_.byteLength||m.byteOffset!=_.byteOffset)return!1;m=m.buffer,_=_.buffer;case b:return!(m.byteLength!=_.byteLength||!A(new t(m),new t(_)));case c:case s:case h:return r(+m,+_);case f:return m.name==_.name&&m.message==_.message;case d:case v:return m==_+"";case l:var j=i;case y:var I=P&o;if(j||(j=a),m.size!=_.size&&!I)return!1;var $=E.get(m);if($)return $==_;P|=u,E.set(m,_);var R=n(j(m),j(_),P,M,A,E);return E.delete(m),R;case p:if(O)return O.call(m)==O.call(_)}return!1}return uu=g,uu}var cu,Rp;function rb(){if(Rp)return cu;Rp=1;function e(t,r){for(var n=-1,i=r.length,a=t.length;++n<i;)t[a+n]=r[n];return t}return cu=e,cu}var su,Dp;function nO(){if(Dp)return su;Dp=1;var e=rb(),t=Ie();function r(n,i,a){var o=i(n);return t(n)?o:e(o,a(n))}return su=r,su}var lu,kp;function iO(){if(kp)return lu;kp=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=0,o=[];++n<i;){var u=t[n];r(u,n,t)&&(o[a++]=u)}return o}return lu=e,lu}var fu,qp;function aO(){if(qp)return fu;qp=1;function e(){return[]}return fu=e,fu}var hu,Np;function oO(){if(Np)return hu;Np=1;var e=iO(),t=aO(),r=Object.prototype,n=r.propertyIsEnumerable,i=Object.getOwnPropertySymbols,a=i?function(o){return o==null?[]:(o=Object(o),e(i(o),function(u){return n.call(o,u)}))}:t;return hu=a,hu}var pu,Bp;function uO(){if(Bp)return pu;Bp=1;function e(t,r){for(var n=-1,i=Array(t);++n<t;)i[n]=r(n);return i}return pu=e,pu}var du,Lp;function cO(){if(Lp)return du;Lp=1;var e=dt(),t=vt(),r="[object Arguments]";function n(i){return t(i)&&e(i)==r}return du=n,du}var vu,Fp;function nf(){if(Fp)return vu;Fp=1;var e=cO(),t=vt(),r=Object.prototype,n=r.hasOwnProperty,i=r.propertyIsEnumerable,a=e(function(){return arguments}())?e:function(o){return t(o)&&n.call(o,"callee")&&!i.call(o,"callee")};return vu=a,vu}var Fr={exports:{}},yu,Up;function sO(){if(Up)return yu;Up=1;function e(){return!1}return yu=e,yu}Fr.exports;var Wp;function nb(){return Wp||(Wp=1,function(e,t){var r=nt(),n=sO(),i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s}(Fr,Fr.exports)),Fr.exports}var gu,zp;function af(){if(zp)return gu;zp=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,i){var a=typeof n;return i=i??e,!!i&&(a=="number"||a!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<i}return gu=r,gu}var mu,Hp;function of(){if(Hp)return mu;Hp=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return mu=t,mu}var bu,Gp;function lO(){if(Gp)return bu;Gp=1;var e=dt(),t=of(),r=vt(),n="[object Arguments]",i="[object Array]",a="[object Boolean]",o="[object Date]",u="[object Error]",c="[object Function]",s="[object Map]",f="[object Number]",l="[object Object]",h="[object RegExp]",d="[object Set]",y="[object String]",v="[object WeakMap]",p="[object ArrayBuffer]",b="[object DataView]",w="[object Float32Array]",x="[object Float64Array]",O="[object Int8Array]",g="[object Int16Array]",m="[object Int32Array]",_="[object Uint8Array]",S="[object Uint8ClampedArray]",P="[object Uint16Array]",M="[object Uint32Array]",A={};A[w]=A[x]=A[O]=A[g]=A[m]=A[_]=A[S]=A[P]=A[M]=!0,A[n]=A[i]=A[p]=A[a]=A[b]=A[o]=A[u]=A[c]=A[s]=A[f]=A[l]=A[h]=A[d]=A[y]=A[v]=!1;function E(j){return r(j)&&t(j.length)&&!!A[e(j)]}return bu=E,bu}var xu,Kp;function ib(){if(Kp)return xu;Kp=1;function e(t){return function(r){return t(r)}}return xu=e,xu}var Ur={exports:{}};Ur.exports;var Xp;function fO(){return Xp||(Xp=1,function(e,t){var r=Sm(),n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u}(Ur,Ur.exports)),Ur.exports}var wu,Vp;function ab(){if(Vp)return wu;Vp=1;var e=lO(),t=ib(),r=fO(),n=r&&r.isTypedArray,i=n?t(n):e;return wu=i,wu}var Ou,Yp;function hO(){if(Yp)return Ou;Yp=1;var e=uO(),t=nf(),r=Ie(),n=nb(),i=af(),a=ab(),o=Object.prototype,u=o.hasOwnProperty;function c(s,f){var l=r(s),h=!l&&t(s),d=!l&&!h&&n(s),y=!l&&!h&&!d&&a(s),v=l||h||d||y,p=v?e(s.length,String):[],b=p.length;for(var w in s)(f||u.call(s,w))&&!(v&&(w=="length"||d&&(w=="offset"||w=="parent")||y&&(w=="buffer"||w=="byteLength"||w=="byteOffset")||i(w,b)))&&p.push(w);return p}return Ou=c,Ou}var _u,Zp;function pO(){if(Zp)return _u;Zp=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,i=typeof n=="function"&&n.prototype||e;return r===i}return _u=t,_u}var Su,Jp;function ob(){if(Jp)return Su;Jp=1;function e(t,r){return function(n){return t(r(n))}}return Su=e,Su}var Au,Qp;function dO(){if(Qp)return Au;Qp=1;var e=ob(),t=e(Object.keys,Object);return Au=t,Au}var Pu,ed;function vO(){if(ed)return Pu;ed=1;var e=pO(),t=dO(),r=Object.prototype,n=r.hasOwnProperty;function i(a){if(!e(a))return t(a);var o=[];for(var u in Object(a))n.call(a,u)&&u!="constructor"&&o.push(u);return o}return Pu=i,Pu}var Tu,td;function In(){if(td)return Tu;td=1;var e=Wl(),t=of();function r(n){return n!=null&&t(n.length)&&!e(n)}return Tu=r,Tu}var Eu,rd;function oa(){if(rd)return Eu;rd=1;var e=hO(),t=vO(),r=In();function n(i){return r(i)?e(i):t(i)}return Eu=n,Eu}var ju,nd;function yO(){if(nd)return ju;nd=1;var e=nO(),t=oO(),r=oa();function n(i){return e(i,r,t)}return ju=n,ju}var Mu,id;function gO(){if(id)return Mu;id=1;var e=yO(),t=1,r=Object.prototype,n=r.hasOwnProperty;function i(a,o,u,c,s,f){var l=u&t,h=e(a),d=h.length,y=e(o),v=y.length;if(d!=v&&!l)return!1;for(var p=d;p--;){var b=h[p];if(!(l?b in o:n.call(o,b)))return!1}var w=f.get(a),x=f.get(o);if(w&&x)return w==o&&x==a;var O=!0;f.set(a,o),f.set(o,a);for(var g=l;++p<d;){b=h[p];var m=a[b],_=o[b];if(c)var S=l?c(_,m,b,o,a,f):c(m,_,b,a,o,f);if(!(S===void 0?m===_||s(m,_,u,c,f):S)){O=!1;break}g||(g=b=="constructor")}if(O&&!g){var P=a.constructor,M=o.constructor;P!=M&&"constructor"in a&&"constructor"in o&&!(typeof P=="function"&&P instanceof P&&typeof M=="function"&&M instanceof M)&&(O=!1)}return f.delete(a),f.delete(o),O}return Mu=i,Mu}var Cu,ad;function mO(){if(ad)return Cu;ad=1;var e=zt(),t=nt(),r=e(t,"DataView");return Cu=r,Cu}var $u,od;function bO(){if(od)return $u;od=1;var e=zt(),t=nt(),r=e(t,"Promise");return $u=r,$u}var Iu,ud;function ub(){if(ud)return Iu;ud=1;var e=zt(),t=nt(),r=e(t,"Set");return Iu=r,Iu}var Ru,cd;function xO(){if(cd)return Ru;cd=1;var e=zt(),t=nt(),r=e(t,"WeakMap");return Ru=r,Ru}var Du,sd;function wO(){if(sd)return Du;sd=1;var e=mO(),t=Hl(),r=bO(),n=ub(),i=xO(),a=dt(),o=Am(),u="[object Map]",c="[object Object]",s="[object Promise]",f="[object Set]",l="[object WeakMap]",h="[object DataView]",d=o(e),y=o(t),v=o(r),p=o(n),b=o(i),w=a;return(e&&w(new e(new ArrayBuffer(1)))!=h||t&&w(new t)!=u||r&&w(r.resolve())!=s||n&&w(new n)!=f||i&&w(new i)!=l)&&(w=function(x){var O=a(x),g=O==c?x.constructor:void 0,m=g?o(g):"";if(m)switch(m){case d:return h;case y:return u;case v:return s;case p:return f;case b:return l}return O}),Du=w,Du}var ku,ld;function OO(){if(ld)return ku;ld=1;var e=Zm(),t=tb(),r=rO(),n=gO(),i=wO(),a=Ie(),o=nb(),u=ab(),c=1,s="[object Arguments]",f="[object Array]",l="[object Object]",h=Object.prototype,d=h.hasOwnProperty;function y(v,p,b,w,x,O){var g=a(v),m=a(p),_=g?f:i(v),S=m?f:i(p);_=_==s?l:_,S=S==s?l:S;var P=_==l,M=S==l,A=_==S;if(A&&o(v)){if(!o(p))return!1;g=!0,P=!1}if(A&&!P)return O||(O=new e),g||u(v)?t(v,p,b,w,x,O):r(v,p,_,b,w,x,O);if(!(b&c)){var E=P&&d.call(v,"__wrapped__"),j=M&&d.call(p,"__wrapped__");if(E||j){var I=E?v.value():v,$=j?p.value():p;return O||(O=new e),x(I,$,b,w,O)}}return A?(O||(O=new e),n(v,p,b,w,x,O)):!1}return ku=y,ku}var qu,fd;function uf(){if(fd)return qu;fd=1;var e=OO(),t=vt();function r(n,i,a,o,u){return n===i?!0:n==null||i==null||!t(n)&&!t(i)?n!==n&&i!==i:e(n,i,a,o,r,u)}return qu=r,qu}var Nu,hd;function _O(){if(hd)return Nu;hd=1;var e=Zm(),t=uf(),r=1,n=2;function i(a,o,u,c){var s=u.length,f=s,l=!c;if(a==null)return!f;for(a=Object(a);s--;){var h=u[s];if(l&&h[2]?h[1]!==a[h[0]]:!(h[0]in a))return!1}for(;++s<f;){h=u[s];var d=h[0],y=a[d],v=h[1];if(l&&h[2]){if(y===void 0&&!(d in a))return!1}else{var p=new e;if(c)var b=c(y,v,d,a,o,p);if(!(b===void 0?t(v,y,r|n,c,p):b))return!1}}return!0}return Nu=i,Nu}var Bu,pd;function cb(){if(pd)return Bu;pd=1;var e=_t();function t(r){return r===r&&!e(r)}return Bu=t,Bu}var Lu,dd;function SO(){if(dd)return Lu;dd=1;var e=cb(),t=oa();function r(n){for(var i=t(n),a=i.length;a--;){var o=i[a],u=n[o];i[a]=[o,u,e(u)]}return i}return Lu=r,Lu}var Fu,vd;function sb(){if(vd)return Fu;vd=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return Fu=e,Fu}var Uu,yd;function AO(){if(yd)return Uu;yd=1;var e=_O(),t=SO(),r=sb();function n(i){var a=t(i);return a.length==1&&a[0][2]?r(a[0][0],a[0][1]):function(o){return o===i||e(o,i,a)}}return Uu=n,Uu}var Wu,gd;function PO(){if(gd)return Wu;gd=1;function e(t,r){return t!=null&&r in Object(t)}return Wu=e,Wu}var zu,md;function TO(){if(md)return zu;md=1;var e=Em(),t=nf(),r=Ie(),n=af(),i=of(),a=ea();function o(u,c,s){c=e(c,u);for(var f=-1,l=c.length,h=!1;++f<l;){var d=a(c[f]);if(!(h=u!=null&&s(u,d)))break;u=u[d]}return h||++f!=l?h:(l=u==null?0:u.length,!!l&&i(l)&&n(d,l)&&(r(u)||t(u)))}return zu=o,zu}var Hu,bd;function EO(){if(bd)return Hu;bd=1;var e=PO(),t=TO();function r(n,i){return n!=null&&t(n,i,e)}return Hu=r,Hu}var Gu,xd;function jO(){if(xd)return Gu;xd=1;var e=uf(),t=jm(),r=EO(),n=Ul(),i=cb(),a=sb(),o=ea(),u=1,c=2;function s(f,l){return n(f)&&i(l)?a(o(f),l):function(h){var d=t(h,f);return d===void 0&&d===l?r(h,f):e(l,d,u|c)}}return Gu=s,Gu}var Ku,wd;function Er(){if(wd)return Ku;wd=1;function e(t){return t}return Ku=e,Ku}var Xu,Od;function MO(){if(Od)return Xu;Od=1;function e(t){return function(r){return r==null?void 0:r[t]}}return Xu=e,Xu}var Vu,_d;function CO(){if(_d)return Vu;_d=1;var e=Xl();function t(r){return function(n){return e(n,r)}}return Vu=t,Vu}var Yu,Sd;function $O(){if(Sd)return Yu;Sd=1;var e=MO(),t=CO(),r=Ul(),n=ea();function i(a){return r(a)?e(n(a)):t(a)}return Yu=i,Yu}var Zu,Ad;function St(){if(Ad)return Zu;Ad=1;var e=AO(),t=jO(),r=Er(),n=Ie(),i=$O();function a(o){return typeof o=="function"?o:o==null?r:typeof o=="object"?n(o)?t(o[0],o[1]):e(o):i(o)}return Zu=a,Zu}var Ju,Pd;function lb(){if(Pd)return Ju;Pd=1;function e(t,r,n,i){for(var a=t.length,o=n+(i?1:-1);i?o--:++o<a;)if(r(t[o],o,t))return o;return-1}return Ju=e,Ju}var Qu,Td;function IO(){if(Td)return Qu;Td=1;function e(t){return t!==t}return Qu=e,Qu}var ec,Ed;function RO(){if(Ed)return ec;Ed=1;function e(t,r,n){for(var i=n-1,a=t.length;++i<a;)if(t[i]===r)return i;return-1}return ec=e,ec}var tc,jd;function DO(){if(jd)return tc;jd=1;var e=lb(),t=IO(),r=RO();function n(i,a,o){return a===a?r(i,a,o):e(i,t,o)}return tc=n,tc}var rc,Md;function kO(){if(Md)return rc;Md=1;var e=DO();function t(r,n){var i=r==null?0:r.length;return!!i&&e(r,n,0)>-1}return rc=t,rc}var nc,Cd;function qO(){if(Cd)return nc;Cd=1;function e(t,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;)if(n(r,t[i]))return!0;return!1}return nc=e,nc}var ic,$d;function NO(){if($d)return ic;$d=1;function e(){}return ic=e,ic}var ac,Id;function BO(){if(Id)return ac;Id=1;var e=ub(),t=NO(),r=rf(),n=1/0,i=e&&1/r(new e([,-0]))[1]==n?function(a){return new e(a)}:t;return ac=i,ac}var oc,Rd;function LO(){if(Rd)return oc;Rd=1;var e=Jm(),t=kO(),r=qO(),n=eb(),i=BO(),a=rf(),o=200;function u(c,s,f){var l=-1,h=t,d=c.length,y=!0,v=[],p=v;if(f)y=!1,h=r;else if(d>=o){var b=s?null:i(c);if(b)return a(b);y=!1,h=n,p=new e}else p=s?[]:v;e:for(;++l<d;){var w=c[l],x=s?s(w):w;if(w=f||w!==0?w:0,y&&x===x){for(var O=p.length;O--;)if(p[O]===x)continue e;s&&p.push(x),v.push(w)}else h(p,x,f)||(p!==v&&p.push(x),v.push(w))}return v}return oc=u,oc}var uc,Dd;function FO(){if(Dd)return uc;Dd=1;var e=St(),t=LO();function r(n,i){return n&&n.length?t(n,e(i,2)):[]}return uc=r,uc}var UO=FO();const kd=ue(UO);function fb(e,t,r){return t===!0?kd(e,r):Y(t)?kd(e,t):e}function ur(e){"@babel/helpers - typeof";return ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ur(e)}var WO=["ref"];function qd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function it(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qd(Object(r),!0).forEach(function(n){ua(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zO(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Nd(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pb(n.key),n)}}function HO(e,t,r){return t&&Nd(e.prototype,t),r&&Nd(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function GO(e,t,r){return t=li(t),KO(e,hb()?Reflect.construct(t,r||[],li(e).constructor):t.apply(e,r))}function KO(e,t){if(t&&(ur(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return XO(e)}function XO(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function hb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(hb=function(){return!!e})()}function li(e){return li=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},li(e)}function VO(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ls(e,t)}function Ls(e,t){return Ls=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ls(e,t)}function ua(e,t,r){return t=pb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pb(e){var t=YO(e,"string");return ur(t)=="symbol"?t:t+""}function YO(e,t){if(ur(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ur(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function ZO(e,t){if(e==null)return{};var r=JO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function JO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function QO(e){return e.value}function e_(e,t){if(T.isValidElement(e))return T.cloneElement(e,t);if(typeof e=="function")return T.createElement(e,t);t.ref;var r=ZO(t,WO);return T.createElement(tf,r)}var Bd=1,rr=function(e){function t(){var r;zO(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=GO(this,t,[].concat(i)),ua(r,"lastBoundingBox",{width:-1,height:-1}),r}return VO(t,e),HO(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>Bd||Math.abs(i.height-this.lastBoundingBox.height)>Bd)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?it({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,h;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var d=this.getBBoxSnapshot();l={left:((s||0)-d.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();h={top:((f||0)-y.height)/2}}else h=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return it(it({},l),h)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=it(it({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return T.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(d){n.wrapperNode=d}},e_(a,it(it({},this.props),{},{payload:fb(f,s,QO)})))}}],[{key:"getWithHeight",value:function(n,i){var a=it(it({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&F(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(q.PureComponent);ua(rr,"displayName","Legend");ua(rr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var cc,Ld;function t_(){if(Ld)return cc;Ld=1;var e=Mn(),t=nf(),r=Ie(),n=e?e.isConcatSpreadable:void 0;function i(a){return r(a)||t(a)||!!(n&&a&&a[n])}return cc=i,cc}var sc,Fd;function db(){if(Fd)return sc;Fd=1;var e=rb(),t=t_();function r(n,i,a,o,u){var c=-1,s=n.length;for(a||(a=t),u||(u=[]);++c<s;){var f=n[c];i>0&&a(f)?i>1?r(f,i-1,a,o,u):e(u,f):o||(u[u.length]=f)}return u}return sc=r,sc}var lc,Ud;function r_(){if(Ud)return lc;Ud=1;function e(t){return function(r,n,i){for(var a=-1,o=Object(r),u=i(r),c=u.length;c--;){var s=u[t?c:++a];if(n(o[s],s,o)===!1)break}return r}}return lc=e,lc}var fc,Wd;function n_(){if(Wd)return fc;Wd=1;var e=r_(),t=e();return fc=t,fc}var hc,zd;function vb(){if(zd)return hc;zd=1;var e=n_(),t=oa();function r(n,i){return n&&e(n,i,t)}return hc=r,hc}var pc,Hd;function i_(){if(Hd)return pc;Hd=1;var e=In();function t(r,n){return function(i,a){if(i==null)return i;if(!e(i))return r(i,a);for(var o=i.length,u=n?o:-1,c=Object(i);(n?u--:++u<o)&&a(c[u],u,c)!==!1;);return i}}return pc=t,pc}var dc,Gd;function cf(){if(Gd)return dc;Gd=1;var e=vb(),t=i_(),r=t(e);return dc=r,dc}var vc,Kd;function yb(){if(Kd)return vc;Kd=1;var e=cf(),t=In();function r(n,i){var a=-1,o=t(n)?Array(n.length):[];return e(n,function(u,c,s){o[++a]=i(u,c,s)}),o}return vc=r,vc}var yc,Xd;function a_(){if(Xd)return yc;Xd=1;function e(t,r){var n=t.length;for(t.sort(r);n--;)t[n]=t[n].value;return t}return yc=e,yc}var gc,Vd;function o_(){if(Vd)return gc;Vd=1;var e=Pr();function t(r,n){if(r!==n){var i=r!==void 0,a=r===null,o=r===r,u=e(r),c=n!==void 0,s=n===null,f=n===n,l=e(n);if(!s&&!l&&!u&&r>n||u&&c&&f&&!s&&!l||a&&c&&f||!i&&f||!o)return 1;if(!a&&!u&&!l&&r<n||l&&i&&o&&!a&&!u||s&&i&&o||!c&&o||!f)return-1}return 0}return gc=t,gc}var mc,Yd;function u_(){if(Yd)return mc;Yd=1;var e=o_();function t(r,n,i){for(var a=-1,o=r.criteria,u=n.criteria,c=o.length,s=i.length;++a<c;){var f=e(o[a],u[a]);if(f){if(a>=s)return f;var l=i[a];return f*(l=="desc"?-1:1)}}return r.index-n.index}return mc=t,mc}var bc,Zd;function c_(){if(Zd)return bc;Zd=1;var e=Kl(),t=Xl(),r=St(),n=yb(),i=a_(),a=ib(),o=u_(),u=Er(),c=Ie();function s(f,l,h){l.length?l=e(l,function(v){return c(v)?function(p){return t(p,v.length===1?v[0]:v)}:v}):l=[u];var d=-1;l=e(l,a(r));var y=n(f,function(v,p,b){var w=e(l,function(x){return x(v)});return{criteria:w,index:++d,value:v}});return i(y,function(v,p){return o(v,p,h)})}return bc=s,bc}var xc,Jd;function s_(){if(Jd)return xc;Jd=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return xc=e,xc}var wc,Qd;function l_(){if(Qd)return wc;Qd=1;var e=s_(),t=Math.max;function r(n,i,a){return i=t(i===void 0?n.length-1:i,0),function(){for(var o=arguments,u=-1,c=t(o.length-i,0),s=Array(c);++u<c;)s[u]=o[i+u];u=-1;for(var f=Array(i+1);++u<i;)f[u]=o[u];return f[i]=a(s),e(n,this,f)}}return wc=r,wc}var Oc,ev;function f_(){if(ev)return Oc;ev=1;function e(t){return function(){return t}}return Oc=e,Oc}var _c,tv;function gb(){if(tv)return _c;tv=1;var e=zt(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch{}}();return _c=t,_c}var Sc,rv;function h_(){if(rv)return Sc;rv=1;var e=f_(),t=gb(),r=Er(),n=t?function(i,a){return t(i,"toString",{configurable:!0,enumerable:!1,value:e(a),writable:!0})}:r;return Sc=n,Sc}var Ac,nv;function p_(){if(nv)return Ac;nv=1;var e=800,t=16,r=Date.now;function n(i){var a=0,o=0;return function(){var u=r(),c=t-(u-o);if(o=u,c>0){if(++a>=e)return arguments[0]}else a=0;return i.apply(void 0,arguments)}}return Ac=n,Ac}var Pc,iv;function d_(){if(iv)return Pc;iv=1;var e=h_(),t=p_(),r=t(e);return Pc=r,Pc}var Tc,av;function v_(){if(av)return Tc;av=1;var e=Er(),t=l_(),r=d_();function n(i,a){return r(t(i,a,e),i+"")}return Tc=n,Tc}var Ec,ov;function ca(){if(ov)return Ec;ov=1;var e=zl(),t=In(),r=af(),n=_t();function i(a,o,u){if(!n(u))return!1;var c=typeof o;return(c=="number"?t(u)&&r(o,u.length):c=="string"&&o in u)?e(u[o],a):!1}return Ec=i,Ec}var jc,uv;function y_(){if(uv)return jc;uv=1;var e=db(),t=c_(),r=v_(),n=ca(),i=r(function(a,o){if(a==null)return[];var u=o.length;return u>1&&n(a,o[0],o[1])?o=[]:u>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),t(a,e(o,1),[])});return jc=i,jc}var g_=y_();const sf=ue(g_);function Yr(e){"@babel/helpers - typeof";return Yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yr(e)}function Fs(){return Fs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fs.apply(this,arguments)}function m_(e,t){return O_(e)||w_(e,t)||x_(e,t)||b_()}function b_(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function x_(e,t){if(e){if(typeof e=="string")return cv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cv(e,t)}}function cv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function w_(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function O_(e){if(Array.isArray(e))return e}function sv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Mc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sv(Object(r),!0).forEach(function(n){__(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function __(e,t,r){return t=S_(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function S_(e){var t=A_(e,"string");return Yr(t)=="symbol"?t:t+""}function A_(e,t){if(Yr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function P_(e){return Array.isArray(e)&&be(e[0])&&be(e[1])?e.join(" ~ "):e}var T_=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,h=t.itemSorter,d=t.wrapperClassName,y=t.labelClassName,v=t.label,p=t.labelFormatter,b=t.accessibilityLayer,w=b===void 0?!1:b,x=function(){if(f&&f.length){var E={padding:0,margin:0},j=(h?sf(f,h):f).map(function(I,$){if(I.type==="none")return null;var R=Mc({display:"block",paddingTop:4,paddingBottom:4,color:I.color||"#000"},u),k=I.formatter||l||P_,N=I.value,B=I.name,z=N,G=B;if(k&&z!=null&&G!=null){var U=k(N,B,I,$,f);if(Array.isArray(U)){var K=m_(U,2);z=K[0],G=K[1]}else z=U}return T.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat($),style:R},be(G)?T.createElement("span",{className:"recharts-tooltip-item-name"},G):null,be(G)?T.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,T.createElement("span",{className:"recharts-tooltip-item-value"},z),T.createElement("span",{className:"recharts-tooltip-item-unit"},I.unit||""))});return T.createElement("ul",{className:"recharts-tooltip-item-list",style:E},j)}return null},O=Mc({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),g=Mc({margin:0},s),m=!Z(v),_=m?v:"",S=ne("recharts-default-tooltip",d),P=ne("recharts-tooltip-label",y);m&&p&&f!==void 0&&f!==null&&(_=p(v,f));var M=w?{role:"status","aria-live":"assertive"}:{};return T.createElement("div",Fs({className:S,style:O},M),T.createElement("p",{className:P,style:g},T.isValidElement(_)?_:"".concat(_)),x())};function Zr(e){"@babel/helpers - typeof";return Zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zr(e)}function Wn(e,t,r){return t=E_(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function E_(e){var t=j_(e,"string");return Zr(t)=="symbol"?t:t+""}function j_(e,t){if(Zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var $r="recharts-tooltip-wrapper",M_={visibility:"hidden"};function C_(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return ne($r,Wn(Wn(Wn(Wn({},"".concat($r,"-right"),F(r)&&t&&F(t.x)&&r>=t.x),"".concat($r,"-left"),F(r)&&t&&F(t.x)&&r<t.x),"".concat($r,"-bottom"),F(n)&&t&&F(t.y)&&n>=t.y),"".concat($r,"-top"),F(n)&&t&&F(t.y)&&n<t.y))}function lv(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&F(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var h=f,d=c[n];return h<d?Math.max(l,c[n]):Math.max(f,c[n])}var y=l+u,v=c[n]+s;return y>v?Math.max(f,c[n]):Math.max(l,c[n])}function $_(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function I_(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=lv({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=lv({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=$_({translateX:f,translateY:l,useTranslate3d:u})):s=M_,{cssProperties:s,cssClasses:C_({translateX:f,translateY:l,coordinate:r})}}function cr(e){"@babel/helpers - typeof";return cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},cr(e)}function fv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?fv(Object(r),!0).forEach(function(n){Ws(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function R_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function D_(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,bb(n.key),n)}}function k_(e,t,r){return t&&D_(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function q_(e,t,r){return t=fi(t),N_(e,mb()?Reflect.construct(t,r||[],fi(e).constructor):t.apply(e,r))}function N_(e,t){if(t&&(cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return B_(e)}function B_(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function mb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(mb=function(){return!!e})()}function fi(e){return fi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},fi(e)}function L_(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Us(e,t)}function Us(e,t){return Us=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Us(e,t)}function Ws(e,t,r){return t=bb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bb(e){var t=F_(e,"string");return cr(t)=="symbol"?t:t+""}function F_(e,t){if(cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var pv=1,U_=function(e){function t(){var r;R_(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=q_(this,t,[].concat(i)),Ws(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Ws(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return L_(t,e),k_(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>pv||Math.abs(n.height-this.state.lastBoundingBox.height)>pv)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,h=i.isAnimationActive,d=i.offset,y=i.position,v=i.reverseDirection,p=i.useTranslate3d,b=i.viewBox,w=i.wrapperStyle,x=I_({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:d,position:y,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:p,viewBox:b}),O=x.cssClasses,g=x.cssProperties,m=hv(hv({transition:h&&a?"transform ".concat(u,"ms ").concat(c):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},w);return T.createElement("div",{tabIndex:-1,className:O,style:m,ref:function(S){n.wrapperNode=S}},s)}}])}(q.PureComponent),W_=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},Rn={isSsr:W_()};function sr(e){"@babel/helpers - typeof";return sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},sr(e)}function dv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?dv(Object(r),!0).forEach(function(n){lf(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function z_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function H_(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,wb(n.key),n)}}function G_(e,t,r){return t&&H_(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function K_(e,t,r){return t=hi(t),X_(e,xb()?Reflect.construct(t,r||[],hi(e).constructor):t.apply(e,r))}function X_(e,t){if(t&&(sr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return V_(e)}function V_(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(xb=function(){return!!e})()}function hi(e){return hi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},hi(e)}function Y_(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zs(e,t)}function zs(e,t){return zs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},zs(e,t)}function lf(e,t,r){return t=wb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wb(e){var t=Z_(e,"string");return sr(t)=="symbol"?t:t+""}function Z_(e,t){if(sr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(sr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function J_(e){return e.dataKey}function Q_(e,t){return T.isValidElement(e)?T.cloneElement(e,t):typeof e=="function"?T.createElement(e,t):T.createElement(T_,t)}var at=function(e){function t(){return z_(this,t),K_(this,t,arguments)}return Y_(t,e),G_(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,h=i.isAnimationActive,d=i.offset,y=i.payload,v=i.payloadUniqBy,p=i.position,b=i.reverseDirection,w=i.useTranslate3d,x=i.viewBox,O=i.wrapperStyle,g=y??[];l&&g.length&&(g=fb(y.filter(function(_){return _.value!=null&&(_.hide!==!0||n.props.includeHidden)}),v,J_));var m=g.length>0;return T.createElement(U_,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:h,active:a,coordinate:f,hasPayload:m,offset:d,position:p,reverseDirection:b,useTranslate3d:w,viewBox:x,wrapperStyle:O},Q_(s,vv(vv({},this.props),{},{payload:g})))}}])}(q.PureComponent);lf(at,"displayName","Tooltip");lf(at,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Rn.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var Cc,yv;function eS(){if(yv)return Cc;yv=1;var e=nt(),t=function(){return e.Date.now()};return Cc=t,Cc}var $c,gv;function tS(){if(gv)return $c;gv=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return $c=t,$c}var Ic,mv;function rS(){if(mv)return Ic;mv=1;var e=tS(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return Ic=r,Ic}var Rc,bv;function Ob(){if(bv)return Rc;bv=1;var e=rS(),t=_t(),r=Pr(),n=NaN,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,u=parseInt;function c(s){if(typeof s=="number")return s;if(r(s))return n;if(t(s)){var f=typeof s.valueOf=="function"?s.valueOf():s;s=t(f)?f+"":f}if(typeof s!="string")return s===0?s:+s;s=e(s);var l=a.test(s);return l||o.test(s)?u(s.slice(2),l?2:8):i.test(s)?n:+s}return Rc=c,Rc}var Dc,xv;function nS(){if(xv)return Dc;xv=1;var e=_t(),t=eS(),r=Ob(),n="Expected a function",i=Math.max,a=Math.min;function o(u,c,s){var f,l,h,d,y,v,p=0,b=!1,w=!1,x=!0;if(typeof u!="function")throw new TypeError(n);c=r(c)||0,e(s)&&(b=!!s.leading,w="maxWait"in s,h=w?i(r(s.maxWait)||0,c):h,x="trailing"in s?!!s.trailing:x);function O(j){var I=f,$=l;return f=l=void 0,p=j,d=u.apply($,I),d}function g(j){return p=j,y=setTimeout(S,c),b?O(j):d}function m(j){var I=j-v,$=j-p,R=c-I;return w?a(R,h-$):R}function _(j){var I=j-v,$=j-p;return v===void 0||I>=c||I<0||w&&$>=h}function S(){var j=t();if(_(j))return P(j);y=setTimeout(S,m(j))}function P(j){return y=void 0,x&&f?O(j):(f=l=void 0,d)}function M(){y!==void 0&&clearTimeout(y),p=0,f=v=l=y=void 0}function A(){return y===void 0?d:P(t())}function E(){var j=t(),I=_(j);if(f=arguments,l=this,v=j,I){if(y===void 0)return g(v);if(w)return clearTimeout(y),y=setTimeout(S,c),O(v)}return y===void 0&&(y=setTimeout(S,c)),d}return E.cancel=M,E.flush=A,E}return Dc=o,Dc}var kc,wv;function iS(){if(wv)return kc;wv=1;var e=nS(),t=_t(),r="Expected a function";function n(i,a,o){var u=!0,c=!0;if(typeof i!="function")throw new TypeError(r);return t(o)&&(u="leading"in o?!!o.leading:u,c="trailing"in o?!!o.trailing:c),e(i,a,{leading:u,maxWait:a,trailing:c})}return kc=n,kc}var aS=iS();const _b=ue(aS);function Jr(e){"@babel/helpers - typeof";return Jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jr(e)}function Ov(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ov(Object(r),!0).forEach(function(n){oS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ov(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function oS(e,t,r){return t=uS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uS(e){var t=cS(e,"string");return Jr(t)=="symbol"?t:t+""}function cS(e,t){if(Jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function sS(e,t){return pS(e)||hS(e,t)||fS(e,t)||lS()}function lS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fS(e,t){if(e){if(typeof e=="string")return _v(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _v(e,t)}}function _v(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function hS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function pS(e){if(Array.isArray(e))return e}var ZD=q.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,h=e.maxHeight,d=e.children,y=e.debounce,v=y===void 0?0:y,p=e.id,b=e.className,w=e.onResize,x=e.style,O=x===void 0?{}:x,g=q.useRef(null),m=q.useRef();m.current=w,q.useImperativeHandle(t,function(){return Object.defineProperty(g.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),g.current},configurable:!0})});var _=q.useState({containerWidth:i.width,containerHeight:i.height}),S=sS(_,2),P=S[0],M=S[1],A=q.useCallback(function(j,I){M(function($){var R=Math.round(j),k=Math.round(I);return $.containerWidth===R&&$.containerHeight===k?$:{containerWidth:R,containerHeight:k}})},[]);q.useEffect(function(){var j=function(B){var z,G=B[0].contentRect,U=G.width,K=G.height;A(U,K),(z=m.current)===null||z===void 0||z.call(m,U,K)};v>0&&(j=_b(j,v,{trailing:!0,leading:!1}));var I=new ResizeObserver(j),$=g.current.getBoundingClientRect(),R=$.width,k=$.height;return A(R,k),I.observe(g.current),function(){I.disconnect()}},[A,v]);var E=q.useMemo(function(){var j=P.containerWidth,I=P.containerHeight;if(j<0||I<0)return null;Nt(Rt(o)||Rt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),Nt(!r||r>0,"The aspect(%s) must be greater than zero.",r);var $=Rt(o)?j:o,R=Rt(c)?I:c;r&&r>0&&($?R=$/r:R&&($=R*r),h&&R>h&&(R=h)),Nt($>0||R>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,$,R,o,c,f,l,r);var k=!Array.isArray(d)&&st(d.type).endsWith("Chart");return T.Children.map(d,function(N){return T.isValidElement(N)?q.cloneElement(N,zn({width:$,height:R},k?{style:zn({height:"100%",width:"100%",maxHeight:R,maxWidth:$},N.props.style)}:{})):N})},[r,d,c,h,l,f,P,o]);return T.createElement("div",{id:p?"".concat(p):void 0,className:ne("recharts-responsive-container",b),style:zn(zn({},O),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:h}),ref:g},E)}),Sb=function(t){return null};Sb.displayName="Cell";function Qr(e){"@babel/helpers - typeof";return Qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qr(e)}function Sv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Hs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sv(Object(r),!0).forEach(function(n){dS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function dS(e,t,r){return t=vS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vS(e){var t=yS(e,"string");return Qr(t)=="symbol"?t:t+""}function yS(e,t){if(Qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Yt={widthCache:{},cacheCount:0},gS=2e3,mS={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Av="recharts_measurement_span";function bS(e){var t=Hs({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var zr=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||Rn.isSsr)return{width:0,height:0};var n=bS(r),i=JSON.stringify({text:t,copyStyle:n});if(Yt.widthCache[i])return Yt.widthCache[i];try{var a=document.getElementById(Av);a||(a=document.createElement("span"),a.setAttribute("id",Av),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Hs(Hs({},mS),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return Yt.widthCache[i]=c,++Yt.cacheCount>gS&&(Yt.cacheCount=0,Yt.widthCache={}),c}catch{return{width:0,height:0}}},xS=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function en(e){"@babel/helpers - typeof";return en=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},en(e)}function pi(e,t){return SS(e)||_S(e,t)||OS(e,t)||wS()}function wS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function OS(e,t){if(e){if(typeof e=="string")return Pv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pv(e,t)}}function Pv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _S(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function SS(e){if(Array.isArray(e))return e}function AS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Tv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,TS(n.key),n)}}function PS(e,t,r){return t&&Tv(e.prototype,t),r&&Tv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function TS(e){var t=ES(e,"string");return en(t)=="symbol"?t:t+""}function ES(e,t){if(en(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(en(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ev=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,jv=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,jS=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,MS=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Ab={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},CS=Object.keys(Ab),Jt="NaN";function $S(e,t){return e*Ab[t]}var Hn=function(){function e(t,r){AS(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!jS.test(r)&&(this.num=NaN,this.unit=""),CS.includes(r)&&(this.num=$S(t,r),this.unit="px")}return PS(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=MS.exec(r))!==null&&n!==void 0?n:[],a=pi(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function Pb(e){if(e.includes(Jt))return Jt;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=Ev.exec(t))!==null&&r!==void 0?r:[],i=pi(n,4),a=i[1],o=i[2],u=i[3],c=Hn.parse(a??""),s=Hn.parse(u??""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return Jt;t=t.replace(Ev,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,h=(l=jv.exec(t))!==null&&l!==void 0?l:[],d=pi(h,4),y=d[1],v=d[2],p=d[3],b=Hn.parse(y??""),w=Hn.parse(p??""),x=v==="+"?b.add(w):b.subtract(w);if(x.isNaN())return Jt;t=t.replace(jv,x.toString())}return t}var Mv=/\(([^()]*)\)/;function IS(e){for(var t=e;t.includes("(");){var r=Mv.exec(t),n=pi(r,2),i=n[1];t=t.replace(Mv,Pb(i))}return t}function RS(e){var t=e.replace(/\s+/g,"");return t=IS(t),t=Pb(t),t}function DS(e){try{return RS(e)}catch{return Jt}}function qc(e){var t=DS(e.slice(5,-1));return t===Jt?"":t}var kS=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],qS=["dx","dy","angle","className","breakAll"];function Gs(){return Gs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gs.apply(this,arguments)}function Cv(e,t){if(e==null)return{};var r=NS(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function NS(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function $v(e,t){return US(e)||FS(e,t)||LS(e,t)||BS()}function BS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function LS(e,t){if(e){if(typeof e=="string")return Iv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Iv(e,t)}}function Iv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function FS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function US(e){if(Array.isArray(e))return e}var Tb=/[ \f\n\r\t\v\u2028\u2029]+/,Eb=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];Z(r)||(n?a=r.toString().split(""):a=r.toString().split(Tb));var o=a.map(function(c){return{word:c,width:zr(c,i).width}}),u=n?0:zr(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},WS=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=F(o),l=u,h=function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return $.reduce(function(R,k){var N=k.word,B=k.width,z=R[R.length-1];if(z&&(i==null||a||z.width+B+n<Number(i)))z.words.push(N),z.width+=B+n;else{var G={words:[N],width:B};R.push(G)}return R},[])},d=h(r),y=function($){return $.reduce(function(R,k){return R.width>k.width?R:k})};if(!f)return d;for(var v="…",p=function($){var R=l.slice(0,$),k=Eb({breakAll:s,style:c,children:R+v}).wordsWithComputedWidth,N=h(k),B=N.length>o||y(N).width>Number(i);return[B,N]},b=0,w=l.length-1,x=0,O;b<=w&&x<=l.length-1;){var g=Math.floor((b+w)/2),m=g-1,_=p(m),S=$v(_,2),P=S[0],M=S[1],A=p(g),E=$v(A,1),j=E[0];if(!P&&!j&&(b=g+1),P&&j&&(w=g-1),!P&&j){O=M;break}x++}return O||d},Rv=function(t){var r=Z(t)?[]:t.toString().split(Tb);return[{words:r}]},zS=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!Rn.isSsr){var c,s,f=Eb({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,h=f.spaceWidth;c=l,s=h}else return Rv(i);return WS({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return Rv(i)},Dv="#808080",di=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,h=t.textAnchor,d=h===void 0?"start":h,y=t.verticalAnchor,v=y===void 0?"end":y,p=t.fill,b=p===void 0?Dv:p,w=Cv(t,kS),x=q.useMemo(function(){return zS({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:l,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,l,w.style,w.width]),O=w.dx,g=w.dy,m=w.angle,_=w.className,S=w.breakAll,P=Cv(w,qS);if(!be(n)||!be(a))return null;var M=n+(F(O)?O:0),A=a+(F(g)?g:0),E;switch(v){case"start":E=qc("calc(".concat(s,")"));break;case"middle":E=qc("calc(".concat((x.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:E=qc("calc(".concat(x.length-1," * -").concat(u,")"));break}var j=[];if(l){var I=x[0].width,$=w.width;j.push("scale(".concat((F($)?$/I:1)/I,")"))}return m&&j.push("rotate(".concat(m,", ").concat(M,", ").concat(A,")")),j.length&&(P.transform=j.join(" ")),T.createElement("text",Gs({},re(P,!0),{x:M,y:A,className:ne("recharts-text",_),textAnchor:d,fill:b.includes("url")?Dv:b}),x.map(function(R,k){var N=R.words.join(S?"":" ");return T.createElement("tspan",{x:M,dy:k===0?E:u,key:"".concat(N,"-").concat(k)},N)}))};function wt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function HS(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ff(e){let t,r,n;e.length!==2?(t=wt,r=(u,c)=>wt(e(u),c),n=(u,c)=>e(u)-c):(t=e===wt||e===HS?e:GS,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function GS(){return 0}function jb(e){return e===null?NaN:+e}function*KS(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const XS=ff(wt),Dn=XS.right;ff(jb).center;class kv extends Map{constructor(t,r=ZS){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(qv(this,t))}has(t){return super.has(qv(this,t))}set(t,r){return super.set(VS(this,t),r)}delete(t){return super.delete(YS(this,t))}}function qv({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function VS({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function YS({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function ZS(e){return e!==null&&typeof e=="object"?e.valueOf():e}function JS(e=wt){if(e===wt)return Mb;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function Mb(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const QS=Math.sqrt(50),eA=Math.sqrt(10),tA=Math.sqrt(2);function vi(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=QS?10:a>=eA?5:a>=tA?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?vi(e,t,r*2):[u,c,s]}function Ks(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?vi(t,e,r):vi(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function Xs(e,t,r){return t=+t,e=+e,r=+r,vi(e,t,r)[2]}function Vs(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?Xs(t,e,r):Xs(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Nv(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function Bv(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function Cb(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?Mb:JS(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),d=Math.max(r,Math.floor(t-s*l/c+h)),y=Math.min(n,Math.floor(t+(c-s)*l/c+h));Cb(e,t,d,y,i)}const a=e[t];let o=r,u=n;for(Ir(e,r,t),i(e[n],a)>0&&Ir(e,r,n);o<u;){for(Ir(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?Ir(e,r,u):(++u,Ir(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function Ir(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function rA(e,t,r){if(e=Float64Array.from(KS(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return Bv(e);if(t>=1)return Nv(e);var n,i=(n-1)*t,a=Math.floor(i),o=Nv(Cb(e,a).subarray(0,a+1)),u=Bv(e.subarray(a+1));return o+(u-o)*(i-a)}}function nA(e,t,r=jb){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function iA(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function We(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function yt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Ys=Symbol("implicit");function hf(){var e=new kv,t=[],r=[],n=Ys;function i(a){let o=e.get(a);if(o===void 0){if(n!==Ys)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new kv;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return hf(t,r).unknown(n)},We.apply(i,arguments),i}function tn(){var e=hf().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var h=t().length,d=i<n,y=d?i:n,v=d?n:i;a=(v-y)/Math.max(1,h-c+s*2),u&&(a=Math.floor(a)),y+=(v-y-a*(h-c))*f,o=a*(1-c),u&&(y=Math.round(y),o=Math.round(o));var p=iA(h).map(function(b){return y+a*b});return r(d?p.reverse():p)}return e.domain=function(h){return arguments.length?(t(h),l()):t()},e.range=function(h){return arguments.length?([n,i]=h,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(h){return[n,i]=h,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(h){return arguments.length?(u=!!h,l()):u},e.padding=function(h){return arguments.length?(c=Math.min(1,s=+h),l()):c},e.paddingInner=function(h){return arguments.length?(c=Math.min(1,h),l()):c},e.paddingOuter=function(h){return arguments.length?(s=+h,l()):s},e.align=function(h){return arguments.length?(f=Math.max(0,Math.min(1,h)),l()):f},e.copy=function(){return tn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},We.apply(l(),arguments)}function $b(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return $b(t())},e}function Hr(){return $b(tn.apply(null,arguments).paddingInner(1))}function pf(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function Ib(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function kn(){}var rn=.7,yi=1/rn,nr="\\s*([+-]?\\d+)\\s*",nn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",et="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",aA=/^#([0-9a-f]{3,8})$/,oA=new RegExp(`^rgb\\(${nr},${nr},${nr}\\)$`),uA=new RegExp(`^rgb\\(${et},${et},${et}\\)$`),cA=new RegExp(`^rgba\\(${nr},${nr},${nr},${nn}\\)$`),sA=new RegExp(`^rgba\\(${et},${et},${et},${nn}\\)$`),lA=new RegExp(`^hsl\\(${nn},${et},${et}\\)$`),fA=new RegExp(`^hsla\\(${nn},${et},${et},${nn}\\)$`),Lv={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};pf(kn,an,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Fv,formatHex:Fv,formatHex8:hA,formatHsl:pA,formatRgb:Uv,toString:Uv});function Fv(){return this.rgb().formatHex()}function hA(){return this.rgb().formatHex8()}function pA(){return Rb(this).formatHsl()}function Uv(){return this.rgb().formatRgb()}function an(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=aA.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Wv(t):r===3?new $e(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?Gn(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?Gn(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=oA.exec(e))?new $e(t[1],t[2],t[3],1):(t=uA.exec(e))?new $e(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=cA.exec(e))?Gn(t[1],t[2],t[3],t[4]):(t=sA.exec(e))?Gn(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=lA.exec(e))?Gv(t[1],t[2]/100,t[3]/100,1):(t=fA.exec(e))?Gv(t[1],t[2]/100,t[3]/100,t[4]):Lv.hasOwnProperty(e)?Wv(Lv[e]):e==="transparent"?new $e(NaN,NaN,NaN,0):null}function Wv(e){return new $e(e>>16&255,e>>8&255,e&255,1)}function Gn(e,t,r,n){return n<=0&&(e=t=r=NaN),new $e(e,t,r,n)}function dA(e){return e instanceof kn||(e=an(e)),e?(e=e.rgb(),new $e(e.r,e.g,e.b,e.opacity)):new $e}function Zs(e,t,r,n){return arguments.length===1?dA(e):new $e(e,t,r,n??1)}function $e(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}pf($e,Zs,Ib(kn,{brighter(e){return e=e==null?yi:Math.pow(yi,e),new $e(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?rn:Math.pow(rn,e),new $e(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new $e(Bt(this.r),Bt(this.g),Bt(this.b),gi(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:zv,formatHex:zv,formatHex8:vA,formatRgb:Hv,toString:Hv}));function zv(){return`#${Dt(this.r)}${Dt(this.g)}${Dt(this.b)}`}function vA(){return`#${Dt(this.r)}${Dt(this.g)}${Dt(this.b)}${Dt((isNaN(this.opacity)?1:this.opacity)*255)}`}function Hv(){const e=gi(this.opacity);return`${e===1?"rgb(":"rgba("}${Bt(this.r)}, ${Bt(this.g)}, ${Bt(this.b)}${e===1?")":`, ${e})`}`}function gi(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Bt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Dt(e){return e=Bt(e),(e<16?"0":"")+e.toString(16)}function Gv(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new Ke(e,t,r,n)}function Rb(e){if(e instanceof Ke)return new Ke(e.h,e.s,e.l,e.opacity);if(e instanceof kn||(e=an(e)),!e)return new Ke;if(e instanceof Ke)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new Ke(o,u,c,e.opacity)}function yA(e,t,r,n){return arguments.length===1?Rb(e):new Ke(e,t,r,n??1)}function Ke(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}pf(Ke,yA,Ib(kn,{brighter(e){return e=e==null?yi:Math.pow(yi,e),new Ke(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?rn:Math.pow(rn,e),new Ke(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new $e(Nc(e>=240?e-240:e+120,i,n),Nc(e,i,n),Nc(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new Ke(Kv(this.h),Kn(this.s),Kn(this.l),gi(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=gi(this.opacity);return`${e===1?"hsl(":"hsla("}${Kv(this.h)}, ${Kn(this.s)*100}%, ${Kn(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Kv(e){return e=(e||0)%360,e<0?e+360:e}function Kn(e){return Math.max(0,Math.min(1,e||0))}function Nc(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const df=e=>()=>e;function gA(e,t){return function(r){return e+r*t}}function mA(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function bA(e){return(e=+e)==1?Db:function(t,r){return r-t?mA(t,r,e):df(isNaN(t)?r:t)}}function Db(e,t){var r=t-e;return r?gA(e,r):df(isNaN(e)?t:e)}const Xv=function e(t){var r=bA(t);function n(i,a){var o=r((i=Zs(i)).r,(a=Zs(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=Db(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function xA(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function wA(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function OA(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=jr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function _A(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function mi(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function SA(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=jr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var Js=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Bc=new RegExp(Js.source,"g");function AA(e){return function(){return e}}function PA(e){return function(t){return e(t)+""}}function TA(e,t){var r=Js.lastIndex=Bc.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=Js.exec(e))&&(i=Bc.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:mi(n,i)})),r=Bc.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?PA(c[0].x):AA(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function jr(e,t){var r=typeof t,n;return t==null||r==="boolean"?df(t):(r==="number"?mi:r==="string"?(n=an(t))?(t=n,Xv):TA:t instanceof an?Xv:t instanceof Date?_A:wA(t)?xA:Array.isArray(t)?OA:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?SA:mi)(e,t)}function vf(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function EA(e,t){t===void 0&&(t=e,e=jr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function jA(e){return function(){return e}}function bi(e){return+e}var Vv=[0,1];function je(e){return e}function Qs(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:jA(isNaN(t)?NaN:.5)}function MA(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function CA(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Qs(i,n),a=r(o,a)):(n=Qs(n,i),a=r(a,o)),function(u){return a(n(u))}}function $A(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Qs(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=Dn(e,u,1,n)-1;return a[c](i[c](u))}}function qn(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function sa(){var e=Vv,t=Vv,r=jr,n,i,a,o=je,u,c,s;function f(){var h=Math.min(e.length,t.length);return o!==je&&(o=MA(e[0],e[h-1])),u=h>2?$A:CA,c=s=null,l}function l(h){return h==null||isNaN(h=+h)?a:(c||(c=u(e.map(n),t,r)))(n(o(h)))}return l.invert=function(h){return o(i((s||(s=u(t,e.map(n),mi)))(h)))},l.domain=function(h){return arguments.length?(e=Array.from(h,bi),f()):e.slice()},l.range=function(h){return arguments.length?(t=Array.from(h),f()):t.slice()},l.rangeRound=function(h){return t=Array.from(h),r=vf,f()},l.clamp=function(h){return arguments.length?(o=h?!0:je,f()):o!==je},l.interpolate=function(h){return arguments.length?(r=h,f()):r},l.unknown=function(h){return arguments.length?(a=h,l):a},function(h,d){return n=h,i=d,f()}}function yf(){return sa()(je,je)}function IA(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function xi(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function lr(e){return e=xi(Math.abs(e)),e?e[1]:NaN}function RA(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function DA(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var kA=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function on(e){if(!(t=kA.exec(e)))throw new Error("invalid format: "+e);var t;return new gf({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}on.prototype=gf.prototype;function gf(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}gf.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function qA(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var kb;function NA(e,t){var r=xi(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(kb=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+xi(e,Math.max(0,t+a-1))[0]}function Yv(e,t){var r=xi(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Zv={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:IA,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Yv(e*100,t),r:Yv,s:NA,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Jv(e){return e}var Qv=Array.prototype.map,ey=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function BA(e){var t=e.grouping===void 0||e.thousands===void 0?Jv:RA(Qv.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Jv:DA(Qv.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=on(l);var h=l.fill,d=l.align,y=l.sign,v=l.symbol,p=l.zero,b=l.width,w=l.comma,x=l.precision,O=l.trim,g=l.type;g==="n"?(w=!0,g="g"):Zv[g]||(x===void 0&&(x=12),O=!0,g="g"),(p||h==="0"&&d==="=")&&(p=!0,h="0",d="=");var m=v==="$"?r:v==="#"&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",_=v==="$"?n:/[%p]/.test(g)?o:"",S=Zv[g],P=/[defgprs%]/.test(g);x=x===void 0?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,x)):Math.max(0,Math.min(20,x));function M(A){var E=m,j=_,I,$,R;if(g==="c")j=S(A)+j,A="";else{A=+A;var k=A<0||1/A<0;if(A=isNaN(A)?c:S(Math.abs(A),x),O&&(A=qA(A)),k&&+A==0&&y!=="+"&&(k=!1),E=(k?y==="("?y:u:y==="-"||y==="("?"":y)+E,j=(g==="s"?ey[8+kb/3]:"")+j+(k&&y==="("?")":""),P){for(I=-1,$=A.length;++I<$;)if(R=A.charCodeAt(I),48>R||R>57){j=(R===46?i+A.slice(I+1):A.slice(I))+j,A=A.slice(0,I);break}}}w&&!p&&(A=t(A,1/0));var N=E.length+A.length+j.length,B=N<b?new Array(b-N+1).join(h):"";switch(w&&p&&(A=t(B+A,B.length?b-j.length:1/0),B=""),d){case"<":A=E+A+j+B;break;case"=":A=E+B+A+j;break;case"^":A=B.slice(0,N=B.length>>1)+E+A+j+B.slice(N);break;default:A=B+E+A+j;break}return a(A)}return M.toString=function(){return l+""},M}function f(l,h){var d=s((l=on(l),l.type="f",l)),y=Math.max(-8,Math.min(8,Math.floor(lr(h)/3)))*3,v=Math.pow(10,-y),p=ey[8+y/3];return function(b){return d(v*b)+p}}return{format:s,formatPrefix:f}}var Xn,mf,qb;LA({thousands:",",grouping:[3],currency:["$",""]});function LA(e){return Xn=BA(e),mf=Xn.format,qb=Xn.formatPrefix,Xn}function FA(e){return Math.max(0,-lr(Math.abs(e)))}function UA(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(lr(t)/3)))*3-lr(Math.abs(e)))}function WA(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,lr(t)-lr(e))+1}function Nb(e,t,r,n){var i=Vs(e,t,r),a;switch(n=on(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=UA(i,o))&&(n.precision=a),qb(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=WA(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=FA(i))&&(n.precision=a-(n.type==="%")*2);break}}return mf(n)}function At(e){var t=e.domain;return e.ticks=function(r){var n=t();return Ks(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return Nb(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=Xs(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function wi(){var e=yf();return e.copy=function(){return qn(e,wi())},We.apply(e,arguments),At(e)}function Bb(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,bi),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Bb(e).unknown(t)},e=arguments.length?Array.from(e,bi):[0,1],At(r)}function Lb(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function ty(e){return Math.log(e)}function ry(e){return Math.exp(e)}function zA(e){return-Math.log(-e)}function HA(e){return-Math.exp(-e)}function GA(e){return isFinite(e)?+("1e"+e):e<0?0:e}function KA(e){return e===10?GA:e===Math.E?Math.exp:t=>Math.pow(e,t)}function XA(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function ny(e){return(t,r)=>-e(-t,r)}function bf(e){const t=e(ty,ry),r=t.domain;let n=10,i,a;function o(){return i=XA(n),a=KA(n),r()[0]<0?(i=ny(i),a=ny(a),e(zA,HA)):e(ty,ry),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let h=i(s),d=i(f),y,v;const p=u==null?10:+u;let b=[];if(!(n%1)&&d-h<p){if(h=Math.floor(h),d=Math.ceil(d),s>0){for(;h<=d;++h)for(y=1;y<n;++y)if(v=h<0?y/a(-h):y*a(h),!(v<s)){if(v>f)break;b.push(v)}}else for(;h<=d;++h)for(y=n-1;y>=1;--y)if(v=h>0?y/a(-h):y*a(h),!(v<s)){if(v>f)break;b.push(v)}b.length*2<p&&(b=Ks(s,f,p))}else b=Ks(h,d,Math.min(d-h,p)).map(a);return l?b.reverse():b},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=on(c)).precision==null&&(c.trim=!0),c=mf(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(Lb(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function Fb(){const e=bf(sa()).domain([1,10]);return e.copy=()=>qn(e,Fb()).base(e.base()),We.apply(e,arguments),e}function iy(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function ay(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function xf(e){var t=1,r=e(iy(t),ay(t));return r.constant=function(n){return arguments.length?e(iy(t=+n),ay(t)):t},At(r)}function Ub(){var e=xf(sa());return e.copy=function(){return qn(e,Ub()).constant(e.constant())},We.apply(e,arguments)}function oy(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function VA(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function YA(e){return e<0?-e*e:e*e}function wf(e){var t=e(je,je),r=1;function n(){return r===1?e(je,je):r===.5?e(VA,YA):e(oy(r),oy(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},At(t)}function Of(){var e=wf(sa());return e.copy=function(){return qn(e,Of()).exponent(e.exponent())},We.apply(e,arguments),e}function ZA(){return Of.apply(null,arguments).exponent(.5)}function uy(e){return Math.sign(e)*e*e}function JA(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function Wb(){var e=yf(),t=[0,1],r=!1,n;function i(a){var o=JA(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(uy(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,bi)).map(uy)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Wb(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},We.apply(i,arguments),At(i)}function zb(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=nA(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[Dn(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(wt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return zb().domain(e).range(t).unknown(n)},We.apply(a,arguments)}function Hb(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[Dn(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return Hb().domain([e,t]).range(i).unknown(a)},We.apply(At(o),arguments)}function Gb(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[Dn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Gb().domain(e).range(t).unknown(r)},We.apply(i,arguments)}const Lc=new Date,Fc=new Date;function xe(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>xe(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(Lc.setTime(+a),Fc.setTime(+o),e(Lc),e(Fc),Math.floor(r(Lc,Fc))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Oi=xe(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Oi.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?xe(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Oi);Oi.range;const ut=1e3,Le=ut*60,ct=Le*60,ft=ct*24,_f=ft*7,cy=ft*30,Uc=ft*365,kt=xe(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*ut)},(e,t)=>(t-e)/ut,e=>e.getUTCSeconds());kt.range;const Sf=xe(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ut)},(e,t)=>{e.setTime(+e+t*Le)},(e,t)=>(t-e)/Le,e=>e.getMinutes());Sf.range;const Af=xe(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Le)},(e,t)=>(t-e)/Le,e=>e.getUTCMinutes());Af.range;const Pf=xe(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ut-e.getMinutes()*Le)},(e,t)=>{e.setTime(+e+t*ct)},(e,t)=>(t-e)/ct,e=>e.getHours());Pf.range;const Tf=xe(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*ct)},(e,t)=>(t-e)/ct,e=>e.getUTCHours());Tf.range;const Nn=xe(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Le)/ft,e=>e.getDate()-1);Nn.range;const la=xe(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/ft,e=>e.getUTCDate()-1);la.range;const Kb=xe(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/ft,e=>Math.floor(e/ft));Kb.range;function Ht(e){return xe(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Le)/_f)}const fa=Ht(0),_i=Ht(1),QA=Ht(2),eP=Ht(3),fr=Ht(4),tP=Ht(5),rP=Ht(6);fa.range;_i.range;QA.range;eP.range;fr.range;tP.range;rP.range;function Gt(e){return xe(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/_f)}const ha=Gt(0),Si=Gt(1),nP=Gt(2),iP=Gt(3),hr=Gt(4),aP=Gt(5),oP=Gt(6);ha.range;Si.range;nP.range;iP.range;hr.range;aP.range;oP.range;const Ef=xe(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Ef.range;const jf=xe(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());jf.range;const ht=xe(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());ht.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:xe(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});ht.range;const pt=xe(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());pt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:xe(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});pt.range;function Xb(e,t,r,n,i,a){const o=[[kt,1,ut],[kt,5,5*ut],[kt,15,15*ut],[kt,30,30*ut],[a,1,Le],[a,5,5*Le],[a,15,15*Le],[a,30,30*Le],[i,1,ct],[i,3,3*ct],[i,6,6*ct],[i,12,12*ct],[n,1,ft],[n,2,2*ft],[r,1,_f],[t,1,cy],[t,3,3*cy],[e,1,Uc]];function u(s,f,l){const h=f<s;h&&([s,f]=[f,s]);const d=l&&typeof l.range=="function"?l:c(s,f,l),y=d?d.range(s,+f+1):[];return h?y.reverse():y}function c(s,f,l){const h=Math.abs(f-s)/l,d=ff(([,,p])=>p).right(o,h);if(d===o.length)return e.every(Vs(s/Uc,f/Uc,l));if(d===0)return Oi.every(Math.max(Vs(s,f,l),1));const[y,v]=o[h/o[d-1][2]<o[d][2]/h?d-1:d];return y.every(v)}return[u,c]}const[uP,cP]=Xb(pt,jf,ha,Kb,Tf,Af),[sP,lP]=Xb(ht,Ef,fa,Nn,Pf,Sf);function Wc(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function zc(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Rr(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function fP(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=Dr(i),f=kr(i),l=Dr(a),h=kr(a),d=Dr(o),y=kr(o),v=Dr(u),p=kr(u),b=Dr(c),w=kr(c),x={a:k,A:N,b:B,B:z,c:null,d:dy,e:dy,f:RP,g:zP,G:GP,H:CP,I:$P,j:IP,L:Vb,m:DP,M:kP,p:G,q:U,Q:gy,s:my,S:qP,u:NP,U:BP,V:LP,w:FP,W:UP,x:null,X:null,y:WP,Y:HP,Z:KP,"%":yy},O={a:K,A:ce,b:pe,B:Re,c:null,d:vy,e:vy,f:ZP,g:uT,G:sT,H:XP,I:VP,j:YP,L:Zb,m:JP,M:QP,p:Et,q:Me,Q:gy,s:my,S:eT,u:tT,U:rT,V:nT,w:iT,W:aT,x:null,X:null,y:oT,Y:cT,Z:lT,"%":yy},g={a:M,A,b:E,B:j,c:I,d:hy,e:hy,f:TP,g:fy,G:ly,H:py,I:py,j:_P,L:PP,m:OP,M:SP,p:P,q:wP,Q:jP,s:MP,S:AP,u:yP,U:gP,V:mP,w:vP,W:bP,x:$,X:R,y:fy,Y:ly,Z:xP,"%":EP};x.x=m(r,x),x.X=m(n,x),x.c=m(t,x),O.x=m(r,O),O.X=m(n,O),O.c=m(t,O);function m(L,X){return function(V){var D=[],fe=-1,J=0,ye=L.length,ge,Ce,gt;for(V instanceof Date||(V=new Date(+V));++fe<ye;)L.charCodeAt(fe)===37&&(D.push(L.slice(J,fe)),(Ce=sy[ge=L.charAt(++fe)])!=null?ge=L.charAt(++fe):Ce=ge==="e"?" ":"0",(gt=X[ge])&&(ge=gt(V,Ce)),D.push(ge),J=fe+1);return D.push(L.slice(J,fe)),D.join("")}}function _(L,X){return function(V){var D=Rr(1900,void 0,1),fe=S(D,L,V+="",0),J,ye;if(fe!=V.length)return null;if("Q"in D)return new Date(D.Q);if("s"in D)return new Date(D.s*1e3+("L"in D?D.L:0));if(X&&!("Z"in D)&&(D.Z=0),"p"in D&&(D.H=D.H%12+D.p*12),D.m===void 0&&(D.m="q"in D?D.q:0),"V"in D){if(D.V<1||D.V>53)return null;"w"in D||(D.w=1),"Z"in D?(J=zc(Rr(D.y,0,1)),ye=J.getUTCDay(),J=ye>4||ye===0?Si.ceil(J):Si(J),J=la.offset(J,(D.V-1)*7),D.y=J.getUTCFullYear(),D.m=J.getUTCMonth(),D.d=J.getUTCDate()+(D.w+6)%7):(J=Wc(Rr(D.y,0,1)),ye=J.getDay(),J=ye>4||ye===0?_i.ceil(J):_i(J),J=Nn.offset(J,(D.V-1)*7),D.y=J.getFullYear(),D.m=J.getMonth(),D.d=J.getDate()+(D.w+6)%7)}else("W"in D||"U"in D)&&("w"in D||(D.w="u"in D?D.u%7:"W"in D?1:0),ye="Z"in D?zc(Rr(D.y,0,1)).getUTCDay():Wc(Rr(D.y,0,1)).getDay(),D.m=0,D.d="W"in D?(D.w+6)%7+D.W*7-(ye+5)%7:D.w+D.U*7-(ye+6)%7);return"Z"in D?(D.H+=D.Z/100|0,D.M+=D.Z%100,zc(D)):Wc(D)}}function S(L,X,V,D){for(var fe=0,J=X.length,ye=V.length,ge,Ce;fe<J;){if(D>=ye)return-1;if(ge=X.charCodeAt(fe++),ge===37){if(ge=X.charAt(fe++),Ce=g[ge in sy?X.charAt(fe++):ge],!Ce||(D=Ce(L,V,D))<0)return-1}else if(ge!=V.charCodeAt(D++))return-1}return D}function P(L,X,V){var D=s.exec(X.slice(V));return D?(L.p=f.get(D[0].toLowerCase()),V+D[0].length):-1}function M(L,X,V){var D=d.exec(X.slice(V));return D?(L.w=y.get(D[0].toLowerCase()),V+D[0].length):-1}function A(L,X,V){var D=l.exec(X.slice(V));return D?(L.w=h.get(D[0].toLowerCase()),V+D[0].length):-1}function E(L,X,V){var D=b.exec(X.slice(V));return D?(L.m=w.get(D[0].toLowerCase()),V+D[0].length):-1}function j(L,X,V){var D=v.exec(X.slice(V));return D?(L.m=p.get(D[0].toLowerCase()),V+D[0].length):-1}function I(L,X,V){return S(L,t,X,V)}function $(L,X,V){return S(L,r,X,V)}function R(L,X,V){return S(L,n,X,V)}function k(L){return o[L.getDay()]}function N(L){return a[L.getDay()]}function B(L){return c[L.getMonth()]}function z(L){return u[L.getMonth()]}function G(L){return i[+(L.getHours()>=12)]}function U(L){return 1+~~(L.getMonth()/3)}function K(L){return o[L.getUTCDay()]}function ce(L){return a[L.getUTCDay()]}function pe(L){return c[L.getUTCMonth()]}function Re(L){return u[L.getUTCMonth()]}function Et(L){return i[+(L.getUTCHours()>=12)]}function Me(L){return 1+~~(L.getUTCMonth()/3)}return{format:function(L){var X=m(L+="",x);return X.toString=function(){return L},X},parse:function(L){var X=_(L+="",!1);return X.toString=function(){return L},X},utcFormat:function(L){var X=m(L+="",O);return X.toString=function(){return L},X},utcParse:function(L){var X=_(L+="",!0);return X.toString=function(){return L},X}}}var sy={"-":"",_:" ",0:"0"},_e=/^\s*\d+/,hP=/^%/,pP=/[\\^$*+?|[\]().{}]/g;function Q(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function dP(e){return e.replace(pP,"\\$&")}function Dr(e){return new RegExp("^(?:"+e.map(dP).join("|")+")","i")}function kr(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function vP(e,t,r){var n=_e.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function yP(e,t,r){var n=_e.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function gP(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function mP(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function bP(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function ly(e,t,r){var n=_e.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function fy(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function xP(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function wP(e,t,r){var n=_e.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function OP(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function hy(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function _P(e,t,r){var n=_e.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function py(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function SP(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function AP(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function PP(e,t,r){var n=_e.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function TP(e,t,r){var n=_e.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function EP(e,t,r){var n=hP.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function jP(e,t,r){var n=_e.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function MP(e,t,r){var n=_e.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function dy(e,t){return Q(e.getDate(),t,2)}function CP(e,t){return Q(e.getHours(),t,2)}function $P(e,t){return Q(e.getHours()%12||12,t,2)}function IP(e,t){return Q(1+Nn.count(ht(e),e),t,3)}function Vb(e,t){return Q(e.getMilliseconds(),t,3)}function RP(e,t){return Vb(e,t)+"000"}function DP(e,t){return Q(e.getMonth()+1,t,2)}function kP(e,t){return Q(e.getMinutes(),t,2)}function qP(e,t){return Q(e.getSeconds(),t,2)}function NP(e){var t=e.getDay();return t===0?7:t}function BP(e,t){return Q(fa.count(ht(e)-1,e),t,2)}function Yb(e){var t=e.getDay();return t>=4||t===0?fr(e):fr.ceil(e)}function LP(e,t){return e=Yb(e),Q(fr.count(ht(e),e)+(ht(e).getDay()===4),t,2)}function FP(e){return e.getDay()}function UP(e,t){return Q(_i.count(ht(e)-1,e),t,2)}function WP(e,t){return Q(e.getFullYear()%100,t,2)}function zP(e,t){return e=Yb(e),Q(e.getFullYear()%100,t,2)}function HP(e,t){return Q(e.getFullYear()%1e4,t,4)}function GP(e,t){var r=e.getDay();return e=r>=4||r===0?fr(e):fr.ceil(e),Q(e.getFullYear()%1e4,t,4)}function KP(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Q(t/60|0,"0",2)+Q(t%60,"0",2)}function vy(e,t){return Q(e.getUTCDate(),t,2)}function XP(e,t){return Q(e.getUTCHours(),t,2)}function VP(e,t){return Q(e.getUTCHours()%12||12,t,2)}function YP(e,t){return Q(1+la.count(pt(e),e),t,3)}function Zb(e,t){return Q(e.getUTCMilliseconds(),t,3)}function ZP(e,t){return Zb(e,t)+"000"}function JP(e,t){return Q(e.getUTCMonth()+1,t,2)}function QP(e,t){return Q(e.getUTCMinutes(),t,2)}function eT(e,t){return Q(e.getUTCSeconds(),t,2)}function tT(e){var t=e.getUTCDay();return t===0?7:t}function rT(e,t){return Q(ha.count(pt(e)-1,e),t,2)}function Jb(e){var t=e.getUTCDay();return t>=4||t===0?hr(e):hr.ceil(e)}function nT(e,t){return e=Jb(e),Q(hr.count(pt(e),e)+(pt(e).getUTCDay()===4),t,2)}function iT(e){return e.getUTCDay()}function aT(e,t){return Q(Si.count(pt(e)-1,e),t,2)}function oT(e,t){return Q(e.getUTCFullYear()%100,t,2)}function uT(e,t){return e=Jb(e),Q(e.getUTCFullYear()%100,t,2)}function cT(e,t){return Q(e.getUTCFullYear()%1e4,t,4)}function sT(e,t){var r=e.getUTCDay();return e=r>=4||r===0?hr(e):hr.ceil(e),Q(e.getUTCFullYear()%1e4,t,4)}function lT(){return"+0000"}function yy(){return"%"}function gy(e){return+e}function my(e){return Math.floor(+e/1e3)}var Zt,Qb,e0;fT({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function fT(e){return Zt=fP(e),Qb=Zt.format,Zt.parse,e0=Zt.utcFormat,Zt.utcParse,Zt}function hT(e){return new Date(e)}function pT(e){return e instanceof Date?+e:+new Date(+e)}function Mf(e,t,r,n,i,a,o,u,c,s){var f=yf(),l=f.invert,h=f.domain,d=s(".%L"),y=s(":%S"),v=s("%I:%M"),p=s("%I %p"),b=s("%a %d"),w=s("%b %d"),x=s("%B"),O=s("%Y");function g(m){return(c(m)<m?d:u(m)<m?y:o(m)<m?v:a(m)<m?p:n(m)<m?i(m)<m?b:w:r(m)<m?x:O)(m)}return f.invert=function(m){return new Date(l(m))},f.domain=function(m){return arguments.length?h(Array.from(m,pT)):h().map(hT)},f.ticks=function(m){var _=h();return e(_[0],_[_.length-1],m??10)},f.tickFormat=function(m,_){return _==null?g:s(_)},f.nice=function(m){var _=h();return(!m||typeof m.range!="function")&&(m=t(_[0],_[_.length-1],m??10)),m?h(Lb(_,m)):f},f.copy=function(){return qn(f,Mf(e,t,r,n,i,a,o,u,c,s))},f}function dT(){return We.apply(Mf(sP,lP,ht,Ef,fa,Nn,Pf,Sf,kt,Qb).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function vT(){return We.apply(Mf(uP,cP,pt,jf,ha,la,Tf,Af,kt,e0).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function pa(){var e=0,t=1,r,n,i,a,o=je,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(h){var d,y;return arguments.length?([d,y]=h,o=l(d,y),s):[o(0),o(1)]}}return s.range=f(jr),s.rangeRound=f(vf),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function Pt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function t0(){var e=At(pa()(je));return e.copy=function(){return Pt(e,t0())},yt.apply(e,arguments)}function r0(){var e=bf(pa()).domain([1,10]);return e.copy=function(){return Pt(e,r0()).base(e.base())},yt.apply(e,arguments)}function n0(){var e=xf(pa());return e.copy=function(){return Pt(e,n0()).constant(e.constant())},yt.apply(e,arguments)}function Cf(){var e=wf(pa());return e.copy=function(){return Pt(e,Cf()).exponent(e.exponent())},yt.apply(e,arguments)}function yT(){return Cf.apply(null,arguments).exponent(.5)}function i0(){var e=[],t=je;function r(n){if(n!=null&&!isNaN(n=+n))return t((Dn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(wt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>rA(e,a/n))},r.copy=function(){return i0(t).domain(e)},yt.apply(r,arguments)}function da(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=je,f,l=!1,h;function d(v){return isNaN(v=+v)?h:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:c),s(l?Math.max(0,Math.min(1,v)):v))}d.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,d):[e,t,r]},d.clamp=function(v){return arguments.length?(l=!!v,d):l},d.interpolator=function(v){return arguments.length?(s=v,d):s};function y(v){return function(p){var b,w,x;return arguments.length?([b,w,x]=p,s=EA(v,[b,w,x]),d):[s(0),s(.5),s(1)]}}return d.range=y(jr),d.rangeRound=y(vf),d.unknown=function(v){return arguments.length?(h=v,d):h},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,d}}function a0(){var e=At(da()(je));return e.copy=function(){return Pt(e,a0())},yt.apply(e,arguments)}function o0(){var e=bf(da()).domain([.1,1,10]);return e.copy=function(){return Pt(e,o0()).base(e.base())},yt.apply(e,arguments)}function u0(){var e=xf(da());return e.copy=function(){return Pt(e,u0()).constant(e.constant())},yt.apply(e,arguments)}function $f(){var e=wf(da());return e.copy=function(){return Pt(e,$f()).exponent(e.exponent())},yt.apply(e,arguments)}function gT(){return $f.apply(null,arguments).exponent(.5)}const by=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:tn,scaleDiverging:a0,scaleDivergingLog:o0,scaleDivergingPow:$f,scaleDivergingSqrt:gT,scaleDivergingSymlog:u0,scaleIdentity:Bb,scaleImplicit:Ys,scaleLinear:wi,scaleLog:Fb,scaleOrdinal:hf,scalePoint:Hr,scalePow:Of,scaleQuantile:zb,scaleQuantize:Hb,scaleRadial:Wb,scaleSequential:t0,scaleSequentialLog:r0,scaleSequentialPow:Cf,scaleSequentialQuantile:i0,scaleSequentialSqrt:yT,scaleSequentialSymlog:n0,scaleSqrt:ZA,scaleSymlog:Ub,scaleThreshold:Gb,scaleTime:dT,scaleUtc:vT,tickFormat:Nb},Symbol.toStringTag,{value:"Module"}));var Hc,xy;function c0(){if(xy)return Hc;xy=1;var e=Pr();function t(r,n,i){for(var a=-1,o=r.length;++a<o;){var u=r[a],c=n(u);if(c!=null&&(s===void 0?c===c&&!e(c):i(c,s)))var s=c,f=u}return f}return Hc=t,Hc}var Gc,wy;function mT(){if(wy)return Gc;wy=1;function e(t,r){return t>r}return Gc=e,Gc}var Kc,Oy;function bT(){if(Oy)return Kc;Oy=1;var e=c0(),t=mT(),r=Er();function n(i){return i&&i.length?e(i,r,t):void 0}return Kc=n,Kc}var xT=bT();const va=ue(xT);var Xc,_y;function wT(){if(_y)return Xc;_y=1;function e(t,r){return t<r}return Xc=e,Xc}var Vc,Sy;function OT(){if(Sy)return Vc;Sy=1;var e=c0(),t=wT(),r=Er();function n(i){return i&&i.length?e(i,r,t):void 0}return Vc=n,Vc}var _T=OT();const ya=ue(_T);var Yc,Ay;function ST(){if(Ay)return Yc;Ay=1;var e=Kl(),t=St(),r=yb(),n=Ie();function i(a,o){var u=n(a)?e:r;return u(a,t(o,3))}return Yc=i,Yc}var Zc,Py;function AT(){if(Py)return Zc;Py=1;var e=db(),t=ST();function r(n,i){return e(t(n,i),1)}return Zc=r,Zc}var PT=AT();const TT=ue(PT);var Jc,Ty;function ET(){if(Ty)return Jc;Ty=1;var e=uf();function t(r,n){return e(r,n)}return Jc=t,Jc}var jT=ET();const If=ue(jT);var Mr=1e9,MT={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Df,le=!0,Ue="[DecimalError] ",Lt=Ue+"Invalid argument: ",Rf=Ue+"Exponent out of range: ",Cr=Math.floor,It=Math.pow,CT=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,qe,we=1e7,se=7,s0=9007199254740991,Ai=Cr(s0/se),W={};W.absoluteValue=W.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};W.comparedTo=W.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};W.decimalPlaces=W.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*se;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};W.dividedBy=W.div=function(e){return lt(this,new this.constructor(e))};W.dividedToIntegerBy=W.idiv=function(e){var t=this,r=t.constructor;return ae(lt(t,new r(e),0,1),r.precision)};W.equals=W.eq=function(e){return!this.cmp(e)};W.exponent=function(){return ve(this)};W.greaterThan=W.gt=function(e){return this.cmp(e)>0};W.greaterThanOrEqualTo=W.gte=function(e){return this.cmp(e)>=0};W.isInteger=W.isint=function(){return this.e>this.d.length-2};W.isNegative=W.isneg=function(){return this.s<0};W.isPositive=W.ispos=function(){return this.s>0};W.isZero=function(){return this.s===0};W.lessThan=W.lt=function(e){return this.cmp(e)<0};W.lessThanOrEqualTo=W.lte=function(e){return this.cmp(e)<1};W.logarithm=W.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(qe))throw Error(Ue+"NaN");if(r.s<1)throw Error(Ue+(r.s?"NaN":"-Infinity"));return r.eq(qe)?new n(0):(le=!1,t=lt(un(r,a),un(e,a),a),le=!0,ae(t,i))};W.minus=W.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?h0(t,e):l0(t,(e.s=-e.s,e))};W.modulo=W.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Ue+"NaN");return r.s?(le=!1,t=lt(r,e,0,1).times(e),le=!0,r.minus(t)):ae(new n(r),i)};W.naturalExponential=W.exp=function(){return f0(this)};W.naturalLogarithm=W.ln=function(){return un(this)};W.negated=W.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};W.plus=W.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?l0(t,e):h0(t,(e.s=-e.s,e))};W.precision=W.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Lt+e);if(t=ve(i)+1,n=i.d.length-1,r=n*se+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};W.squareRoot=W.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Ue+"NaN")}for(e=ve(u),le=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=Qe(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Cr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(lt(u,a,o+2)).times(.5),Qe(a.d).slice(0,o)===(t=Qe(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(ae(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return le=!0,ae(n,r)};W.times=W.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,h=f.d,d=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=h.length,s=d.length,c<s&&(a=h,h=d,d=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+d[n]*h[i-n-1]+t,a[i--]=u%we|0,t=u/we|0;a[i]=(a[i]+t)%we|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,le?ae(e,l.precision):e};W.toDecimalPlaces=W.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(rt(e,0,Mr),t===void 0?t=n.rounding:rt(t,0,8),ae(r,e+ve(r)+1,t))};W.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Wt(n,!0):(rt(e,0,Mr),t===void 0?t=i.rounding:rt(t,0,8),n=ae(new i(n),e+1,t),r=Wt(n,!0,e+1)),r};W.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Wt(i):(rt(e,0,Mr),t===void 0?t=a.rounding:rt(t,0,8),n=ae(new a(i),e+ve(i)+1,t),r=Wt(n.abs(),!1,e+ve(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};W.toInteger=W.toint=function(){var e=this,t=e.constructor;return ae(new t(e),ve(e)+1,t.rounding)};W.toNumber=function(){return+this};W.toPower=W.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(qe);if(u=new c(u),!u.s){if(e.s<1)throw Error(Ue+"Infinity");return u}if(u.eq(qe))return u;if(n=c.precision,e.eq(qe))return ae(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=s0){for(i=new c(qe),t=Math.ceil(n/se+4),le=!1;r%2&&(i=i.times(u),jy(i.d,t)),r=Cr(r/2),r!==0;)u=u.times(u),jy(u.d,t);return le=!0,e.s<0?new c(qe).div(i):ae(i,n)}}else if(a<0)throw Error(Ue+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,le=!1,i=e.times(un(u,n+s)),le=!0,i=f0(i),i.s=a,i};W.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=ve(i),n=Wt(i,r<=a.toExpNeg||r>=a.toExpPos)):(rt(e,1,Mr),t===void 0?t=a.rounding:rt(t,0,8),i=ae(new a(i),e,t),r=ve(i),n=Wt(i,e<=r||r<=a.toExpNeg,e)),n};W.toSignificantDigits=W.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(rt(e,1,Mr),t===void 0?t=n.rounding:rt(t,0,8)),ae(new n(r),e,t)};W.toString=W.valueOf=W.val=W.toJSON=W[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=ve(e),r=e.constructor;return Wt(e,t<=r.toExpNeg||t>=r.toExpPos)};function l0(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),le?ae(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/se),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/we|0,c[a]%=we;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,le?ae(t,l):t}function rt(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Lt+e)}function Qe(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=se-n.length,r&&(a+=mt(r)),a+=n;o=e[t],n=o+"",r=se-n.length,r&&(a+=mt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var lt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%we|0,o=a/we|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*we+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,h,d,y,v,p,b,w,x,O,g,m,_,S,P=n.constructor,M=n.s==i.s?1:-1,A=n.d,E=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(Ue+"Division by zero");for(c=n.e-i.e,_=E.length,g=A.length,d=new P(M),y=d.d=[],s=0;E[s]==(A[s]||0);)++s;if(E[s]>(A[s]||0)&&--c,a==null?w=a=P.precision:o?w=a+(ve(n)-ve(i))+1:w=a,w<0)return new P(0);if(w=w/se+2|0,s=0,_==1)for(f=0,E=E[0],w++;(s<g||f)&&w--;s++)x=f*we+(A[s]||0),y[s]=x/E|0,f=x%E|0;else{for(f=we/(E[0]+1)|0,f>1&&(E=e(E,f),A=e(A,f),_=E.length,g=A.length),O=_,v=A.slice(0,_),p=v.length;p<_;)v[p++]=0;S=E.slice(),S.unshift(0),m=E[0],E[1]>=we/2&&++m;do f=0,u=t(E,v,_,p),u<0?(b=v[0],_!=p&&(b=b*we+(v[1]||0)),f=b/m|0,f>1?(f>=we&&(f=we-1),l=e(E,f),h=l.length,p=v.length,u=t(l,v,h,p),u==1&&(f--,r(l,_<h?S:E,h))):(f==0&&(u=f=1),l=E.slice()),h=l.length,h<p&&l.unshift(0),r(v,l,p),u==-1&&(p=v.length,u=t(E,v,_,p),u<1&&(f++,r(v,_<p?S:E,p))),p=v.length):u===0&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[p++]=A[O]||0:(v=[A[O]],p=1);while((O++<g||v[0]!==void 0)&&w--)}return y[0]||y.shift(),d.e=c,ae(d,o?a+ve(d)+1:a)}}();function f0(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(ve(e)>16)throw Error(Rf+ve(e));if(!e.s)return new f(qe);for(le=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(It(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(qe),f.precision=u;;){if(i=ae(i.times(e),u),r=r.times(++c),o=a.plus(lt(i,r,u)),Qe(o.d).slice(0,u)===Qe(a.d).slice(0,u)){for(;s--;)a=ae(a.times(a),u);return f.precision=l,t==null?(le=!0,ae(a,l)):a}a=o}}function ve(e){for(var t=e.e*se,r=e.d[0];r>=10;r/=10)t++;return t}function Qc(e,t,r){if(t>e.LN10.sd())throw le=!0,r&&(e.precision=r),Error(Ue+"LN10 precision limit exceeded");return ae(new e(e.LN10),t)}function mt(e){for(var t="";e--;)t+="0";return t}function un(e,t){var r,n,i,a,o,u,c,s,f,l=1,h=10,d=e,y=d.d,v=d.constructor,p=v.precision;if(d.s<1)throw Error(Ue+(d.s?"NaN":"-Infinity"));if(d.eq(qe))return new v(0);if(t==null?(le=!1,s=p):s=t,d.eq(10))return t==null&&(le=!0),Qc(v,s);if(s+=h,v.precision=s,r=Qe(y),n=r.charAt(0),a=ve(d),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)d=d.times(e),r=Qe(d.d),n=r.charAt(0),l++;a=ve(d),n>1?(d=new v("0."+r),a++):d=new v(n+"."+r.slice(1))}else return c=Qc(v,s+2,p).times(a+""),d=un(new v(n+"."+r.slice(1)),s-h).plus(c),v.precision=p,t==null?(le=!0,ae(d,p)):d;for(u=o=d=lt(d.minus(qe),d.plus(qe),s),f=ae(d.times(d),s),i=3;;){if(o=ae(o.times(f),s),c=u.plus(lt(o,new v(i),s)),Qe(c.d).slice(0,s)===Qe(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(Qc(v,s+2,p).times(a+""))),u=lt(u,new v(l),s),v.precision=p,t==null?(le=!0,ae(u,p)):u;u=c,i+=2}}function Ey(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=Cr(r/se),e.d=[],n=(r+1)%se,r<0&&(n+=se),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=se;n<i;)e.d.push(+t.slice(n,n+=se));t=t.slice(n),n=se-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),le&&(e.e>Ai||e.e<-Ai))throw Error(Rf+r)}else e.s=0,e.e=0,e.d=[0];return e}function ae(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=se,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/se),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=se,i=n-se+o}if(r!==void 0&&(a=It(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/It(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=ve(e),l.length=1,t=t-a-1,l[0]=It(10,(se-t%se)%se),e.e=Cr(-t/se)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=It(10,se-n),l[f]=i>0?(s/It(10,o-i)%It(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==we&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=we)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(le&&(e.e>Ai||e.e<-Ai))throw Error(Rf+ve(e));return e}function h0(e,t){var r,n,i,a,o,u,c,s,f,l,h=e.constructor,d=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),le?ae(t,d):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(d/se),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=we-1;--c[a],c[i]+=we}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,le?ae(t,d):t):new h(0)}function Wt(e,t,r){var n,i=ve(e),a=Qe(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+mt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+mt(-i-1)+a,r&&(n=r-o)>0&&(a+=mt(n))):i>=o?(a+=mt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+mt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=mt(n))),e.s<0?"-"+a:a}function jy(e,t){if(e.length>t)return e.length=t,!0}function p0(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Lt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return Ey(o,a.toString())}else if(typeof a!="string")throw Error(Lt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,CT.test(a))Ey(o,a);else throw Error(Lt+a)}if(i.prototype=W,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=p0,i.config=i.set=$T,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function $T(e){if(!e||typeof e!="object")throw Error(Ue+"Object expected");var t,r,n,i=["precision",1,Mr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(Cr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Lt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Lt+r+": "+n);return this}var Df=p0(MT);qe=new Df(1);const ie=Df;function IT(e){return qT(e)||kT(e)||DT(e)||RT()}function RT(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function DT(e,t){if(e){if(typeof e=="string")return el(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return el(e,t)}}function kT(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function qT(e){if(Array.isArray(e))return el(e)}function el(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var NT=function(t){return t},d0={},v0=function(t){return t===d0},My=function(t){return function r(){return arguments.length===0||arguments.length===1&&v0(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},BT=function e(t,r){return t===1?r:My(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==d0}).length;return o>=t?r.apply(void 0,i):e(t-o,My(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return v0(l)?c.shift():l});return r.apply(void 0,IT(f).concat(c))}))})},ga=function(t){return BT(t.length,t)},tl=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},LT=ga(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),FT=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return NT;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},rl=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},y0=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function UT(e){var t;return e===0?t=1:t=Math.floor(new ie(e).abs().log(10).toNumber())+1,t}function WT(e,t,r){for(var n=new ie(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var zT=ga(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),HT=ga(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),GT=ga(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const ma={rangeStep:WT,getDigitCount:UT,interpolateNumber:zT,uninterpolateNumber:HT,uninterpolateTruncation:GT};function nl(e){return VT(e)||XT(e)||g0(e)||KT()}function KT(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function XT(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function VT(e){if(Array.isArray(e))return il(e)}function cn(e,t){return JT(e)||ZT(e,t)||g0(e,t)||YT()}function YT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function g0(e,t){if(e){if(typeof e=="string")return il(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return il(e,t)}}function il(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ZT(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function JT(e){if(Array.isArray(e))return e}function m0(e){var t=cn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function b0(e,t,r){if(e.lte(0))return new ie(0);var n=ma.getDigitCount(e.toNumber()),i=new ie(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ie(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ie(Math.ceil(c))}function QT(e,t,r){var n=1,i=new ie(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ie(10).pow(ma.getDigitCount(e)-1),i=new ie(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ie(Math.floor(e)))}else e===0?i=new ie(Math.floor((t-1)/2)):r||(i=new ie(Math.floor(e)));var o=Math.floor((t-1)/2),u=FT(LT(function(c){return i.add(new ie(c-o).mul(n)).toNumber()}),tl);return u(0,t)}function x0(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ie(0),tickMin:new ie(0),tickMax:new ie(0)};var a=b0(new ie(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ie(0):(o=new ie(e).add(t).div(2),o=o.sub(new ie(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ie(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?x0(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ie(u).mul(a)),tickMax:o.add(new ie(c).mul(a))})}function eE(e){var t=cn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=m0([r,n]),c=cn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(nl(tl(0,i-1).map(function(){return 1/0}))):[].concat(nl(tl(0,i-1).map(function(){return-1/0})),[f]);return r>n?rl(l):l}if(s===f)return QT(s,i,a);var h=x0(s,f,o,a),d=h.step,y=h.tickMin,v=h.tickMax,p=ma.rangeStep(y,v.add(new ie(.1).mul(d)),d);return r>n?rl(p):p}function tE(e,t){var r=cn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=m0([n,i]),u=cn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=b0(new ie(s).sub(c).div(f-1),a,0),h=[].concat(nl(ma.rangeStep(new ie(c),new ie(s).sub(new ie(.99).mul(l)),l)),[s]);return n>i?rl(h):h}var rE=y0(eE),nE=y0(tE),iE=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function pr(e){"@babel/helpers - typeof";return pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pr(e)}function Pi(){return Pi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pi.apply(this,arguments)}function aE(e,t){return sE(e)||cE(e,t)||uE(e,t)||oE()}function oE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uE(e,t){if(e){if(typeof e=="string")return Cy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Cy(e,t)}}function Cy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function cE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function sE(e){if(Array.isArray(e))return e}function lE(e,t){if(e==null)return{};var r=fE(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function fE(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function hE(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function pE(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_0(n.key),n)}}function dE(e,t,r){return t&&pE(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function vE(e,t,r){return t=Ti(t),yE(e,w0()?Reflect.construct(t,r||[],Ti(e).constructor):t.apply(e,r))}function yE(e,t){if(t&&(pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return gE(e)}function gE(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function w0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(w0=function(){return!!e})()}function Ti(e){return Ti=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ti(e)}function mE(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&al(e,t)}function al(e,t){return al=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},al(e,t)}function O0(e,t,r){return t=_0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _0(e){var t=bE(e,"string");return pr(t)=="symbol"?t:t+""}function bE(e,t){if(pr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ba=function(e){function t(){return hE(this,t),vE(this,t,arguments)}return mE(t,e),dE(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,h=lE(n,iE),d=re(h,!1);this.props.direction==="x"&&f.type!=="number"&&Ut(!1);var y=c.map(function(v){var p=s(v,u),b=p.x,w=p.y,x=p.value,O=p.errorVal;if(!O)return null;var g=[],m,_;if(Array.isArray(O)){var S=aE(O,2);m=S[0],_=S[1]}else m=_=O;if(a==="vertical"){var P=f.scale,M=w+i,A=M+o,E=M-o,j=P(x-m),I=P(x+_);g.push({x1:I,y1:A,x2:I,y2:E}),g.push({x1:j,y1:M,x2:I,y2:M}),g.push({x1:j,y1:A,x2:j,y2:E})}else if(a==="horizontal"){var $=l.scale,R=b+i,k=R-o,N=R+o,B=$(x-m),z=$(x+_);g.push({x1:k,y1:z,x2:N,y2:z}),g.push({x1:R,y1:B,x2:R,y2:z}),g.push({x1:k,y1:B,x2:N,y2:B})}return T.createElement(Oe,Pi({className:"recharts-errorBar",key:"bar-".concat(g.map(function(G){return"".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))},d),g.map(function(G){return T.createElement("line",Pi({},G,{key:"line-".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))}))});return T.createElement(Oe,{className:"recharts-errorBars"},y)}}])}(T.Component);O0(ba,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});O0(ba,"displayName","ErrorBar");function sn(e){"@babel/helpers - typeof";return sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},sn(e)}function $y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ct(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$y(Object(r),!0).forEach(function(n){xE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$y(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xE(e,t,r){return t=wE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wE(e){var t=OE(e,"string");return sn(t)=="symbol"?t:t+""}function OE(e,t){if(sn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(sn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var S0=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=ke(r,rr);if(!o)return null;var u=rr.defaultProps,c=u!==void 0?Ct(Ct({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var h=l.item,d=l.props,y=d.sectors||d.data||[];return f.concat(y.map(function(v){return{type:o.props.iconType||h.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):s=(n||[]).map(function(f){var l=f.item,h=l.type.defaultProps,d=h!==void 0?Ct(Ct({},h),l.props):{},y=d.dataKey,v=d.name,p=d.legendType,b=d.hide;return{inactive:b,dataKey:y,type:c.iconType||p||"square",color:kf(l),value:v||y,payload:d}}),Ct(Ct(Ct({},c),rr.getWithHeight(o,i)),{},{payload:s,item:o})};function ln(e){"@babel/helpers - typeof";return ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ln(e)}function Iy(e){return PE(e)||AE(e)||SE(e)||_E()}function _E(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function SE(e,t){if(e){if(typeof e=="string")return ol(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ol(e,t)}}function AE(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function PE(e){if(Array.isArray(e))return ol(e)}function ol(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ry(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function he(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ry(Object(r),!0).forEach(function(n){ir(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ry(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ir(e,t,r){return t=TE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function TE(e){var t=EE(e,"string");return ln(t)=="symbol"?t:t+""}function EE(e,t){if(ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ze(e,t,r){return Z(e)||Z(t)?r:be(t)?Fe(e,t,r):Y(t)?t(e):r}function Gr(e,t,r,n){var i=TT(e,function(u){return Ze(u,t)});if(r==="number"){var a=i.filter(function(u){return F(u)||parseFloat(u)});return a.length?[ya(a),va(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!Z(u)}):i;return o.map(function(u){return be(u)||u instanceof Date?u:""})}var jE=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,h=s>=u-1?i[0].coordinate:i[s+1].coordinate,d=void 0;if(Xe(l-f)!==Xe(h-l)){var y=[];if(Xe(h-l)===Xe(c[1]-c[0])){d=h;var v=l+c[1]-c[0];y[0]=Math.min(v,(v+f)/2),y[1]=Math.max(v,(v+f)/2)}else{d=f;var p=h+c[1]-c[0];y[0]=Math.min(l,(p+l)/2),y[1]=Math.max(l,(p+l)/2)}var b=[Math.min(l,(d+l)/2),Math.max(l,(d+l)/2)];if(t>b[0]&&t<=b[1]||t>=y[0]&&t<=y[1]){o=i[s].index;break}}else{var w=Math.min(f,h),x=Math.max(f,h);if(t>(w+l)/2&&t<=(x+l)/2){o=i[s].index;break}}}else for(var O=0;O<u;O++)if(O===0&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O>0&&O<u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O===u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2){o=n[O].index;break}return o},kf=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?he(he({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},ME=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),h=0,d=l.length;h<d;h++){var y=f[l[h]],v=y.items,p=y.cateAxisId,b=v.filter(function(_){return st(_.type).indexOf("Bar")>=0});if(b&&b.length){var w=b[0].type.defaultProps,x=w!==void 0?he(he({},w),b[0].props):b[0].props,O=x.barSize,g=x[p];o[g]||(o[g]=[]);var m=Z(O)?r:O;o[g].push({item:b[0],stackList:b.slice(1),barSize:Z(m)?void 0:Ve(m,n,0)})}}return o},CE=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Ve(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var h=!1,d=i/c,y=o.reduce(function(O,g){return O+g.barSize||0},0);y+=(c-1)*s,y>=i&&(y-=(c-1)*s,s=0),y>=i&&d>0&&(h=!0,d*=.9,y=c*d);var v=(i-y)/2>>0,p={offset:v-s,size:0};f=o.reduce(function(O,g){var m={item:g.item,position:{offset:p.offset+p.size+s,size:h?d:g.barSize}},_=[].concat(Iy(O),[m]);return p=_[_.length-1].position,g.stackList&&g.stackList.length&&g.stackList.forEach(function(S){_.push({item:S,position:p})}),_},l)}else{var b=Ve(n,i,0,!0);i-2*b-(c-1)*s<=0&&(s=0);var w=(i-2*b-(c-1)*s)/c;w>1&&(w>>=0);var x=u===+u?Math.min(w,u):w;f=o.reduce(function(O,g,m){var _=[].concat(Iy(O),[{item:g.item,position:{offset:b+(w+s)*m+(w-x)/2,size:x}}]);return g.stackList&&g.stackList.length&&g.stackList.forEach(function(S){_.push({item:S,position:_[_.length-1].position})}),_},l)}return f},$E=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=S0({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,h=f.height,d=s.align,y=s.verticalAlign,v=s.layout;if((v==="vertical"||v==="horizontal"&&y==="middle")&&d!=="center"&&F(t[d]))return he(he({},t),{},ir({},d,t[d]+(l||0)));if((v==="horizontal"||v==="vertical"&&d==="center")&&y!=="middle"&&F(t[y]))return he(he({},t),{},ir({},y,t[y]+(h||0)))}return t},IE=function(t,r,n){return Z(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},A0=function(t,r,n,i,a){var o=r.props.children,u=Ye(o,ba).filter(function(s){return IE(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=Ze(f,n);if(Z(l))return s;var h=Array.isArray(l)?[ya(l),va(l)]:[l,l],d=c.reduce(function(y,v){var p=Ze(f,v,0),b=h[0]-Math.abs(Array.isArray(p)?p[0]:p),w=h[1]+Math.abs(Array.isArray(p)?p[1]:p);return[Math.min(b,y[0]),Math.max(w,y[1])]},[1/0,-1/0]);return[Math.min(d[0],s[0]),Math.max(d[1],s[1])]},[1/0,-1/0])}return null},RE=function(t,r,n,i,a){var o=r.map(function(u){return A0(t,u,n,a,i)}).filter(function(u){return!Z(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},P0=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&A0(t,c,s,i)||Gr(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},T0=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},JD=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},qt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Xe(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var h=a?a.indexOf(l):l;return{coordinate:i(h)+s,value:l,offset:s}});return f.filter(function(l){return!$n(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,h){return{coordinate:i(l)+s,value:l,index:h,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,h){return{coordinate:i(l)+s,value:a?a[l]:l,index:h,offset:s}})},es=new WeakMap,Vn=function(t,r){if(typeof r!="function")return t;es.has(t)||es.set(t,new WeakMap);var n=es.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},E0=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:tn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:wi(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:Hr(),realScaleType:"point"}:a==="category"?{scale:tn(),realScaleType:"band"}:{scale:wi(),realScaleType:"linear"};if(Cn(i)){var c="scale".concat(ra(i));return{scale:(by[c]||Hr)(),realScaleType:by[c]?c:"point"}}return Y(i)?{scale:i}:{scale:Hr(),realScaleType:"point"}},Dy=1e-4,j0=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-Dy,o=Math.max(i[0],i[1])+Dy,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},DE=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},kE=function(t,r){if(!r||r.length!==2||!F(r[0])||!F(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!F(t[0])||t[0]<n)&&(a[0]=n),(!F(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},qE=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=$n(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},NE=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=$n(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},BE={sign:qE,expand:A1,none:ar,silhouette:P1,wiggle:T1,positive:NE},LE=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=BE[n],o=S1().keys(i).value(function(u,c){return+Ze(u,c,0)}).order(ks).offset(a);return o(t)},FE=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,h){var d,y=(d=h.type)!==null&&d!==void 0&&d.defaultProps?he(he({},h.type.defaultProps),h.props):h.props,v=y.stackId,p=y.hide;if(p)return l;var b=y[n],w=l[b]||{hasStack:!1,stackGroups:{}};if(be(v)){var x=w.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};x.items.push(h),w.hasStack=!0,w.stackGroups[v]=x}else w.stackGroups[ta("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[h]};return he(he({},l),{},ir({},b,w))},c),f={};return Object.keys(s).reduce(function(l,h){var d=s[h];if(d.hasStack){var y={};d.stackGroups=Object.keys(d.stackGroups).reduce(function(v,p){var b=d.stackGroups[p];return he(he({},v),{},ir({},p,{numericAxisId:n,cateAxisId:i,items:b.items,stackedData:LE(t,b.items,a)}))},y)}return he(he({},l),{},ir({},h,d))},f)},M0=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=rE(s,a,u);return t.domain([ya(f),va(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),h=nE(l,a,u);return{niceTicks:h}}return null};function QD(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!Z(i[t.dataKey])){var u=ti(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=Ze(i,Z(o)?t.dataKey:o);return Z(c)?null:t.scale(c)}var ky=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=Ze(o,r.dataKey,r.domain[u]);return Z(c)?null:r.scale(c)-a/2+i},UE=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},WE=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?he(he({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(be(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},zE=function(t){return t.reduce(function(r,n){return[ya(n.concat([r[0]]).filter(F)),va(n.concat([r[1]]).filter(F))]},[1/0,-1/0])},C0=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=zE(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},qy=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Ny=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ul=function(t,r,n){if(Y(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(F(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(qy.test(t[0])){var a=+qy.exec(t[0])[1];i[0]=r[0]-a}else Y(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(F(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(Ny.test(t[1])){var o=+Ny.exec(t[1])[1];i[1]=r[1]+o}else Y(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},Ei=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=sf(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},By=function(t,r,n){return!t||!t.length||If(t,Fe(n,"type.defaultProps.domain"))?r:t},$0=function(t,r){var n=t.type.defaultProps?he(he({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return he(he({},re(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:kf(t),value:Ze(r,i),type:c,payload:r,chartType:s,hide:f})};function fn(e){"@babel/helpers - typeof";return fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fn(e)}function Ly(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ot(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ly(Object(r),!0).forEach(function(n){I0(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ly(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function I0(e,t,r){return t=HE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function HE(e){var t=GE(e,"string");return fn(t)=="symbol"?t:t+""}function GE(e,t){if(fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function KE(e,t){return ZE(e)||YE(e,t)||VE(e,t)||XE()}function XE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function VE(e,t){if(e){if(typeof e=="string")return Fy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fy(e,t)}}function Fy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function YE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function ZE(e){if(Array.isArray(e))return e}var ji=Math.PI/180,JE=function(t){return t*180/Math.PI},Ae=function(t,r,n,i){return{x:t+Math.cos(-ji*i)*n,y:r+Math.sin(-ji*i)*n}},QE=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},ek=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.startAngle,s=t.endAngle,f=Ve(t.cx,o,o/2),l=Ve(t.cy,u,u/2),h=QE(o,u,n),d=Ve(t.innerRadius,h,0),y=Ve(t.outerRadius,h,h*.8),v=Object.keys(r);return v.reduce(function(p,b){var w=r[b],x=w.domain,O=w.reversed,g;if(Z(w.range))i==="angleAxis"?g=[c,s]:i==="radiusAxis"&&(g=[d,y]),O&&(g=[g[1],g[0]]);else{g=w.range;var m=g,_=KE(m,2);c=_[0],s=_[1]}var S=E0(w,a),P=S.realScaleType,M=S.scale;M.domain(x).range(g),j0(M);var A=M0(M,ot(ot({},w),{},{realScaleType:P})),E=ot(ot(ot({},w),A),{},{range:g,radius:y,realScaleType:P,scale:M,cx:f,cy:l,innerRadius:d,outerRadius:y,startAngle:c,endAngle:s});return ot(ot({},p),{},I0({},b,E))},{})},ej=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},tj=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=ej({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:JE(s),angleInRadian:s}},rj=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},nj=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},Uy=function(t,r){var n=t.x,i=t.y,a=tj({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=rj(r),l=f.startAngle,h=f.endAngle,d=u,y;if(l<=h){for(;d>h;)d-=360;for(;d<l;)d+=360;y=d>=l&&d<=h}else{for(;d>l;)d-=360;for(;d<h;)d+=360;y=d>=h&&d<=l}return y?ot(ot({},r),{},{radius:o,angle:nj(d,r)}):null},tk=function(t){return!q.isValidElement(t)&&!Y(t)&&typeof t!="boolean"?t.className:""};function hn(e){"@babel/helpers - typeof";return hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hn(e)}var ij=["offset"];function aj(e){return sj(e)||cj(e)||uj(e)||oj()}function oj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uj(e,t){if(e){if(typeof e=="string")return cl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cl(e,t)}}function cj(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function sj(e){if(Array.isArray(e))return cl(e)}function cl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function lj(e,t){if(e==null)return{};var r=fj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function fj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Wy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wy(Object(r),!0).forEach(function(n){hj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hj(e,t,r){return t=pj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pj(e){var t=dj(e,"string");return hn(t)=="symbol"?t:t+""}function dj(e,t){if(hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function pn(){return pn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pn.apply(this,arguments)}var vj=function(t){var r=t.value,n=t.formatter,i=Z(t.children)?r:t.children;return Y(n)?n(i):i},yj=function(t,r){var n=Xe(r-t),i=Math.min(Math.abs(r-t),360);return n*i},gj=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,h=c.outerRadius,d=c.startAngle,y=c.endAngle,v=c.clockWise,p=(l+h)/2,b=yj(d,y),w=b>=0?1:-1,x,O;i==="insideStart"?(x=d+w*o,O=v):i==="insideEnd"?(x=y-w*o,O=!v):i==="end"&&(x=y+w*o,O=v),O=b<=0?O:!O;var g=Ae(s,f,p,x),m=Ae(s,f,p,x+(O?1:-1)*359),_="M".concat(g.x,",").concat(g.y,`
    A`).concat(p,",").concat(p,",0,1,").concat(O?0:1,`,
    `).concat(m.x,",").concat(m.y),S=Z(t.id)?ta("recharts-radial-line-"):t.id;return T.createElement("text",pn({},n,{dominantBaseline:"central",className:ne("recharts-radial-bar-label",u)}),T.createElement("defs",null,T.createElement("path",{id:S,d:_})),T.createElement("textPath",{xlinkHref:"#".concat(S)},r))},mj=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,h=(f+l)/2;if(i==="outside"){var d=Ae(o,u,s+n,h),y=d.x,v=d.y;return{x:y,y:v,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var p=(c+s)/2,b=Ae(o,u,p,h),w=b.x,x=b.y;return{x:w,y:x,textAnchor:"middle",verticalAnchor:"middle"}},bj=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,h=l*i,d=l>0?"end":"start",y=l>0?"start":"end",v=s>=0?1:-1,p=v*i,b=v>0?"end":"start",w=v>0?"start":"end";if(a==="top"){var x={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:d};return me(me({},x),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var O={x:u+s/2,y:c+f+h,textAnchor:"middle",verticalAnchor:y};return me(me({},O),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var g={x:u-p,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return me(me({},g),n?{width:Math.max(g.x-n.x,0),height:f}:{})}if(a==="right"){var m={x:u+s+p,y:c+f/2,textAnchor:w,verticalAnchor:"middle"};return me(me({},m),n?{width:Math.max(n.x+n.width-m.x,0),height:f}:{})}var _=n?{width:s,height:f}:{};return a==="insideLeft"?me({x:u+p,y:c+f/2,textAnchor:w,verticalAnchor:"middle"},_):a==="insideRight"?me({x:u+s-p,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},_):a==="insideTop"?me({x:u+s/2,y:c+h,textAnchor:"middle",verticalAnchor:y},_):a==="insideBottom"?me({x:u+s/2,y:c+f-h,textAnchor:"middle",verticalAnchor:d},_):a==="insideTopLeft"?me({x:u+p,y:c+h,textAnchor:w,verticalAnchor:y},_):a==="insideTopRight"?me({x:u+s-p,y:c+h,textAnchor:b,verticalAnchor:y},_):a==="insideBottomLeft"?me({x:u+p,y:c+f-h,textAnchor:w,verticalAnchor:d},_):a==="insideBottomRight"?me({x:u+s-p,y:c+f-h,textAnchor:b,verticalAnchor:d},_):Tr(a)&&(F(a.x)||Rt(a.x))&&(F(a.y)||Rt(a.y))?me({x:u+Ve(a.x,s),y:c+Ve(a.y,f),textAnchor:"end",verticalAnchor:"end"},_):me({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},_)},xj=function(t){return"cx"in t&&F(t.cx)};function Te(e){var t=e.offset,r=t===void 0?5:t,n=lj(e,ij),i=me({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,h=i.textBreakAll;if(!a||Z(u)&&Z(c)&&!q.isValidElement(s)&&!Y(s))return null;if(q.isValidElement(s))return q.cloneElement(s,i);var d;if(Y(s)){if(d=q.createElement(s,i),q.isValidElement(d))return d}else d=vj(i);var y=xj(a),v=re(i,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return gj(i,d,v);var p=y?mj(i):bj(i);return T.createElement(di,pn({className:ne("recharts-label",l)},v,p,{breakAll:h}),d)}Te.displayName="Label";var R0=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,h=t.y,d=t.top,y=t.left,v=t.width,p=t.height,b=t.clockWise,w=t.labelViewBox;if(w)return w;if(F(v)&&F(p)){if(F(l)&&F(h))return{x:l,y:h,width:v,height:p};if(F(d)&&F(y))return{x:d,y,width:v,height:p}}return F(l)&&F(h)?{x:l,y:h,width:0,height:0}:F(r)&&F(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:b}:t.viewBox?t.viewBox:{}},wj=function(t,r){return t?t===!0?T.createElement(Te,{key:"label-implicit",viewBox:r}):be(t)?T.createElement(Te,{key:"label-implicit",viewBox:r,value:t}):q.isValidElement(t)?t.type===Te?q.cloneElement(t,{key:"label-implicit",viewBox:r}):T.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):Y(t)?T.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):Tr(t)?T.createElement(Te,pn({viewBox:r},t,{key:"label-implicit"})):null:null},Oj=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=R0(t),o=Ye(i,Te).map(function(c,s){return q.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=wj(t.label,r||a);return[u].concat(aj(o))};Te.parseViewBox=R0;Te.renderCallByParent=Oj;var ts,zy;function _j(){if(zy)return ts;zy=1;function e(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}return ts=e,ts}var Sj=_j();const Aj=ue(Sj);function dn(e){"@babel/helpers - typeof";return dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dn(e)}var Pj=["valueAccessor"],Tj=["data","dataKey","clockWise","id","textBreakAll"];function Ej(e){return $j(e)||Cj(e)||Mj(e)||jj()}function jj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Mj(e,t){if(e){if(typeof e=="string")return sl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sl(e,t)}}function Cj(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function $j(e){if(Array.isArray(e))return sl(e)}function sl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Mi(){return Mi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Mi.apply(this,arguments)}function Hy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gy(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hy(Object(r),!0).forEach(function(n){Ij(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ij(e,t,r){return t=Rj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rj(e){var t=Dj(e,"string");return dn(t)=="symbol"?t:t+""}function Dj(e,t){if(dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ky(e,t){if(e==null)return{};var r=kj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function kj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var qj=function(t){return Array.isArray(t.value)?Aj(t.value):t.value};function Ft(e){var t=e.valueAccessor,r=t===void 0?qj:t,n=Ky(e,Pj),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=Ky(n,Tj);return!i||!i.length?null:T.createElement(Oe,{className:"recharts-label-list"},i.map(function(f,l){var h=Z(a)?r(f,l):Ze(f&&f.payload,a),d=Z(u)?{}:{id:"".concat(u,"-").concat(l)};return T.createElement(Te,Mi({},re(f,!0),s,d,{parentViewBox:f.parentViewBox,value:h,textBreakAll:c,viewBox:Te.parseViewBox(Z(o)?f:Gy(Gy({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}Ft.displayName="LabelList";function Nj(e,t){return e?e===!0?T.createElement(Ft,{key:"labelList-implicit",data:t}):T.isValidElement(e)||Y(e)?T.createElement(Ft,{key:"labelList-implicit",data:t,content:e}):Tr(e)?T.createElement(Ft,Mi({data:t},e,{key:"labelList-implicit"})):null:null}function Bj(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Ye(n,Ft).map(function(o,u){return q.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=Nj(e.label,t);return[a].concat(Ej(i))}Ft.renderCallByParent=Bj;function vn(e){"@babel/helpers - typeof";return vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vn(e)}function ll(){return ll=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ll.apply(this,arguments)}function Xy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vy(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xy(Object(r),!0).forEach(function(n){Lj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Lj(e,t,r){return t=Fj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fj(e){var t=Uj(e,"string");return vn(t)=="symbol"?t:t+""}function Uj(e,t){if(vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Wj=function(t,r){var n=Xe(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},Yn=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/ji,h=s?a:a+o*l,d=Ae(r,n,f,h),y=Ae(r,n,i,h),v=s?a-o*l:a,p=Ae(r,n,f*Math.cos(l*ji),v);return{center:d,circleTangency:y,lineTangency:p,theta:l}},D0=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=Wj(o,u),s=o+c,f=Ae(r,n,a,o),l=Ae(r,n,a,s),h="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var d=Ae(r,n,i,o),y=Ae(r,n,i,s);h+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},zj=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=Xe(f-s),h=Yn({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),d=h.circleTangency,y=h.lineTangency,v=h.theta,p=Yn({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),b=p.circleTangency,w=p.lineTangency,x=p.theta,O=c?Math.abs(s-f):Math.abs(s-f)-v-x;if(O<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):D0({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var g="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(d.x,",").concat(d.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(O>180),",").concat(+(l<0),",").concat(b.x,",").concat(b.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(w.x,",").concat(w.y,`
  `);if(i>0){var m=Yn({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),_=m.circleTangency,S=m.lineTangency,P=m.theta,M=Yn({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),A=M.circleTangency,E=M.lineTangency,j=M.theta,I=c?Math.abs(s-f):Math.abs(s-f)-P-j;if(I<0&&o===0)return"".concat(g,"L").concat(r,",").concat(n,"Z");g+="L".concat(E.x,",").concat(E.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(I>180),",").concat(+(l>0),",").concat(_.x,",").concat(_.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(S.x,",").concat(S.y,"Z")}else g+="L".concat(r,",").concat(n,"Z");return g},Hj={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},k0=function(t){var r=Vy(Vy({},Hj),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,h=r.className;if(o<a||f===l)return null;var d=ne("recharts-sector",h),y=o-a,v=Ve(u,y,0,!0),p;return v>0&&Math.abs(f-l)<360?p=zj({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):p=D0({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),T.createElement("path",ll({},re(r,!0),{className:d,d:p,role:"img"}))};function yn(e){"@babel/helpers - typeof";return yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yn(e)}function fl(){return fl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fl.apply(this,arguments)}function Yy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Zy(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yy(Object(r),!0).forEach(function(n){Gj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Gj(e,t,r){return t=Kj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kj(e){var t=Xj(e,"string");return yn(t)=="symbol"?t:t+""}function Xj(e,t){if(yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Jy={curveBasisClosed:p1,curveBasisOpen:d1,curveBasis:h1,curveBumpX:Jw,curveBumpY:Qw,curveLinearClosed:v1,curveLinear:ia,curveMonotoneX:y1,curveMonotoneY:g1,curveNatural:m1,curveStep:b1,curveStepAfter:w1,curveStepBefore:x1},Zn=function(t){return t.x===+t.x&&t.y===+t.y},qr=function(t){return t.x},Nr=function(t){return t.y},Vj=function(t,r){if(Y(t))return t;var n="curve".concat(ra(t));return(n==="curveMonotone"||n==="curveBump")&&r?Jy["".concat(n).concat(r==="vertical"?"Y":"X")]:Jy[n]||ia},Yj=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=Vj(n,u),l=s?a.filter(function(v){return Zn(v)}):a,h;if(Array.isArray(o)){var d=s?o.filter(function(v){return Zn(v)}):o,y=l.map(function(v,p){return Zy(Zy({},v),{},{base:d[p]})});return u==="vertical"?h=Un().y(Nr).x1(qr).x0(function(v){return v.base.x}):h=Un().x(qr).y1(Nr).y0(function(v){return v.base.y}),h.defined(Zn).curve(f),h(y)}return u==="vertical"&&F(o)?h=Un().y(Nr).x1(qr).x0(o):F(o)?h=Un().x(qr).y1(Nr).y0(o):h=qm().x(qr).y(Nr),h.defined(Zn).curve(f),h(l)},Qy=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?Yj(t):i;return T.createElement("path",fl({},re(t,!1),ri(t),{className:ne("recharts-curve",r),d:o,ref:a}))},Zj=Dx();const te=ue(Zj);var Jj=Object.getOwnPropertyNames,Qj=Object.getOwnPropertySymbols,eM=Object.prototype.hasOwnProperty;function eg(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function Jn(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function tg(e){return Jj(e).concat(Qj(e))}var tM=Object.hasOwn||function(e,t){return eM.call(e,t)};function Kt(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var rM="__v",nM="__o",iM="_owner",rg=Object.getOwnPropertyDescriptor,ng=Object.keys;function aM(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function oM(e,t){return Kt(e.getTime(),t.getTime())}function uM(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function cM(e,t){return e===t}function ig(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var h=o.value,d=u.value;if(r.equals(h[0],d[0],c,l,e,t,r)&&r.equals(h[1],d[1],h[0],d[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var sM=Kt;function lM(e,t,r){var n=ng(e),i=n.length;if(ng(t).length!==i)return!1;for(;i-- >0;)if(!q0(e,t,r,n[i]))return!1;return!0}function Br(e,t,r){var n=tg(e),i=n.length;if(tg(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!q0(e,t,r,a)||(o=rg(e,a),u=rg(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function fM(e,t){return Kt(e.valueOf(),t.valueOf())}function hM(e,t){return e.source===t.source&&e.flags===t.flags}function ag(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function pM(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function dM(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function q0(e,t,r,n){return(n===iM||n===nM||n===rM)&&(e.$$typeof||t.$$typeof)?!0:tM(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var vM="[object Arguments]",yM="[object Boolean]",gM="[object Date]",mM="[object Error]",bM="[object Map]",xM="[object Number]",wM="[object Object]",OM="[object RegExp]",_M="[object Set]",SM="[object String]",AM="[object URL]",PM=Array.isArray,og=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,ug=Object.assign,TM=Object.prototype.toString.call.bind(Object.prototype.toString);function EM(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,h=e.areUrlsEqual;return function(y,v,p){if(y===v)return!0;if(y==null||v==null)return!1;var b=typeof y;if(b!==typeof v)return!1;if(b!=="object")return b==="number"?o(y,v,p):b==="function"?i(y,v,p):!1;var w=y.constructor;if(w!==v.constructor)return!1;if(w===Object)return u(y,v,p);if(PM(y))return t(y,v,p);if(og!=null&&og(y))return l(y,v,p);if(w===Date)return r(y,v,p);if(w===RegExp)return s(y,v,p);if(w===Map)return a(y,v,p);if(w===Set)return f(y,v,p);var x=TM(y);return x===gM?r(y,v,p):x===OM?s(y,v,p):x===bM?a(y,v,p):x===_M?f(y,v,p):x===wM?typeof y.then!="function"&&typeof v.then!="function"&&u(y,v,p):x===AM?h(y,v,p):x===mM?n(y,v,p):x===vM?u(y,v,p):x===yM||x===xM||x===SM?c(y,v,p):!1}}function jM(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?Br:aM,areDatesEqual:oM,areErrorsEqual:uM,areFunctionsEqual:cM,areMapsEqual:n?eg(ig,Br):ig,areNumbersEqual:sM,areObjectsEqual:n?Br:lM,arePrimitiveWrappersEqual:fM,areRegExpsEqual:hM,areSetsEqual:n?eg(ag,Br):ag,areTypedArraysEqual:n?Br:pM,areUrlsEqual:dM};if(r&&(i=ug({},i,r(i))),t){var a=Jn(i.areArraysEqual),o=Jn(i.areMapsEqual),u=Jn(i.areObjectsEqual),c=Jn(i.areSetsEqual);i=ug({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function MM(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function CM(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,h=l===void 0?t?new WeakMap:void 0:l,d=f.meta;return r(c,s,{cache:h,equals:i,meta:d,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var $M=Tt();Tt({strict:!0});Tt({circular:!0});Tt({circular:!0,strict:!0});Tt({createInternalComparator:function(){return Kt}});Tt({strict:!0,createInternalComparator:function(){return Kt}});Tt({circular:!0,createInternalComparator:function(){return Kt}});Tt({circular:!0,createInternalComparator:function(){return Kt},strict:!0});function Tt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=jM(e),c=EM(u),s=n?n(c):MM(c);return CM({circular:r,comparator:c,createState:i,equals:s,strict:o})}function IM(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function cg(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):IM(i)};requestAnimationFrame(n)}function hl(e){"@babel/helpers - typeof";return hl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hl(e)}function RM(e){return NM(e)||qM(e)||kM(e)||DM()}function DM(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kM(e,t){if(e){if(typeof e=="string")return sg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sg(e,t)}}function sg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function qM(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function NM(e){if(Array.isArray(e))return e}function BM(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=RM(o),c=u[0],s=u.slice(1);if(typeof c=="number"){cg(i.bind(null,s),c);return}i(c),cg(i.bind(null,s));return}hl(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function gn(e){"@babel/helpers - typeof";return gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gn(e)}function lg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?lg(Object(r),!0).forEach(function(n){N0(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function N0(e,t,r){return t=LM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function LM(e){var t=FM(e,"string");return gn(t)==="symbol"?t:String(t)}function FM(e,t){if(gn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var UM=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},WM=function(t){return t},zM=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},Kr=function(t,r){return Object.keys(r).reduce(function(n,i){return fg(fg({},n),{},N0({},i,t(i,r[i])))},{})},hg=function(t,r,n){return t.map(function(i){return"".concat(zM(i)," ").concat(r,"ms ").concat(n)}).join(",")};function HM(e,t){return XM(e)||KM(e,t)||B0(e,t)||GM()}function GM(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function KM(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function XM(e){if(Array.isArray(e))return e}function VM(e){return JM(e)||ZM(e)||B0(e)||YM()}function YM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function B0(e,t){if(e){if(typeof e=="string")return pl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pl(e,t)}}function ZM(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function JM(e){if(Array.isArray(e))return pl(e)}function pl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Ci=1e-4,L0=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},F0=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},pg=function(t,r){return function(n){var i=L0(t,r);return F0(i,n)}},QM=function(t,r){return function(n){var i=L0(t,r),a=[].concat(VM(i.map(function(o,u){return o*u}).slice(1)),[0]);return F0(a,n)}},dg=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(p){return parseFloat(p)}),f=HM(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=pg(i,o),h=pg(a,u),d=QM(i,o),y=function(b){return b>1?1:b<0?0:b},v=function(b){for(var w=b>1?1:b,x=w,O=0;O<8;++O){var g=l(x)-w,m=d(x);if(Math.abs(g-w)<Ci||m<Ci)return h(x);x=y(x-g/m)}return h(x)};return v.isStepper=!1,v},eC=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,h){var d=-(f-l)*n,y=h*a,v=h+(d-y)*u/1e3,p=h*u/1e3+f;return Math.abs(p-l)<Ci&&Math.abs(v)<Ci?[l,0]:[p,v]};return c.isStepper=!0,c.dt=u,c},tC=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return dg(i);case"spring":return eC();default:if(i.split("(")[0]==="cubic-bezier")return dg(i)}return typeof i=="function"?i:null};function mn(e){"@babel/helpers - typeof";return mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mn(e)}function vg(e){return iC(e)||nC(e)||U0(e)||rC()}function rC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function iC(e){if(Array.isArray(e))return vl(e)}function yg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Se(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yg(Object(r),!0).forEach(function(n){dl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function dl(e,t,r){return t=aC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aC(e){var t=oC(e,"string");return mn(t)==="symbol"?t:String(t)}function oC(e,t){if(mn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(mn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function uC(e,t){return lC(e)||sC(e,t)||U0(e,t)||cC()}function cC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function U0(e,t){if(e){if(typeof e=="string")return vl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return vl(e,t)}}function vl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function sC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function lC(e){if(Array.isArray(e))return e}var $i=function(t,r,n){return t+(r-t)*n},yl=function(t){var r=t.from,n=t.to;return r!==n},fC=function e(t,r,n){var i=Kr(function(a,o){if(yl(o)){var u=t(o.from,o.to,o.velocity),c=uC(u,2),s=c[0],f=c[1];return Se(Se({},o),{},{from:s,velocity:f})}return o},r);return n<1?Kr(function(a,o){return yl(o)?Se(Se({},o),{},{velocity:$i(o.velocity,i[a].velocity,n),from:$i(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const hC=function(e,t,r,n,i){var a=UM(e,t),o=a.reduce(function(p,b){return Se(Se({},p),{},dl({},b,[e[b],t[b]]))},{}),u=a.reduce(function(p,b){return Se(Se({},p),{},dl({},b,{from:e[b],velocity:0,to:t[b]}))},{}),c=-1,s,f,l=function(){return null},h=function(){return Kr(function(b,w){return w.from},u)},d=function(){return!Object.values(u).filter(yl).length},y=function(b){s||(s=b);var w=b-s,x=w/r.dt;u=fC(r,u,x),i(Se(Se(Se({},e),t),h())),s=b,d()||(c=requestAnimationFrame(l))},v=function(b){f||(f=b);var w=(b-f)/n,x=Kr(function(g,m){return $i.apply(void 0,vg(m).concat([r(w)]))},o);if(i(Se(Se(Se({},e),t),x)),w<1)c=requestAnimationFrame(l);else{var O=Kr(function(g,m){return $i.apply(void 0,vg(m).concat([r(1)]))},o);i(Se(Se(Se({},e),t),O))}};return l=r.isStepper?y:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function dr(e){"@babel/helpers - typeof";return dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dr(e)}var pC=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function dC(e,t){if(e==null)return{};var r=vC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function vC(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function rs(e){return bC(e)||mC(e)||gC(e)||yC()}function yC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gC(e,t){if(e){if(typeof e=="string")return gl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gl(e,t)}}function mC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function bC(e){if(Array.isArray(e))return gl(e)}function gl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function gg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function He(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gg(Object(r),!0).forEach(function(n){Wr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Wr(e,t,r){return t=W0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function wC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,W0(n.key),n)}}function OC(e,t,r){return t&&wC(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function W0(e){var t=_C(e,"string");return dr(t)==="symbol"?t:String(t)}function _C(e,t){if(dr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(dr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function SC(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ml(e,t)}function ml(e,t){return ml=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ml(e,t)}function AC(e){var t=PC();return function(){var n=Ii(e),i;if(t){var a=Ii(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return bl(this,i)}}function bl(e,t){if(t&&(dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return xl(e)}function xl(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function PC(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Ii(e){return Ii=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ii(e)}var Ot=function(e){SC(r,e);var t=AC(r);function r(n,i){var a;xC(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,h=o.children,d=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(xl(a)),a.changeStyle=a.changeStyle.bind(xl(a)),!u||d<=0)return a.state={style:{}},typeof h=="function"&&(a.state={style:f}),bl(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof h=="function")return a.state={style:s},bl(a);a.state={style:c?Wr({},c,s):s}}else a.state={style:{}};return a}return OC(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,h=this.state.style;if(u){if(!o){var d={style:c?Wr({},c,f):f};this.state&&h&&(c&&h[c]!==f||!c&&h!==f)&&this.setState(d);return}if(!($M(i.to,f)&&i.canBegin&&i.isActive)){var y=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=y||s?l:i.to;if(this.state&&h){var p={style:c?Wr({},c,v):v};(c&&h[c]!==v||!c&&h!==v)&&this.setState(p)}this.runAnimation(He(He({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,h=i.onAnimationStart,d=hC(o,u,tC(s),c,this.changeStyle),y=function(){a.stopJSAnimation=d()};this.manager.start([h,f,y,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,h=l===void 0?0:l,d=function(v,p,b){if(b===0)return v;var w=p.duration,x=p.easing,O=x===void 0?"ease":x,g=p.style,m=p.properties,_=p.onAnimationEnd,S=b>0?o[b-1]:p,P=m||Object.keys(g);if(typeof O=="function"||O==="spring")return[].concat(rs(v),[a.runJSAnimation.bind(a,{from:S.style,to:g,duration:w,easing:O}),w]);var M=hg(P,w,O),A=He(He(He({},S.style),g),{},{transition:M});return[].concat(rs(v),[A,w,_]).filter(WM)};return this.manager.start([c].concat(rs(o.reduce(d,[f,Math.max(h,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=BM());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,h=i.steps,d=i.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof s=="function"||typeof d=="function"||s==="spring"){this.runJSAnimation(i);return}if(h.length>1){this.runStepAnimation(i);return}var v=u?Wr({},u,c):c,p=hg(Object.keys(v),o,s);y.start([f,a,He(He({},v),{},{transition:p}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=dC(i,pC),s=q.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(d){var y=d.props,v=y.style,p=v===void 0?{}:v,b=y.className,w=q.cloneElement(d,He(He({},c),{},{style:He(He({},p),f),className:b}));return w};return s===1?l(q.Children.only(a)):T.createElement("div",null,q.Children.map(a,function(h){return l(h)}))}}]),r}(q.PureComponent);Ot.displayName="Animate";Ot.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};Ot.propTypes={from:te.oneOfType([te.object,te.string]),to:te.oneOfType([te.object,te.string]),attributeName:te.string,duration:te.number,begin:te.number,easing:te.oneOfType([te.string,te.func]),steps:te.arrayOf(te.shape({duration:te.number.isRequired,style:te.object.isRequired,easing:te.oneOfType([te.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),te.func]),properties:te.arrayOf("string"),onAnimationEnd:te.func})),children:te.oneOfType([te.node,te.func]),isActive:te.bool,canBegin:te.bool,onAnimationEnd:te.func,shouldReAnimate:te.bool,onAnimationStart:te.func,onAnimationReStart:te.func};function bn(e){"@babel/helpers - typeof";return bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bn(e)}function Ri(){return Ri=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ri.apply(this,arguments)}function TC(e,t){return CC(e)||MC(e,t)||jC(e,t)||EC()}function EC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jC(e,t){if(e){if(typeof e=="string")return mg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mg(e,t)}}function mg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function MC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function CC(e){if(Array.isArray(e))return e}function bg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bg(Object(r),!0).forEach(function(n){$C(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $C(e,t,r){return t=IC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function IC(e){var t=RC(e,"string");return bn(t)=="symbol"?t:t+""}function RC(e,t){if(bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var wg=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],h=0,d=4;h<d;h++)l[h]=a[h]>o?o:a[h];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var y=Math.min(o,a);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+c*y,",").concat(r,`
            L `).concat(t+n-c*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+i-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n-c*y,",").concat(r+i,`
            L `).concat(t+c*y,",").concat(r+i,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},DC=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),h=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=h}return!1},kC={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},qf=function(t){var r=xg(xg({},kC),t),n=q.useRef(),i=q.useState(-1),a=TC(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var O=n.current.getTotalLength();O&&u(O)}catch{}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,h=r.radius,d=r.className,y=r.animationEasing,v=r.animationDuration,p=r.animationBegin,b=r.isAnimationActive,w=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var x=ne("recharts-rectangle",d);return w?T.createElement(Ot,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:v,animationEasing:y,isActive:w},function(O){var g=O.width,m=O.height,_=O.x,S=O.y;return T.createElement(Ot,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:v,isActive:b,easing:y},T.createElement("path",Ri({},re(r,!0),{className:x,d:wg(_,S,g,m,h),ref:n})))}):T.createElement("path",Ri({},re(r,!0),{className:x,d:wg(c,s,f,l,h)}))};function wl(){return wl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wl.apply(this,arguments)}var z0=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=ne("recharts-dot",a);return r===+r&&n===+n&&i===+i?T.createElement("circle",wl({},re(t,!1),ri(t),{className:o,cx:r,cy:n,r:i})):null};function xn(e){"@babel/helpers - typeof";return xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xn(e)}var qC=["x","y","top","left","width","height","className"];function Ol(){return Ol=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ol.apply(this,arguments)}function Og(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function NC(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Og(Object(r),!0).forEach(function(n){BC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Og(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function BC(e,t,r){return t=LC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function LC(e){var t=FC(e,"string");return xn(t)=="symbol"?t:t+""}function FC(e,t){if(xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function UC(e,t){if(e==null)return{};var r=WC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function WC(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var zC=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},HC=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,h=t.height,d=h===void 0?0:h,y=t.className,v=UC(t,qC),p=NC({x:n,y:a,top:u,left:s,width:l,height:d},v);return!F(n)||!F(a)||!F(l)||!F(d)||!F(u)||!F(s)?null:T.createElement("path",Ol({},re(p,!0),{className:ne("recharts-cross",y),d:zC(n,a,l,d,u,s)}))},ns,_g;function GC(){if(_g)return ns;_g=1;var e=ob(),t=e(Object.getPrototypeOf,Object);return ns=t,ns}var is,Sg;function KC(){if(Sg)return is;Sg=1;var e=dt(),t=GC(),r=vt(),n="[object Object]",i=Function.prototype,a=Object.prototype,o=i.toString,u=a.hasOwnProperty,c=o.call(Object);function s(f){if(!r(f)||e(f)!=n)return!1;var l=t(f);if(l===null)return!0;var h=u.call(l,"constructor")&&l.constructor;return typeof h=="function"&&h instanceof h&&o.call(h)==c}return is=s,is}var XC=KC();const VC=ue(XC);var as,Ag;function YC(){if(Ag)return as;Ag=1;var e=dt(),t=vt(),r="[object Boolean]";function n(i){return i===!0||i===!1||t(i)&&e(i)==r}return as=n,as}var ZC=YC();const JC=ue(ZC);function wn(e){"@babel/helpers - typeof";return wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wn(e)}function Di(){return Di=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Di.apply(this,arguments)}function QC(e,t){return n$(e)||r$(e,t)||t$(e,t)||e$()}function e$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function t$(e,t){if(e){if(typeof e=="string")return Pg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pg(e,t)}}function Pg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function r$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function n$(e){if(Array.isArray(e))return e}function Tg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Eg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tg(Object(r),!0).forEach(function(n){i$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function i$(e,t,r){return t=a$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function a$(e){var t=o$(e,"string");return wn(t)=="symbol"?t:t+""}function o$(e,t){if(wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var jg=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},u$={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},c$=function(t){var r=Eg(Eg({},u$),t),n=q.useRef(),i=q.useState(-1),a=QC(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var x=n.current.getTotalLength();x&&u(x)}catch{}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,h=r.height,d=r.className,y=r.animationEasing,v=r.animationDuration,p=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||h!==+h||f===0&&l===0||h===0)return null;var w=ne("recharts-trapezoid",d);return b?T.createElement(Ot,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:h,x:c,y:s},duration:v,animationEasing:y,isActive:b},function(x){var O=x.upperWidth,g=x.lowerWidth,m=x.height,_=x.x,S=x.y;return T.createElement(Ot,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:v,easing:y},T.createElement("path",Di({},re(r,!0),{className:w,d:jg(_,S,O,g,m),ref:n})))}):T.createElement("g",null,T.createElement("path",Di({},re(r,!0),{className:w,d:jg(c,s,f,l,h)})))},s$=["option","shapeType","propTransformer","activeClassName","isActive"];function On(e){"@babel/helpers - typeof";return On=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},On(e)}function l$(e,t){if(e==null)return{};var r=f$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function f$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Mg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ki(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Mg(Object(r),!0).forEach(function(n){h$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function h$(e,t,r){return t=p$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p$(e){var t=d$(e,"string");return On(t)=="symbol"?t:t+""}function d$(e,t){if(On(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(On(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function v$(e,t){return ki(ki({},t),e)}function y$(e,t){return e==="symbols"}function Cg(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return T.createElement(qf,r);case"trapezoid":return T.createElement(c$,r);case"sector":return T.createElement(k0,r);case"symbols":if(y$(t))return T.createElement(ef,r);break;default:return null}}function g$(e){return q.isValidElement(e)?e.props:e}function m$(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?v$:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=l$(e,s$),s;if(q.isValidElement(t))s=q.cloneElement(t,ki(ki({},c),g$(t)));else if(Y(t))s=t(c);else if(VC(t)&&!JC(t)){var f=i(t,c);s=T.createElement(Cg,{shapeType:r,elementProps:f})}else{var l=c;s=T.createElement(Cg,{shapeType:r,elementProps:l})}return u?T.createElement(Oe,{className:o},s):s}function xa(e,t){return t!=null&&"trapezoids"in e.props}function wa(e,t){return t!=null&&"sectors"in e.props}function _n(e,t){return t!=null&&"points"in e.props}function b$(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function x$(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function w$(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function O$(e,t){var r;return xa(e,t)?r=b$:wa(e,t)?r=x$:_n(e,t)&&(r=w$),r}function _$(e,t){var r;return xa(e,t)?r="trapezoids":wa(e,t)?r="sectors":_n(e,t)&&(r="points"),r}function S$(e,t){if(xa(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(wa(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return _n(e,t)?t.payload:{}}function A$(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=_$(r,t),a=S$(r,t),o=n.filter(function(c,s){var f=If(a,c),l=r.props[i].filter(function(y){var v=O$(r,t);return v(y,t)}),h=r.props[i].indexOf(l[l.length-1]),d=s===h;return f&&d}),u=n.indexOf(o[o.length-1]);return u}var os,$g;function P$(){if($g)return os;$g=1;var e=Math.ceil,t=Math.max;function r(n,i,a,o){for(var u=-1,c=t(e((i-n)/(a||1)),0),s=Array(c);c--;)s[o?c:++u]=n,n+=a;return s}return os=r,os}var us,Ig;function H0(){if(Ig)return us;Ig=1;var e=Ob(),t=1/0,r=17976931348623157e292;function n(i){if(!i)return i===0?i:0;if(i=e(i),i===t||i===-1/0){var a=i<0?-1:1;return a*r}return i===i?i:0}return us=n,us}var cs,Rg;function T$(){if(Rg)return cs;Rg=1;var e=P$(),t=ca(),r=H0();function n(i){return function(a,o,u){return u&&typeof u!="number"&&t(a,o,u)&&(o=u=void 0),a=r(a),o===void 0?(o=a,a=0):o=r(o),u=u===void 0?a<o?1:-1:r(u),e(a,o,u,i)}}return cs=n,cs}var ss,Dg;function E$(){if(Dg)return ss;Dg=1;var e=T$(),t=e();return ss=t,ss}var j$=E$();const qi=ue(j$);function Sn(e){"@babel/helpers - typeof";return Sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sn(e)}function kg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?kg(Object(r),!0).forEach(function(n){G0(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function G0(e,t,r){return t=M$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function M$(e){var t=C$(e,"string");return Sn(t)=="symbol"?t:t+""}function C$(e,t){if(Sn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Sn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var $$=["Webkit","Moz","O","ms"],I$=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=$$.reduce(function(a,o){return qg(qg({},a),{},G0({},o+n,r))},{});return i[t]=r,i};function vr(e){"@babel/helpers - typeof";return vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vr(e)}function Ni(){return Ni=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ni.apply(this,arguments)}function Ng(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ls(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ng(Object(r),!0).forEach(function(n){De(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ng(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function R$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,X0(n.key),n)}}function D$(e,t,r){return t&&Bg(e.prototype,t),r&&Bg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function k$(e,t,r){return t=Bi(t),q$(e,K0()?Reflect.construct(t,r||[],Bi(e).constructor):t.apply(e,r))}function q$(e,t){if(t&&(vr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return N$(e)}function N$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function K0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(K0=function(){return!!e})()}function Bi(e){return Bi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Bi(e)}function B$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_l(e,t)}function _l(e,t){return _l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_l(e,t)}function De(e,t,r){return t=X0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function X0(e){var t=L$(e,"string");return vr(t)=="symbol"?t:t+""}function L$(e,t){if(vr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(vr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var F$=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=Hr().domain(qi(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},Lg=function(t){return t.changedTouches&&!!t.changedTouches.length},yr=function(e){function t(r){var n;return R$(this,t),n=k$(this,t,[r]),De(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),De(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),De(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),De(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),De(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),De(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),De(n,"handleSlideDragStart",function(i){var a=Lg(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return B$(t,e),D$(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),h=Math.max(i,a),d=t.getIndexInRange(o,l),y=t.getIndexInRange(o,h);return{startIndex:d-d%c,endIndex:y===f?f:y-y%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=Ze(a[n],u,n);return Y(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,h=c.startIndex,d=c.endIndex,y=c.onChange,v=n.pageX-a;v>0?v=Math.min(v,s+f-l-u,s+f-l-o):v<0&&(v=Math.max(v,s-o,s-u));var p=this.getIndex({startX:o+v,endX:u+v});(p.startIndex!==h||p.endIndex!==d)&&y&&y(p),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=Lg(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,h=f.width,d=f.travellerWidth,y=f.onChange,v=f.gap,p=f.data,b={startX:this.state.startX,endX:this.state.endX},w=n.pageX-a;w>0?w=Math.min(w,l+h-d-s):w<0&&(w=Math.max(w,l-s)),b[o]=s+w;var x=this.getIndex(b),O=x.startIndex,g=x.endIndex,m=function(){var S=p.length-1;return o==="startX"&&(u>c?O%v===0:g%v===0)||u<c&&g===S||o==="endX"&&(u>c?g%v===0:O%v===0)||u>c&&g===S};this.setState(De(De({},o,s+w),"brushMoveStartX",n.pageX),function(){y&&m()&&y(x)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var h=l+n;if(!(h===-1||h>=u.length)){var d=u[h];i==="startX"&&d>=s||i==="endX"&&d<=c||this.setState(De({},i,d),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return T.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=q.Children.only(s);return l?T.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,h=c.traveller,d=c.ariaLabel,y=c.data,v=c.startIndex,p=c.endIndex,b=Math.max(n,this.props.x),w=ls(ls({},re(this.props,!1)),{},{x:b,y:s,width:f,height:l}),x=d||"Min value: ".concat((a=y[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=y[p])===null||o===void 0?void 0:o.name);return T.createElement(Oe,{tabIndex:0,role:"slider","aria-label":x,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(g){["ArrowLeft","ArrowRight"].includes(g.key)&&(g.preventDefault(),g.stopPropagation(),u.handleTravellerMoveKeyboard(g.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(h,w))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return T.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,h=f.endX,d=5,y={pointerEvents:"none",fill:s};return T.createElement(Oe,{className:"recharts-brush-texts"},T.createElement(di,Ni({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,h)-d,y:o+u/2},y),this.getTextOfTick(i)),T.createElement(di,Ni({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,h)+c+d,y:o+u/2},y),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,h=this.state,d=h.startX,y=h.endX,v=h.isTextActive,p=h.isSlideMoving,b=h.isTravellerMoving,w=h.isTravellerFocused;if(!i||!i.length||!F(u)||!F(c)||!F(s)||!F(f)||s<=0||f<=0)return null;var x=ne("recharts-brush",a),O=T.Children.count(o)===1,g=I$("userSelect","none");return T.createElement(Oe,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(d,y),this.renderTravellerLayer(d,"startX"),this.renderTravellerLayer(y,"endX"),(v||p||b||w||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return T.createElement(T.Fragment,null,T.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),T.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),T.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return T.isValidElement(n)?a=T.cloneElement(n,i):Y(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return ls({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?F$({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var h=i.scale.domain().map(function(d){return i.scale(d)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:h}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(q.PureComponent);De(yr,"displayName","Brush");De(yr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var fs,Fg;function U$(){if(Fg)return fs;Fg=1;var e=cf();function t(r,n){var i;return e(r,function(a,o,u){return i=n(a,o,u),!i}),!!i}return fs=t,fs}var hs,Ug;function W$(){if(Ug)return hs;Ug=1;var e=Qm(),t=St(),r=U$(),n=Ie(),i=ca();function a(o,u,c){var s=n(o)?e:r;return c&&i(o,u,c)&&(u=void 0),s(o,t(u,3))}return hs=a,hs}var z$=W$();const H$=ue(z$);var tt=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},ps,Wg;function G$(){if(Wg)return ps;Wg=1;var e=gb();function t(r,n,i){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:i,writable:!0}):r[n]=i}return ps=t,ps}var ds,zg;function K$(){if(zg)return ds;zg=1;var e=G$(),t=vb(),r=St();function n(i,a){var o={};return a=r(a,3),t(i,function(u,c,s){e(o,c,a(u,c,s))}),o}return ds=n,ds}var X$=K$();const V$=ue(X$);var vs,Hg;function Y$(){if(Hg)return vs;Hg=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(!r(t[n],n,t))return!1;return!0}return vs=e,vs}var ys,Gg;function Z$(){if(Gg)return ys;Gg=1;var e=cf();function t(r,n){var i=!0;return e(r,function(a,o,u){return i=!!n(a,o,u),i}),i}return ys=t,ys}var gs,Kg;function J$(){if(Kg)return gs;Kg=1;var e=Y$(),t=Z$(),r=St(),n=Ie(),i=ca();function a(o,u,c){var s=n(o)?e:t;return c&&i(o,u,c)&&(u=void 0),s(o,r(u,3))}return gs=a,gs}var Q$=J$();const V0=ue(Q$);var eI=["x","y"];function An(e){"@babel/helpers - typeof";return An=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},An(e)}function Sl(){return Sl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sl.apply(this,arguments)}function Xg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Lr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xg(Object(r),!0).forEach(function(n){tI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tI(e,t,r){return t=rI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rI(e){var t=nI(e,"string");return An(t)=="symbol"?t:t+""}function nI(e,t){if(An(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(An(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function iI(e,t){if(e==null)return{};var r=aI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function aI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function oI(e,t){var r=e.x,n=e.y,i=iI(e,eI),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),h=parseInt(l,10);return Lr(Lr(Lr(Lr(Lr({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:h,name:t.name,radius:t.radius})}function Vg(e){return T.createElement(m$,Sl({shapeType:"rectangle",propTransformer:oI,activeClassName:"recharts-active-bar"},e))}var uI=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||Ut(!1),r)}},cI=["value","background"],Y0;function gr(e){"@babel/helpers - typeof";return gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gr(e)}function sI(e,t){if(e==null)return{};var r=lI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function lI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Li(){return Li=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Li.apply(this,arguments)}function Yg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function de(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yg(Object(r),!0).forEach(function(n){xt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function fI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Zg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,J0(n.key),n)}}function hI(e,t,r){return t&&Zg(e.prototype,t),r&&Zg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function pI(e,t,r){return t=Fi(t),dI(e,Z0()?Reflect.construct(t,r||[],Fi(e).constructor):t.apply(e,r))}function dI(e,t){if(t&&(gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return vI(e)}function vI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Z0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Z0=function(){return!!e})()}function Fi(e){return Fi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Fi(e)}function yI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Al(e,t)}function Al(e,t){return Al=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Al(e,t)}function xt(e,t,r){return t=J0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function J0(e){var t=gI(e,"string");return gr(t)=="symbol"?t:t+""}function gI(e,t){if(gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Bn=function(e){function t(){var r;fI(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=pI(this,t,[].concat(i)),xt(r,"state",{isAnimationFinished:!1}),xt(r,"id",ta("recharts-bar-")),xt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),xt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return yI(t,e),hI(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=re(this.props,!1);return n&&n.map(function(l,h){var d=h===c,y=d?s:o,v=de(de(de({},f),l),{},{isActive:d,option:y,index:h,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return T.createElement(Oe,Li({className:"recharts-bar-rectangle"},ni(i.props,l,h),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(h)}),T.createElement(Vg,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,h=this.state.prevData;return T.createElement(Ot,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(d){var y=d.t,v=a.map(function(p,b){var w=h&&h[b];if(w){var x=Vt(w.x,p.x),O=Vt(w.y,p.y),g=Vt(w.width,p.width),m=Vt(w.height,p.height);return de(de({},p),{},{x:x(y),y:O(y),width:g(y),height:m(y)})}if(o==="horizontal"){var _=Vt(0,p.height),S=_(y);return de(de({},p),{},{y:p.y+p.height-S,height:S})}var P=Vt(0,p.width),M=P(y);return de(de({},p),{},{width:M})});return T.createElement(Oe,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!If(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=re(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,h=sI(s,cI);if(!l)return null;var d=de(de(de(de(de({},h),{},{fill:"#eee"},l),c),ni(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return T.createElement(Vg,Li({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},d))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ye(f,ba);if(!l)return null;var h=s==="vertical"?o[0].height/2:o[0].width/2,d=function(p,b){var w=Array.isArray(p.value)?p.value[1]:p.value;return{x:p.x,y:p.y,value:w,errorVal:Ze(p,b)}},y={clipPath:n?"url(#clipPath-".concat(i,")"):null};return T.createElement(Oe,y,l.map(function(v){return T.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:h,dataPointFormatter:d})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,h=n.height,d=n.isAnimationActive,y=n.background,v=n.id;if(i||!a||!a.length)return null;var p=this.state.isAnimationFinished,b=ne("recharts-bar",o),w=u&&u.allowDataOverflow,x=c&&c.allowDataOverflow,O=w||x,g=Z(v)?this.id:v;return T.createElement(Oe,{className:b},w||x?T.createElement("defs",null,T.createElement("clipPath",{id:"clipPath-".concat(g)},T.createElement("rect",{x:w?s:s-l/2,y:x?f:f-h/2,width:w?l:l*2,height:x?h:h*2}))):null,T.createElement(Oe,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(g,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,g),(!d||p)&&Ft.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(q.PureComponent);Y0=Bn;xt(Bn,"displayName","Bar");xt(Bn,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Rn.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});xt(Bn,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,d=DE(n,r);if(!d)return null;var y=t.layout,v=r.type.defaultProps,p=v!==void 0?de(de({},v),r.props):r.props,b=p.dataKey,w=p.children,x=p.minPointSize,O=y==="horizontal"?o:a,g=s?O.scale.domain():null,m=UE({numericAxis:O}),_=Ye(w,Sb),S=l.map(function(P,M){var A,E,j,I,$,R;s?A=kE(s[f+M],g):(A=Ze(P,b),Array.isArray(A)||(A=[m,A]));var k=uI(x,Y0.defaultProps.minPointSize)(A[1],M);if(y==="horizontal"){var N,B=[o.scale(A[0]),o.scale(A[1])],z=B[0],G=B[1];E=ky({axis:a,ticks:u,bandSize:i,offset:d.offset,entry:P,index:M}),j=(N=G??z)!==null&&N!==void 0?N:void 0,I=d.size;var U=z-G;if($=Number.isNaN(U)?0:U,R={x:E,y:o.y,width:I,height:o.height},Math.abs(k)>0&&Math.abs($)<Math.abs(k)){var K=Xe($||k)*(Math.abs(k)-Math.abs($));j-=K,$+=K}}else{var ce=[a.scale(A[0]),a.scale(A[1])],pe=ce[0],Re=ce[1];if(E=pe,j=ky({axis:o,ticks:c,bandSize:i,offset:d.offset,entry:P,index:M}),I=Re-pe,$=d.size,R={x:a.x,y:j,width:a.width,height:$},Math.abs(k)>0&&Math.abs(I)<Math.abs(k)){var Et=Xe(I||k)*(Math.abs(k)-Math.abs(I));I+=Et}}return de(de(de({},P),{},{x:E,y:j,width:I,height:$,value:s?A:A[1],payload:P,background:R},_&&_[M]&&_[M].props),{},{tooltipPayload:[$0(r,P)],tooltipPosition:{x:E+I/2,y:j+$/2}})});return de({data:S,layout:y},h)});function Pn(e){"@babel/helpers - typeof";return Pn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pn(e)}function mI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Jg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Q0(n.key),n)}}function bI(e,t,r){return t&&Jg(e.prototype,t),r&&Jg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Qg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ge(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Qg(Object(r),!0).forEach(function(n){Oa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Oa(e,t,r){return t=Q0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Q0(e){var t=xI(e,"string");return Pn(t)=="symbol"?t:t+""}function xI(e,t){if(Pn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var rk=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!ke(s,Bn);return f.reduce(function(d,y){var v=r[y],p=v.orientation,b=v.domain,w=v.padding,x=w===void 0?{}:w,O=v.mirror,g=v.reversed,m="".concat(p).concat(O?"Mirror":""),_,S,P,M,A;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var E=b[1]-b[0],j=1/0,I=v.categoricalDomain.sort();if(I.forEach(function(ce,pe){pe>0&&(j=Math.min((ce||0)-(I[pe-1]||0),j))}),Number.isFinite(j)){var $=j/E,R=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(_=$*R/2),v.padding==="no-gap"){var k=Ve(t.barCategoryGap,$*R),N=$*R/2;_=N-k-(N-k)/R*k}}}i==="xAxis"?S=[n.left+(x.left||0)+(_||0),n.left+n.width-(x.right||0)-(_||0)]:i==="yAxis"?S=c==="horizontal"?[n.top+n.height-(x.bottom||0),n.top+(x.top||0)]:[n.top+(x.top||0)+(_||0),n.top+n.height-(x.bottom||0)-(_||0)]:S=v.range,g&&(S=[S[1],S[0]]);var B=E0(v,a,h),z=B.scale,G=B.realScaleType;z.domain(b).range(S),j0(z);var U=M0(z,Ge(Ge({},v),{},{realScaleType:G}));i==="xAxis"?(A=p==="top"&&!O||p==="bottom"&&O,P=n.left,M=l[m]-A*v.height):i==="yAxis"&&(A=p==="left"&&!O||p==="right"&&O,P=l[m]-A*v.width,M=n.top);var K=Ge(Ge(Ge({},v),U),{},{realScaleType:G,x:P,y:M,scale:z,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return K.bandSize=Ei(K,U),!v.hide&&i==="xAxis"?l[m]+=(A?-1:1)*K.height:v.hide||(l[m]+=(A?-1:1)*K.width),Ge(Ge({},d),{},Oa({},y,K))},{})},ex=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},wI=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return ex({x:r,y:n},{x:i,y:a})},tx=function(){function e(t){mI(this,e),this.scale=t}return bI(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();Oa(tx,"EPS",1e-4);var Nf=function(t){var r=Object.keys(t).reduce(function(n,i){return Ge(Ge({},n),{},Oa({},i,tx.create(t[i])))},{});return Ge(Ge({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return V$(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return V0(i,function(a,o){return r[o].isInRange(a)})}})};function OI(e){return(e%180+180)%180}var _I=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=OI(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},ms,em;function SI(){if(em)return ms;em=1;var e=St(),t=In(),r=oa();function n(i){return function(a,o,u){var c=Object(a);if(!t(a)){var s=e(o,3);a=r(a),o=function(l){return s(c[l],l,c)}}var f=i(a,o,u);return f>-1?c[s?a[f]:f]:void 0}}return ms=n,ms}var bs,tm;function AI(){if(tm)return bs;tm=1;var e=H0();function t(r){var n=e(r),i=n%1;return n===n?i?n-i:n:0}return bs=t,bs}var xs,rm;function PI(){if(rm)return xs;rm=1;var e=lb(),t=St(),r=AI(),n=Math.max;function i(a,o,u){var c=a==null?0:a.length;if(!c)return-1;var s=u==null?0:r(u);return s<0&&(s=n(c+s,0)),e(a,t(o,3),s)}return xs=i,xs}var ws,nm;function TI(){if(nm)return ws;nm=1;var e=SI(),t=PI(),r=e(t);return ws=r,ws}var EI=TI();const jI=ue(EI);var MI=Pm();const CI=ue(MI);var $I=CI(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),Bf=q.createContext(void 0),Lf=q.createContext(void 0),rx=q.createContext(void 0),nx=q.createContext({}),ix=q.createContext(void 0),ax=q.createContext(0),ox=q.createContext(0),im=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=$I(a);return T.createElement(Bf.Provider,{value:n},T.createElement(Lf.Provider,{value:i},T.createElement(nx.Provider,{value:a},T.createElement(rx.Provider,{value:f},T.createElement(ix.Provider,{value:o},T.createElement(ax.Provider,{value:s},T.createElement(ox.Provider,{value:c},u)))))))},II=function(){return q.useContext(ix)},ux=function(t){var r=q.useContext(Bf);r==null&&Ut(!1);var n=r[t];return n==null&&Ut(!1),n},nk=function(){var t=q.useContext(Bf);return bt(t)},ik=function(){var t=q.useContext(Lf),r=jI(t,function(n){return V0(n.domain,Number.isFinite)});return r||bt(t)},cx=function(t){var r=q.useContext(Lf);r==null&&Ut(!1);var n=r[t];return n==null&&Ut(!1),n},RI=function(){var t=q.useContext(rx);return t},ak=function(){return q.useContext(nx)},sx=function(){return q.useContext(ox)},lx=function(){return q.useContext(ax)};function mr(e){"@babel/helpers - typeof";return mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mr(e)}function DI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function kI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hx(n.key),n)}}function qI(e,t,r){return t&&kI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function NI(e,t,r){return t=Ui(t),BI(e,fx()?Reflect.construct(t,r||[],Ui(e).constructor):t.apply(e,r))}function BI(e,t){if(t&&(mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return LI(e)}function LI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function fx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(fx=function(){return!!e})()}function Ui(e){return Ui=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ui(e)}function FI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pl(e,t)}function Pl(e,t){return Pl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pl(e,t)}function am(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function om(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?am(Object(r),!0).forEach(function(n){Ff(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):am(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ff(e,t,r){return t=hx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hx(e){var t=UI(e,"string");return mr(t)=="symbol"?t:t+""}function UI(e,t){if(mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function WI(e,t){return KI(e)||GI(e,t)||HI(e,t)||zI()}function zI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function HI(e,t){if(e){if(typeof e=="string")return um(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return um(e,t)}}function um(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function GI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function KI(e){if(Array.isArray(e))return e}function Tl(){return Tl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Tl.apply(this,arguments)}var XI=function(t,r){var n;return T.isValidElement(t)?n=T.cloneElement(t,r):Y(t)?n=t(r):n=T.createElement("line",Tl({},r,{className:"recharts-reference-line-line"})),n},VI=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,h=a.width,d=a.height;if(n){var y=s.y,v=t.y.apply(y,{position:o});if(tt(s,"discard")&&!t.y.isInRange(v))return null;var p=[{x:f+h,y:v},{x:f,y:v}];return c==="left"?p.reverse():p}if(r){var b=s.x,w=t.x.apply(b,{position:o});if(tt(s,"discard")&&!t.x.isInRange(w))return null;var x=[{x:w,y:l+d},{x:w,y:l}];return u==="top"?x.reverse():x}if(i){var O=s.segment,g=O.map(function(m){return t.apply(m,{position:o})});return tt(s,"discard")&&H$(g,function(m){return!t.isInRange(m)})?null:g}return null};function YI(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=II(),f=ux(i),l=cx(a),h=RI();if(!s||!h)return null;Nt(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=Nf({x:f.scale,y:l.scale}),y=be(t),v=be(r),p=n&&n.length===2,b=VI(d,y,v,p,h,e.position,f.orientation,l.orientation,e);if(!b)return null;var w=WI(b,2),x=w[0],O=x.x,g=x.y,m=w[1],_=m.x,S=m.y,P=tt(e,"hidden")?"url(#".concat(s,")"):void 0,M=om(om({clipPath:P},re(e,!0)),{},{x1:O,y1:g,x2:_,y2:S});return T.createElement(Oe,{className:ne("recharts-reference-line",u)},XI(o,M),Te.renderCallByParent(e,wI({x1:O,y1:g,x2:_,y2:S})))}var Uf=function(e){function t(){return DI(this,t),NI(this,t,arguments)}return FI(t,e),qI(t,[{key:"render",value:function(){return T.createElement(YI,this.props)}}])}(T.Component);Ff(Uf,"displayName","ReferenceLine");Ff(Uf,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function El(){return El=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},El.apply(this,arguments)}function br(e){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},br(e)}function cm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function sm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?cm(Object(r),!0).forEach(function(n){_a(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ZI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function JI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dx(n.key),n)}}function QI(e,t,r){return t&&JI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function eR(e,t,r){return t=Wi(t),tR(e,px()?Reflect.construct(t,r||[],Wi(e).constructor):t.apply(e,r))}function tR(e,t){if(t&&(br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return rR(e)}function rR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function px(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(px=function(){return!!e})()}function Wi(e){return Wi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Wi(e)}function nR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jl(e,t)}function jl(e,t){return jl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},jl(e,t)}function _a(e,t,r){return t=dx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dx(e){var t=iR(e,"string");return br(t)=="symbol"?t:t+""}function iR(e,t){if(br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var aR=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=Nf({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return tt(t,"discard")&&!o.isInRange(u)?null:u},Sa=function(e){function t(){return ZI(this,t),eR(this,t,arguments)}return nR(t,e),QI(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=be(i),f=be(a);if(Nt(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=aR(this.props);if(!l)return null;var h=l.x,d=l.y,y=this.props,v=y.shape,p=y.className,b=tt(this.props,"hidden")?"url(#".concat(c,")"):void 0,w=sm(sm({clipPath:b},re(this.props,!0)),{},{cx:h,cy:d});return T.createElement(Oe,{className:ne("recharts-reference-dot",p)},t.renderDot(v,w),Te.renderCallByParent(this.props,{x:h-o,y:d-o,width:2*o,height:2*o}))}}])}(T.Component);_a(Sa,"displayName","ReferenceDot");_a(Sa,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});_a(Sa,"renderDot",function(e,t){var r;return T.isValidElement(e)?r=T.cloneElement(e,t):Y(e)?r=e(t):r=T.createElement(z0,El({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Ml(){return Ml=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ml.apply(this,arguments)}function xr(e){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xr(e)}function lm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?lm(Object(r),!0).forEach(function(n){Aa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function oR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function uR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yx(n.key),n)}}function cR(e,t,r){return t&&uR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function sR(e,t,r){return t=zi(t),lR(e,vx()?Reflect.construct(t,r||[],zi(e).constructor):t.apply(e,r))}function lR(e,t){if(t&&(xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fR(e)}function fR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function vx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(vx=function(){return!!e})()}function zi(e){return zi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},zi(e)}function hR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Cl(e,t)}function Cl(e,t){return Cl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Cl(e,t)}function Aa(e,t,r){return t=yx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yx(e){var t=pR(e,"string");return xr(t)=="symbol"?t:t+""}function pR(e,t){if(xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var dR=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var h=Nf({x:f.scale,y:l.scale}),d={x:t?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(c,{position:"start"}):h.y.rangeMin},y={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:i?h.y.apply(s,{position:"end"}):h.y.rangeMax};return tt(a,"discard")&&(!h.isInRange(d)||!h.isInRange(y))?null:ex(d,y)},Pa=function(e){function t(){return oR(this,t),sR(this,t,arguments)}return hR(t,e),cR(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;Nt(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=be(i),h=be(a),d=be(o),y=be(u),v=this.props.shape;if(!l&&!h&&!d&&!y&&!v)return null;var p=dR(l,h,d,y,this.props);if(!p&&!v)return null;var b=tt(this.props,"hidden")?"url(#".concat(f,")"):void 0;return T.createElement(Oe,{className:ne("recharts-reference-area",c)},t.renderRect(v,fm(fm({clipPath:b},re(this.props,!0)),p)),Te.renderCallByParent(this.props,p))}}])}(T.Component);Aa(Pa,"displayName","ReferenceArea");Aa(Pa,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});Aa(Pa,"renderRect",function(e,t){var r;return T.isValidElement(e)?r=T.cloneElement(e,t):Y(e)?r=e(t):r=T.createElement(qf,Ml({},t,{className:"recharts-reference-area-rect"})),r});function gx(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function vR(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return _I(n,r)}function yR(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function Hi(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function gR(e,t){return gx(e,t+1)}function mR(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var y=n==null?void 0:n[c];if(y===void 0)return{v:gx(n,s)};var v=c,p,b=function(){return p===void 0&&(p=r(y,v)),p},w=y.coordinate,x=c===0||Hi(e,w,b,f,u);x||(c=0,f=o,s+=1),x&&(f=w+e*(b()/2+i),c+=s)},h;s<=a.length;)if(h=l(),h)return h.v;return[]}function Tn(e){"@babel/helpers - typeof";return Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tn(e)}function hm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hm(Object(r),!0).forEach(function(n){bR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function bR(e,t,r){return t=xR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xR(e){var t=wR(e,"string");return Tn(t)=="symbol"?t:t+""}function wR(e,t){if(Tn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function OR(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(h){var d=a[h],y,v=function(){return y===void 0&&(y=r(d,h)),y};if(h===o-1){var p=e*(d.coordinate+e*v()/2-c);a[h]=d=Pe(Pe({},d),{},{tickCoord:p>0?d.coordinate-p*e:d.coordinate})}else a[h]=d=Pe(Pe({},d),{},{tickCoord:d.coordinate});var b=Hi(e,d.tickCoord,v,u,c);b&&(c=d.tickCoord-e*(v()/2+i),a[h]=Pe(Pe({},d),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function _R(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),h=e*(f.coordinate+e*l/2-s);o[u-1]=f=Pe(Pe({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate});var d=Hi(e,f.tickCoord,function(){return l},c,s);d&&(s=f.tickCoord-e*(l/2+i),o[u-1]=Pe(Pe({},f),{},{isShow:!0}))}for(var y=a?u-1:u,v=function(w){var x=o[w],O,g=function(){return O===void 0&&(O=r(x,w)),O};if(w===0){var m=e*(x.coordinate-e*g()/2-c);o[w]=x=Pe(Pe({},x),{},{tickCoord:m<0?x.coordinate-m*e:x.coordinate})}else o[w]=x=Pe(Pe({},x),{},{tickCoord:x.coordinate});var _=Hi(e,x.tickCoord,g,c,s);_&&(c=x.tickCoord+e*(g()/2+i),o[w]=Pe(Pe({},x),{},{isShow:!0}))},p=0;p<y;p++)v(p);return o}function SR(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(F(c)||Rn.isSsr)return gR(i,typeof c=="number"&&F(c)?c:0);var h=[],d=u==="top"||u==="bottom"?"width":"height",y=f&&d==="width"?zr(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(x,O){var g=Y(s)?s(x.value,O):x.value;return d==="width"?vR(zr(g,{fontSize:t,letterSpacing:r}),y,l):zr(g,{fontSize:t,letterSpacing:r})[d]},p=i.length>=2?Xe(i[1].coordinate-i[0].coordinate):1,b=yR(a,p,d);return c==="equidistantPreserveStart"?mR(p,b,v,i,o):(c==="preserveStart"||c==="preserveStartEnd"?h=_R(p,b,v,i,o,c==="preserveStartEnd"):h=OR(p,b,v,i,o),h.filter(function(w){return w.isShow}))}var AR=["viewBox"],PR=["viewBox"],TR=["ticks"];function wr(e){"@babel/helpers - typeof";return wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wr(e)}function Qt(){return Qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qt.apply(this,arguments)}function pm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pm(Object(r),!0).forEach(function(n){Wf(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Os(e,t){if(e==null)return{};var r=ER(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ER(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function jR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function dm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,bx(n.key),n)}}function MR(e,t,r){return t&&dm(e.prototype,t),r&&dm(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function CR(e,t,r){return t=Gi(t),$R(e,mx()?Reflect.construct(t,r||[],Gi(e).constructor):t.apply(e,r))}function $R(e,t){if(t&&(wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return IR(e)}function IR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function mx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(mx=function(){return!!e})()}function Gi(e){return Gi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Gi(e)}function RR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$l(e,t)}function $l(e,t){return $l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},$l(e,t)}function Wf(e,t,r){return t=bx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bx(e){var t=DR(e,"string");return wr(t)=="symbol"?t:t+""}function DR(e,t){if(wr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ta=function(e){function t(r){var n;return jR(this,t),n=CR(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return RR(t,e),MR(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=Os(n,AR),u=this.props,c=u.viewBox,s=Os(u,PR);return!tr(a,c)||!tr(o,s)||!tr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,h=i.tickMargin,d,y,v,p,b,w,x=l?-1:1,O=n.tickSize||f,g=F(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":d=y=n.coordinate,p=o+ +!l*c,v=p-x*O,w=v-x*h,b=g;break;case"left":v=p=n.coordinate,y=a+ +!l*u,d=y-x*O,b=d-x*h,w=g;break;case"right":v=p=n.coordinate,y=a+ +l*u,d=y+x*O,b=d+x*h,w=g;break;default:d=y=n.coordinate,p=o+ +l*c,v=p+x*O,w=v+x*h,b=g;break}return{line:{x1:d,y1:v,x2:y,y2:p},tick:{x:b,y:w}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=Ee(Ee(Ee({},re(this.props,!1)),re(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var h=+(c==="top"&&!s||c==="bottom"&&s);l=Ee(Ee({},l),{},{x1:i,y1:a+h*u,x2:i+o,y2:a+h*u})}else{var d=+(c==="left"&&!s||c==="right"&&s);l=Ee(Ee({},l),{},{x1:i+d*o,y1:a,x2:i+d*o,y2:a+u})}return T.createElement("line",Qt({},l,{className:ne("recharts-cartesian-axis-line",Fe(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,h=u.unit,d=SR(Ee(Ee({},this.props),{},{ticks:n}),i,a),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),p=re(this.props,!1),b=re(f,!1),w=Ee(Ee({},p),{},{fill:"none"},re(c,!1)),x=d.map(function(O,g){var m=o.getTickLineCoord(O),_=m.line,S=m.tick,P=Ee(Ee(Ee(Ee({textAnchor:y,verticalAnchor:v},p),{},{stroke:"none",fill:s},b),S),{},{index:g,payload:O,visibleTicksCount:d.length,tickFormatter:l});return T.createElement(Oe,Qt({className:"recharts-cartesian-axis-tick",key:"tick-".concat(O.value,"-").concat(O.coordinate,"-").concat(O.tickCoord)},ni(o.props,O,g)),c&&T.createElement("line",Qt({},w,_,{className:ne("recharts-cartesian-axis-tick-line",Fe(c,"className"))})),f&&t.renderTickItem(f,P,"".concat(Y(l)?l(O.value,g):O.value).concat(h||"")))});return T.createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,h=l.ticks,d=Os(l,TR),y=h;return Y(c)&&(y=h&&h.length>0?c(this.props):c(d)),o<=0||u<=0||!y||!y.length?null:T.createElement(Oe,{className:ne("recharts-cartesian-axis",s),ref:function(p){n.layerReference=p}},a&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),Te.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return T.isValidElement(n)?o=T.cloneElement(n,i):Y(n)?o=n(i):o=T.createElement(di,Qt({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(q.Component);Wf(Ta,"displayName","CartesianAxis");Wf(Ta,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function Or(e){"@babel/helpers - typeof";return Or=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Or(e)}function kR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function qR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ox(n.key),n)}}function NR(e,t,r){return t&&qR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function BR(e,t,r){return t=Ki(t),LR(e,xx()?Reflect.construct(t,r||[],Ki(e).constructor):t.apply(e,r))}function LR(e,t){if(t&&(Or(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return FR(e)}function FR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(xx=function(){return!!e})()}function Ki(e){return Ki=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ki(e)}function UR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Il(e,t)}function Il(e,t){return Il=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Il(e,t)}function wx(e,t,r){return t=Ox(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ox(e){var t=WR(e,"string");return Or(t)=="symbol"?t:t+""}function WR(e,t){if(Or(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Or(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Rl(){return Rl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Rl.apply(this,arguments)}function zR(e){var t=e.xAxisId,r=sx(),n=lx(),i=ux(t);return i==null?null:T.createElement(Ta,Rl({},i,{className:ne("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return qt(o,!0)}}))}var _x=function(e){function t(){return kR(this,t),BR(this,t,arguments)}return UR(t,e),NR(t,[{key:"render",value:function(){return T.createElement(zR,this.props)}}])}(T.Component);wx(_x,"displayName","XAxis");wx(_x,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function _r(e){"@babel/helpers - typeof";return _r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_r(e)}function HR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function GR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Px(n.key),n)}}function KR(e,t,r){return t&&GR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function XR(e,t,r){return t=Xi(t),VR(e,Sx()?Reflect.construct(t,r||[],Xi(e).constructor):t.apply(e,r))}function VR(e,t){if(t&&(_r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return YR(e)}function YR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Sx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Sx=function(){return!!e})()}function Xi(e){return Xi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Xi(e)}function ZR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Dl(e,t)}function Dl(e,t){return Dl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Dl(e,t)}function Ax(e,t,r){return t=Px(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Px(e){var t=JR(e,"string");return _r(t)=="symbol"?t:t+""}function JR(e,t){if(_r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function kl(){return kl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kl.apply(this,arguments)}var QR=function(t){var r=t.yAxisId,n=sx(),i=lx(),a=cx(r);return a==null?null:T.createElement(Ta,kl({},a,{className:ne("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return qt(u,!0)}}))},Tx=function(e){function t(){return HR(this,t),XR(this,t,arguments)}return ZR(t,e),KR(t,[{key:"render",value:function(){return T.createElement(QR,this.props)}}])}(T.Component);Ax(Tx,"displayName","YAxis");Ax(Tx,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function vm(e){return nD(e)||rD(e)||tD(e)||eD()}function eD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function tD(e,t){if(e){if(typeof e=="string")return ql(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ql(e,t)}}function rD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function nD(e){if(Array.isArray(e))return ql(e)}function ql(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Nl=function(t,r,n,i,a){var o=Ye(t,Uf),u=Ye(t,Sa),c=[].concat(vm(o),vm(u)),s=Ye(t,Pa),f="".concat(i,"Id"),l=i[0],h=r;if(c.length&&(h=c.reduce(function(v,p){if(p.props[f]===n&&tt(p.props,"extendDomain")&&F(p.props[l])){var b=p.props[l];return[Math.min(v[0],b),Math.max(v[1],b)]}return v},h)),s.length){var d="".concat(l,"1"),y="".concat(l,"2");h=s.reduce(function(v,p){if(p.props[f]===n&&tt(p.props,"extendDomain")&&F(p.props[d])&&F(p.props[y])){var b=p.props[d],w=p.props[y];return[Math.min(v[0],b,w),Math.max(v[1],b,w)]}return v},h)}return a&&a.length&&(h=a.reduce(function(v,p){return F(p)?[Math.min(v[0],p),Math.max(v[1],p)]:v},h)),h},_s={exports:{}},ym;function iD(){return ym||(ym=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,h){if(typeof f!="function")throw new TypeError("The listener must be a function");var d=new i(f,l||c,h),y=r?r+s:s;return c._events[y]?c._events[y].fn?c._events[y]=[c._events[y],d]:c._events[y].push(d):(c._events[y]=d,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var h=0,d=l.length,y=new Array(d);h<d;h++)y[h]=l[h].fn;return y},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,h,d,y){var v=r?r+s:s;if(!this._events[v])return!1;var p=this._events[v],b=arguments.length,w,x;if(p.fn){switch(p.once&&this.removeListener(s,p.fn,void 0,!0),b){case 1:return p.fn.call(p.context),!0;case 2:return p.fn.call(p.context,f),!0;case 3:return p.fn.call(p.context,f,l),!0;case 4:return p.fn.call(p.context,f,l,h),!0;case 5:return p.fn.call(p.context,f,l,h,d),!0;case 6:return p.fn.call(p.context,f,l,h,d,y),!0}for(x=1,w=new Array(b-1);x<b;x++)w[x-1]=arguments[x];p.fn.apply(p.context,w)}else{var O=p.length,g;for(x=0;x<O;x++)switch(p[x].once&&this.removeListener(s,p[x].fn,void 0,!0),b){case 1:p[x].fn.call(p[x].context);break;case 2:p[x].fn.call(p[x].context,f);break;case 3:p[x].fn.call(p[x].context,f,l);break;case 4:p[x].fn.call(p[x].context,f,l,h);break;default:if(!w)for(g=1,w=new Array(b-1);g<b;g++)w[g-1]=arguments[g];p[x].fn.apply(p[x].context,w)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,h){var d=r?r+s:s;if(!this._events[d])return this;if(!f)return o(this,d),this;var y=this._events[d];if(y.fn)y.fn===f&&(!h||y.once)&&(!l||y.context===l)&&o(this,d);else{for(var v=0,p=[],b=y.length;v<b;v++)(y[v].fn!==f||h&&!y[v].once||l&&y[v].context!==l)&&p.push(y[v]);p.length?this._events[d]=p.length===1?p[0]:p:o(this,d)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u}(_s)),_s.exports}var aD=iD();const oD=ue(aD);var Ss=new oD,As="recharts.syncMouseEvents";function En(e){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(e)}function uD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function cD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ex(n.key),n)}}function sD(e,t,r){return t&&cD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ps(e,t,r){return t=Ex(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ex(e){var t=lD(e,"string");return En(t)=="symbol"?t:t+""}function lD(e,t){if(En(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(En(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var fD=function(){function e(){uD(this,e),Ps(this,"activeIndex",0),Ps(this,"coordinateList",[]),Ps(this,"layout","horizontal")}return sD(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,h=r.mouseHandlerCallback,d=h===void 0?null:h;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=s??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=d??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,h=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:h})}}}])}();function hD(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&F(n)&&F(i))return!0}return!1}function pD(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function jx(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=Ae(t,r,n,i),u=Ae(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function dD(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,h=Ae(u,c,s,l),d=Ae(u,c,f,l);n=h.x,i=h.y,a=d.x,o=d.y}else return jx(t);return[{x:n,y:i},{x:a,y:o}]}function jn(e){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(e)}function gm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Qn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gm(Object(r),!0).forEach(function(n){vD(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vD(e,t,r){return t=yD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yD(e){var t=gD(e,"string");return jn(t)=="symbol"?t:t+""}function gD(e,t){if(jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function mD(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,h=e.chartName,d=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!d||!a||!o||h!=="ScatterChart"&&i!=="axis")return null;var y,v=Qy;if(h==="ScatterChart")y=o,v=HC;else if(h==="BarChart")y=pD(l,o,c,f),v=qf;else if(l==="radial"){var p=jx(o),b=p.cx,w=p.cy,x=p.radius,O=p.startAngle,g=p.endAngle;y={cx:b,cy:w,startAngle:O,endAngle:g,innerRadius:x,outerRadius:x},v=k0}else y={points:dD(l,o,c)},v=Qy;var m=Qn(Qn(Qn(Qn({stroke:"#ccc",pointerEvents:"none"},c),y),re(d,!1)),{},{payload:u,payloadIndex:s,className:ne("recharts-tooltip-cursor",d.className)});return q.isValidElement(d)?q.cloneElement(d,m):q.createElement(v,m)}var bD=["item"],xD=["children","className","width","height","style","compact","title","desc"];function Sr(e){"@babel/helpers - typeof";return Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(e)}function er(){return er=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},er.apply(this,arguments)}function mm(e,t){return _D(e)||OD(e,t)||Cx(e,t)||wD()}function wD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function OD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function _D(e){if(Array.isArray(e))return e}function bm(e,t){if(e==null)return{};var r=SD(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function SD(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function AD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function PD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,$x(n.key),n)}}function TD(e,t,r){return t&&PD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ED(e,t,r){return t=Vi(t),jD(e,Mx()?Reflect.construct(t,r||[],Vi(e).constructor):t.apply(e,r))}function jD(e,t){if(t&&(Sr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return MD(e)}function MD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Mx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Mx=function(){return!!e})()}function Vi(e){return Vi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Vi(e)}function CD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Bl(e,t)}function Bl(e,t){return Bl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Bl(e,t)}function Ar(e){return RD(e)||ID(e)||Cx(e)||$D()}function $D(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Cx(e,t){if(e){if(typeof e=="string")return Ll(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ll(e,t)}}function ID(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function RD(e){if(Array.isArray(e))return Ll(e)}function Ll(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function xm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function C(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xm(Object(r),!0).forEach(function(n){H(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H(e,t,r){return t=$x(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $x(e){var t=DD(e,"string");return Sr(t)=="symbol"?t:t+""}function DD(e,t){if(Sr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Sr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var kD={xAxis:["bottom","top"],yAxis:["left","right"]},qD={width:"100%",height:"100%"},Ix={x:0,y:0};function ei(e){return e}var ND=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},BD=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return C(C(C({},i),Ae(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return C(C(C({},i),Ae(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return Ix},Ea=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(Ar(u),Ar(s)):u},[]);return o.length>0?o:t&&t.length&&F(i)&&F(a)?t.slice(i,a+1):[]};function Rx(e){return e==="number"?[0,"auto"]:void 0}var Fl=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=Ea(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var h;if(o.dataKey&&!o.allowDuplicatedCategory){var d=l===void 0?u:l;h=ti(d,o.dataKey,i)}else h=l&&l[n]||u[n];return h?[].concat(Ar(c),[$0(s,h)]):c},[])},wm=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=ND(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=jE(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,h=Fl(t,r,f,l),d=BD(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:h,activeCoordinate:d}}return null},LD=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=t.stackOffset,d=T0(f,a);return n.reduce(function(y,v){var p,b=v.type.defaultProps!==void 0?C(C({},v.type.defaultProps),v.props):v.props,w=b.type,x=b.dataKey,O=b.allowDataOverflow,g=b.allowDuplicatedCategory,m=b.scale,_=b.ticks,S=b.includeHidden,P=b[o];if(y[P])return y;var M=Ea(t.data,{graphicalItems:i.filter(function(U){var K,ce=o in U.props?U.props[o]:(K=U.type.defaultProps)===null||K===void 0?void 0:K[o];return ce===P}),dataStartIndex:c,dataEndIndex:s}),A=M.length,E,j,I;hD(b.domain,O,w)&&(E=ul(b.domain,null,O),d&&(w==="number"||m!=="auto")&&(I=Gr(M,x,"category")));var $=Rx(w);if(!E||E.length===0){var R,k=(R=b.domain)!==null&&R!==void 0?R:$;if(x){if(E=Gr(M,x,w),w==="category"&&d){var N=_w(E);g&&N?(j=E,E=qi(0,A)):g||(E=By(k,E,v).reduce(function(U,K){return U.indexOf(K)>=0?U:[].concat(Ar(U),[K])},[]))}else if(w==="category")g?E=E.filter(function(U){return U!==""&&!Z(U)}):E=By(k,E,v).reduce(function(U,K){return U.indexOf(K)>=0||K===""||Z(K)?U:[].concat(Ar(U),[K])},[]);else if(w==="number"){var B=RE(M,i.filter(function(U){var K,ce,pe=o in U.props?U.props[o]:(K=U.type.defaultProps)===null||K===void 0?void 0:K[o],Re="hide"in U.props?U.props.hide:(ce=U.type.defaultProps)===null||ce===void 0?void 0:ce.hide;return pe===P&&(S||!Re)}),x,a,f);B&&(E=B)}d&&(w==="number"||m!=="auto")&&(I=Gr(M,x,"category"))}else d?E=qi(0,A):u&&u[P]&&u[P].hasStack&&w==="number"?E=h==="expand"?[0,1]:C0(u[P].stackGroups,c,s):E=P0(M,i.filter(function(U){var K=o in U.props?U.props[o]:U.type.defaultProps[o],ce="hide"in U.props?U.props.hide:U.type.defaultProps.hide;return K===P&&(S||!ce)}),w,f,!0);if(w==="number")E=Nl(l,E,P,a,_),k&&(E=ul(k,E,O));else if(w==="category"&&k){var z=k,G=E.every(function(U){return z.indexOf(U)>=0});G&&(E=z)}}return C(C({},y),{},H({},P,C(C({},b),{},{axisType:a,domain:E,categoricalDomain:I,duplicateDomain:j,originalDomain:(p=b.domain)!==null&&p!==void 0?p:$,isCategorical:d,layout:f})))},{})},FD=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=Ea(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),d=h.length,y=T0(f,a),v=-1;return n.reduce(function(p,b){var w=b.type.defaultProps!==void 0?C(C({},b.type.defaultProps),b.props):b.props,x=w[o],O=Rx("number");if(!p[x]){v++;var g;return y?g=qi(0,d):u&&u[x]&&u[x].hasStack?(g=C0(u[x].stackGroups,c,s),g=Nl(l,g,x,a)):(g=ul(O,P0(h,n.filter(function(m){var _,S,P=o in m.props?m.props[o]:(_=m.type.defaultProps)===null||_===void 0?void 0:_[o],M="hide"in m.props?m.props.hide:(S=m.type.defaultProps)===null||S===void 0?void 0:S.hide;return P===x&&!M}),"number",f),i.defaultProps.allowDataOverflow),g=Nl(l,g,x,a)),C(C({},p),{},H({},x,C(C({axisType:a},i.defaultProps),{},{hide:!0,orientation:Fe(kD,"".concat(a,".").concat(v%2),null),domain:g,originalDomain:O,isCategorical:y,layout:f})))}return p},{})},UD=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),h=Ye(f,a),d={};return h&&h.length?d=LD(t,{axes:h,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(d=FD(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),d},WD=function(t){var r=bt(t),n=qt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:sf(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:Ei(r,n)}},Om=function(t){var r=t.children,n=t.defaultShowTooltip,i=ke(r,yr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},zD=function(t){return!t||!t.length?!1:t.some(function(r){var n=st(r&&r.type);return n&&n.indexOf("Bar")>=0})},_m=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},HD=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,h=n.margin||{},d=ke(l,yr),y=ke(l,rr),v=Object.keys(c).reduce(function(g,m){var _=c[m],S=_.orientation;return!_.mirror&&!_.hide?C(C({},g),{},H({},S,g[S]+_.width)):g},{left:h.left||0,right:h.right||0}),p=Object.keys(o).reduce(function(g,m){var _=o[m],S=_.orientation;return!_.mirror&&!_.hide?C(C({},g),{},H({},S,Fe(g,"".concat(S))+_.height)):g},{top:h.top||0,bottom:h.bottom||0}),b=C(C({},p),v),w=b.bottom;d&&(b.bottom+=d.props.height||yr.defaultProps.height),y&&r&&(b=$E(b,i,n,r));var x=s-b.left-b.right,O=f-b.top-b.bottom;return C(C({brushBottom:w},b),{},{width:Math.max(x,0),height:Math.max(O,0)})},GD=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},ok=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,h=function(b,w){var x=w.graphicalItems,O=w.stackGroups,g=w.offset,m=w.updateId,_=w.dataStartIndex,S=w.dataEndIndex,P=b.barSize,M=b.layout,A=b.barGap,E=b.barCategoryGap,j=b.maxBarSize,I=_m(M),$=I.numericAxisName,R=I.cateAxisName,k=zD(x),N=[];return x.forEach(function(B,z){var G=Ea(b.data,{graphicalItems:[B],dataStartIndex:_,dataEndIndex:S}),U=B.type.defaultProps!==void 0?C(C({},B.type.defaultProps),B.props):B.props,K=U.dataKey,ce=U.maxBarSize,pe=U["".concat($,"Id")],Re=U["".concat(R,"Id")],Et={},Me=c.reduce(function(jt,Mt){var ja=w["".concat(Mt.axisType,"Map")],zf=U["".concat(Mt.axisType,"Id")];ja&&ja[zf]||Mt.axisType==="zAxis"||Ut(!1);var Hf=ja[zf];return C(C({},jt),{},H(H({},Mt.axisType,Hf),"".concat(Mt.axisType,"Ticks"),qt(Hf)))},Et),L=Me[R],X=Me["".concat(R,"Ticks")],V=O&&O[pe]&&O[pe].hasStack&&WE(B,O[pe].stackGroups),D=st(B.type).indexOf("Bar")>=0,fe=Ei(L,X),J=[],ye=k&&ME({barSize:P,stackGroups:O,totalSize:GD(Me,R)});if(D){var ge,Ce,gt=Z(ce)?j:ce,Xt=(ge=(Ce=Ei(L,X,!0))!==null&&Ce!==void 0?Ce:gt)!==null&&ge!==void 0?ge:0;J=CE({barGap:A,barCategoryGap:E,bandSize:Xt!==fe?Xt:fe,sizeList:ye[Re],maxBarSize:gt}),Xt!==fe&&(J=J.map(function(jt){return C(C({},jt),{},{position:C(C({},jt.position),{},{offset:jt.position.offset-Xt/2})})}))}var Ln=B&&B.type&&B.type.getComposedData;Ln&&N.push({props:C(C({},Ln(C(C({},Me),{},{displayedData:G,props:b,dataKey:K,item:B,bandSize:fe,barPosition:J,offset:g,stackedData:V,layout:M,dataStartIndex:_,dataEndIndex:S}))),{},H(H(H({key:B.key||"item-".concat(z)},$,Me[$]),R,Me[R]),"animationId",m)),childIndex:Rw(B,b.children),item:B})}),N},d=function(b,w){var x=b.props,O=b.dataStartIndex,g=b.dataEndIndex,m=b.updateId;if(!ep({props:x}))return null;var _=x.children,S=x.layout,P=x.stackOffset,M=x.data,A=x.reverseStackOrder,E=_m(S),j=E.numericAxisName,I=E.cateAxisName,$=Ye(_,n),R=FE(M,$,"".concat(j,"Id"),"".concat(I,"Id"),P,A),k=c.reduce(function(U,K){var ce="".concat(K.axisType,"Map");return C(C({},U),{},H({},ce,UD(x,C(C({},K),{},{graphicalItems:$,stackGroups:K.axisType===j&&R,dataStartIndex:O,dataEndIndex:g}))))},{}),N=HD(C(C({},k),{},{props:x,graphicalItems:$}),w==null?void 0:w.legendBBox);Object.keys(k).forEach(function(U){k[U]=f(x,k[U],N,U.replace("Map",""),r)});var B=k["".concat(I,"Map")],z=WD(B),G=h(x,C(C({},k),{},{dataStartIndex:O,dataEndIndex:g,updateId:m,graphicalItems:$,stackGroups:R,offset:N}));return C(C({formattedGraphicalItems:G,graphicalItems:$,offset:N,stackGroups:R},z),k)},y=function(p){function b(w){var x,O,g;return AD(this,b),g=ED(this,b,[w]),H(g,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),H(g,"accessibilityManager",new fD),H(g,"handleLegendBBoxUpdate",function(m){if(m){var _=g.state,S=_.dataStartIndex,P=_.dataEndIndex,M=_.updateId;g.setState(C({legendBBox:m},d({props:g.props,dataStartIndex:S,dataEndIndex:P,updateId:M},C(C({},g.state),{},{legendBBox:m}))))}}),H(g,"handleReceiveSyncEvent",function(m,_,S){if(g.props.syncId===m){if(S===g.eventEmitterSymbol&&typeof g.props.syncMethod!="function")return;g.applySyncEvent(_)}}),H(g,"handleBrushChange",function(m){var _=m.startIndex,S=m.endIndex;if(_!==g.state.dataStartIndex||S!==g.state.dataEndIndex){var P=g.state.updateId;g.setState(function(){return C({dataStartIndex:_,dataEndIndex:S},d({props:g.props,dataStartIndex:_,dataEndIndex:S,updateId:P},g.state))}),g.triggerSyncEvent({dataStartIndex:_,dataEndIndex:S})}}),H(g,"handleMouseEnter",function(m){var _=g.getMouseInfo(m);if(_){var S=C(C({},_),{},{isTooltipActive:!0});g.setState(S),g.triggerSyncEvent(S);var P=g.props.onMouseEnter;Y(P)&&P(S,m)}}),H(g,"triggeredAfterMouseMove",function(m){var _=g.getMouseInfo(m),S=_?C(C({},_),{},{isTooltipActive:!0}):{isTooltipActive:!1};g.setState(S),g.triggerSyncEvent(S);var P=g.props.onMouseMove;Y(P)&&P(S,m)}),H(g,"handleItemMouseEnter",function(m){g.setState(function(){return{isTooltipActive:!0,activeItem:m,activePayload:m.tooltipPayload,activeCoordinate:m.tooltipPosition||{x:m.cx,y:m.cy}}})}),H(g,"handleItemMouseLeave",function(){g.setState(function(){return{isTooltipActive:!1}})}),H(g,"handleMouseMove",function(m){m.persist(),g.throttleTriggeredAfterMouseMove(m)}),H(g,"handleMouseLeave",function(m){g.throttleTriggeredAfterMouseMove.cancel();var _={isTooltipActive:!1};g.setState(_),g.triggerSyncEvent(_);var S=g.props.onMouseLeave;Y(S)&&S(_,m)}),H(g,"handleOuterEvent",function(m){var _=Iw(m),S=Fe(g.props,"".concat(_));if(_&&Y(S)){var P,M;/.*touch.*/i.test(_)?M=g.getMouseInfo(m.changedTouches[0]):M=g.getMouseInfo(m),S((P=M)!==null&&P!==void 0?P:{},m)}}),H(g,"handleClick",function(m){var _=g.getMouseInfo(m);if(_){var S=C(C({},_),{},{isTooltipActive:!0});g.setState(S),g.triggerSyncEvent(S);var P=g.props.onClick;Y(P)&&P(S,m)}}),H(g,"handleMouseDown",function(m){var _=g.props.onMouseDown;if(Y(_)){var S=g.getMouseInfo(m);_(S,m)}}),H(g,"handleMouseUp",function(m){var _=g.props.onMouseUp;if(Y(_)){var S=g.getMouseInfo(m);_(S,m)}}),H(g,"handleTouchMove",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.throttleTriggeredAfterMouseMove(m.changedTouches[0])}),H(g,"handleTouchStart",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.handleMouseDown(m.changedTouches[0])}),H(g,"handleTouchEnd",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.handleMouseUp(m.changedTouches[0])}),H(g,"handleDoubleClick",function(m){var _=g.props.onDoubleClick;if(Y(_)){var S=g.getMouseInfo(m);_(S,m)}}),H(g,"handleContextMenu",function(m){var _=g.props.onContextMenu;if(Y(_)){var S=g.getMouseInfo(m);_(S,m)}}),H(g,"triggerSyncEvent",function(m){g.props.syncId!==void 0&&Ss.emit(As,g.props.syncId,m,g.eventEmitterSymbol)}),H(g,"applySyncEvent",function(m){var _=g.props,S=_.layout,P=_.syncMethod,M=g.state.updateId,A=m.dataStartIndex,E=m.dataEndIndex;if(m.dataStartIndex!==void 0||m.dataEndIndex!==void 0)g.setState(C({dataStartIndex:A,dataEndIndex:E},d({props:g.props,dataStartIndex:A,dataEndIndex:E,updateId:M},g.state)));else if(m.activeTooltipIndex!==void 0){var j=m.chartX,I=m.chartY,$=m.activeTooltipIndex,R=g.state,k=R.offset,N=R.tooltipTicks;if(!k)return;if(typeof P=="function")$=P(N,m);else if(P==="value"){$=-1;for(var B=0;B<N.length;B++)if(N[B].value===m.activeLabel){$=B;break}}var z=C(C({},k),{},{x:k.left,y:k.top}),G=Math.min(j,z.x+z.width),U=Math.min(I,z.y+z.height),K=N[$]&&N[$].value,ce=Fl(g.state,g.props.data,$),pe=N[$]?{x:S==="horizontal"?N[$].coordinate:G,y:S==="horizontal"?U:N[$].coordinate}:Ix;g.setState(C(C({},m),{},{activeLabel:K,activeCoordinate:pe,activePayload:ce,activeTooltipIndex:$}))}else g.setState(m)}),H(g,"renderCursor",function(m){var _,S=g.state,P=S.isTooltipActive,M=S.activeCoordinate,A=S.activePayload,E=S.offset,j=S.activeTooltipIndex,I=S.tooltipAxisBandSize,$=g.getTooltipEventType(),R=(_=m.props.active)!==null&&_!==void 0?_:P,k=g.props.layout,N=m.key||"_recharts-cursor";return T.createElement(mD,{key:N,activeCoordinate:M,activePayload:A,activeTooltipIndex:j,chartName:r,element:m,isActive:R,layout:k,offset:E,tooltipAxisBandSize:I,tooltipEventType:$})}),H(g,"renderPolarAxis",function(m,_,S){var P=Fe(m,"type.axisType"),M=Fe(g.state,"".concat(P,"Map")),A=m.type.defaultProps,E=A!==void 0?C(C({},A),m.props):m.props,j=M&&M[E["".concat(P,"Id")]];return q.cloneElement(m,C(C({},j),{},{className:ne(P,j.className),key:m.key||"".concat(_,"-").concat(S),ticks:qt(j,!0)}))}),H(g,"renderPolarGrid",function(m){var _=m.props,S=_.radialLines,P=_.polarAngles,M=_.polarRadius,A=g.state,E=A.radiusAxisMap,j=A.angleAxisMap,I=bt(E),$=bt(j),R=$.cx,k=$.cy,N=$.innerRadius,B=$.outerRadius;return q.cloneElement(m,{polarAngles:Array.isArray(P)?P:qt($,!0).map(function(z){return z.coordinate}),polarRadius:Array.isArray(M)?M:qt(I,!0).map(function(z){return z.coordinate}),cx:R,cy:k,innerRadius:N,outerRadius:B,key:m.key||"polar-grid",radialLines:S})}),H(g,"renderLegend",function(){var m=g.state.formattedGraphicalItems,_=g.props,S=_.children,P=_.width,M=_.height,A=g.props.margin||{},E=P-(A.left||0)-(A.right||0),j=S0({children:S,formattedGraphicalItems:m,legendWidth:E,legendContent:s});if(!j)return null;var I=j.item,$=bm(j,bD);return q.cloneElement(I,C(C({},$),{},{chartWidth:P,chartHeight:M,margin:A,onBBoxUpdate:g.handleLegendBBoxUpdate}))}),H(g,"renderTooltip",function(){var m,_=g.props,S=_.children,P=_.accessibilityLayer,M=ke(S,at);if(!M)return null;var A=g.state,E=A.isTooltipActive,j=A.activeCoordinate,I=A.activePayload,$=A.activeLabel,R=A.offset,k=(m=M.props.active)!==null&&m!==void 0?m:E;return q.cloneElement(M,{viewBox:C(C({},R),{},{x:R.left,y:R.top}),active:k,label:$,payload:k?I:[],coordinate:j,accessibilityLayer:P})}),H(g,"renderBrush",function(m){var _=g.props,S=_.margin,P=_.data,M=g.state,A=M.offset,E=M.dataStartIndex,j=M.dataEndIndex,I=M.updateId;return q.cloneElement(m,{key:m.key||"_recharts-brush",onChange:Vn(g.handleBrushChange,m.props.onChange),data:P,x:F(m.props.x)?m.props.x:A.left,y:F(m.props.y)?m.props.y:A.top+A.height+A.brushBottom-(S.bottom||0),width:F(m.props.width)?m.props.width:A.width,startIndex:E,endIndex:j,updateId:"brush-".concat(I)})}),H(g,"renderReferenceElement",function(m,_,S){if(!m)return null;var P=g,M=P.clipPathId,A=g.state,E=A.xAxisMap,j=A.yAxisMap,I=A.offset,$=m.type.defaultProps||{},R=m.props,k=R.xAxisId,N=k===void 0?$.xAxisId:k,B=R.yAxisId,z=B===void 0?$.yAxisId:B;return q.cloneElement(m,{key:m.key||"".concat(_,"-").concat(S),xAxis:E[N],yAxis:j[z],viewBox:{x:I.left,y:I.top,width:I.width,height:I.height},clipPathId:M})}),H(g,"renderActivePoints",function(m){var _=m.item,S=m.activePoint,P=m.basePoint,M=m.childIndex,A=m.isRange,E=[],j=_.props.key,I=_.item.type.defaultProps!==void 0?C(C({},_.item.type.defaultProps),_.item.props):_.item.props,$=I.activeDot,R=I.dataKey,k=C(C({index:M,dataKey:R,cx:S.x,cy:S.y,r:4,fill:kf(_.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},re($,!1)),ri($));return E.push(b.renderActiveDot($,k,"".concat(j,"-activePoint-").concat(M))),P?E.push(b.renderActiveDot($,C(C({},k),{},{cx:P.x,cy:P.y}),"".concat(j,"-basePoint-").concat(M))):A&&E.push(null),E}),H(g,"renderGraphicChild",function(m,_,S){var P=g.filterFormatItem(m,_,S);if(!P)return null;var M=g.getTooltipEventType(),A=g.state,E=A.isTooltipActive,j=A.tooltipAxis,I=A.activeTooltipIndex,$=A.activeLabel,R=g.props.children,k=ke(R,at),N=P.props,B=N.points,z=N.isRange,G=N.baseLine,U=P.item.type.defaultProps!==void 0?C(C({},P.item.type.defaultProps),P.item.props):P.item.props,K=U.activeDot,ce=U.hide,pe=U.activeBar,Re=U.activeShape,Et=!!(!ce&&E&&k&&(K||pe||Re)),Me={};M!=="axis"&&k&&k.props.trigger==="click"?Me={onClick:Vn(g.handleItemMouseEnter,m.props.onClick)}:M!=="axis"&&(Me={onMouseLeave:Vn(g.handleItemMouseLeave,m.props.onMouseLeave),onMouseEnter:Vn(g.handleItemMouseEnter,m.props.onMouseEnter)});var L=q.cloneElement(m,C(C({},P.props),Me));function X(Mt){return typeof j.dataKey=="function"?j.dataKey(Mt.payload):null}if(Et)if(I>=0){var V,D;if(j.dataKey&&!j.allowDuplicatedCategory){var fe=typeof j.dataKey=="function"?X:"payload.".concat(j.dataKey.toString());V=ti(B,fe,$),D=z&&G&&ti(G,fe,$)}else V=B==null?void 0:B[I],D=z&&G&&G[I];if(Re||pe){var J=m.props.activeIndex!==void 0?m.props.activeIndex:I;return[q.cloneElement(m,C(C(C({},P.props),Me),{},{activeIndex:J})),null,null]}if(!Z(V))return[L].concat(Ar(g.renderActivePoints({item:P,activePoint:V,basePoint:D,childIndex:I,isRange:z})))}else{var ye,ge=(ye=g.getItemByXY(g.state.activeCoordinate))!==null&&ye!==void 0?ye:{graphicalItem:L},Ce=ge.graphicalItem,gt=Ce.item,Xt=gt===void 0?m:gt,Ln=Ce.childIndex,jt=C(C(C({},P.props),Me),{},{activeIndex:Ln});return[q.cloneElement(Xt,jt),null,null]}return z?[L,null,null]:[L,null]}),H(g,"renderCustomized",function(m,_,S){return q.cloneElement(m,C(C({key:"recharts-customized-".concat(S)},g.props),g.state))}),H(g,"renderMap",{CartesianGrid:{handler:ei,once:!0},ReferenceArea:{handler:g.renderReferenceElement},ReferenceLine:{handler:ei},ReferenceDot:{handler:g.renderReferenceElement},XAxis:{handler:ei},YAxis:{handler:ei},Brush:{handler:g.renderBrush,once:!0},Bar:{handler:g.renderGraphicChild},Line:{handler:g.renderGraphicChild},Area:{handler:g.renderGraphicChild},Radar:{handler:g.renderGraphicChild},RadialBar:{handler:g.renderGraphicChild},Scatter:{handler:g.renderGraphicChild},Pie:{handler:g.renderGraphicChild},Funnel:{handler:g.renderGraphicChild},Tooltip:{handler:g.renderCursor,once:!0},PolarGrid:{handler:g.renderPolarGrid,once:!0},PolarAngleAxis:{handler:g.renderPolarAxis},PolarRadiusAxis:{handler:g.renderPolarAxis},Customized:{handler:g.renderCustomized}}),g.clipPathId="".concat((x=w.id)!==null&&x!==void 0?x:ta("recharts"),"-clip"),g.throttleTriggeredAfterMouseMove=_b(g.triggeredAfterMouseMove,(O=w.throttleDelay)!==null&&O!==void 0?O:1e3/60),g.state={},g}return CD(b,p),TD(b,[{key:"componentDidMount",value:function(){var x,O;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(x=this.props.margin.left)!==null&&x!==void 0?x:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var x=this.props,O=x.children,g=x.data,m=x.height,_=x.layout,S=ke(O,at);if(S){var P=S.props.defaultIndex;if(!(typeof P!="number"||P<0||P>this.state.tooltipTicks.length-1)){var M=this.state.tooltipTicks[P]&&this.state.tooltipTicks[P].value,A=Fl(this.state,g,P,M),E=this.state.tooltipTicks[P].coordinate,j=(this.state.offset.top+m)/2,I=_==="horizontal",$=I?{x:E,y:j}:{y:E,x:j},R=this.state.formattedGraphicalItems.find(function(N){var B=N.item;return B.type.name==="Scatter"});R&&($=C(C({},$),R.props.points[P].tooltipPosition),A=R.props.points[P].tooltipPayload);var k={activeTooltipIndex:P,isTooltipActive:!0,activeLabel:M,activePayload:A,activeCoordinate:$};this.setState(k),this.renderCursor(S),this.accessibilityManager.setIndex(P)}}}},{key:"getSnapshotBeforeUpdate",value:function(x,O){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==O.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==x.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==x.margin){var g,m;this.accessibilityManager.setDetails({offset:{left:(g=this.props.margin.left)!==null&&g!==void 0?g:0,top:(m=this.props.margin.top)!==null&&m!==void 0?m:0}})}return null}},{key:"componentDidUpdate",value:function(x){js([ke(x.children,at)],[ke(this.props.children,at)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var x=ke(this.props.children,at);if(x&&typeof x.props.shared=="boolean"){var O=x.props.shared?"axis":"item";return u.indexOf(O)>=0?O:a}return a}},{key:"getMouseInfo",value:function(x){if(!this.container)return null;var O=this.container,g=O.getBoundingClientRect(),m=xS(g),_={chartX:Math.round(x.pageX-m.left),chartY:Math.round(x.pageY-m.top)},S=g.width/O.offsetWidth||1,P=this.inRange(_.chartX,_.chartY,S);if(!P)return null;var M=this.state,A=M.xAxisMap,E=M.yAxisMap,j=this.getTooltipEventType(),I=wm(this.state,this.props.data,this.props.layout,P);if(j!=="axis"&&A&&E){var $=bt(A).scale,R=bt(E).scale,k=$&&$.invert?$.invert(_.chartX):null,N=R&&R.invert?R.invert(_.chartY):null;return C(C({},_),{},{xValue:k,yValue:N},I)}return I?C(C({},_),I):null}},{key:"inRange",value:function(x,O){var g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,m=this.props.layout,_=x/g,S=O/g;if(m==="horizontal"||m==="vertical"){var P=this.state.offset,M=_>=P.left&&_<=P.left+P.width&&S>=P.top&&S<=P.top+P.height;return M?{x:_,y:S}:null}var A=this.state,E=A.angleAxisMap,j=A.radiusAxisMap;if(E&&j){var I=bt(E);return Uy({x:_,y:S},I)}return null}},{key:"parseEventsOfWrapper",value:function(){var x=this.props.children,O=this.getTooltipEventType(),g=ke(x,at),m={};g&&O==="axis"&&(g.props.trigger==="click"?m={onClick:this.handleClick}:m={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var _=ri(this.props,this.handleOuterEvent);return C(C({},_),m)}},{key:"addListener",value:function(){Ss.on(As,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Ss.removeListener(As,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(x,O,g){for(var m=this.state.formattedGraphicalItems,_=0,S=m.length;_<S;_++){var P=m[_];if(P.item===x||P.props.key===x.key||O===st(P.item.type)&&g===P.childIndex)return P}return null}},{key:"renderClipPath",value:function(){var x=this.clipPathId,O=this.state.offset,g=O.left,m=O.top,_=O.height,S=O.width;return T.createElement("defs",null,T.createElement("clipPath",{id:x},T.createElement("rect",{x:g,y:m,height:_,width:S})))}},{key:"getXScales",value:function(){var x=this.state.xAxisMap;return x?Object.entries(x).reduce(function(O,g){var m=mm(g,2),_=m[0],S=m[1];return C(C({},O),{},H({},_,S.scale))},{}):null}},{key:"getYScales",value:function(){var x=this.state.yAxisMap;return x?Object.entries(x).reduce(function(O,g){var m=mm(g,2),_=m[0],S=m[1];return C(C({},O),{},H({},_,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(x){var O;return(O=this.state.xAxisMap)===null||O===void 0||(O=O[x])===null||O===void 0?void 0:O.scale}},{key:"getYScaleByAxisId",value:function(x){var O;return(O=this.state.yAxisMap)===null||O===void 0||(O=O[x])===null||O===void 0?void 0:O.scale}},{key:"getItemByXY",value:function(x){var O=this.state,g=O.formattedGraphicalItems,m=O.activeItem;if(g&&g.length)for(var _=0,S=g.length;_<S;_++){var P=g[_],M=P.props,A=P.item,E=A.type.defaultProps!==void 0?C(C({},A.type.defaultProps),A.props):A.props,j=st(A.type);if(j==="Bar"){var I=(M.data||[]).find(function(N){return DC(x,N)});if(I)return{graphicalItem:P,payload:I}}else if(j==="RadialBar"){var $=(M.data||[]).find(function(N){return Uy(x,N)});if($)return{graphicalItem:P,payload:$}}else if(xa(P,m)||wa(P,m)||_n(P,m)){var R=A$({graphicalItem:P,activeTooltipItem:m,itemData:E.data}),k=E.activeIndex===void 0?R:E.activeIndex;return{graphicalItem:C(C({},P),{},{childIndex:k}),payload:_n(P,m)?E.data[R]:P.props.data[R]}}}return null}},{key:"render",value:function(){var x=this;if(!ep(this))return null;var O=this.props,g=O.children,m=O.className,_=O.width,S=O.height,P=O.style,M=O.compact,A=O.title,E=O.desc,j=bm(O,xD),I=re(j,!1);if(M)return T.createElement(im,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},T.createElement(Cs,er({},I,{width:_,height:S,title:A,desc:E}),this.renderClipPath(),rp(g,this.renderMap)));if(this.props.accessibilityLayer){var $,R;I.tabIndex=($=this.props.tabIndex)!==null&&$!==void 0?$:0,I.role=(R=this.props.role)!==null&&R!==void 0?R:"application",I.onKeyDown=function(N){x.accessibilityManager.keyboardEvent(N)},I.onFocus=function(){x.accessibilityManager.focus()}}var k=this.parseEventsOfWrapper();return T.createElement(im,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},T.createElement("div",er({className:ne("recharts-wrapper",m),style:C({position:"relative",cursor:"default",width:_,height:S},P)},k,{ref:function(B){x.container=B}}),T.createElement(Cs,er({},I,{width:_,height:S,title:A,desc:E,style:qD}),this.renderClipPath(),rp(g,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(q.Component);H(y,"displayName",r),H(y,"defaultProps",C({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),H(y,"getDerivedStateFromProps",function(p,b){var w=p.dataKey,x=p.data,O=p.children,g=p.width,m=p.height,_=p.layout,S=p.stackOffset,P=p.margin,M=b.dataStartIndex,A=b.dataEndIndex;if(b.updateId===void 0){var E=Om(p);return C(C(C({},E),{},{updateId:0},d(C(C({props:p},E),{},{updateId:0}),b)),{},{prevDataKey:w,prevData:x,prevWidth:g,prevHeight:m,prevLayout:_,prevStackOffset:S,prevMargin:P,prevChildren:O})}if(w!==b.prevDataKey||x!==b.prevData||g!==b.prevWidth||m!==b.prevHeight||_!==b.prevLayout||S!==b.prevStackOffset||!tr(P,b.prevMargin)){var j=Om(p),I={chartX:b.chartX,chartY:b.chartY,isTooltipActive:b.isTooltipActive},$=C(C({},wm(b,x,_)),{},{updateId:b.updateId+1}),R=C(C(C({},j),I),$);return C(C(C({},R),d(C({props:p},R),b)),{},{prevDataKey:w,prevData:x,prevWidth:g,prevHeight:m,prevLayout:_,prevStackOffset:S,prevMargin:P,prevChildren:O})}if(!js(O,b.prevChildren)){var k,N,B,z,G=ke(O,yr),U=G&&(k=(N=G.props)===null||N===void 0?void 0:N.startIndex)!==null&&k!==void 0?k:M,K=G&&(B=(z=G.props)===null||z===void 0?void 0:z.endIndex)!==null&&B!==void 0?B:A,ce=U!==M||K!==A,pe=!Z(x),Re=pe&&!ce?b.updateId:b.updateId+1;return C(C({updateId:Re},d(C(C({props:p},b),{},{updateId:Re,dataStartIndex:U,dataEndIndex:K}),b)),{},{prevChildren:O,dataStartIndex:U,dataEndIndex:K})}return null}),H(y,"renderActiveDot",function(p,b,w){var x;return q.isValidElement(p)?x=q.cloneElement(p,b):Y(p)?x=p(b):x=T.createElement(z0,b),T.createElement(Oe,{className:"recharts-active-dot",key:w},x)});var v=q.forwardRef(function(b,w){return T.createElement(y,er({},b,{ref:w}))});return v.displayName=y.displayName,v};export{$n as $,Ot as A,at as B,Qy as C,z0 as D,Bn as E,rk as F,Rn as G,rr as H,sx as I,lx as J,ak as K,Oe as L,nk as M,ik as N,JD as O,SR as P,qt as Q,ZD as R,m$ as S,di as T,Ta as U,ba as V,YD as W,_x as X,Tx as Y,QD as Z,va as _,mT as a,Uf as a0,ef as a1,VD as a2,St as b,wT as c,ni as d,Te as e,re as f,tk as g,Z as h,Y as i,Ze as j,Fe as k,Vt as l,If as m,F as n,Ft as o,Ae as p,Xe as q,c0 as r,Ye as s,Sb as t,ta as u,QE as v,Ve as w,Nt as x,ok as y,ek as z};
