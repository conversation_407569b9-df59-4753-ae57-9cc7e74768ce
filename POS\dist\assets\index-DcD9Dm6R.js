import{e as a,r as p,j as t}from"./index-CfbMU4Ye.js";import{I as e}from"./item-detail-form-DDtid43_.js";import"./form-DPp_Bp7A.js";import"./pos-api-BBB_ZiZD.js";import{b as c}from"./use-items-in-store-data-BeUnNL6h.js";import"./user-3BSjwAvJ.js";import"./vietqr-api-BHQxfNzq.js";import"./crm-api-8UaIokQG.js";import"./header-CiiJInbE.js";import"./main-B69tr6A0.js";import"./search-context-DXPkaUlN.js";import"./date-range-picker-FRR8J6T3.js";import"./price-source-dialog-BmrbkC0d.js";import"./multi-select-DH0FdNoK.js";import"./exceljs.min-C3DV5hpd.js";import"./core.esm-yKS6rgn3.js";import"./zod-BFJv4_uG.js";import"./use-upload-image-DXVS30gg.js";import"./images-api-RS3EYfrE.js";import"./use-item-types-mN8TSC7t.js";import"./useQuery-BvDWg4vp.js";import"./utils-km2FGkQ4.js";import"./useMutation-C9PewMvL.js";import"./query-keys-3lmd-xp6.js";import"./use-item-classes-DJrzbexi.js";import"./use-units-Cyx-GSz4.js";import"./use-items-CyC70ipU.js";import"./item-api-CO_SoDZ8.js";import"./use-removed-items-DWRDzX0n.js";import"./use-customizations-CZoEvMkV.js";import"./use-customization-by-id-NSWQr-zC.js";import"./use-sources-Cwaxx93K.js";import"./sources-api-BmH0eH79.js";import"./sources-CfiQ7039.js";import"./useCanGoBack-DL6J3LYb.js";import"./calendar-DmzcYdpW.js";import"./createLucideIcon-BH-J_-vM.js";import"./index-CBP3KeI0.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-CSFn543p.js";import"./index-Cqf6DKEV.js";import"./check-C1W3FWto.js";import"./input-D8TU6hMD.js";import"./textarea-BQNTa5Sn.js";import"./combobox-B1W092_-.js";import"./command-Jt-qPT7s.js";import"./dialog-FztlF_ds.js";import"./search-N2Gpb9W7.js";import"./popover-C4SSkcaE.js";import"./chevrons-up-down-BYVIbgdq.js";import"./upload-DzCrKix_.js";import"./collapsible-BVDeq-Zm.js";import"./confirm-dialog-dvjeaHx8.js";import"./alert-dialog-Bac0_q2y.js";import"./circle-help-a_rpWlnp.js";import"./select-_nXsh5SU.js";import"./index-D41EikqA.js";import"./chevron-right-BwGWQXH2.js";import"./items-in-store-api-5JRL88nZ.js";import"./xlsx-DkH2s96g.js";import"./separator-DVvwOaSX.js";import"./createReactComponent-CVG1We1Z.js";import"./scroll-area-Bx6sgJqp.js";import"./IconChevronRight-1SGwHwL2.js";import"./react-icons.esm-DefBGHOQ.js";import"./use-dialog-state-CkZCkUo8.js";import"./modal-D_ZqQrH_.js";import"./date-picker-00goVjYH.js";import"./calendar-BqbXMtoi.js";import"./badge-DNJz5hg4.js";import"./circle-x-BmLiWp0R.js";const Nt=function(){const i=a({from:"/_authenticated/menu/items/items-in-store/detail"}),[r,s]=p.useState(null),o=i==null?void 0:i.id,{data:m,isLoading:n}=c(o,!!o);return o?(p.useEffect(()=>{m&&s(m.data)},[m]),n||!r?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):t.jsx(e,{currentRow:{...r,item_id:"",item_id_barcode:""},isCopyMode:!!r})):t.jsx(e,{})};export{Nt as component};
