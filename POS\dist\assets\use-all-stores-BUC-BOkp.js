import{j as s,B as D,L as m,c as i,r as f,l as y,b as x}from"./index-UcdZ5AHH.js";import{D as h,a as w,b,c as j}from"./dropdown-menu-D3XvynCv.js";import{c as C}from"./createReactComponent-C1S2Ujit.js";import{u as M}from"./useQuery-B4yhTgGk.js";import{g as R}from"./stores-api-CmxnE7jq.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var v=C("outline","menu","IconMenu",[["path",{d:"M4 8l16 0",key:"svg-0"}],["path",{d:"M4 16l16 0",key:"svg-1"}]]);function U({className:e,links:t,...o}){return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"md:hidden",children:s.jsxs(h,{modal:!1,children:[s.jsx(w,{asChild:!0,children:s.jsx(D,{size:"icon",variant:"outline",children:s.jsx(v,{})})}),s.jsx(b,{side:"bottom",align:"start",children:t.map(({title:a,href:r,isActive:c,disabled:n})=>s.jsx(j,{asChild:!0,children:s.jsx(m,{to:r,className:i("line-clamp-1 truncate",c?"":"text-muted-foreground"),disabled:n,children:a})},`${a}-${r}`))})]})}),s.jsx("nav",{className:i("hidden items-center space-x-4 md:flex lg:space-x-6",e),...o,children:t.map(({title:a,href:r,isActive:c,disabled:n})=>s.jsx(m,{to:r,disabled:n,className:i("hover:text-primary line-clamp-1 truncate text-sm font-medium transition-colors",c?"":"text-muted-foreground"),children:a},`${a}-${r}`))})]})}function $(){return T()}function q(){const e=new Date,t=new Date;return t.setDate(e.getDate()-7),{from:t,to:e}}function B(){const e=new Date;e.setHours(0,0,0,0);const t=new Date(e);return t.setHours(23,59,59,999),{from:e,to:t}}function O(e){const t=new Date;t.setHours(0,0,0,0);const o=new Date(t);o.setDate(t.getDate()-e);const a=new Date(t);return a.setHours(23,59,59,999),{from:o,to:a}}function T(){const e=new Date;return{from:new Date(e.getFullYear(),e.getMonth()-2,1),to:e}}const S=f.createContext(null),g=new Date,A=new Date(g.getFullYear(),0,1),L={dateRange:{from:A,to:g},filterType:"monthly",selectedStores:["all-stores"],selectedSources:["all-sources"]};function Y(){const e=f.useContext(S);return e||L}const N=e=>new Promise(t=>setTimeout(t,e));async function P(e,t){const o=[];let a=1,r=!0;const c=50;let n=0;const l=3,u=20;for(;r&&n<l&&a<=u;)try{a>1&&await N(100);const d=(await R({company_uid:e,brand_uid:t,page:a,limit:c})).data||[];d.length===0?(n++,n>=l?r=!1:a++):(n=0,o.push(...d),a++)}catch{a++,n++,n>=l&&(r=!1)}return o}function _({enabled:e=!0}={}){const{selectedBrand:t}=y(),{company:o}=x(),a="269717a1-7bb6-4fa3-9150-dea2f709c081",r="8b8f15f9-6986-4c5a-86bc-f7dba8966659",c=(o==null?void 0:o.id)||a,n=(t==null?void 0:t.id)||r,l=M({queryKey:["stores-all",c,n],queryFn:()=>P(c,n),enabled:e&&!0&&!0,staleTime:5*60*1e3,gcTime:10*60*1e3}),u=l.data||[];return{isLoading:l.isLoading,error:l.error,refetch:l.refetch,stores:u,allStores:u,totalItems:u.length}}export{S as R,U as T,T as a,Y as b,$ as c,q as d,B as e,O as g,_ as u};
