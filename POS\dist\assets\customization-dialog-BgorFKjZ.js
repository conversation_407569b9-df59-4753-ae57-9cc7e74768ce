import{r as I,j as o,R as X,u as z,a3 as $,a4 as p,B as q,l as ne,z as B}from"./index-CfbMU4Ye.js";import{u as ie}from"./use-dialog-state-CkZCkUo8.js";import{u as Y}from"./useQuery-BvDWg4vp.js";import{Q as b}from"./query-keys-3lmd-xp6.js";import{b as v}from"./pos-api-BBB_ZiZD.js";import"./vietqr-api-BHQxfNzq.js";import{u as ae}from"./use-item-types-mN8TSC7t.js";import{u as se}from"./use-item-classes-DJrzbexi.js";import{u as oe}from"./use-units-Cyx-GSz4.js";import"./user-3BSjwAvJ.js";import"./crm-api-8UaIokQG.js";import{C as W}from"./checkbox-CSFn543p.js";import{I as re}from"./input-D8TU6hMD.js";import{P as ce}from"./modal-D_ZqQrH_.js";import"./date-range-picker-FRR8J6T3.js";import{u as le,F as de,a as ue,b as me,c as he,d as ge,e as _e}from"./form-DPp_Bp7A.js";import{C as V}from"./chevron-right-BwGWQXH2.js";import{C as J}from"./select-_nXsh5SU.js";import{s as ye}from"./zod-BFJv4_uG.js";import{D as pe,a as fe,b as Ce,c as Ie,e as be,f as xe}from"./dialog-FztlF_ds.js";import{C as we}from"./combobox-B1W092_-.js";import{E as Fe}from"./exceljs.min-C3DV5hpd.js";import{u as L}from"./useMutation-C9PewMvL.js";const Z=X.createContext(null);function lt({children:e}){const[n,t]=ie(null),[i,a]=I.useState(null);return o.jsx(Z,{value:{open:n,setOpen:t,currentRow:i,setCurrentRow:a},children:e})}const dt=()=>{const e=X.useContext(Z);if(!e)throw new Error("useItemsInCity has to be used within <ItemsInCityContext>");return e},ee=()=>{const e=new Fe.Workbook;return e.creator="POS System",e.lastModifiedBy="POS System",e.created=new Date,e.modified=new Date,e},ve=()=>["ID","Mã món","Thành phố","Tên","Giá","Trạng thái","Mã barcode","Món ăn kèm","Không cập nhật số lượng món ăn kèm","Đơn vị","Nhóm","Tên nhóm","Loại món","Tên loại","Mô tả","SKU","VAT (%)","Thời gian chế biến (phút)","Cho phép sửa giá khi bán","Cấu hình món ảo","Cấu hình món dịch vụ","Cấu hình món ăn là vé buffet","Giờ","Ngày","Thứ tự","Hình ảnh","Công thức inQR cho máy pha trà"],te=e=>{const n=ve(),t=e.addWorksheet("Menu");return t.addRow(n).eachCell(s=>{s.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},s.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},s.alignment={horizontal:"center",vertical:"middle"},s.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((s,d)=>{t.getColumn(d+1).width=s}),t},Te=()=>["ID","Mã món","Thành phố","Tên","Giá (Mặc định 0)","Trạng thái (Mặc định 1)","Mã barcode (Tối đa 13)","Món ăn kèm (Mặc định 0)","Không cập nhật số lượng món ăn kèm (Mặc định 1)","Đơn vị (Mặc định MON)","Nhóm (Mặc định LOẠI KHÁC)","Tên nhóm","Loại món (Mặc định rỗng)","Tên loại","Mô tả","SKU (Tối đa 50)","VAT (%) (Mặc định 0)","Thời gian chế biến (phút) (Mặc định 0)","Cho phép sửa giá khi bán (Mặc định 0)","Cấu hình món ảo (Mặc định 0)","Cấu hình món dịch vụ (Mặc định 0)","Cấu hình món ăn là vé buffet (Mặc định 0)","Giờ (Mặc định 0)","Ngày (Mặc định 0)","Thứ tự (Mặc định 0)","Hình ảnh","Công thức inQR cho máy pha trà"],Se=()=>[["d9692391-3f3f-4754-9416-878d2d8b52ce","ITEM-3279","Hồ Chí Minh","Cà Phê Sữa (L)",0,1,"",0,1,"AM","ITEM_TYPE_OTHER","Uncategory","DA","Đồ ăn","không","",0,10,14,0,0,1,2064384,224,0,"https://image.foodbook.vn/images/20250829/1756431019051-anh-test-may-in-mau-chat-luong-hinh-2.jpg",""],["8119567b-f80e-43ac-8c85-012dd8f56b18","KHOAI","Hồ Chí Minh","pomato",0,1,"",1,1,"TRAI","MAK","MÓN ĂN KÈM","MA","manh","","",0,5,14,1,0,1,6355002,254,0,"https://image.foodbook.vn/images/20250829/1756450169874-vo_tri_2.jpg",""]],ke=()=>[["Chủ nhật","2"],["Thứ 2","4"],["Thứ 3","8"],["Thứ 4","16"],["Thứ 5","32"],["Thứ 6","64"],["Thứ 7","128"],["Ví dụ: CN, T2, T5 = 2 + 4 + 32","38"]],Me=()=>[["0h","1"],["1h","2"],["2h","4"],["3h","8"],["4h","16"],["5h","32"],["6h","64"],["7h","128"],["8h","256"],["9h","512"],["10h","1024"],["11h","2048"],["12h","4096"],["13h","8192"],["14h","16384"],["15h","32768"],["16h","65536"],["17h","131072"],["18h","262144"],["19h","524288"],["20h","1048576"],["21h","2097152"],["22h","4194304"],["23h","8388608"],["Ví dụ: 0h, 1h, 3h = 1 + 2 + 8","11"]],De=e=>{const n=e.filter(t=>t.active===1).map(t=>[t.item_type_id,t.item_type_name]);return n.length>0?n:[["Không có dữ liệu",""]]},je=e=>{const n=e.filter(t=>t.active===1).map(t=>[t.item_class_id,t.item_class_name]);return n.length>0?n:[["Không có dữ liệu",""]]},Ne=e=>{if(!e||e.length===0)return[["Không có dữ liệu",""]];const n=e.map(t=>[t.unit_id,t.unit_name]);return n.length>0?n:[["Không có dữ liệu",""]]},Ee=(e,n)=>{var f,w,R,U,S,k,N,F,K,Q;const t=Te(),i=e.addWorksheet("Template"),a=i.addRow(['Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet "Menu" để nhập dữ liệu.']);i.mergeCells(`A${a.number}:AA${a.number}`),a.getCell(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},a.getCell(1).font={color:{argb:"FFFFFFFF"},bold:!0,size:11},a.getCell(1).alignment={horizontal:"left",vertical:"middle"},a.getCell(1).border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}},i.addRow(t).eachCell(c=>{c.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},c.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},c.alignment={horizontal:"center",vertical:"middle"},c.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),Se().forEach(c=>{i.addRow(c).eachCell(g=>{g.font={size:10},g.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),i.addRow([]);const m=i.addRow(["BẢNG THAM CHIẾU NGÀY","","","BẢNG THAM CHIẾU GIỜ","","","BẢNG THAM CHIẾU NHÓM MÓN","","","BẢNG THAM CHIẾU LOẠI MÓN","","","BẢNG THAM CHIẾU ĐƠN VỊ",""]),r=m.number;i.mergeCells(`A${r}:B${r}`),i.mergeCells(`D${r}:E${r}`),i.mergeCells(`G${r}:H${r}`),i.mergeCells(`J${r}:K${r}`),i.mergeCells(`M${r}:N${r}`),m.eachCell(c=>{c.value&&c.value.toString().trim()!==""&&(c.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},c.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},c.alignment={horizontal:"center",vertical:"middle"},c.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})}),i.addRow(["Thời gian","Giá trị","","Thời gian","Giá trị","","Mã nhóm","Tên nhóm","","Mã loại món","Tên loại món","","Mã đơn vị","Tên đơn vị"]).eachCell(c=>{c.value&&c.value.toString().trim()!==""&&(c.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},c.font={bold:!0,size:10},c.alignment={horizontal:"center",vertical:"middle"},c.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})});const u=ke(),h=Me(),_=n!=null&&n.itemTypes?De(n.itemTypes):[["Không có dữ liệu",""]],x=n!=null&&n.itemClasses?je(n.itemClasses):[["Không có dữ liệu",""]],T=n!=null&&n.units?Ne(n.units):[["Không có dữ liệu",""]],E=Math.max(u.length,h.length,_.length,x.length,T.length),D=[];for(let c=0;c<E;c++){const l=[((f=u[c])==null?void 0:f[0])||"",((w=u[c])==null?void 0:w[1])||"","",((R=h[c])==null?void 0:R[0])||"",((U=h[c])==null?void 0:U[1])||"","",((S=_[c])==null?void 0:S[0])||"",((k=_[c])==null?void 0:k[1])||"","",((N=x[c])==null?void 0:N[0])||"",((F=x[c])==null?void 0:F[1])||"","",((K=T[c])==null?void 0:K[0])||"",((Q=T[c])==null?void 0:Q[1])||""];D.push(l)}return D.forEach(c=>{i.addRow(c).eachCell(g=>{g.value&&g.value.toString().trim()!==""&&(g.font={size:10},g.alignment={horizontal:"left",vertical:"middle"},g.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}})})}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((c,l)=>{i.getColumn(l+1).width=c}),i},ut=async e=>{try{const n=ee();te(n),Ee(n,e);const t=await n.xlsx.writeBuffer(),i=new Blob([t],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),s=`items_import_template_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.xlsx`,d=window.URL.createObjectURL(i),m=document.createElement("a");return m.href=d,m.download=s,document.body.appendChild(m),m.click(),document.body.removeChild(m),window.URL.revokeObjectURL(d),Promise.resolve()}catch(n){return console.error("Error creating Excel file:",n),Promise.reject(n)}},Re=async e=>{try{const n=ee(),t=te(n);e.forEach(a=>{const s=[a.id||"",a.item_code||"",a.city_name||"",a.item_name||"",a.price||0,a.active||1,a.barcode||"",a.is_side_dish||0,a.not_update_side_dish_quantity||1,a.unit_id||"",a.item_type_id||"",a.item_type_name||"",a.item_class_id||"",a.item_class_name||"",a.description||"",a.sku||"",a.vat_percent||0,a.cooking_time||0,a.allow_edit_price||0,a.is_virtual_item||0,a.is_service_item||0,a.is_buffet_item||0,a.time_sale_hour_day||0,a.time_sale_date_week||0,a.sort_order||0,a.image_url||"",a.inqr_formula||""];t.addRow(s)});const i=await n.xlsx.writeBuffer();return new Blob([i],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})}catch(n){throw console.error("Error creating Excel blob:",n),n}},Ae=()=>{try{const e=localStorage.getItem("pos_cities_data");if(e)return JSON.parse(e).map(t=>t.id)}catch{}return[]},G=e=>{if(!e)return null;if(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e))return e;try{const t=localStorage.getItem("pos_cities_data");if(t){const a=JSON.parse(t).find(s=>s.city_name===e);return(a==null?void 0:a.id)||null}}catch{return null}return null},M=new Map,H=new Map,Pe=10*60*1e3;function P(e){return typeof e=="object"&&e!==null&&"response"in e}const C={getItemsInCity:async e=>{const n=e.city_uid&&e.city_uid!=="all"?G(e.city_uid):e.city_uid,t=e.active!==void 0?e.active.toString():"undefined",i=e.skip_limit?"true":"false",a=`${e.company_uid}-${e.brand_uid}-${e.page||1}-${n||"all"}-${e.list_city_uid||"all"}-${e.item_type_uid||"all"}-${e.time_sale_date_week||""}-${t}-${e.reverse||0}-${e.search||""}-${e.limit||50}-${i}`,s=M.get(a);if(s&&Date.now()-s.timestamp<Pe)return s.data;const d=H.get(a);if(d)return d;const m=(async()=>{try{const r=new URLSearchParams;if(r.append("company_uid",e.company_uid),r.append("brand_uid",e.brand_uid),e.page&&r.append("page",e.page.toString()),e.item_type_uid&&r.append("item_type_uid",e.item_type_uid),e.list_city_uid)r.append("list_city_uid",e.list_city_uid);else if(e.city_uid&&e.city_uid!=="all"){const h=G(e.city_uid);h&&r.append("city_uid",h)}else{const h=Ae();h.length>0&&r.append("list_city_uid",h.join(","))}e.time_sale_date_week&&r.append("time_sale_date_week",e.time_sale_date_week),e.reverse!==void 0&&r.append("reverse",e.reverse.toString()),e.search&&r.append("search",e.search),e.active!==void 0&&r.append("active",e.active.toString()),e.limit&&r.append("limit",e.limit.toString()),e.skip_limit&&r.append("skip_limit","true");const y=await v.get(`/mdata/v1/items?${r.toString()}`);if(!y.data||typeof y.data!="object")throw new Error("Invalid response format from items in city API");const u=y.data;return M.set(a,{data:u,timestamp:Date.now()}),u}finally{H.delete(a)}})();return H.set(a,m),m},deleteItemInCity:async e=>{var n;try{const t=new URLSearchParams;t.append("company_uid",e.company_uid),t.append("brand_uid",e.brand_uid),t.append("id",e.id),await v.delete(`/mdata/v1/item?${t.toString()}`),M.clear()}catch(t){throw P(t)&&((n=t.response)==null?void 0:n.status)===404?new Error("Item not found."):t}},deleteMultipleItemsInCity:async e=>{var n;try{const t=new URLSearchParams;t.append("company_uid",e.company_uid),t.append("brand_uid",e.brand_uid),t.append("list_item_uid",e.list_item_uid.join(",")),await v.delete(`/mdata/v1/items?${t.toString()}`),M.clear()}catch(t){throw P(t)&&((n=t.response)==null?void 0:n.status)===404?new Error("Items not found."):t}},downloadTemplate:async e=>{var s;const n=e.city_uid&&e.city_uid!=="all"?G(e.city_uid):null,t=new URLSearchParams({skip_limit:"true",company_uid:e.company_uid,brand_uid:e.brand_uid,...n&&{city_uid:n},...e.item_type_uid&&e.item_type_uid!=="all"&&{item_type_uid:e.item_type_uid},...e.active&&e.active!=="all"&&{active:e.active}}),i=await v.get(`/mdata/v1/items?${t}`),a=Array.isArray((s=i.data)==null?void 0:s.data)?i.data.data:[];return await Re(a)},createItemInCity:async e=>{var n,t;try{const i=await v.post("/mdata/v1/item",e);return M.clear(),i.data.data||i.data}catch(i){throw P(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((t=i.response.data)==null?void 0:t.message)||"Invalid data provided."):i}},updateItemInCity:async e=>{var n,t;try{const i=await v.put("/mdata/v1/item",e,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return M.clear(),i.data.data||i.data}catch(i){throw P(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((t=i.response.data)==null?void 0:t.message)||"Invalid data provided."):i}},getItemByListId:async e=>{var n,t;try{const i=new URLSearchParams({skip_limit:"true",company_uid:e.company_uid,brand_uid:e.brand_uid,is_all:"true",list_item_id:e.list_item_id}),a=await v.get(`/mdata/v1/items?${i}`,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4}),s=Array.isArray((n=a.data)==null?void 0:n.data)?a.data.data:[];if(!s.length)throw new Error("Item not found");return{data:s[0]}}catch(i){throw P(i)&&((t=i.response)==null?void 0:t.status)===404?new Error("Item not found."):i}},getItemById:async e=>{var n;try{const t=new URLSearchParams;t.append("id",e.id),e.company_uid&&t.append("company_uid",e.company_uid),e.brand_uid&&t.append("brand_uid",e.brand_uid);const i=await v.get(`/mdata/v1/item?${t.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from item detail API");return i.data}catch(t){throw P(t)&&((n=t.response)==null?void 0:n.status)===404?new Error("Item not found."):t}},importItems:async e=>(await v.post("/mdata/v1/items/import",{company_uid:e.company_uid,brand_uid:e.brand_uid,items:e.items})).data,updateItemStatus:async e=>{var n,t;try{const a={...(await C.getItemById({id:e.id})).data,active:e.active,company_uid:e.company_uid,brand_uid:e.brand_uid},s=await v.put("/mdata/v1/item",a,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return M.clear(),s.data.data||s.data}catch(i){throw P(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((t=i.response.data)==null?void 0:t.message)||"Invalid data provided."):i}},clearCache:()=>{M.clear(),H.clear()},getCacheStats:()=>({cacheSize:M.size,pendingRequests:H.size})},ze=(e={})=>{const{params:n={},enabled:t=!0}=e,{company:i,brands:a}=z(x=>x.auth),s=a==null?void 0:a[0],d={company_uid:(i==null?void 0:i.id)||"",brand_uid:(s==null?void 0:s.id)||"",page:1,reverse:1,limit:50,...n},m=!!(i!=null&&i.id&&(s!=null&&s.id)),r=Y({queryKey:[b.ITEMS_IN_CITY_LIST,JSON.stringify(d)],queryFn:async()=>(await C.getItemsInCity(d)).data||[],enabled:t&&m,staleTime:5*60*1e3,refetchInterval:10*60*1e3}),y={...d,page:(d.page||1)+1},u=Y({queryKey:[b.ITEMS_IN_CITY_LIST,"next",JSON.stringify(y)],queryFn:async()=>(await C.getItemsInCity(y)).data||[],enabled:t&&m&&(r.data?r.data.length>0:!1),staleTime:2*60*1e3,gcTime:5*60*1e3}),h=d.limit||50,_=(u.data?u.data.length>0:!1)||(r.data?r.data.length===h:!1);return{data:r.data,isLoading:r.isLoading,error:r.error,refetch:r.refetch,isFetching:r.isFetching,nextPageData:u.data||[],hasNextPage:_}},mt=(e,n=!0)=>Y({queryKey:[b.ITEMS_IN_CITY_DETAIL,e],queryFn:()=>C.getItemById({id:e}),enabled:n&&!!e,staleTime:5*60*1e3}),ht=(e={})=>{var r,y,u;const n=ze(e),t=(r=e.params)==null?void 0:r.city_uid,i=(y=e.params)==null?void 0:y.list_city_uid,{data:a=[]}=ae({skip_limit:!0,...t&&t!=="all"?{city_uid:t}:{},...i?{list_city_uid:i}:{}}),{data:s=[]}=se({skip_limit:!0}),{data:d=[]}=oe();return{data:((u=n.data)==null?void 0:u.map(h=>{var k,N;const _=h,T=((k=(_.cities||[])[0])==null?void 0:k.city_name)||"",E=_.item_type_uid?a.find(F=>F.id===_.item_type_uid):null,D=(E==null?void 0:E.item_type_name)||"",j=_.item_class_uid?s.find(F=>F.id===_.item_class_uid):null,f=(j==null?void 0:j.item_class_name)||"",w=_.unit_uid?d.find(F=>F.id===_.unit_uid):null,R=(w==null?void 0:w.unit_name)||"",S=_.is_eat_with===1||_.item_id_eat_with&&_.item_id_eat_with!==""?_.item_id_eat_with||"Món ăn kèm":"";return{...h,code:_.item_id||"",name:h.item_name,price:h.ots_price,vatPercent:h.ots_tax,cookingTime:h.time_cooking,categoryGroup:D,itemType:D,itemClass:f,unit:R,sideItems:S||void 0,city:T,buffetConfig:((N=_.extra_data)==null?void 0:N.is_buffet_item)===1?"Đã cấu hình":"Chưa cấu hình",customization:_.customization_uid||void 0,isActive:!!h.active,createdAt:typeof h.created_at=="number"?new Date(h.created_at*1e3):new Date(new Date(h.created_at).getTime())}}))||[],isLoading:n.isLoading,error:n.error,refetch:n.refetch,isFetching:n.isFetching,nextPageData:n.nextPageData,hasNextPage:n.hasNextPage}},gt=()=>{const e=$(),n=L({mutationFn:t=>C.createItemInCity(t),onSuccess:()=>{C.clearCache(),e.invalidateQueries({queryKey:[b.ITEMS_IN_CITY_LIST]}),p.success("Tạo món thành công!")},onError:t=>{p.error(t.message||"Có lỗi xảy ra khi tạo món")}});return{createItemAsync:n.mutateAsync,isPending:n.isPending}},Le=()=>{const e=$(),n=L({mutationFn:t=>C.updateItemInCity(t),onSuccess:()=>{C.clearCache(),e.invalidateQueries({queryKey:[b.ITEMS_IN_CITY_LIST]}),e.invalidateQueries({queryKey:[b.ITEMS_IN_CITY_DETAIL]}),p.success("Cập nhật món thành công!")},onError:t=>{p.error(t.message||"Có lỗi xảy ra khi cập nhật món")}});return{updateItemAsync:n.mutateAsync,isPending:n.isPending}},_t=()=>{const e=$(),{company:n,brands:t}=z(s=>s.auth),i=t==null?void 0:t[0],a=L({mutationFn:s=>{const d={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",id:s};return C.deleteItemInCity(d)},onSuccess:()=>{e.invalidateQueries({queryKey:[b.ITEMS_IN_CITY_LIST]}),p.success("Xóa món thành công!")},onError:s=>{p.error(s.message||"Có lỗi xảy ra khi xóa món")}});return{deleteItemAsync:a.mutateAsync,isPending:a.isPending}},yt=()=>{const e=$(),{company:n,brands:t}=z(s=>s.auth),i=t==null?void 0:t[0],a=L({mutationFn:s=>{const d={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",list_item_uid:s};return C.deleteMultipleItemsInCity(d)},onSuccess:()=>{e.invalidateQueries({queryKey:[b.ITEMS_IN_CITY_LIST]}),p.success("Xóa món ăn thành công")},onError:s=>{p.error((s==null?void 0:s.message)||"Có lỗi xảy ra khi xóa món ăn")}});return{deleteMultipleItemsAsync:a.mutateAsync,isPending:a.isPending}},pt=()=>{const e=$(),{company:n,brands:t}=z(s=>s.auth),i=t==null?void 0:t[0],a=L({mutationFn:s=>{const d={id:s.id,active:s.active,company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||""};return C.updateItemStatus(d)},onSuccess:()=>{C.clearCache(),e.invalidateQueries({queryKey:[b.ITEMS_IN_CITY_LIST]}),e.invalidateQueries({queryKey:[b.ITEMS_IN_CITY_DETAIL]}),p.success("Cập nhật trạng thái thành công!")},onError:s=>{p.error(s.message||"Có lỗi xảy ra khi cập nhật trạng thái")}});return{updateStatusAsync:a.mutateAsync,isPending:a.isPending}},ft=()=>{const{company:e,brands:n}=z(a=>a.auth),t=n==null?void 0:n[0],i=L({mutationFn:a=>{const s={company_uid:(e==null?void 0:e.id)||"",brand_uid:(t==null?void 0:t.id)||"",...a};return C.downloadTemplate(s)},onSuccess:a=>{const s=window.URL.createObjectURL(a),d=document.createElement("a");d.href=s,d.download=`items-template-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(s),p.success("Tải template thành công!")},onError:a=>{p.error(a.message||"Có lỗi xảy ra khi tải template")}});return{downloadTemplateAsync:i.mutateAsync,isPending:i.isPending}},Ct=()=>{const e=$(),{company:n,brands:t}=z(s=>s.auth),i=t==null?void 0:t[0],a=L({mutationFn:s=>{const d={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",items:s};return C.importItems(d)},onSuccess:()=>{e.invalidateQueries({queryKey:[b.ITEMS_IN_CITY_LIST]}),p.success("Import món thành công!")},onError:s=>{p.error(s.message||"Có lỗi xảy ra khi import món")}});return{importItemsAsync:a.mutateAsync,isPending:a.isPending}};function It({itemsBuffet:e,open:n,onOpenChange:t,onItemsChange:i,items:a,hide:s=!0,enable:d=!0,onEnableChange:m}){const[r,y]=I.useState(""),[u,h]=I.useState([]),[_,x]=I.useState(!1),[T,E]=I.useState(!1),[D,j]=I.useState(!1);I.useEffect(()=>{n&&(h(Array.isArray(e)?e:[]),j(d))},[e,n]);const f=I.useMemo(()=>r?a.filter(l=>{var g;return(g=l.item_name)==null?void 0:g.toLowerCase().includes(r.toLowerCase())}):a,[a,r]),w=I.useMemo(()=>f.length?f.filter(l=>u.includes(l.item_id||"")):[],[f,u]),R=I.useMemo(()=>f.length?f.filter(l=>!u.includes(l.item_id||"")):[],[f,u]),U=l=>{h(g=>g.includes(l)?g.filter(A=>A!==l):[...g,l])},S=w.length,k=f.length,N=k>0&&S===k,F=S>0&&S<k,K=()=>{if(N){const l=f.map(g=>g.item_id);h(g=>g.filter(A=>!l.includes(A)))}else{const l=f.map(g=>g.item_id);h(g=>{const A=[...g];return l.forEach(O=>{A.includes(O||"")||A.push(O||"")}),A})}},Q=()=>{i(u),t(!1)},c=()=>{h([]),t(!1)};return o.jsx(ce,{title:"Chọn danh sách món không đi kèm vé buffet",centerTitle:!0,open:n,onOpenChange:t,onCancel:c,onConfirm:Q,confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:o.jsxs("div",{className:"space-y-4",children:[!s&&o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(W,{id:"enable-buffet",checked:D,onCheckedChange:l=>{const g=!!l;j(g),m==null||m(g)}}),o.jsx("label",{htmlFor:"enable-buffet",className:"cursor-pointer text-blue-600",onClick:()=>j(l=>!l),children:"Cấu hình món ăn là vé buffet"})]}),(s||D)&&o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"flex items-center gap-2",children:o.jsx(re,{placeholder:"Tìm kiếm",value:r,onChange:l=>y(l.target.value),className:"w-full"})}),!s&&o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(q,{type:"button",variant:"outline",className:"flex-1 justify-start",children:"Danh sách món không đi kèm vé buffet"}),o.jsx(q,{type:"button",variant:"link",className:"flex-1 justify-start text-blue-600",onClick:()=>{},children:"Danh sách vé buffet được upsize"})]}),o.jsxs("div",{className:"rounded-lg bg-green-50 p-3",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx(W,{id:"select-all",checked:N,...F&&{"data-indeterminate":"true"},onCheckedChange:K,className:"data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600"}),o.jsxs("label",{htmlFor:"select-all",className:"cursor-pointer text-sm font-medium text-green-700",children:["Đã chọn ",S]}),o.jsx(q,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>x(!_),children:_?o.jsx(V,{className:"h-3 w-3"}):o.jsx(J,{className:"h-3 w-3"})})]}),!_&&w.length>0&&o.jsx("div",{className:"mt-3 space-y-2",children:w.map(l=>o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx(W,{id:`selected-${l.item_id}`,checked:u.includes(l.item_id),onCheckedChange:()=>U(l.item_id)}),o.jsx("label",{htmlFor:`selected-${l.item_id}`,className:"flex-1 cursor-pointer text-sm",children:l.item_name})]},l.item_id))})]}),o.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsxs("div",{className:"text-sm font-medium text-gray-700",children:["Còn lại ",R.length]}),o.jsx(q,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>E(!T),children:T?o.jsx(V,{className:"h-3 w-3"}):o.jsx(J,{className:"h-3 w-3"})})]}),!T&&o.jsx("div",{className:"mt-3 max-h-60 space-y-2 overflow-y-auto",children:R.map(l=>o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx(W,{id:l.item_id,checked:u.includes(l.item_id),onCheckedChange:()=>U(l.item_id)}),o.jsx("label",{htmlFor:l.item_id,className:"flex-1 cursor-pointer text-sm",children:l.item_name})]},l.item_id))})]})]})]})})}const Ue=B.object({customization_uid:B.string().nullable()});function bt({item:e,customizations:n,open:t,onOpenChange:i}){const{updateItemAsync:a}=Le(),{company:s}=z(y=>y.auth),{selectedBrand:d}=ne(),m=le({resolver:ye(Ue),defaultValues:{customization_uid:"none"}});I.useEffect(()=>{if(t)try{m.reset({customization_uid:(e==null?void 0:e.customization_uid)??null})}catch{p.error("Lỗi khi load customization data")}},[t,m,e]);const r=async y=>{try{if(!(e!=null&&e.id)||!(s!=null&&s.id)||!(d!=null&&d.id))throw new Error("Required data is missing");const u=y.customization_uid==="none"?null:y.customization_uid;await a({...e,customization_uid:u}),i(!1)}catch{p.error("Lỗi khi cập nhật customization")}};return e?o.jsx(pe,{open:t,onOpenChange:y=>{i(y),m.reset()},children:o.jsxs(fe,{className:"top-[20%] w-full max-w-4xl translate-y-[-50%]",children:[o.jsx(Ce,{children:o.jsx(Ie,{className:"text-center",children:"Cấu hình customization"})}),o.jsx(de,{...m,children:o.jsxs("form",{onSubmit:m.handleSubmit(r),className:"space-y-4",children:[o.jsx(ue,{control:m.control,name:"customization_uid",render:({field:y})=>o.jsxs(me,{children:[o.jsx(he,{children:"Customization áp dụng cho món"}),o.jsx(ge,{children:o.jsx(we,{value:y.value??"",onValueChange:u=>y.onChange(u===""?null:u),options:n.map(u=>({value:u.id,label:u.name})),placeholder:"Chọn customization...",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không có dữ liệu",className:"w-full"})}),o.jsx(_e,{})]})}),o.jsxs(be,{children:[o.jsx(xe,{asChild:!0,children:o.jsx(q,{variant:"outline",type:"button",children:"Hủy"})}),o.jsx(q,{type:"submit",disabled:m.formState.isSubmitting,children:m.formState.isSubmitting?"Đang lưu...":"Lưu"})]})]})})]})}):null}export{It as B,bt as C,lt as I,yt as a,ft as b,Ct as c,_t as d,pt as e,Le as f,ut as g,ht as h,mt as i,gt as j,dt as u};
