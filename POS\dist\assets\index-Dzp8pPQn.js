import{r as b,R as l,s as Yn}from"./index-UcdZ5AHH.js";import{s as dn}from"./index-5l8gvuVy.js";var Xn=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to <PERSON> is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to <PERSON> is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function hn({packageName:e,customMessages:t}){let i=e;const s={...Xn,...t};function n(r,o){if(!o)return`${i}: ${r}`;let a=r;const u=r.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);for(const d of u){const c=(o[d[1]]||"").toString();a=a.replace(`{{${d[1]}}}`,c)}return`${i}: ${a}`}return{setPackageName({packageName:r}){return typeof r=="string"&&(i=r),this},setMessages({customMessages:r}){return Object.assign(s,r||{}),this},throwInvalidPublishableKeyError(r){throw new Error(n(s.InvalidPublishableKeyErrorMessage,r))},throwInvalidProxyUrl(r){throw new Error(n(s.InvalidProxyUrlErrorMessage,r))},throwMissingPublishableKeyError(){throw new Error(n(s.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw new Error(n(s.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(r){throw new Error(n(s.MissingClerkProvider,r))},throw(r){throw new Error(n(r))}}}var fn=Object.defineProperty,Zn=Object.getOwnPropertyDescriptor,Qn=Object.getOwnPropertyNames,er=Object.prototype.hasOwnProperty,tr=(e,t)=>{for(var i in t)fn(e,i,{get:t[i],enumerable:!0})},nr=(e,t,i,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Qn(t))!er.call(e,n)&&n!==i&&fn(e,n,{get:()=>t[n],enumerable:!(s=Zn(t,n))||s.enumerable});return e},rr=(e,t,i)=>(nr(e,t,"default"),i),ir={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},sr=new Set(["first_factor","second_factor","multi_factor"]),or=new Set(["strict_mfa","strict","moderate","lax"]),ar=e=>typeof e=="number"&&e>0,lr=e=>sr.has(e),ur=e=>or.has(e),Wt=e=>e.startsWith("org:")?e:`org:${e}`,cr=(e,t)=>{const{orgId:i,orgRole:s,orgPermissions:n}=t;return!e.role&&!e.permission||!i||!s||!n?null:e.permission?n.includes(Wt(e.permission)):e.role?s===Wt(e.role):null},Dt=(e,t)=>{const{org:i,user:s}=hr(e),[n,r]=t.split(":"),o=r||n;return n==="org"?i.includes(o):n==="user"?s.includes(o):[...i,...s].includes(o)},dr=(e,t)=>{const{features:i,plans:s}=t;return e.feature&&i?Dt(i,e.feature):e.plan&&s?Dt(s,e.plan):null},hr=e=>{const t=e?e.split(",").map(i=>i.trim()):[];return{org:t.filter(i=>i.split(":")[0].includes("o")).map(i=>i.split(":")[1]),user:t.filter(i=>i.split(":")[0].includes("u")).map(i=>i.split(":")[1])}},fr=e=>{if(!e)return!1;const t=n=>typeof n=="string"?ir[n]:n,i=typeof e=="string"&&ur(e),s=typeof e=="object"&&lr(e.level)&&ar(e.afterMinutes);return i||s?t.bind(null,e):!1},pr=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;const i=fr(e.reverification);if(!i)return null;const{level:s,afterMinutes:n}=i(),[r,o]=t,a=r!==-1?n>r:null,u=o!==-1?n>o:null;switch(s){case"first_factor":return a;case"second_factor":return o!==-1?u:a;case"multi_factor":return o===-1?a:a&&u}},mr=e=>t=>{if(!e.userId)return!1;const i=dr(t,e),s=cr(t,e),n=pr(t,e);return[i||s,n].some(r=>r===null)?[i||s,n].some(r=>r===!0):[i||s,n].every(r=>r===!0)},gr=({authObject:{sessionId:e,sessionStatus:t,userId:i,actor:s,orgId:n,orgRole:r,orgSlug:o,signOut:a,getToken:u,has:d,sessionClaims:c},options:{treatPendingAsSignedOut:m=!0}})=>{if(e===void 0&&i===void 0)return{isLoaded:!1,isSignedIn:void 0,sessionId:e,sessionClaims:void 0,userId:i,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:a,getToken:u};if(e===null&&i===null)return{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:i,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:a,getToken:u};if(m&&t==="pending")return{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:a,getToken:u};if(e&&c&&i&&n&&r)return{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:c,userId:i,actor:s||null,orgId:n,orgRole:r,orgSlug:o||null,has:d,signOut:a,getToken:u};if(e&&c&&i&&!n)return{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:c,userId:i,actor:s||null,orgId:null,orgRole:null,orgSlug:null,has:d,signOut:a,getToken:u}},pn=e=>typeof atob<"u"&&typeof atob=="function"?atob(e):typeof global<"u"&&global.Buffer?new global.Buffer(e,"base64").toString():e,vr=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],mn="pk_live_",_r="pk_test_";function Ft(e,t={}){if(e=e||"",!e||!ft(e)){if(t.fatal&&!e)throw new Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!ft(e))throw new Error("Publishable key not valid.");return null}const i=e.startsWith(mn)?"production":"development";let s=pn(e.split("_")[2]);return s=s.slice(0,-1),t.proxyUrl?s=t.proxyUrl:i!=="development"&&t.domain&&(s=`clerk.${t.domain}`),{instanceType:i,frontendApi:s}}function ft(e=""){try{const t=e.startsWith(mn)||e.startsWith(_r),i=pn(e.split("_")[2]||"").endsWith("$");return t&&i}catch{return!1}}function kr(){const e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;const i=typeof t=="string"?t:t.hostname;let s=e.get(i);return s===void 0&&(s=vr.some(n=>i.endsWith(n)),e.set(i,s)),s}}}var br="METHOD_CALLED";function Cr(e,t){return{event:br,payload:{method:e,...t}}}const gn=0,vn=1,_n=2,Nt=3;var Bt=Object.prototype.hasOwnProperty;function pt(e,t){var i,s;if(e===t)return!0;if(e&&t&&(i=e.constructor)===t.constructor){if(i===Date)return e.getTime()===t.getTime();if(i===RegExp)return e.toString()===t.toString();if(i===Array){if((s=e.length)===t.length)for(;s--&&pt(e[s],t[s]););return s===-1}if(!i||typeof e=="object"){s=0;for(i in e)if(Bt.call(e,i)&&++s&&!Bt.call(t,i)||!(i in t)||!pt(e[i],t[i]))return!1;return Object.keys(t).length===s}}return e!==e&&t!==t}const Z=new WeakMap,ce=()=>{},T=ce(),Be=Object,C=e=>e===T,Y=e=>typeof e=="function",se=(e,t)=>({...e,...t}),kn=e=>Y(e.then),Qe={},Le={},Ot="undefined",Ue=typeof window!=Ot,mt=typeof document!=Ot,Pr=Ue&&"Deno"in window,Sr=()=>Ue&&typeof window.requestAnimationFrame!=Ot,le=(e,t)=>{const i=Z.get(e);return[()=>!C(t)&&e.get(t)||Qe,s=>{if(!C(t)){const n=e.get(t);t in Le||(Le[t]=n),i[5](t,se(n,s),n||Qe)}},i[6],()=>!C(t)&&t in Le?Le[t]:!C(t)&&e.get(t)||Qe]};let gt=!0;const yr=()=>gt,[vt,_t]=Ue&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[ce,ce],wr=()=>{const e=mt&&document.visibilityState;return C(e)||e!=="hidden"},Er=e=>(mt&&document.addEventListener("visibilitychange",e),vt("focus",e),()=>{mt&&document.removeEventListener("visibilitychange",e),_t("focus",e)}),Or=e=>{const t=()=>{gt=!0,e()},i=()=>{gt=!1};return vt("online",t),vt("offline",i),()=>{_t("online",t),_t("offline",i)}},Ur={isOnline:yr,isVisible:wr},Ir={initFocus:Er,initReconnect:Or},Vt=!l.useId,Oe=!Ue||Pr,jr=e=>Sr()?window.requestAnimationFrame(e):setTimeout(e,1),ye=Oe?b.useEffect:b.useLayoutEffect,et=typeof navigator<"u"&&navigator.connection,$t=!Oe&&et&&(["slow-2g","2g"].includes(et.effectiveType)||et.saveData),Te=new WeakMap,tt=(e,t)=>Be.prototype.toString.call(e)===`[object ${t}]`;let Rr=0;const kt=e=>{const t=typeof e,i=tt(e,"Date"),s=tt(e,"RegExp"),n=tt(e,"Object");let r,o;if(Be(e)===e&&!i&&!s){if(r=Te.get(e),r)return r;if(r=++Rr+"~",Te.set(e,r),Array.isArray(e)){for(r="@",o=0;o<e.length;o++)r+=kt(e[o])+",";Te.set(e,r)}if(n){r="#";const a=Be.keys(e).sort();for(;!C(o=a.pop());)C(e[o])||(r+=o+":"+kt(e[o])+",");Te.set(e,r)}}else r=i?e.toJSON():t=="symbol"?e.toString():t=="string"?JSON.stringify(e):""+e;return r},be=e=>{if(Y(e))try{e=e()}catch{e=""}const t=e;return e=typeof e=="string"?e:(Array.isArray(e)?e.length:e)?kt(e):"",[e,t]};let Mr=0;const bt=()=>++Mr;async function bn(...e){const[t,i,s,n]=e,r=se({populateCache:!0,throwOnError:!0},typeof n=="boolean"?{revalidate:n}:n||{});let o=r.populateCache;const a=r.rollbackOnError;let u=r.optimisticData;const d=p=>typeof a=="function"?a(p):a!==!1,c=r.throwOnError;if(Y(i)){const p=i,f=[],g=t.keys();for(const _ of g)!/^\$(inf|sub)\$/.test(_)&&p(t.get(_)._k)&&f.push(_);return Promise.all(f.map(m))}return m(i);async function m(p){const[f]=be(p);if(!f)return;const[g,_]=le(t,f),[P,h,v,y]=Z.get(t),I=()=>{const F=P[f];return(Y(r.revalidate)?r.revalidate(g().data,p):r.revalidate!==!1)&&(delete v[f],delete y[f],F&&F[0])?F[0](_n).then(()=>g().data):g().data};if(e.length<3)return I();let S=s,w;const R=bt();h[f]=[R,0];const k=!C(u),A=g(),E=A.data,z=A._c,B=C(z)?E:z;if(k&&(u=Y(u)?u(B,E):u,_({data:u,_c:B})),Y(S))try{S=S(B)}catch(F){w=F}if(S&&kn(S))if(S=await S.catch(F=>{w=F}),R!==h[f][0]){if(w)throw w;return S}else w&&k&&d(w)&&(o=!0,_({data:B,_c:T}));if(o&&!w)if(Y(o)){const F=o(S,B);_({data:F,error:T,_c:T})}else _({data:S,error:T,_c:T});if(h[f][1]=bt(),Promise.resolve(I()).then(()=>{_({_c:T})}),w){if(c)throw w;return}return S}}const Ht=(e,t)=>{for(const i in e)e[i][0]&&e[i][0](t)},Cn=(e,t)=>{if(!Z.has(e)){const i=se(Ir,t),s=Object.create(null),n=bn.bind(T,e);let r=ce;const o=Object.create(null),a=(c,m)=>{const p=o[c]||[];return o[c]=p,p.push(m),()=>p.splice(p.indexOf(m),1)},u=(c,m,p)=>{e.set(c,m);const f=o[c];if(f)for(const g of f)g(m,p)},d=()=>{if(!Z.has(e)&&(Z.set(e,[s,Object.create(null),Object.create(null),Object.create(null),n,u,a]),!Oe)){const c=i.initFocus(setTimeout.bind(T,Ht.bind(T,s,gn))),m=i.initReconnect(setTimeout.bind(T,Ht.bind(T,s,vn)));r=()=>{c&&c(),m&&m(),Z.delete(e)}}};return d(),[e,n,d,r]}return[e,Z.get(e)[4]]},Lr=(e,t,i,s,n)=>{const r=i.errorRetryCount,o=n.retryCount,a=~~((Math.random()+.5)*(1<<(o<8?o:8)))*i.errorRetryInterval;!C(r)&&o>r||setTimeout(s,a,n)},Tr=pt,[Ie,Pn]=Cn(new Map),Sn=se({onLoadingSlow:ce,onSuccess:ce,onError:ce,onErrorRetry:Lr,onDiscarded:ce,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:$t?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:$t?5e3:3e3,compare:Tr,isPaused:()=>!1,cache:Ie,mutate:Pn,fallback:{}},Ur),yn=(e,t)=>{const i=se(e,t);if(t){const{use:s,fallback:n}=e,{use:r,fallback:o}=t;s&&r&&(i.use=s.concat(r)),n&&o&&(i.fallback=se(n,o))}return i},Ct=b.createContext({}),Ar=e=>{const{value:t}=e,i=b.useContext(Ct),s=Y(t),n=b.useMemo(()=>s?t(i):t,[s,i,t]),r=b.useMemo(()=>s?n:yn(i,n),[s,i,n]),o=n&&n.provider,a=b.useRef(T);o&&!a.current&&(a.current=Cn(o(r.cache||Ie),n));const u=a.current;return u&&(r.cache=u[0],r.mutate=u[1]),ye(()=>{if(u)return u[2]&&u[2](),u[3]},[]),b.createElement(Ct.Provider,se(e,{value:r}))},wn="$inf$",En=Ue&&window.__SWR_DEVTOOLS_USE__,zr=En?window.__SWR_DEVTOOLS_USE__:[],xr=()=>{En&&(window.__SWR_DEVTOOLS_REACT__=l)},On=e=>Y(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(e[1]===null?e[2]:e[1])||{}],Un=()=>se(Sn,b.useContext(Ct)),Wr=(e,t)=>{const[i,s]=be(e),[,,,n]=Z.get(Ie);if(n[i])return n[i];const r=t(s);return n[i]=r,r},Dr=e=>(t,i,s)=>e(t,i&&((...r)=>{const[o]=be(t),[,,,a]=Z.get(Ie);if(o.startsWith(wn))return i(...r);const u=a[o];return C(u)?i(...r):(delete a[o],u)}),s),Fr=zr.concat(Dr),Nr=e=>function(...i){const s=Un(),[n,r,o]=On(i),a=yn(s,o);let u=e;const{use:d}=a,c=(d||[]).concat(Fr);for(let m=c.length;m--;)u=c[m](u);return u(n,r||a.fetcher||null,a)},Br=(e,t,i)=>{const s=t[e]||(t[e]=[]);return s.push(i),()=>{const n=s.indexOf(i);n>=0&&(s[n]=s[s.length-1],s.pop())}},Vr=(e,t)=>(...i)=>{const[s,n,r]=On(i),o=(r.use||[]).concat(t);return e(s,n,{...r,use:o})};xr();const $r=()=>{},Hr=$r(),Pt=Object,Kt=e=>e===Hr,Kr=e=>typeof e=="function",Ae=new WeakMap,nt=(e,t)=>Pt.prototype.toString.call(e)===`[object ${t}]`;let Jr=0;const St=e=>{const t=typeof e,i=nt(e,"Date"),s=nt(e,"RegExp"),n=nt(e,"Object");let r,o;if(Pt(e)===e&&!i&&!s){if(r=Ae.get(e),r)return r;if(r=++Jr+"~",Ae.set(e,r),Array.isArray(e)){for(r="@",o=0;o<e.length;o++)r+=St(e[o])+",";Ae.set(e,r)}if(n){r="#";const a=Pt.keys(e).sort();for(;!Kt(o=a.pop());)Kt(e[o])||(r+=o+":"+St(e[o])+",");Ae.set(e,r)}}else r=i?e.toJSON():t=="symbol"?e.toString():t=="string"?JSON.stringify(e):""+e;return r},qr=e=>{if(Kr(e))try{e=e()}catch{e=""}const t=e;return e=typeof e=="string"?e:(Array.isArray(e)?e.length:e)?St(e):"",[e,t]},Gr=e=>qr(e)[0],rt=l.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),it={dedupe:!0},Yr=(e,t,i)=>{const{cache:s,compare:n,suspense:r,fallbackData:o,revalidateOnMount:a,revalidateIfStale:u,refreshInterval:d,refreshWhenHidden:c,refreshWhenOffline:m,keepPreviousData:p}=i,[f,g,_,P]=Z.get(s),[h,v]=be(e),y=b.useRef(!1),I=b.useRef(!1),S=b.useRef(h),w=b.useRef(t),R=b.useRef(i),k=()=>R.current,A=()=>k().isVisible()&&k().isOnline(),[E,z,B,F]=le(s,h),V=b.useRef({}).current,ee=C(o)?C(i.fallback)?T:i.fallback[h]:o,de=(U,O)=>{for(const N in V){const M=N;if(M==="data"){if(!n(U[M],O[M])&&(!C(U[M])||!n(Pe,O[M])))return!1}else if(O[M]!==U[M])return!1}return!0},x=b.useMemo(()=>{const U=!h||!t?!1:C(a)?k().isPaused()||r?!1:u!==!1:a,O=$=>{const ne=se($);return delete ne._k,U?{isValidating:!0,isLoading:!0,...ne}:ne},N=E(),M=F(),X=O(N),ge=N===M?X:O(M);let D=X;return[()=>{const $=O(E());return de($,D)?(D.data=$.data,D.isLoading=$.isLoading,D.isValidating=$.isValidating,D.error=$.error,D):(D=$,$)},()=>ge]},[s,h]),q=dn.useSyncExternalStore(b.useCallback(U=>B(h,(O,N)=>{de(N,O)||U()}),[s,h]),x[0],x[1]),fe=!y.current,Ye=f[h]&&f[h].length>0,oe=q.data,W=C(oe)?ee&&kn(ee)?rt(ee):ee:oe,pe=q.error,me=b.useRef(W),Pe=p?C(oe)?C(me.current)?W:me.current:oe:W,Me=Ye&&!C(pe)?!1:fe&&!C(a)?a:k().isPaused()?!1:r?C(W)?!1:u:C(W)||u,Lt=!!(h&&t&&fe&&Me),Jn=C(q.isValidating)?Lt:q.isValidating,qn=C(q.isLoading)?Lt:q.isLoading,Se=b.useCallback(async U=>{const O=w.current;if(!h||!O||I.current||k().isPaused())return!1;let N,M,X=!0;const ge=U||{},D=!_[h]||!ge.dedupe,$=()=>Vt?!I.current&&h===S.current&&y.current:h===S.current,ne={isValidating:!1,isLoading:!1},At=()=>{z(ne)},zt=()=>{const G=_[h];G&&G[1]===M&&delete _[h]},xt={isValidating:!0};C(E().data)&&(xt.isLoading=!0);try{if(D&&(z(xt),i.loadingTimeout&&C(E().data)&&setTimeout(()=>{X&&$()&&k().onLoadingSlow(h,i)},i.loadingTimeout),_[h]=[O(v),bt()]),[N,M]=_[h],N=await N,D&&setTimeout(zt,i.dedupingInterval),!_[h]||_[h][1]!==M)return D&&$()&&k().onDiscarded(h),!1;ne.error=T;const G=g[h];if(!C(G)&&(M<=G[0]||M<=G[1]||G[1]===0))return At(),D&&$()&&k().onDiscarded(h),!1;const re=E().data;ne.data=n(re,N)?re:N,D&&$()&&k().onSuccess(N,h,i)}catch(G){zt();const re=k(),{shouldRetryOnError:Xe}=re;re.isPaused()||(ne.error=G,D&&$()&&(re.onError(G,h,re),(Xe===!0||Y(Xe)&&Xe(G))&&(!k().revalidateOnFocus||!k().revalidateOnReconnect||A())&&re.onErrorRetry(G,h,re,Gn=>{const Ze=f[h];Ze&&Ze[0]&&Ze[0](Nt,Gn)},{retryCount:(ge.retryCount||0)+1,dedupe:!0})))}return X=!1,At(),!0},[h,s]),Tt=b.useCallback((...U)=>bn(s,S.current,...U),[]);if(ye(()=>{w.current=t,R.current=i,C(oe)||(me.current=oe)}),ye(()=>{if(!h)return;const U=Se.bind(T,it);let O=0;k().revalidateOnFocus&&(O=Date.now()+k().focusThrottleInterval);const M=Br(h,f,(X,ge={})=>{if(X==gn){const D=Date.now();k().revalidateOnFocus&&D>O&&A()&&(O=D+k().focusThrottleInterval,U())}else if(X==vn)k().revalidateOnReconnect&&A()&&U();else{if(X==_n)return Se();if(X==Nt)return Se(ge)}});return I.current=!1,S.current=h,y.current=!0,z({_k:v}),Me&&(C(W)||Oe?U():jr(U)),()=>{I.current=!0,M()}},[h]),ye(()=>{let U;function O(){const M=Y(d)?d(E().data):d;M&&U!==-1&&(U=setTimeout(N,M))}function N(){!E().error&&(c||k().isVisible())&&(m||k().isOnline())?Se(it).then(O):O()}return O(),()=>{U&&(clearTimeout(U),U=-1)}},[d,c,m,h]),b.useDebugValue(Pe),r&&C(W)&&h){if(!Vt&&Oe)throw new Error("Fallback data is required when using Suspense in SSR.");w.current=t,R.current=i,I.current=!1;const U=P[h];if(!C(U)){const O=Tt(U);rt(O)}if(C(pe)){const O=Se(it);C(Pe)||(O.status="fulfilled",O.value=!0),rt(O)}else throw pe}return{mutate:Tt,get data(){return V.data=!0,Pe},get error(){return V.error=!0,pe},get isValidating(){return V.isValidating=!0,Jn},get isLoading(){return V.isLoading=!0,qn}}},Xr=Be.defineProperty(Ar,"defaultValue",{value:Sn}),Ut=Nr(Yr),Zr=Object.freeze(Object.defineProperty({__proto__:null,SWRConfig:Xr,default:Ut,mutate:Pn,preload:Wr,unstable_serialize:Gr,useSWRConfig:Un},Symbol.toStringTag,{value:"Module"})),Qr=()=>{},ei=Qr(),yt=Object,Jt=e=>e===ei,ti=e=>typeof e=="function",ze=new WeakMap,st=(e,t)=>yt.prototype.toString.call(e)===`[object ${t}]`;let ni=0;const wt=e=>{const t=typeof e,i=st(e,"Date"),s=st(e,"RegExp"),n=st(e,"Object");let r,o;if(yt(e)===e&&!i&&!s){if(r=ze.get(e),r)return r;if(r=++ni+"~",ze.set(e,r),Array.isArray(e)){for(r="@",o=0;o<e.length;o++)r+=wt(e[o])+",";ze.set(e,r)}if(n){r="#";const a=yt.keys(e).sort();for(;!Jt(o=a.pop());)Jt(e[o])||(r+=o+":"+wt(e[o])+",");ze.set(e,r)}}else r=i?e.toJSON():t=="symbol"?e.toString():t=="string"?JSON.stringify(e):""+e;return r},ri=e=>{if(ti(e))try{e=e()}catch{e=""}const t=e;return e=typeof e=="string"?e:(Array.isArray(e)?e.length:e)?wt(e):"",[e,t]},ii=e=>ri(e?e(0,null):null)[0],ot=Promise.resolve(),si=e=>(t,i,s)=>{const n=b.useRef(!1),{cache:r,initialSize:o=1,revalidateAll:a=!1,persistSize:u=!1,revalidateFirstPage:d=!0,revalidateOnMount:c=!1,parallel:m=!1}=s,[,,,p]=Z.get(Ie);let f;try{f=ii(t),f&&(f=wn+f)}catch{}const[g,_,P]=le(r,f),h=b.useCallback(()=>C(g()._l)?o:g()._l,[r,f,o]);dn.useSyncExternalStore(b.useCallback(k=>f?P(f,()=>{k()}):()=>{},[r,f]),h,h);const v=b.useCallback(()=>{const k=g()._l;return C(k)?o:k},[f,o]),y=b.useRef(v());ye(()=>{if(!n.current){n.current=!0;return}f&&_({_l:u?y.current:v()})},[f,r]);const I=c&&!n.current,S=e(f,async k=>{const A=g()._i,E=g()._r;_({_r:T});const z=[],B=v(),[F]=le(r,k),V=F().data,ee=[];let de=null;for(let x=0;x<B;++x){const[q,fe]=be(t(x,m?null:de));if(!q)break;const[Ye,oe]=le(r,q);let W=Ye().data;const pe=a||A||C(W)||d&&!x&&!C(V)||I||V&&!C(V[x])&&!s.compare(V[x],W);if(i&&(typeof E=="function"?E(W,fe):pe)){const me=async()=>{if(!(q in p))W=await i(fe);else{const Me=p[q];delete p[q],W=await Me}oe({data:W,_k:fe}),z[x]=W};m?ee.push(me):await me()}else z[x]=W;m||(de=W)}return m&&await Promise.all(ee.map(x=>x())),_({_i:T}),z},s),w=b.useCallback(function(k,A){const E=typeof A=="boolean"?{revalidate:A}:A||{},z=E.revalidate!==!1;return f?(z&&(C(k)?_({_i:!0,_r:E.revalidate}):_({_i:!1,_r:E.revalidate})),arguments.length?S.mutate(k,{...E,revalidate:z}):S.mutate()):ot},[f,r]),R=b.useCallback(k=>{if(!f)return ot;const[,A]=le(r,f);let E;if(Y(k)?E=k(v()):typeof k=="number"&&(E=k),typeof E!="number")return ot;A({_l:E}),y.current=E;const z=[],[B]=le(r,f);let F=null;for(let V=0;V<E;++V){const[ee]=be(t(V,F)),[de]=le(r,ee),x=ee?de().data:T;if(C(x))return w(B().data);z.push(x),F=x}return w(z)},[f,r,w,v]);return{size:v(),setSize:R,mutate:w,get data(){return S.data},get error(){return S.error},get isValidating(){return S.isValidating},get isLoading(){return S.isLoading}}},oi=Vr(Ut,si);var qt=Object.prototype.hasOwnProperty;function Gt(e,t,i){for(i of e.keys())if(we(i,t))return i}function we(e,t){var i,s,n;if(e===t)return!0;if(e&&t&&(i=e.constructor)===t.constructor){if(i===Date)return e.getTime()===t.getTime();if(i===RegExp)return e.toString()===t.toString();if(i===Array){if((s=e.length)===t.length)for(;s--&&we(e[s],t[s]););return s===-1}if(i===Set){if(e.size!==t.size)return!1;for(s of e)if(n=s,n&&typeof n=="object"&&(n=Gt(t,n),!n)||!t.has(n))return!1;return!0}if(i===Map){if(e.size!==t.size)return!1;for(s of e)if(n=s[0],n&&typeof n=="object"&&(n=Gt(t,n),!n)||!we(s[1],t.get(n)))return!1;return!0}if(i===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(i===DataView){if((s=e.byteLength)===t.byteLength)for(;s--&&e.getInt8(s)===t.getInt8(s););return s===-1}if(ArrayBuffer.isView(e)){if((s=e.byteLength)===t.byteLength)for(;s--&&e[s]===t[s];);return s===-1}if(!i||typeof e=="object"){s=0;for(i in e)if(qt.call(e,i)&&++s&&!qt.call(t,i)||!(i in t)||!we(e[i],t[i]))return!1;return Object.keys(t).length===s}}return e!==e&&t!==t}function ai(e,t){if(!e)throw typeof t=="string"?new Error(t):new Error(`${t.displayName} not found`)}var Ce=(e,t)=>{const{assertCtxFn:i=ai}={},s=l.createContext(void 0);return s.displayName=e,[s,()=>{const o=l.useContext(s);return i(o,`${e} not found`),o.value},()=>{const o=l.useContext(s);return o?o.value:{}}]},It={};tr(It,{useSWR:()=>Ut,useSWRInfinite:()=>oi});rr(It,Zr);var[In,li]=Ce("ClerkInstanceContext"),[ui,Ts]=Ce("UserContext"),[ci,As]=Ce("ClientContext"),[di,zs]=Ce("SessionContext");l.createContext({});var[hi,xs]=Ce("OrganizationContext"),fi=({children:e,organization:t,swrConfig:i})=>l.createElement(It.SWRConfig,{value:i},l.createElement(hi.Provider,{value:{value:{organization:t}}},e));function pi(e){if(!l.useContext(In)){if(typeof e=="function"){e();return}throw new Error(`${e} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}typeof window<"u"?l.useLayoutEffect:l.useEffect;var Yt=we,mi=()=>{try{return!1}catch{}return!1},gi=()=>{try{return!1}catch{}return!1},vi=()=>{try{return!0}catch{}return!1},Xt=new Set,jt=(e,t,i)=>{const s=gi()||vi(),n=e;Xt.has(n)||s||(Xt.add(n),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},te=hn({packageName:"@clerk/clerk-react"});function _i(e){te.setMessages(e).setPackageName(e)}var[ki,bi]=Ce("AuthContext"),Ci=In,jn=li,Pi="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",Si=e=>`You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,yi="Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support",at="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",wi="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",Ei="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",Oi="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",Ui="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",Ii=e=>`<${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,ji=e=>`Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,Ri=e=>`Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,Mi="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",Li="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",Ti="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",Ai="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",zi="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",xi="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",Wi="Missing props. <UserButton.Action /> component requires the following props: label.",Rt=e=>{pi(()=>{te.throwMissingClerkProviderError({source:e})})},Rn=e=>new Promise(t=>{const i=s=>{["ready","degraded"].includes(s)&&(t(),e.off("status",i))};e.on("status",i,{notify:!0})}),Di=e=>async t=>(await Rn(e),e.session?e.session.getToken(t):null),Fi=e=>async(...t)=>(await Rn(e),e.signOut(...t)),Ni=(e={})=>{var t,i;Rt("useAuth");const{treatPendingAsSignedOut:s,...n}=e??{},r=n;let a=bi();a.sessionId===void 0&&a.userId===void 0&&(a=r??{});const u=jn(),d=b.useCallback(Di(u),[u]),c=b.useCallback(Fi(u),[u]);return(t=u.telemetry)==null||t.record(Cr("useAuth",{treatPendingAsSignedOut:s})),Bi({...a,getToken:d,signOut:c},{treatPendingAsSignedOut:s??((i=u.__internal_getOption)==null?void 0:i.call(u,"treatPendingAsSignedOut"))})};function Bi(e,{treatPendingAsSignedOut:t=!0}={}){const{userId:i,orgId:s,orgRole:n,has:r,signOut:o,getToken:a,orgPermissions:u,factorVerificationAge:d,sessionClaims:c}=e??{},m=b.useCallback(f=>r?r(f):mr({userId:i,orgId:s,orgRole:n,orgPermissions:u,factorVerificationAge:d,features:(c==null?void 0:c.fea)||"",plans:(c==null?void 0:c.pla)||""})(f),[r,i,s,n,u,d]),p=gr({authObject:{...e,getToken:a,signOut:o,has:m},options:{treatPendingAsSignedOut:t}});return p||te.throw(yi)}var L=(e,t)=>{const s=(typeof t=="string"?t:t==null?void 0:t.component)||e.displayName||e.name||"Component";e.displayName=s;const n=typeof t=="string"?void 0:t,r=o=>{Rt(s||"withClerk");const a=jn();return!a.loaded&&!(n!=null&&n.renderWhileLoading)?null:l.createElement(e,{...o,component:s,clerk:a})};return r.displayName=`withClerk(${s})`,r},Ws=({children:e,treatPendingAsSignedOut:t})=>{Rt("SignedIn");const{userId:i}=Ni({treatPendingAsSignedOut:t});return i?e:null};L(({clerk:e,...t})=>{const{client:i,session:s}=e,n=i.signedInSessions?i.signedInSessions.length>0:i.activeSessions&&i.activeSessions.length>0;return l.useEffect(()=>{s===null&&n?e.redirectToAfterSignOut():e.redirectToSignIn(t)},[]),null},"RedirectToSignIn");L(({clerk:e,...t})=>(l.useEffect(()=>{e.redirectToSignUp(t)},[]),null),"RedirectToSignUp");L(({clerk:e})=>(l.useEffect(()=>{jt("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),e.redirectToUserProfile()},[]),null),"RedirectToUserProfile");L(({clerk:e})=>(l.useEffect(()=>{jt("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),e.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile");L(({clerk:e})=>(l.useEffect(()=>{jt("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),e.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization");L(({clerk:e,...t})=>(l.useEffect(()=>{e.handleRedirectCallback(t)},[]),null),"AuthenticateWithRedirectCallback");var Mn=e=>{throw TypeError(e)},Mt=(e,t,i)=>t.has(e)||Mn("Cannot "+i),j=(e,t,i)=>(Mt(e,t,"read from private field"),i?i.call(e):t.get(e)),he=(e,t,i)=>t.has(e)?Mn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,i),ve=(e,t,i,s)=>(Mt(e,t,"write to private field"),t.set(e,i),i),Zt=(e,t,i)=>(Mt(e,t,"access private method"),i),Vi=(e,t="5.67.0")=>{if(e)return e;const i=$i(t);return i?i==="snapshot"?"5.67.0":i:Hi(t)},$i=e=>{var t;return(t=e.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/))==null?void 0:t[1]},Hi=e=>e.trim().replace(/^v/,"").split(".")[0];function Ki(e){return e?Ji(e)||Ln(e):!0}function Ji(e){return/^http(s)?:\/\//.test(e||"")}function Ln(e){return e.startsWith("/")}function qi(e){return e?Ln(e)?new URL(e,window.location.origin).toString():e:""}function Gi(e){if(!e)return"";let t;if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}return`clerk.${e.replace(t,"")}`}var Yi={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},Xi=100,Tn=async e=>new Promise(t=>setTimeout(t,e)),An=(e,t)=>t?e*(1+Math.random()):e,Zi=e=>{let t=0;const i=()=>{const s=e.initialDelay,n=e.factor;let r=s*Math.pow(n,t);return r=An(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await Tn(i()),t++}},Qi=async(e,t={})=>{let i=0;const{shouldRetry:s,initialDelay:n,maxDelayBetweenRetries:r,factor:o,retryImmediately:a,jitter:u}={...Yi,...t},d=Zi({initialDelay:n,maxDelayBetweenRetries:r,factor:o,jitter:u});for(;;)try{return await e()}catch(c){if(i++,!s(c,i))throw c;a&&i===1?await Tn(An(Xi,u)):await d()}},es="loadScript cannot be called when document does not exist",ts="loadScript cannot be called without a src";async function ns(e="",t){const{async:i,defer:s,beforeLoad:n,crossOrigin:r,nonce:o}=t||{};return Qi(()=>new Promise((u,d)=>{e||d(new Error(ts)),(!document||!document.body)&&d(es);const c=document.createElement("script");r&&c.setAttribute("crossorigin",r),c.async=i||!1,c.defer=s||!1,c.addEventListener("load",()=>{c.remove(),u(c)}),c.addEventListener("error",()=>{c.remove(),d()}),c.src=e,c.nonce=o,n==null||n(c),document.body.appendChild(c)}),{shouldRetry:(u,d)=>d<=5})}var Qt="Clerk: Failed to load Clerk",{isDevOrStagingUrl:rs}=kr(),zn=hn({packageName:"@clerk/shared"});function is(e){zn.setPackageName({packageName:e})}var ss=async e=>{const t=document.querySelector("script[data-clerk-js-script]");if(t)return new Promise((i,s)=>{t.addEventListener("load",()=>{i(t)}),t.addEventListener("error",()=>{s(Qt)})});if(!(e!=null&&e.publishableKey)){zn.throwMissingPublishableKeyError();return}return ns(os(e),{async:!0,crossOrigin:"anonymous",nonce:e.nonce,beforeLoad:ls(e)}).catch(()=>{throw new Error(Qt)})},os=e=>{var c,m;const{clerkJSUrl:t,clerkJSVariant:i,clerkJSVersion:s,proxyUrl:n,domain:r,publishableKey:o}=e;if(t)return t;let a="";n&&Ki(n)?a=qi(n).replace(/http(s)?:\/\//,""):r&&!rs(((c=Ft(o))==null?void 0:c.frontendApi)||"")?a=Gi(r):a=((m=Ft(o))==null?void 0:m.frontendApi)||"";const u=i?`${i.replace(/\.+$/,"")}.`:"",d=Vi(s);return`https://${a}/npm/@clerk/clerk-js@${d}/dist/clerk.${u}browser.js`},as=e=>{const t={};return e.publishableKey&&(t["data-clerk-publishable-key"]=e.publishableKey),e.proxyUrl&&(t["data-clerk-proxy-url"]=e.proxyUrl),e.domain&&(t["data-clerk-domain"]=e.domain),e.nonce&&(t.nonce=e.nonce),t},ls=e=>t=>{const i=as(e);for(const s in i)t.setAttribute(s,i[s])},K=e=>{mi()&&console.error(`Clerk: ${e}`)};function lt(e,t,i){if(typeof e=="function")return e(t);if(typeof e<"u")return e;if(typeof i<"u")return i}var en=(e,...t)=>{const i={...e};for(const s of t)delete i[s];return i},us=(e,t,i)=>!e&&i?cs(i):ds(t),cs=e=>{const t=e.userId,i=e.user,s=e.sessionId,n=e.sessionStatus,r=e.sessionClaims,o=e.session,a=e.organization,u=e.orgId,d=e.orgRole,c=e.orgPermissions,m=e.orgSlug,p=e.actor,f=e.factorVerificationAge;return{userId:t,user:i,sessionId:s,session:o,sessionStatus:n,sessionClaims:r,organization:a,orgId:u,orgRole:d,orgPermissions:c,orgSlug:m,actor:p,factorVerificationAge:f}},ds=e=>{var _,P,h,v;const t=e.user?e.user.id:e.user,i=e.user,s=e.session?e.session.id:e.session,n=e.session,r=(_=e.session)==null?void 0:_.status,o=e.session?(h=(P=e.session.lastActiveToken)==null?void 0:P.jwt)==null?void 0:h.claims:null,a=e.session?e.session.factorVerificationAge:null,u=n==null?void 0:n.actor,d=e.organization,c=e.organization?e.organization.id:e.organization,m=d==null?void 0:d.slug,p=d&&((v=i==null?void 0:i.organizationMemberships)==null?void 0:v.find(y=>y.organization.id===c)),f=p&&p.permissions,g=p&&p.role;return{userId:t,user:i,sessionId:s,session:n,sessionStatus:r,sessionClaims:o,organization:d,orgId:c,orgRole:g,orgSlug:m,orgPermissions:f,actor:u,factorVerificationAge:a}};function tn(){return typeof window<"u"}var nn=(e,t,i,s,n)=>{const{notify:r}=n||{};let o=e.get(i);o||(o=[],e.set(i,o)),o.push(s),r&&t.has(i)&&s(t.get(i))},rn=(e,t,i)=>(e.get(t)||[]).map(s=>s(i)),sn=(e,t,i)=>{const s=e.get(t);s&&(i?s.splice(s.indexOf(i)>>>0,1):e.set(t,[]))},hs=()=>{const e=new Map,t=new Map,i=new Map;return{on:(...n)=>nn(e,t,...n),prioritizedOn:(...n)=>nn(i,t,...n),emit:(n,r)=>{t.set(n,r),rn(i,n,r),rn(e,n,r)},off:(...n)=>sn(e,...n),prioritizedOff:(...n)=>sn(i,...n),internal:{retrieveListeners:n=>e.get(n)||[]}}},xe={Status:"status"},fs=()=>hs();typeof window<"u"&&!window.global&&(window.global=typeof global>"u"?window:global);var Ve=e=>t=>{try{return l.Children.only(e)}catch{return te.throw(Si(t))}},$e=(e,t)=>(e||(e=t),typeof e=="string"&&(e=l.createElement("button",null,e)),e),He=e=>(...t)=>{if(e&&typeof e=="function")return e(...t)};function ps(e){return typeof e=="function"}var We=new Map;function ms(e,t,i=1){l.useEffect(()=>{const s=We.get(e)||0;return s==i?te.throw(t):(We.set(e,s+1),()=>{We.set(e,(We.get(e)||1)-1)})},[])}function gs(e,t,i){const s=e.displayName||e.name||t||"Component",n=r=>(ms(t,i),l.createElement(e,{...r}));return n.displayName=`withMaxAllowedInstancesGuard(${s})`,n}var Ee=e=>{const t=Array(e.length).fill(null),[i,s]=b.useState(t);return e.map((n,r)=>({id:n.id,mount:o=>s(a=>a.map((u,d)=>d===r?o:u)),unmount:()=>s(o=>o.map((a,u)=>u===r?null:a)),portal:()=>l.createElement(l.Fragment,null,i[r]?Yn.createPortal(n.component,i[r]):null)}))},H=(e,t)=>!!e&&l.isValidElement(e)&&(e==null?void 0:e.type)===t,xn=(e,t)=>Fn({children:e,reorderItemsLabels:["account","security"],LinkComponent:Re,PageComponent:je,MenuItemsComponent:Je,componentName:"UserProfile"},t),Wn=(e,t)=>Fn({children:e,reorderItemsLabels:["general","members"],LinkComponent:Ge,PageComponent:qe,componentName:"OrganizationProfile"},t),Dn=e=>{const t=[],i=[Ge,qe,Je,je,Re];return l.Children.forEach(e,s=>{i.some(n=>H(s,n))||t.push(s)}),t},Fn=(e,t)=>{const{children:i,LinkComponent:s,PageComponent:n,MenuItemsComponent:r,reorderItemsLabels:o,componentName:a}=e,{allowForAnyChildren:u=!1}=t||{},d=[];l.Children.forEach(i,v=>{if(!H(v,n)&&!H(v,s)&&!H(v,r)){v&&!u&&K(Ii(a));return}const{props:y}=v,{children:I,label:S,url:w,labelIcon:R}=y;if(H(v,n))if(on(y,o))d.push({label:S});else if(ut(y))d.push({label:S,labelIcon:R,children:I,url:w});else{K(ji(a));return}if(H(v,s))if(ct(y))d.push({label:S,labelIcon:R,url:w});else{K(Ri(a));return}});const c=[],m=[],p=[];d.forEach((v,y)=>{if(ut(v)){c.push({component:v.children,id:y}),m.push({component:v.labelIcon,id:y});return}ct(v)&&p.push({component:v.labelIcon,id:y})});const f=Ee(c),g=Ee(m),_=Ee(p),P=[],h=[];return d.forEach((v,y)=>{if(on(v,o)){P.push({label:v.label});return}if(ut(v)){const{portal:I,mount:S,unmount:w}=f.find(E=>E.id===y),{portal:R,mount:k,unmount:A}=g.find(E=>E.id===y);P.push({label:v.label,url:v.url,mount:S,unmount:w,mountIcon:k,unmountIcon:A}),h.push(I),h.push(R);return}if(ct(v)){const{portal:I,mount:S,unmount:w}=_.find(R=>R.id===y);P.push({label:v.label,url:v.url,mountIcon:S,unmountIcon:w}),h.push(I);return}}),{customPages:P,customPagesPortals:h}},on=(e,t)=>{const{children:i,label:s,url:n,labelIcon:r}=e;return!i&&!n&&!r&&t.some(o=>o===s)},ut=e=>{const{children:t,label:i,url:s,labelIcon:n}=e;return!!t&&!!s&&!!n&&!!i},ct=e=>{const{children:t,label:i,url:s,labelIcon:n}=e;return!t&&!!s&&!!n&&!!i},vs=e=>_s({children:e,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:Je,MenuActionComponent:Bn,MenuLinkComponent:Vn,UserProfileLinkComponent:Re,UserProfilePageComponent:je}),_s=({children:e,MenuItemsComponent:t,MenuActionComponent:i,MenuLinkComponent:s,UserProfileLinkComponent:n,UserProfilePageComponent:r,reorderItemsLabels:o})=>{const a=[],u=[],d=[];l.Children.forEach(e,g=>{if(!H(g,t)&&!H(g,n)&&!H(g,r)){g&&K(Mi);return}if(H(g,n)||H(g,r))return;const{props:_}=g;l.Children.forEach(_.children,P=>{if(!H(P,i)&&!H(P,s)){P&&K(Li);return}const{props:h}=P,{label:v,labelIcon:y,href:I,onClick:S,open:w}=h;if(H(P,i))if(an(h,o))a.push({label:v});else if(dt(h)){const R={label:v,labelIcon:y};if(S!==void 0)a.push({...R,onClick:S});else if(w!==void 0)a.push({...R,open:w.startsWith("/")?w:`/${w}`});else{K("Custom menu item must have either onClick or open property");return}}else{K(Wi);return}if(H(P,s))if(ht(h))a.push({label:v,labelIcon:y,href:I});else{K(xi);return}})});const c=[],m=[];a.forEach((g,_)=>{dt(g)&&c.push({component:g.labelIcon,id:_}),ht(g)&&m.push({component:g.labelIcon,id:_})});const p=Ee(c),f=Ee(m);return a.forEach((g,_)=>{if(an(g,o)&&u.push({label:g.label}),dt(g)){const{portal:P,mount:h,unmount:v}=p.find(I=>I.id===_),y={label:g.label,mountIcon:h,unmountIcon:v};"onClick"in g?y.onClick=g.onClick:"open"in g&&(y.open=g.open),u.push(y),d.push(P)}if(ht(g)){const{portal:P,mount:h,unmount:v}=f.find(y=>y.id===_);u.push({label:g.label,href:g.href,mountIcon:h,unmountIcon:v}),d.push(P)}}),{customMenuItems:u,customMenuItemsPortals:d}},an=(e,t)=>{const{children:i,label:s,onClick:n,labelIcon:r}=e;return!i&&!n&&!r&&t.some(o=>o===s)},dt=e=>{const{label:t,labelIcon:i,onClick:s,open:n}=e;return!!i&&!!t&&(typeof s=="function"||typeof n=="string")},ht=e=>{const{label:t,href:i,labelIcon:s}=e;return!!i&&!!s&&!!t};function ks(e){const{root:t=document==null?void 0:document.body,selector:i,timeout:s=0}=e;return new Promise((n,r)=>{if(!t){r(new Error("No root element provided"));return}let o=t;if(i&&(o=t==null?void 0:t.querySelector(i)),(o==null?void 0:o.childElementCount)&&o.childElementCount>0){n();return}const u=new MutationObserver(d=>{for(const c of d)if(c.type==="childList"&&(!o&&i&&(o=t==null?void 0:t.querySelector(i)),o!=null&&o.childElementCount&&o.childElementCount>0)){u.disconnect(),n();return}});u.observe(t,{childList:!0,subtree:!0}),s>0&&setTimeout(()=>{u.disconnect(),r(new Error("Timeout waiting for element children"))},s)})}function Q(e){const t=b.useRef(),[i,s]=b.useState("rendering");return b.useEffect(()=>{if(!e)throw new Error("Clerk: no component name provided, unable to detect mount.");typeof window<"u"&&!t.current&&(t.current=ks({selector:`[data-clerk-component="${e}"]`}).then(()=>{s("rendered")}).catch(()=>{s("error")}))},[e]),i}var De=e=>"mount"in e,ln=e=>"open"in e,un=e=>e==null?void 0:e.map(({mountIcon:t,unmountIcon:i,...s})=>s),J=class extends l.PureComponent{constructor(){super(...arguments),this.rootRef=l.createRef()}componentDidUpdate(e){var t,i,s,n;if(!De(e)||!De(this.props))return;const r=en(e.props,"customPages","customMenuItems","children"),o=en(this.props.props,"customPages","customMenuItems","children"),a=((t=r.customPages)==null?void 0:t.length)!==((i=o.customPages)==null?void 0:i.length),u=((s=r.customMenuItems)==null?void 0:s.length)!==((n=o.customMenuItems)==null?void 0:n.length),d=un(e.props.customMenuItems),c=un(this.props.props.customMenuItems);(!Yt(r,o)||!Yt(d,c)||a||u)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(De(this.props)&&this.props.mount(this.rootRef.current,this.props.props),ln(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(De(this.props)&&this.props.unmount(this.rootRef.current),ln(this.props)&&this.props.close())}render(){const{hideRootHtmlElement:e=!1}=this.props,t={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return l.createElement(l.Fragment,null,!e&&l.createElement("div",{...t}),this.props.children)}},Ke=e=>{var t,i;return l.createElement(l.Fragment,null,(t=e==null?void 0:e.customPagesPortals)==null?void 0:t.map((s,n)=>b.createElement(s,{key:n})),(i=e==null?void 0:e.customMenuItemsPortals)==null?void 0:i.map((s,n)=>b.createElement(s,{key:n})))},Ds=L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}};return l.createElement(l.Fragment,null,r&&i,e.loaded&&l.createElement(J,{component:t,mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:s,rootProps:o}))},{component:"SignIn",renderWhileLoading:!0}),Fs=L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}};return l.createElement(l.Fragment,null,r&&i,e.loaded&&l.createElement(J,{component:t,mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:s,rootProps:o}))},{component:"SignUp",renderWhileLoading:!0});function je({children:e}){return K(wi),l.createElement(l.Fragment,null,e)}function Re({children:e}){return K(Ei),l.createElement(l.Fragment,null,e)}var bs=L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}},{customPages:a,customPagesPortals:u}=xn(s.children);return l.createElement(l.Fragment,null,r&&i,l.createElement(J,{component:t,mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...s,customPages:a},rootProps:o},l.createElement(Ke,{customPagesPortals:u})))},{component:"UserProfile",renderWhileLoading:!0});Object.assign(bs,{Page:je,Link:Re});var Nn=b.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),Cs=L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}},{customPages:a,customPagesPortals:u}=xn(s.children,{allowForAnyChildren:!!s.__experimental_asProvider}),d=Object.assign(s.userProfileProps||{},{customPages:a}),{customMenuItems:c,customMenuItemsPortals:m}=vs(s.children),p=Dn(s.children),f={mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...s,userProfileProps:d,customMenuItems:c}},g={customPagesPortals:u,customMenuItemsPortals:m};return l.createElement(Nn.Provider,{value:f},r&&i,e.loaded&&l.createElement(J,{component:t,...f,hideRootHtmlElement:!!s.__experimental_asProvider,rootProps:o},s.__experimental_asProvider?p:null,l.createElement(Ke,{...g})))},{component:"UserButton",renderWhileLoading:!0});function Je({children:e}){return K(Ti),l.createElement(l.Fragment,null,e)}function Bn({children:e}){return K(Ai),l.createElement(l.Fragment,null,e)}function Vn({children:e}){return K(zi),l.createElement(l.Fragment,null,e)}function Ps(e){const t=b.useContext(Nn),i={...t,props:{...t.props,...e}};return l.createElement(J,{...i})}var Ns=Object.assign(Cs,{UserProfilePage:je,UserProfileLink:Re,MenuItems:Je,Action:Bn,Link:Vn,__experimental_Outlet:Ps});function qe({children:e}){return K(Oi),l.createElement(l.Fragment,null,e)}function Ge({children:e}){return K(Ui),l.createElement(l.Fragment,null,e)}var Ss=L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}},{customPages:a,customPagesPortals:u}=Wn(s.children);return l.createElement(l.Fragment,null,r&&i,e.loaded&&l.createElement(J,{component:t,mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...s,customPages:a},rootProps:o},l.createElement(Ke,{customPagesPortals:u})))},{component:"OrganizationProfile",renderWhileLoading:!0});Object.assign(Ss,{Page:qe,Link:Ge});L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}};return l.createElement(l.Fragment,null,r&&i,e.loaded&&l.createElement(J,{component:t,mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:s,rootProps:o}))},{component:"CreateOrganization",renderWhileLoading:!0});var $n=b.createContext({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),ys=L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}},{customPages:a,customPagesPortals:u}=Wn(s.children,{allowForAnyChildren:!!s.__experimental_asProvider}),d=Object.assign(s.organizationProfileProps||{},{customPages:a}),c=Dn(s.children),m={mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...s,organizationProfileProps:d},rootProps:o,component:t};return e.__experimental_prefetchOrganizationSwitcher(),l.createElement($n.Provider,{value:m},l.createElement(l.Fragment,null,r&&i,e.loaded&&l.createElement(J,{...m,hideRootHtmlElement:!!s.__experimental_asProvider},s.__experimental_asProvider?c:null,l.createElement(Ke,{customPagesPortals:u}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0});function ws(e){const t=b.useContext($n),i={...t,props:{...t.props,...e}};return l.createElement(J,{...i})}Object.assign(ys,{OrganizationProfilePage:qe,OrganizationProfileLink:Ge,__experimental_Outlet:ws});L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}};return l.createElement(l.Fragment,null,r&&i,e.loaded&&l.createElement(J,{component:t,mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:s,rootProps:o}))},{component:"OrganizationList",renderWhileLoading:!0});L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}};return l.createElement(l.Fragment,null,r&&i,e.loaded&&l.createElement(J,{component:t,open:e.openGoogleOneTap,close:e.closeGoogleOneTap,updateProps:e.__unstable__updateProps,props:s,rootProps:o}))},{component:"GoogleOneTap",renderWhileLoading:!0});L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}};return l.createElement(l.Fragment,null,r&&i,e.loaded&&l.createElement(J,{component:t,mount:e.mountWaitlist,unmount:e.unmountWaitlist,updateProps:e.__unstable__updateProps,props:s,rootProps:o}))},{component:"Waitlist",renderWhileLoading:!0});L(({clerk:e,component:t,fallback:i,...s})=>{const r=Q(t)==="rendering"||!e.loaded,o={...r&&i&&{style:{display:"none"}}};return l.createElement(l.Fragment,null,r&&i,e.loaded&&l.createElement(J,{component:t,mount:e.mountPricingTable,unmount:e.unmountPricingTable,updateProps:e.__unstable__updateProps,props:s,rootProps:o}))},{component:"PricingTable",renderWhileLoading:!0});L(({clerk:e,children:t,...i})=>{const{signUpFallbackRedirectUrl:s,forceRedirectUrl:n,fallbackRedirectUrl:r,signUpForceRedirectUrl:o,mode:a,initialValues:u,withSignUp:d,oauthFlow:c,...m}=i;t=$e(t,"Sign in");const p=Ve(t)("SignInButton"),f=()=>{const P={forceRedirectUrl:n,fallbackRedirectUrl:r,signUpFallbackRedirectUrl:s,signUpForceRedirectUrl:o,initialValues:u,withSignUp:d,oauthFlow:c};return a==="modal"?e.openSignIn({...P,appearance:i.appearance}):e.redirectToSignIn({...P,signInFallbackRedirectUrl:r,signInForceRedirectUrl:n})},_={...m,onClick:async P=>(p&&typeof p=="object"&&"props"in p&&await He(p.props.onClick)(P),f())};return l.cloneElement(p,_)},{component:"SignInButton",renderWhileLoading:!0});L(({clerk:e,children:t,...i})=>{const{fallbackRedirectUrl:s,forceRedirectUrl:n,signInFallbackRedirectUrl:r,signInForceRedirectUrl:o,mode:a,unsafeMetadata:u,initialValues:d,oauthFlow:c,...m}=i;t=$e(t,"Sign up");const p=Ve(t)("SignUpButton"),f=()=>{const P={fallbackRedirectUrl:s,forceRedirectUrl:n,signInFallbackRedirectUrl:r,signInForceRedirectUrl:o,unsafeMetadata:u,initialValues:d,oauthFlow:c};return a==="modal"?e.openSignUp({...P,appearance:i.appearance}):e.redirectToSignUp({...P,signUpFallbackRedirectUrl:s,signUpForceRedirectUrl:n})},_={...m,onClick:async P=>(p&&typeof p=="object"&&"props"in p&&await He(p.props.onClick)(P),f())};return l.cloneElement(p,_)},{component:"SignUpButton",renderWhileLoading:!0});L(({clerk:e,children:t,...i})=>{const{redirectUrl:s="/",signOutOptions:n,...r}=i;t=$e(t,"Sign out");const o=Ve(t)("SignOutButton"),a=()=>e.signOut({redirectUrl:s,...n}),d={...r,onClick:async c=>(await He(o.props.onClick)(c),a())};return l.cloneElement(o,d)},{component:"SignOutButton",renderWhileLoading:!0});L(({clerk:e,children:t,...i})=>{const{redirectUrl:s,...n}=i;t=$e(t,"Sign in with Metamask");const r=Ve(t)("SignInWithMetamaskButton"),o=async()=>{async function d(){await e.authenticateWithMetamask({redirectUrl:s||void 0})}d()},u={...n,onClick:async d=>(await He(r.props.onClick)(d),o())};return l.cloneElement(r,u)},{component:"SignInWithMetamask",renderWhileLoading:!0});typeof globalThis.__BUILD_DISABLE_RHC__>"u"&&(globalThis.__BUILD_DISABLE_RHC__=!1);var Es={name:"@clerk/clerk-react",version:"5.31.4",environment:"production"},Fe,_e,ke,ae,ie,ue,Ne,Et,Hn=class Kn{constructor(t){he(this,Ne),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],he(this,Fe,"loading"),he(this,_e),he(this,ke),he(this,ae),he(this,ie,fs()),this.buildSignInUrl=n=>{const r=()=>{var o;return((o=this.clerkjs)==null?void 0:o.buildSignInUrl(n))||""};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("buildSignInUrl",r)},this.buildSignUpUrl=n=>{const r=()=>{var o;return((o=this.clerkjs)==null?void 0:o.buildSignUpUrl(n))||""};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("buildSignUpUrl",r)},this.buildAfterSignInUrl=(...n)=>{const r=()=>{var o;return((o=this.clerkjs)==null?void 0:o.buildAfterSignInUrl(...n))||""};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("buildAfterSignInUrl",r)},this.buildAfterSignUpUrl=(...n)=>{const r=()=>{var o;return((o=this.clerkjs)==null?void 0:o.buildAfterSignUpUrl(...n))||""};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("buildAfterSignUpUrl",r)},this.buildAfterSignOutUrl=()=>{const n=()=>{var r;return((r=this.clerkjs)==null?void 0:r.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("buildAfterSignOutUrl",n)},this.buildNewSubscriptionRedirectUrl=()=>{const n=()=>{var r;return((r=this.clerkjs)==null?void 0:r.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",n)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{const n=()=>{var r;return((r=this.clerkjs)==null?void 0:r.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",n)},this.buildUserProfileUrl=()=>{const n=()=>{var r;return((r=this.clerkjs)==null?void 0:r.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("buildUserProfileUrl",n)},this.buildCreateOrganizationUrl=()=>{const n=()=>{var r;return((r=this.clerkjs)==null?void 0:r.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("buildCreateOrganizationUrl",n)},this.buildOrganizationProfileUrl=()=>{const n=()=>{var r;return((r=this.clerkjs)==null?void 0:r.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("buildOrganizationProfileUrl",n)},this.buildWaitlistUrl=()=>{const n=()=>{var r;return((r=this.clerkjs)==null?void 0:r.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("buildWaitlistUrl",n)},this.buildUrlWithAuth=n=>{const r=()=>{var o;return((o=this.clerkjs)==null?void 0:o.buildUrlWithAuth(n))||""};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("buildUrlWithAuth",r)},this.handleUnauthenticated=async()=>{const n=()=>{var r;return(r=this.clerkjs)==null?void 0:r.handleUnauthenticated()};this.clerkjs&&this.loaded?n():this.premountMethodCalls.set("handleUnauthenticated",n)},this.on=(...n)=>{var r;if((r=this.clerkjs)!=null&&r.on)return this.clerkjs.on(...n);j(this,ie).on(...n)},this.off=(...n)=>{var r;if((r=this.clerkjs)!=null&&r.off)return this.clerkjs.off(...n);j(this,ie).off(...n)},this.addOnLoaded=n=>{this.loadedListeners.push(n),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(n=>n()),this.loadedListeners=[]},this.beforeLoad=n=>{if(!n)throw new Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=n=>{var r;if(!n)throw new Error("Failed to hydrate latest Clerk JS");return this.clerkjs=n,this.premountMethodCalls.forEach(o=>o()),this.premountAddListenerCalls.forEach((o,a)=>{o.nativeUnsubscribe=n.addListener(a)}),(r=j(this,ie).internal.retrieveListeners("status"))==null||r.forEach(o=>{this.on("status",o,{notify:!0})}),this.preopenSignIn!==null&&n.openSignIn(this.preopenSignIn),this.preopenCheckout!==null&&n.__internal_openCheckout(this.preopenCheckout),this.preopenPlanDetails!==null&&n.__internal_openPlanDetails(this.preopenPlanDetails),this.preopenSignUp!==null&&n.openSignUp(this.preopenSignUp),this.preopenUserProfile!==null&&n.openUserProfile(this.preopenUserProfile),this.preopenUserVerification!==null&&n.__internal_openReverification(this.preopenUserVerification),this.preopenOneTap!==null&&n.openGoogleOneTap(this.preopenOneTap),this.preopenOrganizationProfile!==null&&n.openOrganizationProfile(this.preopenOrganizationProfile),this.preopenCreateOrganization!==null&&n.openCreateOrganization(this.preopenCreateOrganization),this.preOpenWaitlist!==null&&n.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((o,a)=>{n.mountSignIn(a,o)}),this.premountSignUpNodes.forEach((o,a)=>{n.mountSignUp(a,o)}),this.premountUserProfileNodes.forEach((o,a)=>{n.mountUserProfile(a,o)}),this.premountUserButtonNodes.forEach((o,a)=>{n.mountUserButton(a,o)}),this.premountOrganizationListNodes.forEach((o,a)=>{n.mountOrganizationList(a,o)}),this.premountWaitlistNodes.forEach((o,a)=>{n.mountWaitlist(a,o)}),this.premountPricingTableNodes.forEach((o,a)=>{n.mountPricingTable(a,o)}),typeof this.clerkjs.status>"u"&&j(this,ie).emit(xe.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async n=>{const r=await Zt(this,Ne,Et).call(this);if(r&&"__unstable__updateProps"in r)return r.__unstable__updateProps(n)},this.__experimental_navigateToTask=async n=>this.clerkjs?this.clerkjs.__experimental_navigateToTask(n):Promise.reject(),this.setActive=n=>this.clerkjs?this.clerkjs.setActive(n):Promise.reject(),this.openSignIn=n=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(n):this.preopenSignIn=n},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=n=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(n):this.preopenCheckout=n},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=n=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(n):this.preopenPlanDetails=n},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openReverification=n=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(n):this.preopenUserVerification=n},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=n=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(n):this.preopenOneTap=n},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=n=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(n):this.preopenUserProfile=n},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=n=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(n):this.preopenOrganizationProfile=n},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=n=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(n):this.preopenCreateOrganization=n},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=n=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(n):this.preOpenWaitlist=n},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=n=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(n):this.preopenSignUp=n},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(n,r):this.premountSignInNodes.set(n,r)},this.unmountSignIn=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(n):this.premountSignInNodes.delete(n)},this.mountSignUp=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(n,r):this.premountSignUpNodes.set(n,r)},this.unmountSignUp=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(n):this.premountSignUpNodes.delete(n)},this.mountUserProfile=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(n,r):this.premountUserProfileNodes.set(n,r)},this.unmountUserProfile=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(n):this.premountUserProfileNodes.delete(n)},this.mountOrganizationProfile=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(n,r):this.premountOrganizationProfileNodes.set(n,r)},this.unmountOrganizationProfile=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(n):this.premountOrganizationProfileNodes.delete(n)},this.mountCreateOrganization=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(n,r):this.premountCreateOrganizationNodes.set(n,r)},this.unmountCreateOrganization=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(n):this.premountCreateOrganizationNodes.delete(n)},this.mountOrganizationSwitcher=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(n,r):this.premountOrganizationSwitcherNodes.set(n,r)},this.unmountOrganizationSwitcher=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(n):this.premountOrganizationSwitcherNodes.delete(n)},this.__experimental_prefetchOrganizationSwitcher=()=>{const n=()=>{var r;return(r=this.clerkjs)==null?void 0:r.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?n():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",n)},this.mountOrganizationList=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(n,r):this.premountOrganizationListNodes.set(n,r)},this.unmountOrganizationList=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(n):this.premountOrganizationListNodes.delete(n)},this.mountUserButton=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(n,r):this.premountUserButtonNodes.set(n,r)},this.unmountUserButton=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(n):this.premountUserButtonNodes.delete(n)},this.mountWaitlist=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(n,r):this.premountWaitlistNodes.set(n,r)},this.unmountWaitlist=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(n):this.premountWaitlistNodes.delete(n)},this.mountPricingTable=(n,r)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(n,r):this.premountPricingTableNodes.set(n,r)},this.unmountPricingTable=n=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(n):this.premountPricingTableNodes.delete(n)},this.addListener=n=>{if(this.clerkjs)return this.clerkjs.addListener(n);{const r=()=>{var o;const a=this.premountAddListenerCalls.get(n);a&&((o=a.nativeUnsubscribe)==null||o.call(a),this.premountAddListenerCalls.delete(n))};return this.premountAddListenerCalls.set(n,{unsubscribe:r,nativeUnsubscribe:void 0}),r}},this.navigate=n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.navigate(n)};this.clerkjs&&this.loaded?r():this.premountMethodCalls.set("navigate",r)},this.redirectWithAuth=async(...n)=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.redirectWithAuth(...n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("redirectWithAuth",r)},this.redirectToSignIn=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.redirectToSignIn(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("redirectToSignIn",r)},this.redirectToSignUp=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.redirectToSignUp(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("redirectToSignUp",r)},this.redirectToUserProfile=async()=>{const n=()=>{var r;return(r=this.clerkjs)==null?void 0:r.redirectToUserProfile()};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("redirectToUserProfile",n)},this.redirectToAfterSignUp=()=>{const n=()=>{var r;return(r=this.clerkjs)==null?void 0:r.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("redirectToAfterSignUp",n)},this.redirectToAfterSignIn=()=>{const n=()=>{var r;return(r=this.clerkjs)==null?void 0:r.redirectToAfterSignIn()};this.clerkjs&&this.loaded?n():this.premountMethodCalls.set("redirectToAfterSignIn",n)},this.redirectToAfterSignOut=()=>{const n=()=>{var r;return(r=this.clerkjs)==null?void 0:r.redirectToAfterSignOut()};this.clerkjs&&this.loaded?n():this.premountMethodCalls.set("redirectToAfterSignOut",n)},this.redirectToOrganizationProfile=async()=>{const n=()=>{var r;return(r=this.clerkjs)==null?void 0:r.redirectToOrganizationProfile()};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("redirectToOrganizationProfile",n)},this.redirectToCreateOrganization=async()=>{const n=()=>{var r;return(r=this.clerkjs)==null?void 0:r.redirectToCreateOrganization()};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("redirectToCreateOrganization",n)},this.redirectToWaitlist=async()=>{const n=()=>{var r;return(r=this.clerkjs)==null?void 0:r.redirectToWaitlist()};if(this.clerkjs&&this.loaded)return n();this.premountMethodCalls.set("redirectToWaitlist",n)},this.handleRedirectCallback=async n=>{var r;const o=()=>{var a;return(a=this.clerkjs)==null?void 0:a.handleRedirectCallback(n)};this.clerkjs&&this.loaded?(r=o())==null||r.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",o)},this.handleGoogleOneTapCallback=async(n,r)=>{var o;const a=()=>{var u;return(u=this.clerkjs)==null?void 0:u.handleGoogleOneTapCallback(n,r)};this.clerkjs&&this.loaded?(o=a())==null||o.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",a)},this.handleEmailLinkVerification=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.handleEmailLinkVerification(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("handleEmailLinkVerification",r)},this.authenticateWithMetamask=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.authenticateWithMetamask(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("authenticateWithMetamask",r)},this.authenticateWithCoinbaseWallet=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.authenticateWithCoinbaseWallet(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",r)},this.authenticateWithOKXWallet=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.authenticateWithOKXWallet(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("authenticateWithOKXWallet",r)},this.authenticateWithWeb3=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.authenticateWithWeb3(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("authenticateWithWeb3",r)},this.authenticateWithGoogleOneTap=async n=>(await Zt(this,Ne,Et).call(this)).authenticateWithGoogleOneTap(n),this.createOrganization=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.createOrganization(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("createOrganization",r)},this.getOrganization=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.getOrganization(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("getOrganization",r)},this.joinWaitlist=async n=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.joinWaitlist(n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("joinWaitlist",r)},this.signOut=async(...n)=>{const r=()=>{var o;return(o=this.clerkjs)==null?void 0:o.signOut(...n)};if(this.clerkjs&&this.loaded)return r();this.premountMethodCalls.set("signOut",r)};const{Clerk:i=null,publishableKey:s}=t||{};ve(this,ae,s),ve(this,ke,t==null?void 0:t.proxyUrl),ve(this,_e,t==null?void 0:t.domain),this.options=t,this.Clerk=i,this.mode=tn()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=Es),j(this,ie).emit(xe.Status,"loading"),j(this,ie).prioritizedOn(xe.Status,n=>ve(this,Fe,n)),j(this,ae)&&this.loadClerkJS()}get publishableKey(){return j(this,ae)}get loaded(){var t;return((t=this.clerkjs)==null?void 0:t.loaded)||!1}get status(){var t;return this.clerkjs?((t=this.clerkjs)==null?void 0:t.status)||(this.clerkjs.loaded?"ready":"loading"):j(this,Fe)}static getOrCreateInstance(t){return(!tn()||!j(this,ue)||t.Clerk&&j(this,ue).Clerk!==t.Clerk||j(this,ue).publishableKey!==t.publishableKey)&&ve(this,ue,new Kn(t)),j(this,ue)}static clearInstance(){ve(this,ue,null)}get domain(){return typeof window<"u"&&window.location?lt(j(this,_e),new URL(window.location.href),""):typeof j(this,_e)=="function"?te.throw(at):j(this,_e)||""}get proxyUrl(){return typeof window<"u"&&window.location?lt(j(this,ke),new URL(window.location.href),""):typeof j(this,ke)=="function"?te.throw(at):j(this,ke)||""}__internal_getOption(t){var i,s;return(i=this.clerkjs)!=null&&i.__internal_getOption?(s=this.clerkjs)==null?void 0:s.__internal_getOption(t):this.options[t]}get sdkMetadata(){var t;return((t=this.clerkjs)==null?void 0:t.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var t;return(t=this.clerkjs)==null?void 0:t.instanceType}get frontendApi(){var t;return((t=this.clerkjs)==null?void 0:t.frontendApi)||""}get isStandardBrowser(){var t;return((t=this.clerkjs)==null?void 0:t.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return typeof window<"u"&&window.location?lt(this.options.isSatellite,new URL(window.location.href),!1):typeof this.options.isSatellite=="function"?te.throw(at):!1}async loadClerkJS(){var t;if(!(this.mode!=="browser"||this.loaded)){typeof window<"u"&&(window.__clerk_publishable_key=j(this,ae),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let i;ps(this.Clerk)?(i=new this.Clerk(j(this,ae),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(i),await i.load(this.options)):(i=this.Clerk,i.loaded||(this.beforeLoad(i),await i.load(this.options))),global.Clerk=i}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await ss({...this.options,publishableKey:j(this,ae),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw new Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}return(t=global.Clerk)!=null&&t.loaded?this.hydrateClerkJS(global.Clerk):void 0}catch(i){const s=i;j(this,ie).emit(xe.Status,"error"),console.error(s.stack||s.message||s);return}}}get version(){var t;return(t=this.clerkjs)==null?void 0:t.version}get client(){if(this.clerkjs)return this.clerkjs.client}get session(){if(this.clerkjs)return this.clerkjs.session}get user(){if(this.clerkjs)return this.clerkjs.user}get organization(){if(this.clerkjs)return this.clerkjs.organization}get telemetry(){if(this.clerkjs)return this.clerkjs.telemetry}get __unstable__environment(){if(this.clerkjs)return this.clerkjs.__unstable__environment}get isSignedIn(){return this.clerkjs?this.clerkjs.isSignedIn:!1}get billing(){var t;return(t=this.clerkjs)==null?void 0:t.billing}__unstable__setEnvironment(...t){if(this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs)this.clerkjs.__unstable__setEnvironment(t);else return}};Fe=new WeakMap;_e=new WeakMap;ke=new WeakMap;ae=new WeakMap;ie=new WeakMap;ue=new WeakMap;Ne=new WeakSet;Et=function(){return new Promise(e=>{this.addOnLoaded(()=>e(this.clerkjs))})};he(Hn,ue);var cn=Hn;function Os(e){const{isomorphicClerkOptions:t,initialState:i,children:s}=e,{isomorphicClerk:n,clerkStatus:r}=Us(t),[o,a]=l.useState({client:n.client,session:n.session,user:n.user,organization:n.organization});l.useEffect(()=>n.addListener(B=>a({...B})),[]);const u=us(n.loaded,o,i),d=l.useMemo(()=>({value:n}),[r]),c=l.useMemo(()=>({value:o.client}),[o.client]),{sessionId:m,sessionStatus:p,sessionClaims:f,session:g,userId:_,user:P,orgId:h,actor:v,organization:y,orgRole:I,orgSlug:S,orgPermissions:w,factorVerificationAge:R}=u,k=l.useMemo(()=>({value:{sessionId:m,sessionStatus:p,sessionClaims:f,userId:_,actor:v,orgId:h,orgRole:I,orgSlug:S,orgPermissions:w,factorVerificationAge:R}}),[m,p,_,v,h,I,S,R,f==null?void 0:f.__raw]),A=l.useMemo(()=>({value:g}),[m,g]),E=l.useMemo(()=>({value:P}),[_,P]),z=l.useMemo(()=>({value:{organization:y}}),[h,y]);return l.createElement(Ci.Provider,{value:d},l.createElement(ci.Provider,{value:c},l.createElement(di.Provider,{value:A},l.createElement(fi,{...z.value},l.createElement(ki.Provider,{value:k},l.createElement(ui.Provider,{value:E},s))))))}var Us=e=>{const t=l.useMemo(()=>cn.getOrCreateInstance(e),[]),[i,s]=l.useState(t.status);return l.useEffect(()=>{t.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),l.useEffect(()=>{t.__unstable__updateProps({options:e})},[e.localization]),l.useEffect(()=>(t.on("status",s),()=>t.off("status",s)),[t]),l.useEffect(()=>()=>{cn.clearInstance()},[]),{isomorphicClerk:t,clerkStatus:i}};function Is(e){const{initialState:t,children:i,__internal_bypassMissingPublishableKey:s,...n}=e,{publishableKey:r="",Clerk:o}=n;return!o&&!s&&(r?r&&!ft(r)&&te.throwInvalidPublishableKeyError({key:r}):te.throwMissingPublishableKeyError()),l.createElement(Os,{initialState:t,isomorphicClerkOptions:n},i)}var js=gs(Is,"ClerkProvider",Pi);js.displayName="ClerkProvider";_i({packageName:"@clerk/clerk-react"});is("@clerk/clerk-react");export{js as C,Ws as S,Ns as U,Fs as a,Ds as b,Ni as u};
