import{r as l,D as F,P as j,$ as V,j as w,c as K}from"./index-UcdZ5AHH.js";import{R as Ce,P as Se,O as Ie,a as Re}from"./calendar-BZ1UqQsL.js";import{D as je,b as De,c as Ae,d as _e,a as Me}from"./dialog-DmI079wB.js";import{S as $e}from"./search-B6f_4BGP.js";var fe=1,Ne=.9,Pe=.8,Fe=.17,ee=.1,te=.999,Ke=.9999,Le=.99,qe=/[\\\/_+.#"@\[\(\{&]/,Ve=/[\\\/_+.#"@\[\(\{&]/g,ze=/[\s-]/,ge=/[\s-]/g;function ne(e,n,r,s,o,d,c){if(d===n.length)return o===e.length?fe:Le;var f=`${o},${d}`;if(c[f]!==void 0)return c[f];for(var b=s.charAt(d),u=r.indexOf(b,o),v=0,g,S,y,I;u>=0;)g=ne(e,n,r,s,u+1,d+1,c),g>v&&(u===o?g*=fe:qe.test(e.charAt(u-1))?(g*=Pe,y=e.slice(o,u-1).match(Ve),y&&o>0&&(g*=Math.pow(te,y.length))):ze.test(e.charAt(u-1))?(g*=Ne,I=e.slice(o,u-1).match(ge),I&&o>0&&(g*=Math.pow(te,I.length))):(g*=Fe,o>0&&(g*=Math.pow(te,u-o))),e.charAt(u)!==n.charAt(d)&&(g*=Ke)),(g<ee&&r.charAt(u-1)===s.charAt(d+1)||s.charAt(d+1)===s.charAt(d)&&r.charAt(u-1)!==s.charAt(d))&&(S=ne(e,n,r,s,u+1,d+2,c),S*ee>g&&(g=S*ee)),g>v&&(v=g),u=r.indexOf(b,u+1);return c[f]=v,v}function pe(e){return e.toLowerCase().replace(ge," ")}function Oe(e,n,r){return e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,ne(e,n,pe(e),pe(n),0,0,{})}var q='[cmdk-group=""]',re='[cmdk-group-items=""]',Be='[cmdk-group-heading=""]',he='[cmdk-item=""]',ve=`${he}:not([aria-disabled="true"])`,le="cmdk-item-select",N="data-value",Ge=(e,n,r)=>Oe(e,n,r),be=l.createContext(void 0),z=()=>l.useContext(be),xe=l.createContext(void 0),ae=()=>l.useContext(xe),ke=l.createContext(void 0),we=l.forwardRef((e,n)=>{let r=P(()=>{var t,i;return{search:"",value:(i=(t=e.value)!=null?t:e.defaultValue)!=null?i:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),s=P(()=>new Set),o=P(()=>new Map),d=P(()=>new Map),c=P(()=>new Set),f=ye(e),{label:b,children:u,value:v,onValueChange:g,filter:S,shouldFilter:y,loop:I,disablePointerSelection:H=!1,vimBindings:D=!0,...O}=e,T=F(),oe=F(),U=F(),A=l.useRef(null),x=tt();_(()=>{if(v!==void 0){let t=v.trim();r.current.value=t,E.emit()}},[v]),_(()=>{x(6,ie)},[]);let E=l.useMemo(()=>({subscribe:t=>(c.current.add(t),()=>c.current.delete(t)),snapshot:()=>r.current,setState:(t,i,m)=>{var a,p,h,C;if(!Object.is(r.current[t],i)){if(r.current[t]=i,t==="search")Y(),J(),x(1,X);else if(t==="value"){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let k=document.getElementById(U);k?k.focus():(a=document.getElementById(T))==null||a.focus()}if(x(7,()=>{var k;r.current.selectedItemId=(k=$())==null?void 0:k.id,E.emit()}),m||x(5,ie),((p=f.current)==null?void 0:p.value)!==void 0){let k=i??"";(C=(h=f.current).onValueChange)==null||C.call(h,k);return}}E.emit()}},emit:()=>{c.current.forEach(t=>t())}}),[]),W=l.useMemo(()=>({value:(t,i,m)=>{var a;i!==((a=d.current.get(t))==null?void 0:a.value)&&(d.current.set(t,{value:i,keywords:m}),r.current.filtered.items.set(t,ue(i,m)),x(2,()=>{J(),E.emit()}))},item:(t,i)=>(s.current.add(t),i&&(o.current.has(i)?o.current.get(i).add(t):o.current.set(i,new Set([t]))),x(3,()=>{Y(),J(),r.current.value||X(),E.emit()}),()=>{d.current.delete(t),s.current.delete(t),r.current.filtered.items.delete(t);let m=$();x(4,()=>{Y(),(m==null?void 0:m.getAttribute("id"))===t&&X(),E.emit()})}),group:t=>(o.current.has(t)||o.current.set(t,new Set),()=>{d.current.delete(t),o.current.delete(t)}),filter:()=>f.current.shouldFilter,label:b||e["aria-label"],getDisablePointerSelection:()=>f.current.disablePointerSelection,listId:T,inputId:U,labelId:oe,listInnerRef:A}),[]);function ue(t,i){var m,a;let p=(a=(m=f.current)==null?void 0:m.filter)!=null?a:Ge;return t?p(t,r.current.search,i):0}function J(){if(!r.current.search||f.current.shouldFilter===!1)return;let t=r.current.filtered.items,i=[];r.current.filtered.groups.forEach(a=>{let p=o.current.get(a),h=0;p.forEach(C=>{let k=t.get(C);h=Math.max(k,h)}),i.push([a,h])});let m=A.current;L().sort((a,p)=>{var h,C;let k=a.getAttribute("id"),B=p.getAttribute("id");return((h=t.get(B))!=null?h:0)-((C=t.get(k))!=null?C:0)}).forEach(a=>{let p=a.closest(re);p?p.appendChild(a.parentElement===p?a:a.closest(`${re} > *`)):m.appendChild(a.parentElement===m?a:a.closest(`${re} > *`))}),i.sort((a,p)=>p[1]-a[1]).forEach(a=>{var p;let h=(p=A.current)==null?void 0:p.querySelector(`${q}[${N}="${encodeURIComponent(a[0])}"]`);h==null||h.parentElement.appendChild(h)})}function X(){let t=L().find(m=>m.getAttribute("aria-disabled")!=="true"),i=t==null?void 0:t.getAttribute(N);E.setState("value",i||void 0)}function Y(){var t,i,m,a;if(!r.current.search||f.current.shouldFilter===!1){r.current.filtered.count=s.current.size;return}r.current.filtered.groups=new Set;let p=0;for(let h of s.current){let C=(i=(t=d.current.get(h))==null?void 0:t.value)!=null?i:"",k=(a=(m=d.current.get(h))==null?void 0:m.keywords)!=null?a:[],B=ue(C,k);r.current.filtered.items.set(h,B),B>0&&p++}for(let[h,C]of o.current)for(let k of C)if(r.current.filtered.items.get(k)>0){r.current.filtered.groups.add(h);break}r.current.filtered.count=p}function ie(){var t,i,m;let a=$();a&&(((t=a.parentElement)==null?void 0:t.firstChild)===a&&((m=(i=a.closest(q))==null?void 0:i.querySelector(Be))==null||m.scrollIntoView({block:"nearest"})),a.scrollIntoView({block:"nearest"}))}function $(){var t;return(t=A.current)==null?void 0:t.querySelector(`${he}[aria-selected="true"]`)}function L(){var t;return Array.from(((t=A.current)==null?void 0:t.querySelectorAll(ve))||[])}function Q(t){let i=L()[t];i&&E.setState("value",i.getAttribute(N))}function Z(t){var i;let m=$(),a=L(),p=a.findIndex(C=>C===m),h=a[p+t];(i=f.current)!=null&&i.loop&&(h=p+t<0?a[a.length-1]:p+t===a.length?a[0]:a[p+t]),h&&E.setState("value",h.getAttribute(N))}function de(t){let i=$(),m=i==null?void 0:i.closest(q),a;for(;m&&!a;)m=t>0?Ze(m,q):et(m,q),a=m==null?void 0:m.querySelector(ve);a?E.setState("value",a.getAttribute(N)):Z(t)}let ce=()=>Q(L().length-1),se=t=>{t.preventDefault(),t.metaKey?ce():t.altKey?de(1):Z(1)},me=t=>{t.preventDefault(),t.metaKey?Q(0):t.altKey?de(-1):Z(-1)};return l.createElement(j.div,{ref:n,tabIndex:-1,...O,"cmdk-root":"",onKeyDown:t=>{var i;(i=O.onKeyDown)==null||i.call(O,t);let m=t.nativeEvent.isComposing||t.keyCode===229;if(!(t.defaultPrevented||m))switch(t.key){case"n":case"j":{D&&t.ctrlKey&&se(t);break}case"ArrowDown":{se(t);break}case"p":case"k":{D&&t.ctrlKey&&me(t);break}case"ArrowUp":{me(t);break}case"Home":{t.preventDefault(),Q(0);break}case"End":{t.preventDefault(),ce();break}case"Enter":{t.preventDefault();let a=$();if(a){let p=new Event(le);a.dispatchEvent(p)}}}}},l.createElement("label",{"cmdk-label":"",htmlFor:W.inputId,id:W.labelId,style:nt},b),G(e,t=>l.createElement(xe.Provider,{value:E},l.createElement(be.Provider,{value:W},t))))}),He=l.forwardRef((e,n)=>{var r,s;let o=F(),d=l.useRef(null),c=l.useContext(ke),f=z(),b=ye(e),u=(s=(r=b.current)==null?void 0:r.forceMount)!=null?s:c==null?void 0:c.forceMount;_(()=>{if(!u)return f.item(o,c==null?void 0:c.id)},[u]);let v=Ee(o,d,[e.value,e.children,d],e.keywords),g=ae(),S=R(x=>x.value&&x.value===v.current),y=R(x=>u||f.filter()===!1?!0:x.search?x.filtered.items.get(o)>0:!0);l.useEffect(()=>{let x=d.current;if(!(!x||e.disabled))return x.addEventListener(le,I),()=>x.removeEventListener(le,I)},[y,e.onSelect,e.disabled]);function I(){var x,E;H(),(E=(x=b.current).onSelect)==null||E.call(x,v.current)}function H(){g.setState("value",v.current,!0)}if(!y)return null;let{disabled:D,value:O,onSelect:T,forceMount:oe,keywords:U,...A}=e;return l.createElement(j.div,{ref:V(d,n),...A,id:o,"cmdk-item":"",role:"option","aria-disabled":!!D,"aria-selected":!!S,"data-disabled":!!D,"data-selected":!!S,onPointerMove:D||f.getDisablePointerSelection()?void 0:H,onClick:D?void 0:I},e.children)}),Te=l.forwardRef((e,n)=>{let{heading:r,children:s,forceMount:o,...d}=e,c=F(),f=l.useRef(null),b=l.useRef(null),u=F(),v=z(),g=R(y=>o||v.filter()===!1?!0:y.search?y.filtered.groups.has(c):!0);_(()=>v.group(c),[]),Ee(c,f,[e.value,e.heading,b]);let S=l.useMemo(()=>({id:c,forceMount:o}),[o]);return l.createElement(j.div,{ref:V(f,n),...d,"cmdk-group":"",role:"presentation",hidden:g?void 0:!0},r&&l.createElement("div",{ref:b,"cmdk-group-heading":"","aria-hidden":!0,id:u},r),G(e,y=>l.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?u:void 0},l.createElement(ke.Provider,{value:S},y))))}),Ue=l.forwardRef((e,n)=>{let{alwaysRender:r,...s}=e,o=l.useRef(null),d=R(c=>!c.search);return!r&&!d?null:l.createElement(j.div,{ref:V(o,n),...s,"cmdk-separator":"",role:"separator"})}),We=l.forwardRef((e,n)=>{let{onValueChange:r,...s}=e,o=e.value!=null,d=ae(),c=R(u=>u.search),f=R(u=>u.selectedItemId),b=z();return l.useEffect(()=>{e.value!=null&&d.setState("search",e.value)},[e.value]),l.createElement(j.input,{ref:n,...s,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":b.listId,"aria-labelledby":b.labelId,"aria-activedescendant":f,id:b.inputId,type:"text",value:o?e.value:c,onChange:u=>{o||d.setState("search",u.target.value),r==null||r(u.target.value)}})}),Je=l.forwardRef((e,n)=>{let{children:r,label:s="Suggestions",...o}=e,d=l.useRef(null),c=l.useRef(null),f=R(u=>u.selectedItemId),b=z();return l.useEffect(()=>{if(c.current&&d.current){let u=c.current,v=d.current,g,S=new ResizeObserver(()=>{g=requestAnimationFrame(()=>{let y=u.offsetHeight;v.style.setProperty("--cmdk-list-height",y.toFixed(1)+"px")})});return S.observe(u),()=>{cancelAnimationFrame(g),S.unobserve(u)}}},[]),l.createElement(j.div,{ref:V(d,n),...o,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":f,"aria-label":s,id:b.listId},G(e,u=>l.createElement("div",{ref:V(c,b.listInnerRef),"cmdk-list-sizer":""},u)))}),Xe=l.forwardRef((e,n)=>{let{open:r,onOpenChange:s,overlayClassName:o,contentClassName:d,container:c,...f}=e;return l.createElement(Ce,{open:r,onOpenChange:s},l.createElement(Se,{container:c},l.createElement(Ie,{"cmdk-overlay":"",className:o}),l.createElement(Re,{"aria-label":e.label,"cmdk-dialog":"",className:d},l.createElement(we,{ref:n,...f}))))}),Ye=l.forwardRef((e,n)=>R(r=>r.filtered.count===0)?l.createElement(j.div,{ref:n,...e,"cmdk-empty":"",role:"presentation"}):null),Qe=l.forwardRef((e,n)=>{let{progress:r,children:s,label:o="Loading...",...d}=e;return l.createElement(j.div,{ref:n,...d,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":o},G(e,c=>l.createElement("div",{"aria-hidden":!0},c)))}),M=Object.assign(we,{List:Je,Item:He,Input:We,Group:Te,Separator:Ue,Dialog:Xe,Empty:Ye,Loading:Qe});function Ze(e,n){let r=e.nextElementSibling;for(;r;){if(r.matches(n))return r;r=r.nextElementSibling}}function et(e,n){let r=e.previousElementSibling;for(;r;){if(r.matches(n))return r;r=r.previousElementSibling}}function ye(e){let n=l.useRef(e);return _(()=>{n.current=e}),n}var _=typeof window>"u"?l.useEffect:l.useLayoutEffect;function P(e){let n=l.useRef();return n.current===void 0&&(n.current=e()),n}function R(e){let n=ae(),r=()=>e(n.snapshot());return l.useSyncExternalStore(n.subscribe,r,r)}function Ee(e,n,r,s=[]){let o=l.useRef(),d=z();return _(()=>{var c;let f=(()=>{var u;for(let v of r){if(typeof v=="string")return v.trim();if(typeof v=="object"&&"current"in v)return v.current?(u=v.current.textContent)==null?void 0:u.trim():o.current}})(),b=s.map(u=>u.trim());d.value(e,f,b),(c=n.current)==null||c.setAttribute(N,f),o.current=f}),o}var tt=()=>{let[e,n]=l.useState(),r=P(()=>new Map);return _(()=>{r.current.forEach(s=>s()),r.current=new Map},[e]),(s,o)=>{r.current.set(s,o),n({})}};function rt(e){let n=e.type;return typeof n=="function"?n(e.props):"render"in n?n.render(e.props):e}function G({asChild:e,children:n},r){return e&&l.isValidElement(n)?l.cloneElement(rt(n),{ref:n.ref},r(n.props.children)):r(n)}var nt={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function lt({className:e,...n}){return w.jsx(M,{"data-slot":"command",className:K("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...n})}function dt({title:e="Command Palette",description:n="Search for a command to run...",children:r,...s}){return w.jsxs(je,{...s,children:[w.jsxs(De,{className:"sr-only",children:[w.jsx(Ae,{children:e}),w.jsx(_e,{children:n})]}),w.jsx(Me,{className:"overflow-hidden p-0",children:w.jsx(lt,{className:"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:r})})]})}function ct({className:e,...n}){return w.jsxs("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[w.jsx($e,{className:"size-4 shrink-0 opacity-50"}),w.jsx(M.Input,{"data-slot":"command-input",className:K("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...n})]})}function st({className:e,...n}){return w.jsx(M.List,{"data-slot":"command-list",className:K("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",e),...n})}function mt({...e}){return w.jsx(M.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...e})}function ft({className:e,...n}){return w.jsx(M.Group,{"data-slot":"command-group",className:K("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...n})}function pt({className:e,...n}){return w.jsx(M.Separator,{"data-slot":"command-separator",className:K("bg-border -mx-1 h-px",e),...n})}function vt({className:e,...n}){return w.jsx(M.Item,{"data-slot":"command-item",className:K("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}export{lt as C,ct as a,st as b,mt as c,ft as d,vt as e,pt as f,dt as g};
