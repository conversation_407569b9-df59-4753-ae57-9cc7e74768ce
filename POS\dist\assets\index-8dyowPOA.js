import{j as e,r as u,R as ve,B as _,l as ae,b as ne,a3 as Se,a4 as W,u as Ge}from"./index-CVQ6JZo2.js";import{H as Je}from"./header-DiKooCuw.js";import{M as Ze}from"./main-BD6dUgw2.js";import{P as es}from"./profile-dropdown-BEO1YZeq.js";import{S as ss,T as ts}from"./search-bbGy26eb.js";import{u as Ce}from"./use-areas-BEwI37-s.js";import{D as he}from"./data-table-column-header-C6NnwWvT.js";import{u as as}from"./use-dialog-state-Duzyky_e.js";import"./vietqr-api-_ZZrmuU0.js";import{a as ns}from"./pos-api-mRg02iop.js";import{u as be}from"./use-stores-vqnk7iRZ.js";import{u as is}from"./use-item-types-DnnQzY0g.js";import{u as rs}from"./use-items-266COz_R.js";import{u as ls}from"./use-removed-items-Yn7fGIc0.js";import{u as os}from"./use-sources-cLXJkEjy.js";import"./user-BJzEhOTa.js";import"./crm-api-CDzLLTww.js";import{C as G}from"./checkbox-BfLSzhzg.js";import{I as K}from"./input-Al6WtUZF.js";import{L as O}from"./form-CzmGigtT.js";import{u as we}from"./useMutation-ZsyDznMu.js";import{p as oe}from"./printer-position-api-BTydSSlP.js";import{Q as ie}from"./query-keys-3lmd-xp6.js";import{u as _e}from"./useQuery-HgcIHxlE.js";import{D as Te,a as Ie,e as Pe}from"./dialog-DDrduXt3.js";import{S as ke}from"./scroll-area-CGsZUbT-.js";import{S as Z,a as ee,b as se,c as te,d as V,C as De,f as Ae}from"./select-BFhNE0YE.js";import{X as Fe}from"./calendar-BszTCdZH.js";import{S as ue}from"./search-B4Rlb4i6.js";import"./date-range-picker-CXbMaowj.js";import{a as cs,C as ds}from"./chevron-right-CxgpqvrH.js";import{S as I}from"./skeleton-CzG-rpGS.js";import{T as Oe,a as Ue,b as X,c as Y,d as Re,e as H}from"./table-BOc3nItc.js";import{u as ms,g as hs,a as us,b as xs,c as ps,d as gs,e as fs,f as xe}from"./index-BCpVAkPF.js";import{P as js}from"./plus-D3Ku3i4v.js";import{D as ys}from"./data-table-view-options-CLYJxScT.js";import{S as pe}from"./store-Kh9fmGvf.js";import{T as Ns}from"./trash-2-_PrOpZAV.js";import"./separator-BcoNozmD.js";import"./avatar-q0u2_bqW.js";import"./dropdown-menu-lhfGm_WJ.js";import"./index-LVHINuqD.js";import"./index-CtK-wKtB.js";import"./index-C34iUvGy.js";import"./check-BE_j5GZD.js";import"./createLucideIcon-DKVxsQv7.js";import"./search-context-CkCLuJFL.js";import"./command-Nb4B17YQ.js";import"./createReactComponent-DSXPaZ4c.js";import"./IconChevronRight-LXWXuzjR.js";import"./IconSearch-DjIF9VGJ.js";import"./images-api-DZkYJB2_.js";import"./react-icons.esm-DMMA_g0o.js";import"./stores-api-BwFq9epY.js";import"./item-api-RI9c_YlZ.js";import"./sources-api-BbeVL2d3.js";import"./sources-CfiQ7039.js";import"./index-nc1u7392.js";import"./utils-km2FGkQ4.js";import"./isSameMonth-C8JQo-AN.js";import"./popover-DnoSPJNX.js";function vs({areaIds:s,storeUid:t}){const{data:a=[],isLoading:i}=Ce({storeUid:t});if(!s)return e.jsx("div",{className:"text-muted-foreground text-sm",children:"—"});const h=s.split(",").filter(Boolean);if(i)return e.jsx("div",{className:"text-muted-foreground max-w-[280px] truncate text-sm",title:h.join(", "),children:h.join(", ")});const o=new Map(a.map(r=>[r.area_id,r.area_name])),n=h.map(r=>o.get(r)||r).join(", ");return e.jsx("div",{className:"text-muted-foreground max-w-[280px] truncate text-sm",title:n,children:n})}const Ss=[{id:"index",header:"#",cell:({row:s})=>e.jsx("div",{className:"w-[50px]",children:s.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"printerPositionName",header:({column:s})=>e.jsx(he,{column:s,title:"Tên vị trí máy in"}),cell:({row:s})=>e.jsx("div",{className:"text-sm font-medium",children:s.getValue("printerPositionName")}),enableSorting:!1,enableHiding:!1},{accessorKey:"areaIds",header:({column:s})=>e.jsx(he,{column:s,title:"Khu vực"}),cell:({row:s})=>{const t=s.original;return e.jsx(vs,{areaIds:t.areaIds||"",storeUid:t.storeUid||""})},enableSorting:!1}],Ee=ve.createContext(null);function Cs({children:s}){const[t,a]=as(null),[i,h]=u.useState(null),[o,m]=u.useState([]);return e.jsx(Ee,{value:{open:t,setOpen:a,currentRow:i,setCurrentRow:h,selectedRows:o,setSelectedRows:m},children:s})}const J=()=>{const s=ve.useContext(Ee);if(!s)throw new Error("usePrinterPositionInStore has to be used within <PrinterPositionInStoreContext>");return s};function bs(){const{setOpen:s}=J();return e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs(_,{size:"sm",className:"h-8",onClick:()=>s("create"),children:[e.jsx(js,{className:"mr-2 h-4 w-4"}),"Tạo vị trí máy in"]})})}function ws({table:s,onStoreFilter:t,onScopeTypeChange:a,currentScopeType:i="0",currentStoreId:h=null}){var U;const{setOpen:o,setSelectedRows:m}=J(),[n,r]=u.useState(null),[x,g]=u.useState(i);u.useEffect(()=>{g(i)},[i]),u.useEffect(()=>{r(h??null)},[h]);const{selectedBrand:p}=ae(),{companyUid:N}=ne(),{data:S=[],isLoading:C}=be({params:{company_uid:N||"",brand_uid:(p==null?void 0:p.id)||""},enabled:!!(N&&(p!=null&&p.id))}),P=s.getState().columnFilters.length>0||n!==null||x!=="0",l=s.getFilteredSelectedRowModel().rows,c=l.length>0,f=()=>{const j=l.map(v=>v.original);m(j),o("bulk-delete")},k=j=>{var T;const v=j===""?null:j;r(v),v===null&&((T=s.getColumn("printerPositionName"))==null||T.setFilterValue(""),g("0")),t&&t(v)},L=j=>{var v;(v=s.getColumn("printerPositionName"))==null||v.setFilterValue(""),a&&a(j)},D=()=>{s.resetColumnFilters(),r(null),g("0"),t&&t(null)};return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsx(K,{placeholder:"Tìm kiếm vị trí máy in...",value:((U=s.getColumn("printerPositionName"))==null?void 0:U.getFilterValue())??"",onChange:j=>{var v;return(v=s.getColumn("printerPositionName"))==null?void 0:v.setFilterValue(j.target.value)},className:"h-8 w-[150px] lg:w-[250px]",disabled:n===null}),e.jsxs(Z,{value:x,onValueChange:L,disabled:n===null,children:[e.jsxs(ee,{className:"h-8 w-[200px]",children:[e.jsx(pe,{className:"mr-2 h-4 w-4"}),e.jsx(se,{placeholder:"Phạm vi"})]}),e.jsxs(te,{className:"z-50",children:[e.jsx(V,{value:"0",children:"Cửa hàng và thương hiệu"}),e.jsx(V,{value:"1",children:"Cửa hàng"})]})]}),e.jsxs(Z,{value:n||void 0,onValueChange:k,children:[e.jsxs(ee,{className:"h-8 w-[200px]",children:[e.jsx(pe,{className:"mr-2 h-4 w-4"}),e.jsx(se,{placeholder:"Chọn cửa hàng *"})]}),e.jsxs(te,{className:"z-50",children:[C&&e.jsx(V,{value:"loading",disabled:!0,children:"Đang tải..."}),!C&&S.length===0&&e.jsx(V,{value:"no-stores",disabled:!0,children:"Không có cửa hàng nào"}),!C&&S.map(j=>e.jsx(V,{value:j.id,children:j.name},j.id))]})]}),P&&e.jsxs(_,{variant:"ghost",onClick:D,className:"h-8 px-2 lg:px-3",children:["Reset",e.jsx(Fe,{className:"ml-2 h-4 w-4"})]}),c&&e.jsxs("div",{className:"ml-4 flex items-center space-x-2",children:[e.jsxs("span",{className:"text-muted-foreground text-sm",children:[l.length," hàng được chọn"]}),e.jsx(_,{variant:"ghost",size:"sm",onClick:()=>s.resetRowSelection(),className:"h-8",children:"Hủy chọn"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[c&&e.jsxs(_,{variant:"destructive",size:"sm",onClick:f,className:"h-8",children:[e.jsx(Ns,{className:"mr-2 h-4 w-4"}),"Xóa (",l.length,")"]}),e.jsx(ys,{table:s})]})]})}const _s=()=>{const s=Se();return we({mutationFn:t=>oe.createPrinterPosition(t),onSuccess:t=>{s.invalidateQueries({queryKey:[ie.PRINTER_POSITIONS_IN_STORE]}),W.success(t.message||"Tạo vị trí máy in trong cửa hàng thành công")},onError:t=>{W.error(t.message||"Có lỗi xảy ra khi tạo vị trí máy in trong cửa hàng")}})},Ts=()=>{const s=Se();return we({mutationFn:t=>oe.updatePrinterPosition(t),onSuccess:t=>{s.invalidateQueries({queryKey:[ie.PRINTER_POSITIONS_IN_STORE]}),W.success(t.message||"Cập nhật vị trí máy in trong cửa hàng thành công")},onError:t=>{W.error(t.message||"Có lỗi xảy ra khi cập nhật vị trí máy in trong cửa hàng")}})},Is=(s={})=>{const{params:t={},enabled:a=!0}=s,{company:i,brands:h}=Ge(r=>r.auth),o=h==null?void 0:h[0],m={company_uid:(i==null?void 0:i.id)||"",brand_uid:(o==null?void 0:o.id)||"",page:1,...t},n=!!(i!=null&&i.id&&(o!=null&&o.id));return _e({queryKey:[ie.PRINTER_POSITIONS_IN_STORE,m],queryFn:async()=>{const r=await oe.getPrinterPositions(m),x=Array.isArray(r.data)?r.data:[],g=r.total_item||0,p=m.page||1,N=m.limit||10,S=p*N<g;return{data:x,total:g,hasNextPage:S}},enabled:a&&n,staleTime:0,gcTime:0,retry:!1,initialData:{data:[],total:0,hasNextPage:!1}})},Ps=(s={})=>{const{data:t,isLoading:a,error:i,refetch:h}=Is({params:s.params,enabled:s.enabled});return{data:u.useMemo(()=>!(t!=null&&t.data)||!Array.isArray(t.data)?[]:t.data.map(n=>({id:n.id,printerPositionId:n.printer_position_id,printerPositionName:n.printer_position_name,listItemTypeId:n.list_item_type_id||"",listItemId:n.list_item_id||"",storeUid:n.store_uid||"",areaIds:n.area_ids||"",sources:n.sources||"",applyWithStore:n.apply_with_store||0,sort:n.sort||0,brandUid:n.brand_uid||"",companyUid:n.company_uid||"",revision:n.revision||0,isActive:!n.deleted,createdAt:new Date(n.created_at),updatedAt:new Date(n.updated_at),createdBy:n.created_by||"",updatedBy:n.updated_by||"",deleted:n.deleted||!1,deletedBy:n.deleted_by||null,deletedAt:n.deleted_at?new Date(n.deleted_at):null,originalData:n})),[t==null?void 0:t.data]),total:(t==null?void 0:t.total)||0,hasNextPage:(t==null?void 0:t.hasNextPage)||!1,isLoading:a,error:i,refetch:h}},ks=(s,t=!0)=>{const{selectedBrand:a}=ae(),{companyUid:i}=ne();return _e({queryKey:[ie.PRINTER_POSITIONS,"detail",s,i,a==null?void 0:a.id],queryFn:async()=>{var m;if(!i||!(a!=null&&a.id)||!s)return null;const h=new URLSearchParams({company_uid:i,brand_uid:a.id,id:s}),o=await ns.get(`/pos/v1/printer-position/detail?${h.toString()}`);return(m=o==null?void 0:o.data)!=null&&m.data?o.data.data:o!=null&&o.data?o.data:null},enabled:t&&!!s&&!!i&&!!(a!=null&&a.id),staleTime:5*60*1e3,gcTime:10*60*1e3})};function Ds({open:s,onOpenChange:t,areas:a,selectedAreaIds:i,onConfirm:h,onCancel:o}){const[m,n]=u.useState([]),[r,x]=u.useState(""),g=a.filter((l,c,f)=>c===f.findIndex(k=>k.id===l.id));u.useEffect(()=>{s&&n([...i])},[s,i]);const p=g.filter(l=>m.includes(l.area_id)&&l.area_name.toLowerCase().includes(r.toLowerCase())),N=g.filter(l=>!m.includes(l.area_id)&&l.area_name.toLowerCase().includes(r.toLowerCase())),S=()=>{h(m),x("")},C=()=>{n([]),x(""),o()},P=(l,c)=>{n(c?f=>f.includes(l)?f:[...f,l]:f=>f.filter(k=>k!==l))};return e.jsx(Te,{open:s,onOpenChange:t,children:e.jsxs(Ie,{className:"max-h-[90vh] w-[800px] max-w-4xl lg:max-w-4xl",children:[e.jsxs("div",{className:"space-y-6 p-2",children:[e.jsx(K,{placeholder:"Tìm kiếm khu vực...",value:r,onChange:l=>x(l.target.value)}),e.jsx(ke,{className:"h-[60vh] w-full",children:e.jsxs("div",{className:"space-y-4",children:[p.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 flex items-center gap-2 rounded bg-green-50 p-3 text-base font-medium text-green-600",children:e.jsxs("span",{children:["✓ Đã chọn ",p.length," khu vực"]})}),e.jsx("div",{className:"space-y-3",children:p.map(l=>e.jsx(ge,{area:l,isSelected:!0,onToggle:P},l.id))})]}),N.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 rounded bg-gray-50 p-3 text-base font-medium text-gray-600",children:e.jsxs("span",{children:["Còn lại ",N.length," khu vực"]})}),e.jsx("div",{className:"space-y-3",children:N.map(l=>e.jsx(ge,{area:l,isSelected:!1,onToggle:P},l.id))})]})]})})]}),e.jsxs(Pe,{children:[e.jsx(_,{variant:"outline",onClick:C,children:"Hủy"}),e.jsx(_,{onClick:S,children:"Xong"})]})]})})}function ge({area:s,isSelected:t,onToggle:a}){const i=s.area_id;return e.jsxs("div",{className:"flex items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(G,{id:i,checked:t,onCheckedChange:h=>a(i,!!h),className:"h-5 w-5"}),e.jsx(O,{htmlFor:i,className:"flex-1 cursor-pointer text-base",children:s.area_name})]})}function As({categories:s,selectedCategories:t,onToggleCategory:a}){const[i,h]=u.useState(!1),o=s.filter(n=>t.includes(n.id)),m=s.filter(n=>!t.includes(n.id));return e.jsx("div",{className:"space-y-2",children:e.jsxs("div",{className:"rounded-md border",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between border-b bg-green-50 p-3 text-sm text-green-600 transition-colors hover:bg-green-100",onClick:()=>h(!i),children:[e.jsxs("span",{children:["✓ Đã chọn ",t.length]}),o.length>0&&(i?e.jsx(De,{className:"h-4 w-4"}):e.jsx(Ae,{className:"h-4 w-4"}))]}),o.length>0&&!i&&e.jsx("div",{className:"border-b",children:e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"max-h-48 space-y-2 overflow-y-auto",children:o.map(n=>e.jsx(fe,{category:n,isSelected:!0,onToggle:a},n.id))})})}),m.length>0&&e.jsxs("div",{children:[o.length>0&&e.jsx("div",{className:"border-b bg-gray-50 p-3 text-sm text-gray-600",children:e.jsxs("span",{children:["Còn lại ",m.length]})}),e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"max-h-48 space-y-2 overflow-y-auto",children:m.map(n=>e.jsx(fe,{category:n,isSelected:!1,onToggle:a},n.id))})})]})]})})}function fe({category:s,isSelected:t,onToggle:a}){return e.jsxs("div",{className:"flex items-center space-x-3 rounded border-b border-gray-100 py-2 last:border-b-0 hover:bg-gray-50",children:[e.jsx(G,{id:s.id,checked:t,onCheckedChange:i=>a(s.id,!!i),className:"h-4 w-4"}),e.jsx(O,{htmlFor:s.id,className:"flex-1 cursor-pointer text-sm leading-relaxed",children:s.item_type_name})]})}function Fs({items:s,isLoading:t=!1,cities:a=[],stores:i=[],mode:h,onChangeMode:o,selectedCityUid:m,onChangeCity:n,selectedStoreUid:r,onChangeStore:x,selectedItems:g=[],onItemsChange:p}){const[N,S]=u.useState(!1),C=s.filter(c=>g.includes(c.id)),P=s.filter(c=>!g.includes(c.id)),l=(c,f)=>{p&&p(f?[...g,c]:g.filter(k=>k!==c))};return t?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Đang tải dữ liệu món ăn..."})]})}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{variant:h==="city"?"default":"outline",size:"sm",onClick:()=>o("city"),className:"h-10",children:"Món tại thành phố"}),e.jsx(_,{variant:h==="store"?"default":"outline",size:"sm",onClick:()=>o("store"),className:"h-10",children:"Món tại cửa hàng"}),e.jsx("div",{className:"ml-auto",children:h==="store"?e.jsxs(Z,{value:r,onValueChange:c=>x==null?void 0:x(c),children:[e.jsx(ee,{className:"h-10 w-56",children:e.jsx(se,{placeholder:"Chọn cửa hàng"})}),e.jsx(te,{children:i.map(c=>e.jsx(V,{value:c.id,children:c.store_name||c.name},c.id))})]}):e.jsxs(Z,{value:m,onValueChange:c=>n==null?void 0:n(c),children:[e.jsx(ee,{className:"h-10 w-40",children:e.jsx(se,{placeholder:"Chọn thành phố"})}),e.jsx(te,{children:a.map(c=>e.jsx(V,{value:c.id,children:c.city_name},c.id))})]})})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between border-b bg-green-50 p-3 text-sm text-green-600 transition-colors hover:bg-green-100",onClick:()=>S(!N),children:[e.jsxs("span",{children:["✓ Đã chọn ",g.length]}),C.length>0&&(N?e.jsx(De,{className:"h-4 w-4"}):e.jsx(Ae,{className:"h-4 w-4"}))]}),C.length>0&&!N&&e.jsx("div",{className:"border-b",children:e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"max-h-48 space-y-2 overflow-y-auto",children:C.map(c=>e.jsx(je,{item:c,isSelected:!0,onToggle:l},c.id))})})}),P.length>0&&e.jsxs("div",{children:[C.length>0&&e.jsx("div",{className:"border-b bg-gray-50 p-3 text-sm text-gray-600",children:e.jsxs("span",{children:["Còn lại ",P.length]})}),e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"max-h-48 space-y-2 overflow-y-auto",children:P.map(c=>e.jsx(je,{item:c,isSelected:!1,onToggle:l},c.id))})})]})]})]})}function je({item:s,isSelected:t,onToggle:a}){return e.jsxs("div",{className:"flex items-center space-x-3 rounded border-b border-gray-100 py-2 last:border-b-0 hover:bg-gray-50",children:[e.jsx(G,{id:s.id,checked:t,onCheckedChange:i=>a(s.id,!!i),className:"h-4 w-4"}),e.jsxs(O,{htmlFor:s.id,className:"flex-1 cursor-pointer text-sm leading-relaxed",children:[s.item_name," - ",s.item_id]})]})}function Os({open:s,onOpenChange:t,orderSources:a,selectedOrderSources:i,onConfirm:h,onCancel:o}){const[m,n]=u.useState([]),[r,x]=u.useState(""),g=a.filter((l,c,f)=>c===f.findIndex(k=>k.sourceId===l.sourceId));u.useEffect(()=>{s&&n([...i])},[s,i]);const p=g.filter(l=>m.includes(l.sourceId)&&l.sourceName.toLowerCase().includes(r.toLowerCase())),N=g.filter(l=>!m.includes(l.sourceId)&&l.sourceName.toLowerCase().includes(r.toLowerCase())),S=()=>{h(m),x("")},C=()=>{n([]),x(""),o()},P=(l,c)=>{n(c?[...m,l]:m.filter(f=>f!==l))};return e.jsx(Te,{open:s,onOpenChange:t,children:e.jsxs(Ie,{className:"max-h-[90vh] w-[800px] max-w-4xl lg:max-w-4xl",children:[e.jsxs("div",{className:"space-y-6 p-2",children:[e.jsx(Us,{value:r,onChange:x}),e.jsx(Rs,{selectedSources:p,unselectedSources:N,selectedIds:m,onToggle:P})]}),e.jsxs(Pe,{children:[e.jsx(_,{variant:"outline",onClick:C,children:"Hủy"}),e.jsx(_,{onClick:S,children:"Xong"})]})]})})}function Us({value:s,onChange:t}){return e.jsx(K,{placeholder:"Tìm kiếm",value:s,onChange:a=>t(a.target.value)})}function Rs({selectedSources:s,unselectedSources:t,onToggle:a}){return e.jsx(ke,{className:"h-[60vh] w-full",children:e.jsxs("div",{className:"space-y-4",children:[s.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 flex items-center gap-2 rounded bg-green-50 p-3 text-base font-medium text-green-600",children:e.jsxs("span",{children:["✓ Đã chọn ",s.length]})}),e.jsx("div",{className:"space-y-3",children:s.map(i=>e.jsx(ye,{source:i,isSelected:!0,onToggle:a},i.sourceId))})]}),t.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 rounded bg-gray-50 p-3 text-base font-medium text-gray-600",children:e.jsxs("span",{children:["Còn lại ",t.length]})}),e.jsx("div",{className:"space-y-3",children:t.map(i=>e.jsx(ye,{source:i,isSelected:!1,onToggle:a},i.sourceId))})]})]})})}function ye({source:s,isSelected:t,onToggle:a}){return e.jsxs("div",{className:"flex items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(G,{id:s.sourceId,checked:t,onCheckedChange:i=>a(s.sourceId,!!i),className:"h-5 w-5"}),e.jsx(O,{htmlFor:s.sourceId,className:"flex-1 cursor-pointer text-base",children:s.sourceName})]})}function Ne({open:s,onOpenChange:t,currentRow:a}){const{selectedBrand:i}=ae(),{companyUid:h}=ne(),o=!!a,m=_s(),n=Ts(),{data:r}=ks((a==null?void 0:a.id)||"",o&&!!(a!=null&&a.id)),[x,g]=u.useState(""),[p,N]=u.useState(""),[S,C]=u.useState("city"),{data:P}=os(),{data:l,isLoading:c}=is({store_uid:(r==null?void 0:r.store_uid)||void 0,skip_limit:!0,active:1}),{data:f=[]}=ls(),{data:k=[]}=be(),L=()=>S==="city"?{city_uid:x||void 0,skip_limit:!0,active:1}:{list_store_uid:p||void 0,skip_limit:!0,active:1},{data:D,isLoading:U}=rs({params:L(),enabled:S==="city"?!!x:!!p}),[j,v]=u.useState(!1),[T,b]=u.useState(!1),[q,ce]=u.useState(""),Le=(l||[]).filter(y=>y.item_type_name.toLowerCase().includes(q.toLowerCase())),[d,A]=u.useState({printerPositionName:"",applyToAllCategories:!1,selectedCategories:[],selectedItems:[],applicationType:"category",selectedOrderSources:[],selectedStoreUids:[],selectedAreaIds:[]});u.useEffect(()=>{s&&!a&&A({printerPositionName:"",applyToAllCategories:!1,selectedCategories:[],selectedItems:[],applicationType:"category",selectedOrderSources:[],selectedStoreUids:[],selectedAreaIds:[]})},[s,a]),u.useEffect(()=>{if(s&&o&&r&&l&&!c){const z=(r.list_item_type_id?r.list_item_type_id.split(",").filter(Boolean):[]).map(w=>{const M=l.find(F=>F.id===w);if(M)return M.id;const Q=l.find(F=>F.item_type_id===w);if(Q)return Q.id;const $=l.find(F=>F.item_type_name.toLowerCase()===w.toLowerCase());return $?$.id:w}).filter(Boolean),R=r.sources?r.sources.split(",").filter(Boolean):[],B=(l||[]).filter(w=>w.item_type_id!=="ITEM_TYPE_OTHER").map(w=>w.id),re=B.length>0&&B.every(w=>z.includes(w)),le=(r.list_item_id?r.list_item_id.split(",").filter(Boolean):[]).map(w=>{if(!D)return w;const M=D.find(F=>F.id===w);if(M)return M.id;const Q=D.find(F=>F.item_id===w);if(Q)return Q.id;const $=D.find(F=>F.item_name.toLowerCase()===w.toLowerCase());return $?$.id:w}).filter(Boolean),We=le.length>0?"item":"category";A(w=>{const M=w.applicationType==="item"&&le.length===0;return{printerPositionName:r.printer_position_name||"",applyToAllCategories:re,selectedCategories:z,selectedItems:le,applicationType:M?"item":We,selectedOrderSources:R,selectedStoreUids:r.store_uid?[r.store_uid]:[],selectedAreaIds:r.area_ids?r.area_ids.split(",").filter(Boolean):[]}})}},[s,o,r,l,c,D]);const Me=d.selectedStoreUids.length===1?d.selectedStoreUids[0]:"",{data:Ve=[]}=Ce({storeUid:Me}),de=()=>{t(!1),A({printerPositionName:"",applyToAllCategories:!1,selectedCategories:[],selectedItems:[],applicationType:"category",selectedOrderSources:[],selectedStoreUids:[],selectedAreaIds:[]}),v(!1),b(!1)};u.useEffect(()=>{!x&&f.length>0&&g(f[0].id)},[f,x]);const me=d.printerPositionName.trim()!=="",qe=async()=>{if(me){if(!h||!(i!=null&&i.id)){W.error("Thiếu thông tin công ty hoặc thương hiệu");return}try{const y={printer_position_name:d.printerPositionName,company_uid:h,brand_uid:i.id,store_uid:d.selectedStoreUids.length===1?d.selectedStoreUids[0]:void 0,printer_position_id:o&&(r!=null&&r.printer_position_id)?r.printer_position_id:`POSITION_PRINTER-${Math.random().toString(36).substring(2,6).toUpperCase()}`,list_item_type_id:d.applicationType==="category"?d.selectedCategories.join(","):"",list_item_id:d.applicationType==="item"?d.selectedItems.join(","):"",sources:d.selectedOrderSources.join(","),apply_with_store:d.selectedStoreUids.length>0?1:0,area_ids:d.selectedAreaIds.join(",")};o&&a?await n.mutateAsync({id:a.id,...y}):await m.mutateAsync(y),de()}catch{}}},ze=y=>{if(y){const z=(l||[]).filter(R=>R.item_type_id!=="ITEM_TYPE_OTHER").map(R=>R.id);A({...d,applyToAllCategories:y,selectedCategories:z})}else A({...d,applyToAllCategories:y,selectedCategories:[]})},He=()=>{v(!0)},Ke=y=>{A({...d,selectedOrderSources:y}),v(!1)},Be=()=>{v(!1)},Qe=(y,z)=>{const R=z?[...d.selectedCategories,y]:d.selectedCategories.filter(E=>E!==y),B=(l||[]).filter(E=>E.item_type_id!=="ITEM_TYPE_OTHER").map(E=>E.id),re=B.length>0&&B.every(E=>R.includes(E));A({...d,selectedCategories:R,applyToAllCategories:re})},$e=()=>{b(!0)},Ye=y=>{A({...d,selectedAreaIds:y}),b(!1)},Xe=()=>{b(!1)};return s?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(_,{variant:"ghost",size:"sm",onClick:de,className:"flex items-center",children:e.jsx(Fe,{className:"h-4 w-4"})}),e.jsx(_,{type:"button",disabled:!me||m.isPending||n.isPending,onClick:qe,children:m.isPending||n.isPending?"Đang lưu...":"Lưu"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:o?"Chỉnh sửa vị trí máy in":"Tạo vị trí máy in"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"rounded-lg border bg-white p-6 shadow-sm",children:e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(O,{htmlFor:"printer-name",className:"min-w-[200px] text-sm font-medium",children:["Nhập tên vị trí máy in ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(K,{id:"printer-name",value:d.printerPositionName,onChange:y=>A({...d,printerPositionName:y.target.value}),placeholder:"Tên vị trí máy in",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(O,{className:"min-w-[200px] text-sm font-medium",children:"Chọn khu vực áp dụng"}),e.jsx(_,{type:"button",variant:"outline",onClick:$e,className:"border-blue-200 text-sm text-blue-600 hover:bg-blue-50",disabled:d.selectedStoreUids.length!==1,children:d.selectedAreaIds.length>0?`${d.selectedAreaIds.length} khu vực được áp dụng`:"Chưa chọn khu vực"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(O,{className:"min-w-[200px] text-sm font-medium",children:"Áp dụng ngành đơn"}),e.jsx(_,{type:"button",variant:"outline",onClick:He,className:"border-blue-200 text-sm text-blue-600 hover:bg-blue-50",children:d.selectedOrderSources.length>0?`${d.selectedOrderSources.length} nguồn đơn được áp dụng`:"0 nguồn đơn được áp dụng"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(O,{className:"text-sm font-medium",children:"Các nhóm món được dùng tại vị trí"}),d.applicationType==="category"&&e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex flex-shrink-0 items-center space-x-2",children:[e.jsx(G,{id:"apply-all",checked:d.applyToAllCategories,onCheckedChange:ze}),e.jsx(O,{htmlFor:"apply-all",className:"text-sm whitespace-nowrap text-blue-600",children:"Áp dụng cho tất cả nhóm món"})]}),e.jsx("div",{className:"max-w-full flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"absolute top-1/2 left-2 h-3 w-3 -translate-y-1/2 text-gray-400"}),e.jsx(K,{placeholder:"Tìm kiếm nhóm món...",value:q,onChange:y=>ce(y.target.value),className:"h-8 w-full pl-7 text-sm"})]})})]}),d.applicationType==="item"&&e.jsx("div",{className:"flex items-center justify-between gap-4",children:e.jsx("div",{className:"max-w-full flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"absolute top-1/2 left-2 h-3 w-3 -translate-y-1/2 text-gray-400"}),e.jsx(K,{placeholder:"Tìm kiếm",value:q,onChange:y=>ce(y.target.value),className:"h-8 w-full pl-7 text-sm"})]})})}),e.jsxs("div",{className:"flex items-center gap-4 border-y py-4",children:[e.jsx(O,{className:"min-w-[200px] text-sm font-medium",children:"Áp dụng cho"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(_,{type:"button",variant:d.applicationType==="category"?"default":"outline",size:"sm",onClick:()=>{A({...d,applicationType:"category"})},children:"Nhóm"}),e.jsx(_,{type:"button",variant:d.applicationType==="item"?"default":"outline",size:"sm",onClick:()=>{A({...d,applicationType:"item"})},children:"Món ăn"})]})]}),d.applicationType==="category"?e.jsx(As,{categories:Le,selectedCategories:d.selectedCategories,onToggleCategory:Qe}):e.jsx(Fs,{items:D||[],isLoading:U,cities:f,selectedCityUid:x,onChangeCity:g,stores:k,selectedStoreUid:p,onChangeStore:N,mode:S,onChangeMode:C,selectedItems:d.selectedItems,onItemsChange:y=>A({...d,selectedItems:y})})]})]})})})}),e.jsx(Os,{open:j,onOpenChange:v,orderSources:P||[],selectedOrderSources:d.selectedOrderSources,onConfirm:Ke,onCancel:Be}),e.jsx(Ds,{open:T,onOpenChange:b,areas:Ve,selectedAreaIds:d.selectedAreaIds,onConfirm:Ye,onCancel:Xe})]}):null}function Es(){const{open:s,setOpen:t,currentRow:a,setCurrentRow:i}=J();return e.jsxs(e.Fragment,{children:[e.jsx(Ne,{open:s==="create",onOpenChange:()=>t(null)},"printer-position-create"),a&&e.jsx(e.Fragment,{children:e.jsx(Ne,{open:s==="update",onOpenChange:()=>{t(null),setTimeout(()=>{i(null)},500)},currentRow:a},`printer-position-update-${a.id}`)})]})}function Ls({currentPage:s,onPageChange:t,hasNextPage:a}){const i=()=>{s>1&&t(s-1)},h=()=>{a&&t(s+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(_,{variant:"outline",size:"sm",onClick:i,disabled:s===1,className:"flex items-center gap-2",children:[e.jsx(cs,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:s}),e.jsxs(_,{variant:"outline",size:"sm",onClick:h,disabled:!a,className:"flex items-center gap-2",children:["Sau",e.jsx(ds,{className:"h-4 w-4"})]})]})}function Ms(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsx(I,{className:"h-8 w-[250px]"}),e.jsx(I,{className:"h-8 w-[180px]"})]}),e.jsx(I,{className:"h-8 w-[100px]"})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(Oe,{children:[e.jsx(Ue,{children:e.jsxs(X,{children:[e.jsx(Y,{className:"w-[50px]",children:e.jsx(I,{className:"h-4 w-4"})}),e.jsx(Y,{className:"w-[50px]",children:e.jsx(I,{className:"h-4 w-6"})}),e.jsx(Y,{children:e.jsx(I,{className:"h-4 w-32"})}),e.jsx(Y,{className:"w-[50px]"})]})}),e.jsx(Re,{children:Array.from({length:8}).map((s,t)=>e.jsxs(X,{children:[e.jsx(H,{children:e.jsx(I,{className:"h-4 w-4"})}),e.jsx(H,{children:e.jsx(I,{className:"h-4 w-6"})}),e.jsx(H,{children:e.jsx(I,{className:"h-4 w-40"})}),e.jsx(H,{children:e.jsx(I,{className:"h-8 w-8"})})]},t))})]})}),e.jsxs("div",{className:"flex items-center justify-between px-2",children:[e.jsx(I,{className:"h-4 w-32"}),e.jsxs("div",{className:"flex items-center space-x-6 lg:space-x-8",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(I,{className:"h-4 w-24"}),e.jsx(I,{className:"h-8 w-16"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(I,{className:"h-8 w-8"}),e.jsx(I,{className:"h-8 w-8"}),e.jsx(I,{className:"h-8 w-8"}),e.jsx(I,{className:"h-8 w-8"})]})]})]})]})}function Vs({columns:s,data:t,onStoreFilterChange:a,onScopeTypeChange:i,hasStoreSelected:h=!1,currentScopeType:o="0",currentStoreId:m=null}){var v;const{setOpen:n,setCurrentRow:r,open:x}=J(),[g,p]=u.useState({}),[N,S]=u.useState({}),[C,P]=u.useState([]),[l,c]=u.useState([]),[f,k]=u.useState(null);u.useEffect(()=>{x!=="bulk-delete"&&p({})},[x]);const L=u.useMemo(()=>t,[t,f]),D=T=>{r(T),n("update")},U=T=>{k(T),a&&a(T)},j=ms({data:L,columns:s,state:{sorting:l,columnVisibility:N,rowSelection:g,columnFilters:C},enableRowSelection:!0,onRowSelectionChange:p,onSortingChange:c,onColumnFiltersChange:P,onColumnVisibilityChange:S,getCoreRowModel:fs(),getFilteredRowModel:gs(),getPaginationRowModel:ps(),getSortedRowModel:xs(),getFacetedRowModel:us(),getFacetedUniqueValues:hs()});return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Vị trí máy in tại cửa hàng"}),e.jsx(bs,{})]}),e.jsx(ws,{table:j,onStoreFilter:U,onScopeTypeChange:i,currentScopeType:o,currentStoreId:m}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(Oe,{children:[e.jsx(Ue,{children:j.getHeaderGroups().map(T=>e.jsx(X,{children:T.headers.map(b=>e.jsx(Y,{colSpan:b.colSpan,children:b.isPlaceholder?null:xe(b.column.columnDef.header,b.getContext())},b.id))},T.id))}),e.jsx(Re,{children:(v=j.getRowModel().rows)!=null&&v.length?j.getRowModel().rows.map(T=>e.jsx(X,{"data-state":T.getIsSelected()&&"selected",className:"cursor-pointer",onClick:b=>{const q=b.target;q.closest("[data-actions]")||q.closest('[role="checkbox"]')||D(T.original)},children:T.getVisibleCells().map(b=>e.jsx(H,{...b.column.id==="actions"?{"data-actions":!0}:{},children:xe(b.column.columnDef.cell,b.getContext())},b.id))},T.id)):e.jsx(X,{children:e.jsx(H,{colSpan:s.length,className:"h-24 text-center",children:h?"Không có dữ liệu vị trí máy in cho cửa hàng này.":e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Vui lòng chọn cửa hàng để xem dữ liệu vị trí máy in"})})})})})]})})]})}function qs(){const{selectedBrand:s}=ae(),{companyUid:t}=ne(),{open:a}=J(),[i,h]=u.useState(null),[o,m]=u.useState("0"),[n,r]=u.useState(1),[x]=u.useState(10),g=i||"",p={company_uid:t||"",brand_uid:(s==null?void 0:s.id)||"",...g&&{store_uid:g},...o==="1"&&{apply_with_store:1},page:n,limit:x},N=!!i||o==="1",{data:S,hasNextPage:C,isLoading:P,error:l,refetch:c}=Ps({params:p,enabled:N}),f=N?S||[]:[],k=j=>{m(j),r(1)},L=j=>{r(j)},D=j=>{h(j),r(1)},U=!a||a!=="create"&&a!=="update";return l?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu vị trí máy in"}),e.jsx("button",{onClick:()=>c(),className:"text-primary text-sm hover:underline",children:"Thử lại"})]})}):e.jsxs(e.Fragment,{children:[U&&e.jsxs(e.Fragment,{children:[e.jsx(Je,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ss,{}),e.jsx(ts,{}),e.jsx(es,{})]})}),e.jsx(Ze,{children:e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:P?e.jsx(Ms,{}):e.jsxs(e.Fragment,{children:[e.jsx(Vs,{columns:Ss,data:f,onStoreFilterChange:D,onScopeTypeChange:k,hasStoreSelected:!!i,currentScopeType:o,currentStoreId:i}),f.length>0&&e.jsx(Ls,{currentPage:n,onPageChange:L,hasNextPage:C})]})})})]}),e.jsx(Es,{})]})}function zs(){return e.jsx(Cs,{children:e.jsx(qs,{})})}const Wt=zs;export{Wt as component};
