import{j as e,B as f}from"./index-UcdZ5AHH.js";import{T as b}from"./table-pagination-CokcdUZG.js";import{v as T}from"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{T as k,a as N,b as c,c as m,d as v,e as o}from"./table-DHWQVnPn.js";import{B as x}from"./badge-BlAal7b-.js";import{f as M}from"./isSameMonth-C8JQo-AN.js";function D({data:t,columns:r,isLoading:h=!1,onRowClick:d,pageSize:p=20,emptyMessage:y="Không có dữ liệu",loadingMessage:j="Đang tải...",className:u}){const g=s=>{try{const i=new Date(s);return M(i,"dd-MM-yyyy HH:mm:ss",{locale:T})}catch{return s}},_=(s,i,n)=>{const a=i[s.key];return s.render?s.render(a,i,n):s.key==="Allow_Self_Order"||s.key==="Allow_Take_Away"?e.jsx(x,{variant:a===1?"default":"destructive",children:a===1?"✓":"✗"}):s.key==="Active"?e.jsx(x,{variant:a===1?"default":"destructive",children:a===1?"Hoạt động":"Không hoạt động"}):typeof a=="string"&&a.includes("T")&&a.includes(":")?e.jsx("span",{className:"text-muted-foreground text-sm",children:g(a)}):typeof a=="number"&&(String(s.key).includes("Price")||String(s.key).includes("price"))?e.jsxs("span",{children:[a.toLocaleString("vi-VN")," đ"]}):a};return h?e.jsx("div",{className:"flex h-64 items-center justify-center",children:e.jsx("div",{className:"text-muted-foreground",children:j})}):t.length?e.jsx(b,{data:t,pageSize:p,children:(s,i)=>e.jsx("div",{className:`rounded-md border ${u}`,children:e.jsxs(k,{children:[e.jsx(N,{children:e.jsxs(c,{children:[e.jsx(m,{className:"w-12",children:"#"}),r.map((n,a)=>e.jsx(m,{style:{width:n.width},children:n.header},a))]})}),e.jsx(v,{children:s.map((n,a)=>e.jsxs(c,{className:d?"cursor-pointer hover:bg-gray-50":"",onClick:()=>d==null?void 0:d(n),children:[e.jsx(o,{children:i.startIndex+a+1}),r.map((l,w)=>e.jsx(o,{style:{width:l.width},children:_(l,n,a)},w))]},a))})]})})}):e.jsx("div",{className:"flex h-64 items-center justify-center",children:e.jsx("div",{className:"text-muted-foreground",children:y})})}const L=t=>[{key:"Item_Id",header:"Mã combo",width:"150px",render:r=>e.jsx("span",{className:"font-medium",children:r})},{key:"Item_Name",header:"Tên combo",width:"250px",render:r=>e.jsx("span",{className:"font-medium",children:r})},{key:"Item_Image_Path",header:"Ảnh",width:"80px",render:r=>e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded border-2 border-dashed border-gray-300 bg-gray-100",children:r?e.jsx("img",{src:r,alt:"combo",className:"h-full w-full rounded object-cover"}):e.jsx("span",{className:"text-xs text-gray-400",children:"Ảnh"})})},{key:"Allow_Self_Order",header:"Bán tại chỗ",width:"100px"},{key:"Allow_Take_Away",header:"Bán mang đi",width:"100px"},{key:"Last_Updated",header:"Thời gian cập nhật",width:"180px"},{key:"actions",header:"Chỉnh sửa",width:"100px",render:(r,h)=>e.jsx(f,{variant:"default",size:"sm",onClick:d=>{d.stopPropagation(),t==null||t(h)},children:"Chỉnh sửa"})}],O=t=>[{key:"Item_Id",header:"Mã món",width:"120px",render:r=>e.jsx("span",{className:"font-medium",children:r})},{key:"Item_Name",header:"Tên món",render:r=>e.jsx("span",{className:"font-medium",children:r})},{key:"Item_Image_Path",header:"Ảnh",width:"80px",render:r=>e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded border-2 border-dashed border-gray-300 bg-gray-100",children:r?e.jsx("img",{src:r,alt:"combo",className:"h-full w-full rounded object-cover"}):e.jsx("span",{className:"text-xs text-gray-400",children:"Ảnh"})})},{key:"Item_Type_Id",header:"Nhóm món",render:r=>r==="ITEM_TYPE_OTHER"?"Món thường":r!=null&&r.startsWith("ITEM_TYPE-")?r.replace("ITEM_TYPE-",""):r},{key:"Is_Eat_With",header:"Loại món",render:r=>r===1?"Món ăn kèm":"Món thường"},{key:"Allow_Self_Order",header:"Bán tại chỗ",width:"100px"},{key:"Allow_Take_Away",header:"Bán mang đi",width:"100px"},{key:"Sort",header:"Thứ tự",width:"80px"},{key:"Last_Updated",header:"Thời gian cập nhật",width:"180px"},{key:"actions",header:"Chỉnh sửa",width:"100px",render:(r,h)=>e.jsx(f,{variant:"default",size:"sm",onClick:d=>{d.stopPropagation(),t==null||t(h)},children:"Chỉnh sửa"})}];export{D,L as a,O as c};
