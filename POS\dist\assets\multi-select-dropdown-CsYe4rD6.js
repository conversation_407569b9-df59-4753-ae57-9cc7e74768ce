import{r as l,j as s,B as P,c as h}from"./index-CVQ6JZo2.js";import{C as f}from"./checkbox-BfLSzhzg.js";import{I as D}from"./input-Al6WtUZF.js";import{P as O,a as A,b as B}from"./popover-DnoSPJNX.js";import{C as E}from"./select-BFhNE0YE.js";import{S as I}from"./search-B4Rlb4i6.js";function z({options:c,value:r,onValueChange:i,placeholder:m="Chọn...",searchPlaceholder:u="Tìm kiếm...",className:p,disabled:g=!1,isLoading:j=!1,showSelectAll:b=!0,selectAllLabel:v="Chọn tất cả",emptyText:N="Không có dữ liệu",loadingText:w="Đang tải..."}){const[x,y]=l.useState(!1),[d,C]=l.useState(""),t=l.useMemo(()=>d?c.filter(e=>e.label.toLowerCase().includes(d.toLowerCase())):c,[c,d]),o=l.useMemo(()=>t.length===0?!1:t.every(e=>e.disabled||r.includes(e.value)),[t,r]),S=l.useMemo(()=>t.some(e=>r.includes(e.value)),[t,r]),k=()=>{if(o){const e=t.map(a=>a.value),n=r.filter(a=>!e.includes(a));i(n)}else{const e=t.filter(a=>!a.disabled).map(a=>a.value),n=[...new Set([...r,...e])];i(n)}},T=e=>{r.includes(e)?i(r.filter(n=>n!==e)):i([...r,e])},M=()=>{if(r.length===0)return m;if(r.length===1){const e=c.find(n=>n.value===r[0]);return(e==null?void 0:e.label)||m}return`Đã chọn ${r.length} mục`};return s.jsxs(O,{open:x,onOpenChange:y,children:[s.jsx(A,{asChild:!0,children:s.jsxs(P,{variant:"outline",role:"combobox","aria-expanded":x,className:h("w-full justify-between",r.length===0&&"text-muted-foreground",p),disabled:g,children:[s.jsx("span",{className:"truncate",children:M()}),s.jsx(E,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),s.jsx(B,{className:"w-[var(--radix-popover-trigger-width)] p-0",align:"start",children:s.jsxs("div",{className:"flex flex-col",children:[s.jsxs("div",{className:"flex items-center border-b px-3 py-2",children:[s.jsx(I,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),s.jsx(D,{placeholder:u,onChange:e=>C(e.target.value),className:"border-0 p-0 focus-visible:ring-0"})]}),s.jsx("div",{className:"max-h-60 overflow-auto",children:j?s.jsx("div",{className:"text-muted-foreground flex items-center justify-center py-6 text-sm",children:w}):t.length===0?s.jsx("div",{className:"text-muted-foreground flex items-center justify-center py-6 text-sm",children:N}):s.jsxs(s.Fragment,{children:[b&&t.length>1&&s.jsx("div",{className:"border-b",children:s.jsxs("div",{className:"hover:bg-accent flex cursor-pointer items-center space-x-2 px-3 py-2",onClick:k,children:[s.jsx(f,{checked:o,"data-state":!o&&S?"indeterminate":void 0,onChange:()=>{}}),s.jsx("span",{className:"text-sm font-medium",children:v})]})}),t.map(e=>s.jsxs("div",{className:h("hover:bg-accent flex cursor-pointer items-center space-x-2 px-3 py-2",e.disabled&&"cursor-not-allowed opacity-50"),onClick:()=>!e.disabled&&T(e.value),children:[s.jsx(f,{checked:r.includes(e.value),disabled:e.disabled,onChange:()=>{}}),s.jsx("span",{className:"text-sm",children:e.label})]},e.value))]})})]})})]})}export{z as M};
