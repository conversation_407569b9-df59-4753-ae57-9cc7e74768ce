import{j as e,e as m}from"./index-UcdZ5AHH.js";import{c as h}from"./crm-api-APQEjHWd.js";import"./pos-api-j20LMGrC.js";import{u as g}from"./useQuery-B4yhTgGk.js";import{C as x}from"./query-keys-DQo7uRnN.js";import"./user-9ajIul7r.js";import{M as p}from"./main-C1Ukb9JX.js";import{C as u,a as j,b as y,d as b}from"./card-ulE1yKb5.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{T as f,a as N,b as o,c as a,d as _,e as n}from"./table-DHWQVnPn.js";import"./utils-km2FGkQ4.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./select-DOexGcsG.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";const T=async t=>{const i=new URLSearchParams({page:t.page.toString(),type:t.type,date_start:t.date_start,date_end:t.date_end,pos_parent:t.pos_parent});return h.get(`/billing/get-detail-using-month?${i.toString()}`)};function C(t){return g({queryKey:[x.BILLING_DETAIL,t],queryFn:async()=>{if(!t)throw new Error("Params are required");return(await T(t)).data},enabled:!!t&&!!t.date_start&&!!t.type})}function D({data:t,isLoading:i}){const d=r=>{try{return new Date(r).toLocaleString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch{return r}},l=r=>{const c={ACTIVE:{label:"Hoạt động",className:"bg-green-100 text-green-800"},INACTIVE:{label:"Không hoạt động",className:"bg-red-100 text-red-800"},PENDING:{label:"Chờ xử lý",className:"bg-yellow-100 text-yellow-800"}}[r]||{label:r,className:"bg-gray-100 text-gray-800"};return e.jsx("span",{className:`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${c.className}`,children:c.label})};return i?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-muted-foreground",children:"Đang tải..."})}):e.jsx("div",{className:"rounded-md border",children:e.jsxs(f,{children:[e.jsx(N,{children:e.jsxs(o,{children:[e.jsx(a,{className:"w-12",children:"#"}),e.jsx(a,{children:"ID"}),e.jsx(a,{children:"Mã chương trình"}),e.jsx(a,{children:"Tên chương trình"}),e.jsx(a,{children:"Thời gian bắt đầu"}),e.jsx(a,{children:"Thời gian kết thúc"}),e.jsx(a,{children:"Trạng thái"})]})}),e.jsx(_,{children:t.length===0?e.jsx(o,{children:e.jsx(n,{colSpan:7,className:"text-center py-8 text-muted-foreground",children:"Không có dữ liệu"})}):t.map((r,s)=>e.jsxs(o,{children:[e.jsx(n,{className:"font-medium",children:s+1}),e.jsx(n,{children:r.id}),e.jsx(n,{children:r.program_code}),e.jsx(n,{children:r.program_name}),e.jsx(n,{children:d(r.start_time)}),e.jsx(n,{children:d(r.end_time)}),e.jsx(n,{children:l(r.status)})]},r.id))})]})})}function w(){var r,s;const{date:t,type:i}=m({from:"/_authenticated/crm/billing-detail"}),l=C({page:1,type:i,date_start:t,date_end:t,pos_parent:"BRAND-953H"});return e.jsx(p,{children:e.jsxs("div",{className:"container mx-auto space-y-6 py-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Chi tiết lưu lượng sử dụng"})}),e.jsxs(u,{children:[e.jsx(j,{children:e.jsxs(y,{children:["Kết quả: ",((r=l.data)==null?void 0:r.count)||0," Bản ghi"]})}),e.jsx(b,{children:e.jsx(D,{data:((s=l.data)==null?void 0:s.vouchers)||[],isLoading:l.isLoading})})]})]})})}const O=w;export{O as component};
