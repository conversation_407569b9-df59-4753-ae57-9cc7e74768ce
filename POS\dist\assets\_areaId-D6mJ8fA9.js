import{aY as t,j as i}from"./index-UcdZ5AHH.js";import"./pos-api-j20LMGrC.js";import"./header-CE1GZ327.js";import"./main-C1Ukb9JX.js";import"./search-context-DK2BgvuK.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{A as m}from"./area-form-CYjWVcmf.js";import"./separator-C5UQ7YqK.js";import"./command-DJT46NtT.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DmI079wB.js";import"./search-B6f_4BGP.js";import"./createReactComponent-C1S2Ujit.js";import"./scroll-area-DQUG4R9C.js";import"./index-MuNXZ_zP.js";import"./select-DOexGcsG.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";import"./IconChevronRight-CnyriCST.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./images-api-BMYin8XI.js";import"./use-areas-DvC-z67k.js";import"./useQuery-B4yhTgGk.js";import"./utils-km2FGkQ4.js";import"./useMutation-q12VR5WX.js";import"./query-keys-3lmd-xp6.js";import"./input-CBpgGfUv.js";const B=function(){const{areaId:o}=t.useParams(),{store_uid:r}=t.useSearch();return console.log("🔍 AreaDetailPage rendered"),console.log("📍 Route params:",{areaId:o}),console.log("🔍 Route search:",{store_uid:r}),console.log("📊 Full URL:",window.location.href),i.jsx(m,{areaId:o,storeUid:r})};export{B as component};
