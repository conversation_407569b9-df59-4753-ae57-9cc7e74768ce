import{u as a}from"./useQuery-BvDWg4vp.js";import{ay as d}from"./index-CfbMU4Ye.js";import{a as s}from"./pos-api-BBB_ZiZD.js";const u=async(e={})=>{var n;const t=new URLSearchParams;e.skipLimit&&t.append("skip_limit","true"),e.page!==void 0&&t.append("page",e.page.toString()),e.results_per_page!==void 0&&t.append("results_per_page",e.results_per_page.toString()),e.companyUid&&t.append("company_uid",e.companyUid),e.brandUid&&t.append("brand_uid",e.brandUid),e.storeUid&&t.append("store_uid",e.storeUid);const i=await s.get(`/mdata/v1/sources?${t.toString()}`);if((n=i.data)!=null&&n.data){const{convertApiSourceToSource:r}=await d(async()=>{const{convertApiSourceToSource:o}=await import("./sources-CfiQ7039.js");return{convertApiSourceToSource:o}},[]);return i.data.data.map(r)}return[]};function l(e={}){return a({queryKey:["sales-channels",e],queryFn:()=>u(e),staleTime:5*60*1e3,gcTime:10*60*1e3})}export{l as u};
