import{j as e,B as a}from"./index-UcdZ5AHH.js";import{D as h,a as p,b as D,c as g,e as y}from"./dialog-DmI079wB.js";const v=({title:t,open:i,onOpenChange:n,onCancel:o,onConfirm:r,children:x,cancelText:c="Hủy",confirmText:d="Lưu",isLoading:s=!1,confirmDisabled:j=!1,hideButtons:m=!1,centerTitle:f=!1,maxWidth:u="sm:max-w-[400px]",disableCancelButton:l=!1})=>e.jsx(h,{open:i,onOpenChange:n,children:e.jsxs(p,{className:u,children:[e.jsx(D,{children:e.jsx(g,{className:`text-lg font-medium ${f?"text-center":""}`,children:t})}),e.jsx("div",{className:"py-4",children:x}),!m&&e.jsx(y,{children:e.jsxs("div",{className:`flex w-full ${l?"justify-end":"justify-between"}`,children:[!l&&e.jsx(a,{type:"button",variant:"outline",onClick:o,disabled:s,children:c}),e.jsx(a,{onClick:r,disabled:s||j,children:s?"Đang xử lý...":d})]})})]})});export{v as P};
