import{j as t,c as l}from"./index-CVQ6JZo2.js";function o({className:a,...e}){return t.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:t.jsx("table",{"data-slot":"table",className:l("w-full caption-bottom text-sm",a),...e})})}function r({className:a,...e}){return t.jsx("thead",{"data-slot":"table-header",className:l("[&_tr]:border-b",a),...e})}function n({className:a,...e}){return t.jsx("tbody",{"data-slot":"table-body",className:l("[&_tr:last-child]:border-0",a),...e})}function c({className:a,...e}){return t.jsx("tr",{"data-slot":"table-row",className:l("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...e})}function d({className:a,...e}){return t.jsx("th",{"data-slot":"table-head",className:l("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...e})}function b({className:a,...e}){return t.jsx("td",{"data-slot":"table-cell",className:l("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...e})}export{o as T,r as a,c as b,d as c,n as d,b as e};
