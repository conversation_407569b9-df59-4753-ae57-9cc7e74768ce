import{j as e,r as S,a4 as L,aA as H,a as K,B as N}from"./index-UcdZ5AHH.js";import{M as U,L as W,a as J}from"./loading-spinner-Cs4HDR-a.js";import"./search-context-DK2BgvuK.js";import"./date-range-picker-DxA68ufO.js";import{L as v}from"./form-D_U5B5Go.js";import{I as T}from"./input-CBpgGfUv.js";import{C as w,S as Q,a as Y,b as Z,c as ee,d as se}from"./select-DOexGcsG.js";import"./pos-api-j20LMGrC.js";import"./vietqr-api-9FERZtmQ.js";import{u as te}from"./use-items-D1TaTLXR.js";import{u as ne}from"./use-customization-by-id-C_Tj_zpw.js";import"./user-9ajIul7r.js";import"./crm-api-APQEjHWd.js";import{u as ie,a as ae,b as oe,c as me,C as re,A as le,I as ce}from"./use-modal-state-DlillLp-.js";import{P as de}from"./modal-DNIlBRJT.js";import{C as y}from"./checkbox-CDB9_T0n.js";import{C as b,a as D,b as O}from"./collapsible-Dz-Iaa-P.js";import{C as G}from"./chevron-right-Dup7TmpK.js";import{X as he}from"./calendar-BZ1UqQsL.js";import"./skeleton-B5wLl279.js";import"./command-DJT46NtT.js";import"./dialog-DmI079wB.js";import"./search-B6f_4BGP.js";import"./createLucideIcon-D7O7McKr.js";import"./createReactComponent-C1S2Ujit.js";import"./scroll-area-DQUG4R9C.js";import"./index-MuNXZ_zP.js";import"./IconChevronRight-CnyriCST.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";import"./useQuery-B4yhTgGk.js";import"./utils-km2FGkQ4.js";import"./item-api-BSj8C3Ww.js";import"./query-keys-3lmd-xp6.js";import"./use-customizations-DjbYm2qv.js";import"./useMutation-q12VR5WX.js";import"./table-DHWQVnPn.js";import"./useCanGoBack-CgcmcCI3.js";import"./use-update-customization-BarN9DX2.js";function ue({open:d,onOpenChange:g,onCancel:a,onConfirm:s,dishSearchTerm:r,setDishSearchTerm:i,selectedSectionOpen:o,setSelectedSectionOpen:j,remainingSectionOpen:f,setRemainingSectionOpen:I,selectedDishes:m,handleDishToggle:p,selectedDishItems:C,remainingDishItems:l}){return e.jsx(de,{title:"Chọn món áp dụng customization",open:d,onOpenChange:g,onCancel:a,onConfirm:s,confirmText:"Xác nhận",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(T,{placeholder:"Tìm kiếm",value:r,onChange:n=>i(n.target.value),className:"w-full"}),e.jsxs(b,{open:o,onOpenChange:j,children:[e.jsxs(D,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Đã chọn (",C.length,")"]}),o?e.jsx(w,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]}),e.jsx(O,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:C.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Chưa có món nào được chọn"}):C.map(n=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(y,{checked:m.has(n.id),onCheckedChange:()=>p(n.id)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:n.item_name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[n.ots_price.toLocaleString("vi-VN")," đ"]})]})]},n.id))})})]}),e.jsxs(b,{open:f,onOpenChange:I,children:[e.jsxs(D,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Còn lại (",l.length,")"]}),f?e.jsx(w,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]}),e.jsx(O,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:l.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Không có món nào"}):l.map(n=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(y,{checked:m.has(n.id),onCheckedChange:()=>p(n.id)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:n.item_name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[n.ots_price.toLocaleString("vi-VN")," đ"]})]})]},n.id))})})]})]})})}function pe(){const[d,g]=S.useState(new Set),[a,s]=S.useState(""),[r,i]=S.useState(!0),[o,j]=S.useState(!0),f=n=>{const x=new Set(d);x.has(n)?x.delete(n):x.add(n),g(x)},I=()=>(L.success(`Đã chọn ${d.size} món`),!0),m=()=>{s(""),i(!0),j(!0)},p=n=>n.filter(x=>x.item_name.toLowerCase().includes(a.toLowerCase()));return{selectedDishes:d,dishSearchTerm:a,selectedSectionOpen:r,remainingSectionOpen:o,setDishSearchTerm:s,setSelectedSectionOpen:i,setRemainingSectionOpen:j,setSelectedDishes:g,handleDishToggle:f,handleConfirmDishSelection:I,resetDishSelection:m,getFilteredDishes:p,getSelectedDishItems:n=>p(n).filter(M=>d.has(M.id)),getRemainingDishItems:n=>p(n).filter(M=>!d.has(M.id)),hasSelectedDishes:d.size>0,selectedDishesCount:d.size}}function xe(){const g=H({from:"/_authenticated/menu/customization/customization-in-store/detail/$customizationId"}).customizationId,a=ie({isEdit:!0,customizationId:g||""}),s=ae(),r=oe({onConfirm:t=>s.setMenuItems(t)}),i=pe(),o=me(),[j,f]=S.useState(!1),{currentBrandStores:I}=K(),{data:m,isLoading:p,error:C}=ne(g||""),{data:l=[],isLoading:n}=te({params:{list_store_uid:a.selectedStoreId,skip_limit:!0,active:1},enabled:!!a.selectedStoreId&&!!g});if(S.useEffect(()=>{var t;if(m&&!p&&(a.setExistingCustomization(m),a.setCustomizationName(m.name),a.setSelectedStoreId(m.storeUid||""),(t=m.data)!=null&&t.LstItem_Options)){const c=m.data.LstItem_Options.map(h=>({id:h.id,name:h.Name,minRequired:h.Min_Permitted,maxAllowed:h.Max_Permitted,items:h.LstItem_Id.map(u=>({id:u,name:u,price:0,code:u}))}));s.setCustomizationGroups(c)}},[m,p]),S.useEffect(()=>{if(l.length>0){if(s.customizationGroups.length>0){const t=s.customizationGroups.map(c=>({...c,items:c.items.map(h=>{const u=l.find($=>$.item_id===h.code);return u?{id:u.id,name:u.item_name,price:u.ots_price,code:u.item_id}:h})}));s.setCustomizationGroups(t)}if(m!=null&&m.listItem&&m.listItem.length>0){const t=new Set;m.listItem.forEach(c=>{const h=l.find(u=>u.item_id===c);h&&t.add(h.id)}),i.setSelectedDishes(t)}}},[l,m]),!g)return e.jsx(U,{});const x=()=>{s.handleCreateGroup(),o.setCreateGroupModalOpen(!0)},M=t=>{s.handleEditGroup(t),o.setCreateGroupModalOpen(!0)},z=()=>{o.handleCloseModal(),s.resetGroupForm(),r.resetSelection()},_=()=>{o.handleCloseAddItemModal(),r.resetSelection()},A=()=>{r.handleConfirmMenuItems(l),o.handleCloseAddItemModal()},R=()=>{r.setSelectedMenuItemsFromGroup(s.menuItems),o.handleAddMenuItem(a.selectedStoreId)},E=()=>{s.handleSaveGroup(l)&&z()},k=async()=>{if(!m){L.error("Đang tải dữ liệu customization. Vui lòng thử lại.");return}await a.handleSave(s.customizationGroups,i.selectedDishes,l)},F=()=>{o.setDishModalOpen(!0)},P=()=>i.selectedDishesCount===0?"Chọn món áp dụng":`${i.selectedDishesCount} món`,V=i.getSelectedDishItems(l),q=i.getRemainingDishItems(l),B=r.getSelectedMenuItemsList(l),X=r.getRemainingMenuItemsList(l);return p?e.jsx(W,{}):C?e.jsx(J,{}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(N,{variant:"ghost",size:"sm",onClick:a.handleBack,className:"flex items-center",children:e.jsx(he,{className:"h-4 w-4"})}),e.jsx(N,{type:"button",disabled:a.isSubmitting||!a.isFormValid,className:"min-w-[100px]",onClick:k,children:a.isSubmitting?"Đang cập nhật...":"Cập nhật"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:"Sửa customization"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{htmlFor:"customization-name",className:"min-w-[200px] text-sm font-medium",children:["Tên customization ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(T,{id:"customization-name",value:a.customizationName,onChange:t=>a.setCustomizationName(t.target.value),placeholder:"Nhập tên customization",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{htmlFor:"store-select",className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(Q,{value:a.selectedStoreId,onValueChange:a.setSelectedStoreId,children:[e.jsx(Y,{className:"flex-1",children:e.jsx(Z,{placeholder:"Chọn cửa hàng"})}),e.jsx(ee,{children:I.map(t=>e.jsx(se,{value:t.id,children:t.store_name},t.id))})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Áp dụng customization cho món"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Chọn các món mà customization này sẽ áp dụng"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(v,{className:"min-w-[200px] text-sm font-medium",children:"Món ăn"}),e.jsx("div",{className:"flex-1",children:e.jsx(N,{type:"button",variant:"outline",onClick:F,className:"w-full justify-start text-left",disabled:n,children:n?"Đang tải...":P()})})]})]}),e.jsx("div",{className:"flex justify-center pt-4",children:e.jsx(N,{onClick:x,children:"Tạo nhóm"})}),s.customizationGroups.length>0&&e.jsxs("div",{className:"space-y-6 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Danh sách nhóm đã tạo"}),s.customizationGroups.map(t=>e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:t.name}),e.jsxs("span",{className:"ml-2 text-sm text-gray-500",children:["(Chọn từ ",t.minRequired," đến ",t.maxAllowed," món)"]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{variant:"outline",size:"sm",onClick:()=>M(t.id),children:"Sửa"}),e.jsx(N,{variant:"outline",size:"sm",onClick:()=>s.handleDeleteGroup(t.id),className:"text-red-600 hover:text-red-700",children:"Xóa"})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4",children:t.items.map(c=>e.jsxs("div",{className:"rounded-md border bg-gray-50 p-3 text-center",children:[e.jsx("p",{className:"text-sm font-medium",children:c.name}),e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["(",c.code,")"]}),e.jsxs("p",{className:"mt-1 text-sm font-medium text-green-600",children:[c.price.toLocaleString("vi-VN")," ₫"]})]},c.id))})]},t.id))]})]})})}),e.jsx(re,{open:o.createGroupModalOpen,onOpenChange:o.setCreateGroupModalOpen,onCancel:z,onConfirm:E,onAddMenuItem:R,isEditing:s.isEditing,customizationName:a.customizationName,groupName:s.groupName,setGroupName:s.setGroupName,minRequired:s.minRequired,setMinRequired:s.setMinRequired,maxAllowed:s.maxAllowed,setMaxAllowed:s.setMaxAllowed,menuItems:s.menuItems}),e.jsx(le,{open:o.addItemModalOpen,onOpenChange:o.setAddItemModalOpen,onCancel:_,onConfirm:A,menuItemSearchTerm:r.menuItemSearchTerm,setMenuItemSearchTerm:r.setMenuItemSearchTerm,selectedMenuSectionOpen:r.selectedMenuSectionOpen,setSelectedMenuSectionOpen:r.setSelectedMenuSectionOpen,remainingMenuSectionOpen:r.remainingMenuSectionOpen,setRemainingMenuSectionOpen:r.setRemainingMenuSectionOpen,selectedMenuItems:r.selectedMenuItems,handleMenuItemToggle:r.handleMenuItemToggle,selectedMenuItemsList:B,remainingMenuItemsList:X}),e.jsx(ue,{open:o.dishModalOpen,onOpenChange:o.setDishModalOpen,onCancel:o.handleCloseDishModal,onConfirm:()=>{i.handleConfirmDishSelection(),o.handleCloseDishModal()},dishSearchTerm:i.dishSearchTerm,setDishSearchTerm:i.setDishSearchTerm,selectedSectionOpen:i.selectedSectionOpen,setSelectedSectionOpen:i.setSelectedSectionOpen,remainingSectionOpen:i.remainingSectionOpen,setRemainingSectionOpen:i.setRemainingSectionOpen,selectedDishes:i.selectedDishes,handleDishToggle:i.handleDishToggle,selectedDishItems:V,remainingDishItems:q}),e.jsx(ce,{open:j,onOpenChange:f,items:l,selectedItems:Array.from(i.selectedDishes),onItemsSelected:t=>{const c=new Set(t);i.setSelectedDishes(c),f(!1)}})]})}const as=function(){return e.jsx(xe,{})};export{as as component};
