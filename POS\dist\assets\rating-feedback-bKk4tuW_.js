import{j as e,r as i,B as h}from"./index-UcdZ5AHH.js";import{C as f,d as b,a as E,b as F}from"./card-ulE1yKb5.js";import{I as G}from"./input-CBpgGfUv.js";import{c as B}from"./createLucideIcon-D7O7McKr.js";import{D as A}from"./date-range-picker-4ETtQM5F.js";import{a as k,b as u}from"./subMonths-BRhS7Uii.js";import{s as y,f as S}from"./isSameMonth-C8JQo-AN.js";import"./popover-BeZit_vZ.js";import"./index-DPUGtNbb.js";import"./calendar-DZLqW2ag.js";import"./chevron-right-Dup7TmpK.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Q=B("star",O);function x({rating:m,maxRating:r=5,size:j="md",showValue:o=!1,className:N=""}){const g={sm:"h-3 w-3",md:"h-4 w-4",lg:"h-5 w-5"},_=Array.from({length:r},(v,c)=>{const t=c+1<=m;return e.jsx(Q,{className:`${g[j]} ${t?"fill-yellow-400 text-yellow-400":"fill-gray-200 text-gray-200"}`},c)});return e.jsxs("div",{className:`flex items-center gap-1 ${N}`,children:[_,o&&e.jsxs("span",{className:"text-sm text-gray-600 ml-1",children:["(",m,")"]})]})}function U({ratingData:m}){return e.jsx("div",{className:"space-y-3",children:m.map(r=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center gap-1 w-20",children:e.jsx(x,{rating:r.stars,size:"sm"})}),e.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden",children:e.jsx("div",{className:"bg-yellow-400 h-full rounded-full transition-all duration-300",style:{width:`${r.percentage}%`}})}),e.jsxs("div",{className:"text-sm text-blue-600 w-12 text-right",children:[r.percentage.toFixed(1),"%"]})]},r.stars))})}const Y={rate_total_count:125,rate_average:4.2,number_of_rate_1_star:5,number_of_rate_2_star:8,number_of_rate_3_star:15,number_of_rate_4_star:42,number_of_rate_5_star:55,report_by_pos:[{pos_name:"Cửa hàng Quận 1",total_rating:210,rating_count:50,average_rating:4.2,star_5:20,star_4:15,star_3:8,star_2:4,star_1:3},{pos_name:"Cửa hàng Quận 3",total_rating:185,rating_count:45,average_rating:4.1,star_5:18,star_4:12,star_3:10,star_2:3,star_1:2},{pos_name:"Cửa hàng Quận 7",total_rating:120,rating_count:30,average_rating:4,star_5:12,star_4:10,star_3:5,star_2:2,star_1:1}]};function q(){const[m,r]=i.useState(null),[j,o]=i.useState(null),[N,g]=i.useState(""),[_,v]=i.useState(""),[c,p]=i.useState("7days"),[t]=i.useState(Y);i.useEffect(()=>{d("7days")},[]);const C=(a,s)=>`${S(a,"dd/MM/yyyy")} - ${S(s,"dd/MM/yyyy")}`,d=a=>{const s=new Date;let n,l=k(s);switch(a){case"today":n=y(s);break;case"yesterday":n=y(u(s,1)),l=k(u(s,1));break;case"7days":n=y(u(s,6));break;case"15days":n=y(u(s,14));break;case"30days":n=y(u(s,29));break;default:return}r(n),o(l),g(C(n,l)),p(a)},z=a=>{g(a),p("");const s=/^(\d{2}\/\d{2}\/\d{4})\s*-\s*(\d{2}\/\d{2}\/\d{4})$/,n=a.match(s);if(n)try{const[,l,H]=n,[T,$,M]=l.split("/").map(Number),[I,L,P]=H.split("/").map(Number),D=new Date(M,$-1,T),w=new Date(P,L-1,I);!isNaN(D.getTime())&&!isNaN(w.getTime())&&(r(D),o(w))}catch(l){console.error("Invalid date format:",l)}},R=[{stars:5,count:t.number_of_rate_5_star,percentage:t.rate_total_count>0?t.number_of_rate_5_star/t.rate_total_count*100:0},{stars:4,count:t.number_of_rate_4_star,percentage:t.rate_total_count>0?t.number_of_rate_4_star/t.rate_total_count*100:0},{stars:3,count:t.number_of_rate_3_star,percentage:t.rate_total_count>0?t.number_of_rate_3_star/t.rate_total_count*100:0},{stars:2,count:t.number_of_rate_2_star,percentage:t.rate_total_count>0?t.number_of_rate_2_star/t.rate_total_count*100:0},{stars:1,count:t.number_of_rate_1_star,percentage:t.rate_total_count>0?t.number_of_rate_1_star/t.rate_total_count*100:0}];return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Phản hồi khách hàng"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Theo dõi và phân tích đánh giá của khách hàng"})]}),e.jsxs("div",{className:"flex justify-end items-center gap-2 mb-6",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(h,{variant:"outline",size:"sm",className:`text-xs ${c==="today"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("today"),children:"Hôm nay"}),e.jsx(h,{variant:"outline",size:"sm",className:`text-xs ${c==="yesterday"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("yesterday"),children:"Hôm qua"}),e.jsx(h,{variant:"outline",size:"sm",className:`text-xs ${c==="7days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("7days"),children:"7 ngày trước"}),e.jsx(h,{variant:"outline",size:"sm",className:`text-xs ${c==="15days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("15days"),children:"15 ngày trước"}),e.jsx(h,{variant:"outline",size:"sm",className:`text-xs ${c==="30days"?"bg-green-100 text-green-700 border-green-300":""}`,onClick:()=>d("30days"),children:"30 ngày trước"})]}),e.jsx(A,{startDate:m,endDate:j,onDateChange:(a,s)=>{r(a),o(s),a&&s&&(g(C(a,s)),p(""))},dateRange:N,onDateRangeChange:z})]}),e.jsx(f,{className:"mb-6",children:e.jsx(b,{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-10 gap-8",children:[e.jsxs("div",{className:"col-span-3 space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl font-bold text-gray-900 mb-2",children:t.rate_total_count}),e.jsx("div",{className:"text-sm text-gray-600 uppercase tracking-wide",children:"LƯỢT ĐÁNH GIÁ"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl font-bold text-gray-900 mb-2",children:t.rate_average.toFixed(2)}),e.jsx("div",{className:"text-sm text-gray-600 uppercase tracking-wide",children:"ĐIỂM TRUNG BÌNH"})]})]}),e.jsx("div",{className:"col-span-7",children:e.jsx(U,{ratingData:R})})]})})}),e.jsxs(f,{children:[e.jsx(E,{children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"flex-1"}),e.jsx(F,{className:"text-base font-medium text-gray-700 text-center flex-1",children:"ĐÁNH GIÁ THEO CỬA HÀNG"}),e.jsx("div",{className:"flex-1 flex justify-end",children:e.jsx(G,{placeholder:"Tìm kiếm nhanh",value:_,onChange:a=>v(a.target.value),className:"w-64"})})]})}),e.jsx(b,{children:e.jsx("div",{className:"overflow-hidden",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b bg-gray-50",children:[e.jsx("th",{className:"text-left p-3 text-sm font-medium text-gray-600",children:"Cửa hàng"}),e.jsx("th",{className:"text-center p-3 text-sm font-medium text-gray-600",children:"Tổng điểm"}),e.jsx("th",{className:"text-center p-3 text-sm font-medium text-gray-600",children:"Số lượt đánh giá"}),e.jsx("th",{className:"text-center p-3 text-sm font-medium text-gray-600",children:"Trung bình"}),e.jsx("th",{className:"text-center p-3 text-sm font-medium text-gray-600",children:e.jsx(x,{rating:5,size:"sm"})}),e.jsx("th",{className:"text-center p-3 text-sm font-medium text-gray-600",children:e.jsx(x,{rating:4,size:"sm"})}),e.jsx("th",{className:"text-center p-3 text-sm font-medium text-gray-600",children:e.jsx(x,{rating:3,size:"sm"})}),e.jsx("th",{className:"text-center p-3 text-sm font-medium text-gray-600",children:e.jsx(x,{rating:2,size:"sm"})}),e.jsx("th",{className:"text-center p-3 text-sm font-medium text-gray-600",children:e.jsx(x,{rating:1,size:"sm"})})]})}),e.jsx("tbody",{children:t.report_by_pos.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:9,className:"text-center p-8 text-gray-500",children:"No data available in table"})}):t.report_by_pos.filter(a=>_===""||a.pos_name.toLowerCase().includes(_.toLowerCase())).map((a,s)=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsx("td",{className:"p-3 text-sm text-gray-900",children:a.pos_name}),e.jsx("td",{className:"p-3 text-sm text-gray-900 text-center",children:a.total_rating}),e.jsx("td",{className:"p-3 text-sm text-gray-900 text-center",children:a.rating_count}),e.jsx("td",{className:"p-3 text-sm text-gray-900 text-center",children:a.average_rating.toFixed(2)}),e.jsx("td",{className:"p-3 text-sm text-gray-900 text-center",children:a.star_5}),e.jsx("td",{className:"p-3 text-sm text-gray-900 text-center",children:a.star_4}),e.jsx("td",{className:"p-3 text-sm text-gray-900 text-center",children:a.star_3}),e.jsx("td",{className:"p-3 text-sm text-gray-900 text-center",children:a.star_2}),e.jsx("td",{className:"p-3 text-sm text-gray-900 text-center",children:a.star_1})]},s))})]})})})]}),e.jsx(f,{className:"mt-6",children:e.jsxs(b,{className:"p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-4 text-center",children:"KHÁCH HÀNG BÌNH LUẬN"}),e.jsx("div",{className:"text-center text-gray-500 py-8",children:"Chưa có bình luận"})]})})]})}const ne=q;export{ne as component};
