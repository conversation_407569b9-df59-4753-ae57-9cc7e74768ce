import{e as L,r as P,j as e,B}from"./index-UcdZ5AHH.js";import"./crm-api-APQEjHWd.js";import"./pos-api-j20LMGrC.js";import"./user-9ajIul7r.js";import{u as T,a as I}from"./use-pos-parent-settings-BYvn8rEV.js";import"./date-range-picker-DxA68ufO.js";import"./form-D_U5B5Go.js";import{I as c}from"./input-CBpgGfUv.js";import{S as E,a as F,b as H,c as R,d as g}from"./select-DOexGcsG.js";import"./useQuery-B4yhTgGk.js";import"./utils-km2FGkQ4.js";import"./query-keys-DQo7uRnN.js";import"./settings-api-BaqQi8Fw.js";import"./calendar-BZ1UqQsL.js";import"./createLucideIcon-D7O7McKr.js";import"./index-DPUGtNbb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-Dup7TmpK.js";import"./react-icons.esm-DpPH1mSm.js";import"./popover-BeZit_vZ.js";import"./index-MuNXZ_zP.js";import"./index-BKS-UfoD.js";import"./check-vnaEv-AC.js";function V(){const x=L({strict:!1}).pos_parent||"BRAND-953H",[l,N]=P.useState({phone:"",name:"",birthday:"",gender:"",email:"",address:""}),{data:u,isLoading:f}=T({pos_parent:x}),{data:y,isLoading:_}=I({pos_parent:x}),S=f||_,o=(n,m)=>{N(h=>({...h,[n]:m}))},w=n=>{n.preventDefault(),console.log("Form submitted:",l)};if(S)return e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-gray-100",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Đang tải..."})]})});const a=u==null?void 0:u.data,i=y,b=(a==null?void 0:a.banner)||(i==null?void 0:i.image)||"",j=(a==null?void 0:a.logo)||(i==null?void 0:i.Logo_Image)||"",C=(i==null?void 0:i.name)||"Tutimi-Bình Lợi";return e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-gray-100 p-4",children:e.jsxs("div",{className:"w-full max-w-md overflow-hidden rounded-lg bg-white p-4 shadow-lg",children:[e.jsxs("div",{className:"relative h-48 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600",children:[b&&e.jsx("img",{src:b,alt:"Header",className:"h-full w-full rounded-lg object-cover"}),e.jsxs("div",{className:"absolute bottom-4 left-4 flex items-center space-x-2",children:[j&&e.jsx("img",{src:j,alt:"Logo",className:"h-8 w-8 rounded bg-white"}),e.jsx("span",{className:"text-sm font-semibold text-white",children:C})]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("h1",{className:"mb-6 text-center text-2xl font-bold text-gray-800",children:"Đăng Ký Thành Viên"}),e.jsxs("form",{onSubmit:w,className:"space-y-4",children:[e.jsx("div",{children:e.jsx(c,{id:"phone",type:"tel",placeholder:"Nhập số điện thoại của bạn",value:l.phone,onChange:n=>o("phone",n.target.value),required:!0,className:"mt-1"})}),(()=>{var v;const n=((v=a==null?void 0:a.form_data)==null?void 0:v.filter(t=>t.active&&t.field_id!=="phone"))||[],m=[];let h=!1;return n.forEach((t,q)=>{if(h){h=!1;return}const p=r=>{switch(r.field_id){case"name":return e.jsx(c,{id:r.field_id,type:"text",placeholder:"Họ và tên",value:l.name,onChange:s=>o("name",s.target.value),required:r.require,className:"mt-1"});case"birthday":return e.jsx(c,{id:r.field_id,type:"date",placeholder:"Chọn ngày sinh",value:l.birthday,onChange:s=>o("birthday",s.target.value),required:r.require,className:"mt-1"});case"gender":return e.jsxs(E,{value:l.gender,onValueChange:s=>o("gender",s),children:[e.jsx(F,{className:"mt-1",children:e.jsx(H,{placeholder:"Chọn giới tính"})}),e.jsxs(R,{children:[e.jsx(g,{value:"male",children:"Nam"}),e.jsx(g,{value:"female",children:"Nữ"}),e.jsx(g,{value:"other",children:"Khác"})]})]});case"email":return e.jsx(c,{id:r.field_id,type:"email",placeholder:"Email",value:l.email,onChange:s=>o("email",s.target.value),required:r.require,className:"mt-1"});case"address":return e.jsx(c,{id:r.field_id,type:"text",placeholder:"Địa chỉ",value:l.address,onChange:s=>o("address",s.target.value),required:r.require,className:"mt-1"});default:return e.jsx(c,{id:r.field_id,type:"text",placeholder:r.field_name,required:r.require,className:"mt-1"})}},d=n[q+1];if(t.field_id==="birthday"&&(d==null?void 0:d.field_id)==="gender"||t.field_id==="gender"&&(d==null?void 0:d.field_id)==="birthday"){const r=t.field_id==="birthday"?t:d,s=t.field_id==="gender"?t:d;m.push(e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsx("div",{children:p(r)}),e.jsx("div",{children:p(s)})]},`${r.field_id}-${s.field_id}`)),h=!0}else m.push(e.jsx("div",{children:p(t)},t.field_id))}),m})(),e.jsx(B,{type:"submit",className:"w-full bg-blue-600 hover:bg-blue-700",children:"Xác Thực Thông Tin"})]})]})]})})}const oe=V;export{oe as component};
