import{u as f}from"./useQuery-HgcIHxlE.js";import{u as T,l as g,a3 as m}from"./index-CVQ6JZo2.js";import{u as _}from"./useMutation-ZsyDznMu.js";import{b as d}from"./pos-api-mRg02iop.js";import{Q as c}from"./query-keys-3lmd-xp6.js";const o=new Map,u=new Map,I=15*60*1e3,y={getItemTypes:async e=>{const t=`${e.company_uid}-${e.brand_uid}-${e.skip_limit||!1}-${e.page||1}-${e.limit||50}-${e.search||""}-${e.active??1}-${e.store_uid||"all"}-${e.apply_with_store||0}`,a=o.get(t);if(a&&Date.now()-a.timestamp<I)return a.data;const n=u.get(t);if(n)return n;const s=(async()=>{try{const i=new URLSearchParams;i.append("company_uid",e.company_uid),i.append("brand_uid",e.brand_uid),e.skip_limit!==void 0&&i.append("skip_limit",e.skip_limit.toString()),e.page&&i.append("page",e.page.toString()),e.limit&&i.append("limit",e.limit.toString()),e.search&&i.append("search",e.search),e.active!==void 0&&i.append("active",e.active.toString()),e.store_uid&&i.append("store_uid",e.store_uid),e.apply_with_store!==void 0&&i.append("apply_with_store",e.apply_with_store.toString());const r=await d.get(`/mdata/v1/item-types?${i.toString()}`);if(!r.data||typeof r.data!="object")throw new Error("Invalid response format from item types API");const l=r.data;return o.set(t,{data:l,timestamp:Date.now()}),l}finally{u.delete(t)}})();return u.set(t,s),s},clearCache:()=>{o.clear(),u.clear()},getCacheStats:()=>({cacheSize:o.size,pendingRequests:u.size}),getItemTypeById:e=>{for(const t of o.values()){const a=t.data.data.find(n=>n.id===e);if(a)return a}},getItemTypeByTypeId:e=>{for(const t of o.values()){const a=t.data.data.find(n=>n.item_type_id===e);if(a)return a}},getItemTypeByIdFromServer:async e=>{const t=await d.get(`/mdata/v1/item-type?id=${e}`);if(!t.data||typeof t.data!="object")throw new Error("Invalid response format from item type API");const a=t.data;if(!a.data)throw new Error("Item type not found");return a.data},updateItemTypeStatus:async e=>{const t={...e,list_item:e.list_item||[]},a=await d.put("/mdata/v1/item-type",t,{headers:{Accept:"application/json, text/plain, */*","Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return o.clear(),a.data.data},updateItemType:async e=>{const t={...e,list_item:e.list_item||[]},a=await d.put("/mdata/v1/item-type",t,{headers:{Accept:"application/json, text/plain, */*","Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return o.clear(),a.data.data},deleteItemType:async e=>{const t=localStorage.getItem("pos_user_data"),a=localStorage.getItem("pos_brands_data");let n="",s="";if(t)try{n=JSON.parse(t).company_uid||""}catch{}if(a)try{const r=JSON.parse(a);Array.isArray(r)&&r.length>0&&(s=r[0].id||"")}catch{}if(!n||!s)throw new Error("Company or brand UID not found in localStorage");const i=new URLSearchParams;i.append("company_uid",n),i.append("brand_uid",s),i.append("id",e),await d.delete(`/mdata/v1/item-type?${i.toString()}`),o.clear()},bulkCreateItemTypes:async(e,t)=>{const a=localStorage.getItem("pos_user_data"),n=localStorage.getItem("pos_brands_data");let s="",i="";if(a)try{s=JSON.parse(a).company_uid||""}catch{}if(n)try{const p=JSON.parse(n);Array.isArray(p)&&p.length>0&&(i=p[0].id||"")}catch{}if(!s||!i)throw new Error("Company or brand UID not found in localStorage");const r=e.map(p=>({...p,company_uid:s,brand_uid:i,...t&&{store_uid:t}})),l=await d.post("/mdata/v1/item-types",r,{headers:{Accept:"application/json, text/plain, */*","Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return o.clear(),l.data.data||[]}};function C(e={}){const{enabled:t=!0,...a}=e,{company:n}=T(r=>r.auth),{selectedBrand:s}=g(),i={company_uid:(n==null?void 0:n.id)||"",brand_uid:(s==null?void 0:s.id)||"",skip_limit:!0,...a};return f({queryKey:[c.ITEM_TYPES,i],queryFn:async()=>(await y.getItemTypes(i)).data||[],enabled:t&&!!(n!=null&&n.id&&(s!=null&&s.id)),staleTime:5*60*1e3,gcTime:10*60*1e3})}const q=()=>{const e=m();return _({mutationFn:t=>y.updateItemTypeStatus(t),onSuccess:(t,a)=>{e.invalidateQueries({queryKey:[c.ITEM_TYPES]}),e.invalidateQueries({queryKey:[c.ITEM_CATEGORIES,"single",a.id]}),e.invalidateQueries({queryKey:[c.ITEM_CATEGORIES]})}})},D=()=>{const e=m();return _({mutationFn:t=>y.updateItemTypeStatus(t),onSuccess:(t,a)=>{e.invalidateQueries({queryKey:[c.ITEM_TYPES]}),e.invalidateQueries({queryKey:[c.ITEM_CATEGORIES,"single",a.id]})}})},A=()=>{const e=m();return _({mutationFn:t=>y.deleteItemType(t),onSuccess:()=>{e.invalidateQueries({queryKey:[c.ITEM_TYPES]})}})},U=e=>{const t=m();return _({mutationFn:a=>y.bulkCreateItemTypes(a,e),onSuccess:()=>{t.invalidateQueries({queryKey:[c.ITEM_TYPES]})}})};export{U as a,q as b,A as c,D as d,y as i,C as u};
